{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/svelte.mjs"], "sourcesContent": ["import javascript from './javascript.mjs';\nimport typescript from './typescript.mjs';\nimport coffee from './coffee.mjs';\nimport stylus from './stylus.mjs';\nimport sass from './sass.mjs';\nimport css from './css.mjs';\nimport scss from './scss.mjs';\nimport less from './less.mjs';\nimport postcss from './postcss.mjs';\nimport pug from './pug.mjs';\nimport markdown from './markdown.mjs';\nimport './html.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"Svelte\", \"fileTypes\": [\"svelte\"], \"injections\": { \"L:(meta.script.svelte | meta.style.svelte) (meta.lang.js | meta.lang.javascript) - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.js\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.svelte\", \"patterns\": [{ \"include\": \"source.js\" }] }] }, \"L:(meta.script.svelte | meta.style.svelte) (meta.lang.ts | meta.lang.typescript) - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.ts\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.svelte\", \"patterns\": [{ \"include\": \"source.ts\" }] }] }, \"L:(meta.script.svelte | meta.style.svelte) meta.lang.coffee - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.coffee\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.svelte\", \"patterns\": [{ \"include\": \"source.coffee\" }] }] }, \"L:(source.ts, source.js, source.coffee)\": { \"patterns\": [{ \"match\": `(?<![_$./'\"[:alnum:]])\\\\$(?=[_[:alpha:]][_$[:alnum:]]*)`, \"name\": \"punctuation.definition.variable.svelte\" }, { \"match\": `(?<![_$./'\"[:alnum:]])(\\\\$\\\\$)(?=props|restProps|slots)`, \"name\": \"punctuation.definition.variable.svelte\" }] }, \"L:meta.script.svelte - meta.lang - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.js\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.svelte\", \"patterns\": [{ \"include\": \"source.js\" }] }] }, \"L:meta.style.svelte - meta.lang - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.css\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.svelte\", \"patterns\": [{ \"include\": \"source.css\" }] }] }, \"L:meta.style.svelte meta.lang.css - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.css\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.svelte\", \"patterns\": [{ \"include\": \"source.css\" }] }] }, \"L:meta.style.svelte meta.lang.less - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.css.less\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.svelte\", \"patterns\": [{ \"include\": \"source.css.less\" }] }] }, \"L:meta.style.svelte meta.lang.postcss - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.css.postcss\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.svelte\", \"patterns\": [{ \"include\": \"source.css.postcss\" }] }] }, \"L:meta.style.svelte meta.lang.sass - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.sass\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.svelte\", \"patterns\": [{ \"include\": \"source.sass\" }] }] }, \"L:meta.style.svelte meta.lang.scss - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.css.scss\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.svelte\", \"patterns\": [{ \"include\": \"source.css.scss\" }] }] }, \"L:meta.style.svelte meta.lang.stylus - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"source.stylus\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.svelte\", \"patterns\": [{ \"include\": \"source.stylus\" }] }] }, \"L:meta.template.svelte - meta.lang - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)\\\\s\", \"end\": \"(?=</template)\", \"patterns\": [{ \"include\": \"#scope\" }] }] }, \"L:meta.template.svelte meta.lang.pug - (meta source)\": { \"patterns\": [{ \"begin\": \"(?<=>)(?!</)\", \"contentName\": \"text.pug\", \"end\": \"(?=</)\", \"name\": \"meta.embedded.block.svelte\", \"patterns\": [{ \"include\": \"text.pug\" }] }] } }, \"name\": \"svelte\", \"patterns\": [{ \"include\": \"#scope\" }], \"repository\": { \"attributes\": { \"patterns\": [{ \"include\": \"#attributes-directives\" }, { \"include\": \"#attributes-keyvalue\" }, { \"include\": \"#attributes-interpolated\" }] }, \"attributes-directives\": { \"begin\": \"(?<!<)(on|use|bind|transition|in|out|animate|let|class|style)(:)(?:((?:--)?[_$[:alpha:]][_\\\\-$[:alnum:]]*(?=\\\\s*=))|((?:--)?[_$[:alpha:]][_\\\\-$[:alnum:]]*))((\\\\|\\\\w+)*)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#attributes-directives-keywords\" }] }, \"2\": { \"name\": \"punctuation.definition.keyword.svelte\" }, \"3\": { \"patterns\": [{ \"include\": \"#attributes-directives-types-assigned\" }] }, \"4\": { \"patterns\": [{ \"include\": \"#attributes-directives-types\" }] }, \"5\": { \"patterns\": [{ \"match\": \"\\\\w+\", \"name\": \"support.function.svelte\" }, { \"match\": \"\\\\|\", \"name\": \"punctuation.separator.svelte\" }] } }, \"end\": \"(?=\\\\s*+[^=\\\\s])\", \"name\": \"meta.directive.$1.svelte\", \"patterns\": [{ \"begin\": \"=\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.key-value.svelte\" } }, \"end\": \"(?<=[^\\\\s=])(?!\\\\s*=)|(?=/?>)\", \"patterns\": [{ \"include\": \"#attributes-value\" }] }] }, \"attributes-directives-keywords\": { \"patterns\": [{ \"match\": \"on|use|bind\", \"name\": \"keyword.control.svelte\" }, { \"match\": \"transition|in|out|animate\", \"name\": \"keyword.other.animation.svelte\" }, { \"match\": \"let\", \"name\": \"storage.type.svelte\" }, { \"match\": \"class|style\", \"name\": \"entity.other.attribute-name.svelte\" }] }, \"attributes-directives-types\": { \"patterns\": [{ \"match\": \"(?<=(on):).*$\", \"name\": \"entity.name.type.svelte\" }, { \"match\": \"(?<=(bind):).*$\", \"name\": \"variable.parameter.svelte\" }, { \"match\": \"(?<=(use|transition|in|out|animate):).*$\", \"name\": \"variable.function.svelte\" }, { \"match\": \"(?<=(let|class|style):).*$\", \"name\": \"variable.parameter.svelte\" }] }, \"attributes-directives-types-assigned\": { \"patterns\": [{ \"match\": \"(?<=(bind):)this$\", \"name\": \"variable.language.svelte\" }, { \"match\": \"(?<=(bind):).*$\", \"name\": \"entity.name.type.svelte\" }, { \"match\": \"(?<=(class):).*$\", \"name\": \"entity.other.attribute-name.class.svelte\" }, { \"match\": \"(?<=(style):).*$\", \"name\": \"support.type.property-name.svelte\" }, { \"include\": \"#attributes-directives-types\" }] }, \"attributes-generics\": { \"begin\": `(generics)(=)([\"'])`, \"beginCaptures\": { \"1\": { \"name\": \"entity.other.attribute-name.svelte\" }, \"2\": { \"name\": \"punctuation.separator.key-value.svelte\" }, \"3\": { \"name\": \"punctuation.definition.string.begin.svelte\" } }, \"contentName\": \"meta.embedded.expression.svelte source.ts\", \"end\": \"(\\\\3)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.svelte\" } }, \"patterns\": [{ \"include\": \"#type-parameters\" }] }, \"attributes-interpolated\": { \"begin\": \"(?<!:|=)\\\\s*({)\", \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.svelte\" } }, \"contentName\": \"meta.embedded.expression.svelte source.ts\", \"end\": \"(\\\\})\", \"patterns\": [{ \"include\": \"source.ts\" }] }, \"attributes-keyvalue\": { \"begin\": \"((?:--)?[_$[:alpha:]][_\\\\-$[:alnum:]]*)\", \"beginCaptures\": { \"0\": { \"patterns\": [{ \"match\": \"--.*\", \"name\": \"support.type.property-name.svelte\" }, { \"match\": \".*\", \"name\": \"entity.other.attribute-name.svelte\" }] } }, \"end\": \"(?=\\\\s*+[^=\\\\s])\", \"name\": \"meta.attribute.$1.svelte\", \"patterns\": [{ \"begin\": \"=\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.key-value.svelte\" } }, \"end\": \"(?<=[^\\\\s=])(?!\\\\s*=)|(?=/?>)\", \"patterns\": [{ \"include\": \"#attributes-value\" }] }] }, \"attributes-value\": { \"patterns\": [{ \"include\": \"#interpolation\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.svelte\" }, \"2\": { \"name\": \"constant.numeric.decimal.svelte\" }, \"3\": { \"name\": \"punctuation.definition.string.end.svelte\" }, \"4\": { \"name\": \"constant.numeric.decimal.svelte\" } }, \"match\": `(?:(['\"])([0-9._]+[\\\\w%]{,4})(\\\\1))|(?:([0-9._]+[\\\\w%]{,4})(?=\\\\s|/?>))` }, { \"match\": \"([^\\\\s\\\"'=<>`/]|/(?!>))+\", \"name\": \"string.unquoted.svelte\", \"patterns\": [{ \"include\": \"#interpolation\" }] }, { \"begin\": `(['\"])`, \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.svelte\" } }, \"end\": \"\\\\1\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.svelte\" } }, \"name\": \"string.quoted.svelte\", \"patterns\": [{ \"include\": \"#interpolation\" }] }] }, \"comments\": { \"begin\": \"<!--\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.svelte\" } }, \"end\": \"-->\", \"name\": \"comment.block.svelte\", \"patterns\": [{ \"begin\": \"(@)(component)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.keyword.svelte\" }, \"2\": { \"name\": \"storage.type.class.component.svelte keyword.declaration.class.component.svelte\" } }, \"contentName\": \"comment.block.documentation.svelte\", \"end\": \"(?=-->)\", \"patterns\": [{ \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"text.html.markdown\" }] } }, \"match\": \".*?(?=-->)\" }, { \"include\": \"text.html.markdown\" }] }, { \"match\": \"\\\\G-?>|<!--(?!>)|<!-(?=-->)|--!>\", \"name\": \"invalid.illegal.characters-not-allowed-here.svelte\" }] }, \"destructuring\": { \"patterns\": [{ \"begin\": \"(?={)\", \"end\": \"(?<=})\", \"name\": \"meta.embedded.expression.svelte source.ts\", \"patterns\": [{ \"include\": \"source.ts#object-binding-pattern\" }] }, { \"begin\": \"(?=\\\\[)\", \"end\": \"(?<=\\\\])\", \"name\": \"meta.embedded.expression.svelte source.ts\", \"patterns\": [{ \"include\": \"source.ts#array-binding-pattern\" }] }] }, \"destructuring-const\": { \"patterns\": [{ \"begin\": \"(?={)\", \"end\": \"(?<=})\", \"name\": \"meta.embedded.expression.svelte source.ts\", \"patterns\": [{ \"include\": \"source.ts#object-binding-pattern-const\" }] }, { \"begin\": \"(?=\\\\[)\", \"end\": \"(?<=\\\\])\", \"name\": \"meta.embedded.expression.svelte source.ts\", \"patterns\": [{ \"include\": \"source.ts#array-binding-pattern-const\" }] }] }, \"interpolation\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.begin.svelte\" } }, \"contentName\": \"meta.embedded.expression.svelte source.ts\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.svelte\" } }, \"patterns\": [{ \"begin\": \"\\\\G\\\\s*(?={)\", \"end\": \"(?<=})\", \"patterns\": [{ \"include\": \"source.ts#object-literal\" }] }, { \"include\": \"source.ts\" }] }] }, \"scope\": { \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#special-tags\" }, { \"include\": \"#tags\" }, { \"include\": \"#interpolation\" }, { \"begin\": \"(?<=>|})\", \"end\": \"(?=<|{)\", \"name\": \"text.svelte\" }] }, \"special-tags\": { \"patterns\": [{ \"include\": \"#special-tags-void\" }, { \"include\": \"#special-tags-block-begin\" }, { \"include\": \"#special-tags-block-end\" }] }, \"special-tags-block-begin\": { \"begin\": \"({)\\\\s*(#([a-z]*))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.block.begin.svelte\" }, \"2\": { \"patterns\": [{ \"include\": \"#special-tags-keywords\" }] } }, \"end\": \"(})\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.end.svelte\" } }, \"name\": \"meta.special.$3.svelte meta.special.start.svelte\", \"patterns\": [{ \"include\": \"#special-tags-modes\" }] }, \"special-tags-block-end\": { \"begin\": \"({)\\\\s*(/([a-z]*))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.block.begin.svelte\" }, \"2\": { \"patterns\": [{ \"include\": \"#special-tags-keywords\" }] } }, \"end\": \"(})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.block.end.svelte\" } }, \"name\": \"meta.special.$3.svelte meta.special.end.svelte\" }, \"special-tags-keywords\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.keyword.svelte\" }, \"2\": { \"patterns\": [{ \"match\": \"if|else\\\\s+if|else\", \"name\": \"keyword.control.conditional.svelte\" }, { \"match\": \"each|key\", \"name\": \"keyword.control.svelte\" }, { \"match\": \"await|then|catch\", \"name\": \"keyword.control.flow.svelte\" }, { \"match\": \"snippet\", \"name\": \"keyword.control.svelte\" }, { \"match\": \"html\", \"name\": \"keyword.other.svelte\" }, { \"match\": \"render\", \"name\": \"keyword.other.svelte\" }, { \"match\": \"debug\", \"name\": \"keyword.other.debugger.svelte\" }, { \"match\": \"const\", \"name\": \"storage.type.svelte\" }] } }, \"match\": \"([#@/:])(else\\\\s+if|[a-z]*)\" }, \"special-tags-modes\": { \"patterns\": [{ \"begin\": \"(?<=(if|key|then|catch|snippet|html|render).*?)\\\\G\", \"end\": \"(?=})\", \"name\": \"meta.embedded.expression.svelte source.ts\", \"patterns\": [{ \"include\": \"source.ts\" }] }, { \"begin\": \"(?<=const.*?)\\\\G\", \"end\": \"(?=})\", \"patterns\": [{ \"include\": \"#destructuring-const\" }, { \"begin\": \"\\\\G\\\\s*([_$[:alpha:]][_$[:alnum:]]+)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"variable.other.constant.svelte\" } }, \"end\": \"(?=\\\\=)\" }, { \"begin\": \"(?=\\\\=)\", \"end\": \"(?=})\", \"name\": \"meta.embedded.expression.svelte source.ts\", \"patterns\": [{ \"include\": \"source.ts\" }] }] }, { \"begin\": \"(?<=each.*?)\\\\G\", \"end\": \"(?=})\", \"patterns\": [{ \"begin\": \"\\\\G\\\\s*?(?=\\\\S)\", \"contentName\": \"meta.embedded.expression.svelte source.ts\", \"end\": \"(?=(?:^\\\\s*|\\\\s+)(as))\", \"patterns\": [{ \"include\": \"source.ts\" }] }, { \"begin\": \"(as)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.as.svelte\" } }, \"end\": \"(?=})\", \"patterns\": [{ \"include\": \"#destructuring\" }, { \"begin\": \"\\\\(\", \"captures\": { \"0\": { \"name\": \"meta.brace.round.svelte\" } }, \"contentName\": \"meta.embedded.expression.svelte source.ts\", \"end\": \"\\\\)|(?=})\", \"patterns\": [{ \"include\": \"source.ts\" }] }, { \"captures\": { \"1\": { \"name\": \"meta.embedded.expression.svelte source.ts\", \"patterns\": [{ \"include\": \"source.ts\" }] } }, \"match\": \"(\\\\s*([_$[:alpha:]][_$[:alnum:]]*)\\\\s*)\" }, { \"match\": \",\", \"name\": \"punctuation.separator.svelte\" }] }] }, { \"begin\": \"(?<=await.*?)\\\\G\", \"end\": \"(?=})\", \"patterns\": [{ \"begin\": \"\\\\G\\\\s*?(?=\\\\S)\", \"contentName\": \"meta.embedded.expression.svelte source.ts\", \"end\": \"\\\\s+(then)|(?=})\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.flow.svelte\" } }, \"patterns\": [{ \"include\": \"source.ts\" }] }, { \"begin\": \"(?<=then\\\\b)\", \"contentName\": \"meta.embedded.expression.svelte source.ts\", \"end\": \"(?=})\", \"patterns\": [{ \"include\": \"source.ts\" }] }] }, { \"begin\": \"(?<=debug.*?)\\\\G\", \"end\": \"(?=})\", \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"meta.embedded.expression.svelte source.ts\", \"patterns\": [{ \"include\": \"source.ts\" }] } }, \"match\": \"[_$[:alpha:]][_$[:alnum:]]*\" }, { \"match\": \",\", \"name\": \"punctuation.separator.svelte\" }] }] }, \"special-tags-void\": { \"begin\": \"({)\\\\s*((?:[@:])(else\\\\s+if|[a-z]*))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.block.begin.svelte\" }, \"2\": { \"patterns\": [{ \"include\": \"#special-tags-keywords\" }] } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.end.svelte\" } }, \"name\": \"meta.special.$3.svelte\", \"patterns\": [{ \"include\": \"#special-tags-modes\" }] }, \"tags\": { \"patterns\": [{ \"include\": \"#tags-lang\" }, { \"include\": \"#tags-void\" }, { \"include\": \"#tags-general-end\" }, { \"include\": \"#tags-general-start\" }] }, \"tags-end-node\": { \"captures\": { \"1\": { \"name\": \"meta.tag.end.svelte punctuation.definition.tag.begin.svelte\" }, \"2\": { \"name\": \"meta.tag.end.svelte\", \"patterns\": [{ \"include\": \"#tags-name\" }] }, \"3\": { \"name\": \"meta.tag.end.svelte punctuation.definition.tag.end.svelte\" }, \"4\": { \"name\": \"meta.tag.start.svelte punctuation.definition.tag.end.svelte\" } }, \"match\": \"(</)(.*?)\\\\s*(>)|(/>)\" }, \"tags-general-end\": { \"begin\": \"(</)([^/\\\\s>]*)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.tag.end.svelte punctuation.definition.tag.begin.svelte\" }, \"2\": { \"name\": \"meta.tag.end.svelte\", \"patterns\": [{ \"include\": \"#tags-name\" }] } }, \"end\": \"(>)\", \"endCaptures\": { \"1\": { \"name\": \"meta.tag.end.svelte punctuation.definition.tag.end.svelte\" } }, \"name\": \"meta.scope.tag.$2.svelte\" }, \"tags-general-start\": { \"begin\": \"(<)([^/\\\\s>/]*)\", \"beginCaptures\": { \"0\": { \"patterns\": [{ \"include\": \"#tags-start-node\" }] } }, \"end\": \"(/?>)\", \"endCaptures\": { \"1\": { \"name\": \"meta.tag.start.svelte punctuation.definition.tag.end.svelte\" } }, \"name\": \"meta.scope.tag.$2.svelte\", \"patterns\": [{ \"include\": \"#tags-start-attributes\" }] }, \"tags-lang\": { \"begin\": \"<(script|style|template)\", \"beginCaptures\": { \"0\": { \"patterns\": [{ \"include\": \"#tags-start-node\" }] } }, \"end\": \"</\\\\1\\\\s*>|/>\", \"endCaptures\": { \"0\": { \"patterns\": [{ \"include\": \"#tags-end-node\" }] } }, \"name\": \"meta.$1.svelte\", \"patterns\": [{ \"begin\": `\\\\G(?=\\\\s*[^>]*?(type|lang)\\\\s*=\\\\s*(['\"]|)(?:text/)?(\\\\w+)\\\\2)`, \"end\": \"(?=</|/>)\", \"name\": \"meta.lang.$3.svelte\", \"patterns\": [{ \"include\": \"#tags-lang-start-attributes\" }] }, { \"include\": \"#tags-lang-start-attributes\" }] }, \"tags-lang-start-attributes\": { \"begin\": \"\\\\G\", \"end\": \"(?=/>)|>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.svelte\" } }, \"name\": \"meta.tag.start.svelte\", \"patterns\": [{ \"include\": \"#attributes-generics\" }, { \"include\": \"#attributes\" }] }, \"tags-name\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.control.svelte\" }, \"2\": { \"name\": \"punctuation.definition.keyword.svelte\" }, \"3\": { \"name\": \"entity.name.tag.svelte\" } }, \"match\": \"(svelte)(:)([a-z][\\\\w0-9:-]*)\" }, { \"match\": \"slot\", \"name\": \"keyword.control.svelte\" }, { \"match\": \"[A-Z][a-zA-Z0-9_]*\", \"name\": \"support.class.component.svelte\" }, { \"match\": \"[a-z][\\\\w0-9:]*-[\\\\w0-9:-]*\", \"name\": \"meta.tag.custom.svelte entity.name.tag.svelte\" }, { \"match\": \"[a-z][\\\\w0-9:-]*\", \"name\": \"entity.name.tag.svelte\" }] }, \"tags-start-attributes\": { \"begin\": \"\\\\G\", \"end\": \"(?=/?>)\", \"name\": \"meta.tag.start.svelte\", \"patterns\": [{ \"include\": \"#attributes\" }] }, \"tags-start-node\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.svelte\" }, \"2\": { \"patterns\": [{ \"include\": \"#tags-name\" }] } }, \"match\": \"(<)([^/\\\\s>/]*)\", \"name\": \"meta.tag.start.svelte\" }, \"tags-void\": { \"begin\": \"(<)(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.svelte\" }, \"2\": { \"name\": \"entity.name.tag.svelte\" } }, \"end\": \"/?>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.begin.svelte\" } }, \"name\": \"meta.tag.void.svelte\", \"patterns\": [{ \"include\": \"#attributes\" }] }, \"type-parameters\": { \"name\": \"meta.type.parameters.ts\", \"patterns\": [{ \"include\": \"source.ts#comment\" }, { \"match\": \"(?<![_$[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(extends|in|out|const)(?![_$[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"storage.modifier.ts\" }, { \"include\": \"source.ts#type\" }, { \"include\": \"source.ts#punctuation-comma\" }, { \"match\": \"(=)(?!>)\", \"name\": \"keyword.operator.assignment.ts\" }] } }, \"scopeName\": \"source.svelte\", \"embeddedLangs\": [\"javascript\", \"typescript\", \"coffee\", \"stylus\", \"sass\", \"css\", \"scss\", \"less\", \"postcss\", \"pug\", \"markdown\"] });\nvar svelte = [\n  ...javascript,\n  ...typescript,\n  ...coffee,\n  ...stylus,\n  ...sass,\n  ...css,\n  ...scss,\n  ...less,\n  ...postcss,\n  ...pug,\n  ...markdown,\n  lang\n];\n\nexport { svelte as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,UAAU,aAAa,CAAC,QAAQ,GAAG,cAAc,EAAE,oGAAoG,EAAE,YAAY,CAAC,EAAE,SAAS,gBAAgB,eAAe,aAAa,OAAO,UAAU,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,oGAAoG,EAAE,YAAY,CAAC,EAAE,SAAS,gBAAgB,eAAe,aAAa,OAAO,UAAU,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,+EAA+E,EAAE,YAAY,CAAC,EAAE,SAAS,gBAAgB,eAAe,iBAAiB,OAAO,UAAU,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,2CAA2C,EAAE,YAAY,CAAC,EAAE,SAAS,2DAA2D,QAAQ,yCAAyC,GAAG,EAAE,SAAS,2DAA2D,QAAQ,yCAAyC,CAAC,EAAE,GAAG,oDAAoD,EAAE,YAAY,CAAC,EAAE,SAAS,gBAAgB,eAAe,aAAa,OAAO,UAAU,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,mDAAmD,EAAE,YAAY,CAAC,EAAE,SAAS,gBAAgB,eAAe,cAAc,OAAO,UAAU,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,qDAAqD,EAAE,YAAY,CAAC,EAAE,SAAS,gBAAgB,eAAe,cAAc,OAAO,UAAU,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,sDAAsD,EAAE,YAAY,CAAC,EAAE,SAAS,gBAAgB,eAAe,mBAAmB,OAAO,UAAU,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,CAAC,EAAE,GAAG,yDAAyD,EAAE,YAAY,CAAC,EAAE,SAAS,gBAAgB,eAAe,sBAAsB,OAAO,UAAU,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,qBAAqB,CAAC,EAAE,CAAC,EAAE,GAAG,sDAAsD,EAAE,YAAY,CAAC,EAAE,SAAS,gBAAgB,eAAe,eAAe,OAAO,UAAU,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,sDAAsD,EAAE,YAAY,CAAC,EAAE,SAAS,gBAAgB,eAAe,mBAAmB,OAAO,UAAU,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,CAAC,EAAE,GAAG,wDAAwD,EAAE,YAAY,CAAC,EAAE,SAAS,gBAAgB,eAAe,iBAAiB,OAAO,UAAU,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,sDAAsD,EAAE,YAAY,CAAC,EAAE,SAAS,aAAa,OAAO,kBAAkB,YAAY,CAAC,EAAE,WAAW,SAAS,CAAC,EAAE,CAAC,EAAE,GAAG,wDAAwD,EAAE,YAAY,CAAC,EAAE,SAAS,gBAAgB,eAAe,YAAY,OAAO,UAAU,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,QAAQ,UAAU,YAAY,CAAC,EAAE,WAAW,SAAS,CAAC,GAAG,cAAc,EAAE,cAAc,EAAE,YAAY,CAAC,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,2BAA2B,CAAC,EAAE,GAAG,yBAAyB,EAAE,SAAS,4KAA4K,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,kCAAkC,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,wCAAwC,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,+BAA+B,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,QAAQ,QAAQ,0BAA0B,GAAG,EAAE,SAAS,OAAO,QAAQ,+BAA+B,CAAC,EAAE,EAAE,GAAG,OAAO,oBAAoB,QAAQ,4BAA4B,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,iCAAiC,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,CAAC,EAAE,GAAG,kCAAkC,EAAE,YAAY,CAAC,EAAE,SAAS,eAAe,QAAQ,yBAAyB,GAAG,EAAE,SAAS,6BAA6B,QAAQ,iCAAiC,GAAG,EAAE,SAAS,OAAO,QAAQ,sBAAsB,GAAG,EAAE,SAAS,eAAe,QAAQ,qCAAqC,CAAC,EAAE,GAAG,+BAA+B,EAAE,YAAY,CAAC,EAAE,SAAS,iBAAiB,QAAQ,0BAA0B,GAAG,EAAE,SAAS,mBAAmB,QAAQ,4BAA4B,GAAG,EAAE,SAAS,4CAA4C,QAAQ,2BAA2B,GAAG,EAAE,SAAS,8BAA8B,QAAQ,4BAA4B,CAAC,EAAE,GAAG,wCAAwC,EAAE,YAAY,CAAC,EAAE,SAAS,qBAAqB,QAAQ,2BAA2B,GAAG,EAAE,SAAS,mBAAmB,QAAQ,0BAA0B,GAAG,EAAE,SAAS,oBAAoB,QAAQ,2CAA2C,GAAG,EAAE,SAAS,oBAAoB,QAAQ,oCAAoC,GAAG,EAAE,WAAW,+BAA+B,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,uBAAuB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,eAAe,6CAA6C,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,2BAA2B,EAAE,SAAS,mBAAmB,YAAY,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,eAAe,6CAA6C,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,2CAA2C,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,QAAQ,QAAQ,oCAAoC,GAAG,EAAE,SAAS,MAAM,QAAQ,qCAAqC,CAAC,EAAE,EAAE,GAAG,OAAO,oBAAoB,QAAQ,4BAA4B,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,iCAAiC,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,CAAC,EAAE,GAAG,oBAAoB,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,SAAS,0EAA0E,GAAG,EAAE,SAAS,4BAA4B,QAAQ,0BAA0B,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,SAAS,UAAU,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,SAAS,QAAQ,YAAY,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,OAAO,OAAO,QAAQ,wBAAwB,YAAY,CAAC,EAAE,SAAS,kBAAkB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,iFAAiF,EAAE,GAAG,eAAe,sCAAsC,OAAO,WAAW,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,CAAC,EAAE,EAAE,GAAG,SAAS,aAAa,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,EAAE,SAAS,oCAAoC,QAAQ,qDAAqD,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,SAAS,SAAS,OAAO,UAAU,QAAQ,6CAA6C,YAAY,CAAC,EAAE,WAAW,mCAAmC,CAAC,EAAE,GAAG,EAAE,SAAS,WAAW,OAAO,YAAY,QAAQ,6CAA6C,YAAY,CAAC,EAAE,WAAW,kCAAkC,CAAC,EAAE,CAAC,EAAE,GAAG,uBAAuB,EAAE,YAAY,CAAC,EAAE,SAAS,SAAS,OAAO,UAAU,QAAQ,6CAA6C,YAAY,CAAC,EAAE,WAAW,yCAAyC,CAAC,EAAE,GAAG,EAAE,SAAS,WAAW,OAAO,YAAY,QAAQ,6CAA6C,YAAY,CAAC,EAAE,WAAW,wCAAwC,CAAC,EAAE,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,eAAe,6CAA6C,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,gBAAgB,OAAO,UAAU,YAAY,CAAC,EAAE,WAAW,2BAA2B,CAAC,EAAE,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,SAAS,EAAE,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,SAAS,YAAY,OAAO,WAAW,QAAQ,cAAc,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,4BAA4B,EAAE,SAAS,sBAAsB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,yBAAyB,CAAC,EAAE,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,QAAQ,oDAAoD,YAAY,CAAC,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,0BAA0B,EAAE,SAAS,sBAAsB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,yBAAyB,CAAC,EAAE,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,QAAQ,iDAAiD,GAAG,yBAAyB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,sBAAsB,QAAQ,qCAAqC,GAAG,EAAE,SAAS,YAAY,QAAQ,yBAAyB,GAAG,EAAE,SAAS,oBAAoB,QAAQ,8BAA8B,GAAG,EAAE,SAAS,WAAW,QAAQ,yBAAyB,GAAG,EAAE,SAAS,QAAQ,QAAQ,uBAAuB,GAAG,EAAE,SAAS,UAAU,QAAQ,uBAAuB,GAAG,EAAE,SAAS,SAAS,QAAQ,gCAAgC,GAAG,EAAE,SAAS,SAAS,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,SAAS,8BAA8B,GAAG,sBAAsB,EAAE,YAAY,CAAC,EAAE,SAAS,sDAAsD,OAAO,SAAS,QAAQ,6CAA6C,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,oBAAoB,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,SAAS,4CAA4C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,OAAO,UAAU,GAAG,EAAE,SAAS,WAAW,OAAO,SAAS,QAAQ,6CAA6C,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,mBAAmB,OAAO,SAAS,YAAY,CAAC,EAAE,SAAS,mBAAmB,eAAe,6CAA6C,OAAO,0BAA0B,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,QAAQ,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,iBAAiB,GAAG,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,eAAe,6CAA6C,OAAO,aAAa,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6CAA6C,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,EAAE,GAAG,SAAS,0CAA0C,GAAG,EAAE,SAAS,KAAK,QAAQ,+BAA+B,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,oBAAoB,OAAO,SAAS,YAAY,CAAC,EAAE,SAAS,mBAAmB,eAAe,6CAA6C,OAAO,oBAAoB,eAAe,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,gBAAgB,eAAe,6CAA6C,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,oBAAoB,OAAO,SAAS,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6CAA6C,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,EAAE,GAAG,SAAS,8BAA8B,GAAG,EAAE,SAAS,KAAK,QAAQ,+BAA+B,CAAC,EAAE,CAAC,EAAE,GAAG,qBAAqB,EAAE,SAAS,wCAAwC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,yBAAyB,CAAC,EAAE,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,QAAQ,0BAA0B,YAAY,CAAC,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,QAAQ,EAAE,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8DAA8D,GAAG,KAAK,EAAE,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,4DAA4D,GAAG,KAAK,EAAE,QAAQ,8DAA8D,EAAE,GAAG,SAAS,wBAAwB,GAAG,oBAAoB,EAAE,SAAS,mBAAmB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8DAA8D,GAAG,KAAK,EAAE,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,4DAA4D,EAAE,GAAG,QAAQ,2BAA2B,GAAG,sBAAsB,EAAE,SAAS,mBAAmB,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,mBAAmB,CAAC,EAAE,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,8DAA8D,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,4BAA4B,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,mBAAmB,CAAC,EAAE,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,EAAE,GAAG,QAAQ,kBAAkB,YAAY,CAAC,EAAE,SAAS,mEAAmE,OAAO,aAAa,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,8BAA8B,CAAC,EAAE,GAAG,EAAE,WAAW,8BAA8B,CAAC,EAAE,GAAG,8BAA8B,EAAE,SAAS,OAAO,OAAO,YAAY,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,yBAAyB,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yBAAyB,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,SAAS,gCAAgC,GAAG,EAAE,SAAS,QAAQ,QAAQ,yBAAyB,GAAG,EAAE,SAAS,sBAAsB,QAAQ,iCAAiC,GAAG,EAAE,SAAS,+BAA+B,QAAQ,gDAAgD,GAAG,EAAE,SAAS,oBAAoB,QAAQ,yBAAyB,CAAC,EAAE,GAAG,yBAAyB,EAAE,SAAS,OAAO,OAAO,WAAW,QAAQ,yBAAyB,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,EAAE,GAAG,SAAS,mBAAmB,QAAQ,wBAAwB,GAAG,aAAa,EAAE,SAAS,wFAAwF,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,mBAAmB,EAAE,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,SAAS,iHAAiH,QAAQ,sBAAsB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,8BAA8B,GAAG,EAAE,SAAS,YAAY,QAAQ,iCAAiC,CAAC,EAAE,EAAE,GAAG,aAAa,iBAAiB,iBAAiB,CAAC,cAAc,cAAc,UAAU,UAAU,QAAQ,OAAO,QAAQ,QAAQ,WAAW,OAAO,UAAU,EAAE,CAAC;AACj7iB,IAAI,SAAS;AAAA,EACX,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACF;", "names": []}