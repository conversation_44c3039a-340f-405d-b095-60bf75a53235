{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/rel.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Rel\", \"name\": \"rel\", \"patterns\": [{ \"include\": \"#strings\" }, { \"include\": \"#comment\" }, { \"include\": \"#single-line-comment-consuming-line-ending\" }, { \"include\": \"#deprecated-temporary\" }, { \"include\": \"#operators\" }, { \"include\": \"#symbols\" }, { \"include\": \"#keywords\" }, { \"include\": \"#otherkeywords\" }, { \"include\": \"#types\" }, { \"include\": \"#constants\" }], \"repository\": { \"comment\": { \"patterns\": [{ \"begin\": \"/\\\\*\\\\*(?!/)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.rel\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.rel\" } }, \"name\": \"comment.block.documentation.rel\", \"patterns\": [{ \"include\": \"#docblock\" }] }, { \"begin\": \"(/\\\\*)(?:\\\\s*((@)internal)(?=\\\\s|(\\\\*/)))?\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.rel\" }, \"2\": { \"name\": \"storage.type.internaldeclaration.rel\" }, \"3\": { \"name\": \"punctuation.decorator.internaldeclaration.rel\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.rel\" } }, \"name\": \"comment.block.rel\" }, { \"begin\": 'doc\"\"\"', \"end\": '\"\"\"', \"name\": \"comment.block.documentation.rel\" }, { \"begin\": \"(^[ \\\\t]+)?((//)(?:\\\\s*((@)internal)(?=\\\\s|$))?)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.rel\" }, \"2\": { \"name\": \"comment.line.double-slash.rel\" }, \"3\": { \"name\": \"punctuation.definition.comment.rel\" }, \"4\": { \"name\": \"storage.type.internaldeclaration.rel\" }, \"5\": { \"name\": \"punctuation.decorator.internaldeclaration.rel\" } }, \"contentName\": \"comment.line.double-slash.rel\", \"end\": \"(?=$)\" }] }, \"constants\": { \"patterns\": [{ \"match\": \"(\\\\b(true|false)\\\\b)\", \"name\": \"constant.language.rel\" }] }, \"deprecated-temporary\": { \"patterns\": [{ \"match\": \"@inspect\", \"name\": \"keyword.other.rel\" }] }, \"keywords\": { \"patterns\": [{ \"match\": \"(\\\\b(def|entity|bound|include|ic|forall|exists|\\u2200|\\u2203|return|module|^end)\\\\b)|(((\\\\<)?\\\\|(\\\\>)?)|\\u2200|\\u2203)\", \"name\": \"keyword.control.rel\" }] }, \"operators\": { \"patterns\": [{ \"match\": \"(\\\\b(if|then|else|and|or|not|eq|neq|lt|lt_eq|gt|gt_eq)\\\\b)|(\\\\+|\\\\-|\\\\*|\\\\/|\\xF7|\\\\^|\\\\%|\\\\=|\\\\!\\\\=|\\u2260|\\\\<|\\\\<\\\\=|\\u2264|\\\\>|\\\\>\\\\=|\\u2265|\\\\&)|\\\\s+(end)\", \"name\": \"keyword.other.rel\" }] }, \"otherkeywords\": { \"patterns\": [{ \"match\": \"\\\\s*(@inline)\\\\s*|\\\\s*(@auto_number)\\\\s*|\\\\s*(function)\\\\s|(\\\\b(implies|select|from|\\u2208|where|for|in)\\\\b)|(((\\\\<)?\\\\|(\\\\>)?)|\\u2208)\", \"name\": \"keyword.other.rel\" }] }, \"single-line-comment-consuming-line-ending\": { \"begin\": \"(^[ \\\\t]+)?((//)(?:\\\\s*((@)internal)(?=\\\\s|$))?)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.rel\" }, \"2\": { \"name\": \"comment.line.double-slash.rel\" }, \"3\": { \"name\": \"punctuation.definition.comment.rel\" }, \"4\": { \"name\": \"storage.type.internaldeclaration.rel\" }, \"5\": { \"name\": \"punctuation.decorator.internaldeclaration.rel\" } }, \"contentName\": \"comment.line.double-slash.rel\", \"end\": \"(?=^)\" }, \"strings\": { \"begin\": '\"', \"end\": '\"', \"name\": \"string.quoted.double.rel\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.rel\" }] }, \"symbols\": { \"patterns\": [{ \"match\": \"(:[\\\\[_$[:alpha:]](\\\\]|[_$[:alnum:]]*))\", \"name\": \"variable.parameter.rel\" }] }, \"types\": { \"patterns\": [{ \"match\": \"(\\\\b(Symbol|Char|Bool|Rational|FixedDecimal|Float16|Float32|Float64|Int8|Int16|Int32|Int64|Int128|UInt8|UInt16|UInt32|UInt64|UInt128|Date|DateTime|Day|Week|Month|Year|Nanosecond|Microsecond|Millisecond|Second|Minute|Hour|FilePos|HashValue|AutoNumberValue)\\\\b)\", \"name\": \"entity.name.type.rel\" }] } }, \"scopeName\": \"source.rel\" });\nvar rel = [\n  lang\n];\n\nexport { rel as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,OAAO,QAAQ,OAAO,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,6CAA6C,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,aAAa,CAAC,GAAG,cAAc,EAAE,WAAW,EAAE,YAAY,CAAC,EAAE,SAAS,gBAAgB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,QAAQ,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,QAAQ,mCAAmC,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,8CAA8C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,QAAQ,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,QAAQ,oBAAoB,GAAG,EAAE,SAAS,UAAU,OAAO,OAAO,QAAQ,kCAAkC,GAAG,EAAE,SAAS,oDAAoD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,eAAe,iCAAiC,OAAO,QAAQ,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,SAAS,wBAAwB,QAAQ,wBAAwB,CAAC,EAAE,GAAG,wBAAwB,EAAE,YAAY,CAAC,EAAE,SAAS,YAAY,QAAQ,oBAAoB,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,sGAA0H,QAAQ,sBAAsB,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,SAAS,+IAAiK,QAAQ,oBAAoB,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,SAAS,iIAA2I,QAAQ,oBAAoB,CAAC,EAAE,GAAG,6CAA6C,EAAE,SAAS,oDAAoD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,eAAe,iCAAiC,OAAO,QAAQ,GAAG,WAAW,EAAE,SAAS,KAAK,OAAO,KAAK,QAAQ,4BAA4B,YAAY,CAAC,EAAE,SAAS,SAAS,QAAQ,gCAAgC,CAAC,EAAE,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,SAAS,2CAA2C,QAAQ,yBAAyB,CAAC,EAAE,GAAG,SAAS,EAAE,YAAY,CAAC,EAAE,SAAS,uQAAuQ,QAAQ,uBAAuB,CAAC,EAAE,EAAE,GAAG,aAAa,aAAa,CAAC;AACzgH,IAAI,MAAM;AAAA,EACR;AACF;", "names": []}