{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/glimmer-ts.mjs"], "sourcesContent": ["import typescript from './typescript.mjs';\nimport handlebars from './handlebars.mjs';\nimport './html.mjs';\nimport './javascript.mjs';\nimport './css.mjs';\nimport './yaml.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"Glimmer TS\", \"injections\": { \"L:source.gts -comment -string\": { \"patterns\": [{ \"begin\": \"\\\\s*(<)(template)\\\\s*(>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.html\" }, \"2\": { \"name\": \"entity.name.tag.other.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.html\" } }, \"end\": \"(</)(template)(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.html\" }, \"2\": { \"name\": \"entity.name.tag.other.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.html\" } }, \"name\": \"meta.js.embeddedTemplateWithoutArgs\", \"patterns\": [{ \"include\": \"text.html.handlebars\" }] }, { \"begin\": \"(<)(template)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.html\" }, \"2\": { \"name\": \"entity.name.tag.other.html\" } }, \"end\": \"(</)(template)(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.html\" }, \"2\": { \"name\": \"entity.name.tag.other.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.html\" } }, \"name\": \"meta.js.embeddedTemplateWithArgs\", \"patterns\": [{ \"begin\": \"(?<=\\\\<template)\", \"end\": \"(?=\\\\>)\", \"patterns\": [{ \"include\": \"text.html.handlebars#tag-stuff\" }] }, { \"begin\": \"(>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.end.js\" } }, \"contentName\": \"meta.html.embedded.block\", \"end\": \"(?=</template>)\", \"patterns\": [{ \"include\": \"text.html.handlebars\" }] }] }] } }, \"name\": \"glimmer-ts\", \"patterns\": [{ \"include\": \"source.ts\" }], \"scopeName\": \"source.gts\", \"embeddedLangs\": [\"typescript\", \"handlebars\"], \"aliases\": [\"gts\"] });\nvar glimmerTs = [\n  ...typescript,\n  ...handlebars,\n  lang\n];\n\nexport { glimmerTs as default };\n"], "mappings": ";;;;;;;;;;;;;AAOA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,cAAc,cAAc,EAAE,iCAAiC,EAAE,YAAY,CAAC,EAAE,SAAS,4BAA4B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,qBAAqB,eAAe,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,QAAQ,uCAAuC,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,SAAS,iBAAiB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,qBAAqB,eAAe,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,QAAQ,oCAAoC,YAAY,CAAC,EAAE,SAAS,oBAAoB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,eAAe,4BAA4B,OAAO,mBAAmB,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,QAAQ,cAAc,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,GAAG,aAAa,cAAc,iBAAiB,CAAC,cAAc,YAAY,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC;AAC7/C,IAAI,YAAY;AAAA,EACd,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACF;", "names": []}