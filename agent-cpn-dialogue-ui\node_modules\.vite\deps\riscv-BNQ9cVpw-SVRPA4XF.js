import "./chunk-2LSFTFF7.js";

// node_modules/.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/vue-element-plus-x/dist/riscv-BNQ9cVpw.js
var lang = Object.freeze(JSON.parse(`{"displayName":"RISC-V","fileTypes":["S","s","riscv","asm"],"name":"riscv","patterns":[{"match":"\\\\b(la|lb|lh|lw|ld|nop|li|mv|not|negw??|sext\\\\.w|seqz|snez|sltz|sgtz|beqz|bnez|blez|bgez|bltz|bgtz?|ble|bgtu|bleu|j|jal|jr|ret|call|tail|fence|csr[crsw|]|csr[csw|]i)\\\\b","name":"support.function.pseudo.riscv"},{"match":"\\\\b(addw??|auipc|lui|jalr|beq|bne|blt|bge|bltu|bgeu|lb|lh|lw|ld|lbu|lhu|sb|sh|sw|sd|addiw??|sltiu??|xori|ori|andi|slliw??|srliw??|sraiw??|subw??|sllw??|sltu??|xor|srlw??|sraw??|or|and|fence|fence\\\\.i|csrrw|csrrs|csrrc|csrrwi|csrrsi|csrrci)\\\\b","name":"support.function.riscv"},{"match":"\\\\b(ecall|ebreak|sfence\\\\.vma|mret|sret|uret|wfi)\\\\b","name":"support.function.riscv.privileged"},{"match":"\\\\b(mulh??|mulhsu|mulhu|divu??|remu??|mulw|divw|divuw|remw|remuw)\\\\b","name":"support.function.riscv.m"},{"match":"\\\\b(c\\\\.(?:addi4spn|fld|lq|lw|flw|ld|fsd|sq|sw|fsw|sd|nop|addi|jal|addiw|li|addi16sp|lui|srli|srli64|srai|srai64|andi|sub|xor|or|and|subw|addw|j|beqz|bnez))\\\\b","name":"support.function.riscv.c"},{"match":"\\\\b(lr\\\\.[dw|]|sc\\\\.[dw|]|amoswap\\\\.[dw|]|amoadd\\\\.[dw|]|amoxor\\\\.[dw|]|amoand\\\\.[dw|]|amoor\\\\.[dw|]|amomin\\\\.[dw|]|amomax\\\\.[dw|]|amominu\\\\.[dw|]|amomaxu\\\\.[dw|])\\\\b","name":"support.function.riscv.a"},{"match":"\\\\b(f(?:lw|sw|madd\\\\.s|msub\\\\.s|nmsub\\\\.s|nmadd\\\\.s|add\\\\.s|sub\\\\.s|mul\\\\.s|div\\\\.s|sqrt\\\\.s|sgnj\\\\.s|sgnjn\\\\.s|sgnjx\\\\.s|min\\\\.s|max\\\\.s|cvt\\\\.w\\\\.s|cvt\\\\.wu\\\\.s|mv\\\\.x\\\\.w|eq\\\\.s|lt\\\\.s|le\\\\.s|class\\\\.s|cvt\\\\.s\\\\.wu??|mv\\\\.w\\\\.x|cvt\\\\.l\\\\.s|cvt\\\\.lu\\\\.s|cvt\\\\.s\\\\.lu??))\\\\b","name":"support.function.riscv.f"},{"match":"\\\\b(f(?:ld|sd|madd\\\\.d|msub\\\\.d|nmsub\\\\.d|nmadd\\\\.d|add\\\\.d|sub\\\\.d|mul\\\\.d|div\\\\.d|sqrt\\\\.d|sgnj\\\\.d|sgnjn\\\\.d|sgnjx\\\\.d|min\\\\.d|max\\\\.d|cvt\\\\.s\\\\.d|cvt\\\\.d\\\\.s|eq\\\\.d|lt\\\\.d|le\\\\.d|class\\\\.d|cvt\\\\.w\\\\.d|cvt\\\\.wu\\\\.d|cvt\\\\.d\\\\.wu??|cvt\\\\.l\\\\.d|cvt\\\\.lu\\\\.d|mv\\\\.x\\\\.d|cvt\\\\.d\\\\.lu??|mv\\\\.d\\\\.x))\\\\b","name":"support.function.riscv.d"},{"match":"\\\\.(skip|asciiz??|byte|[248|]byte|data|double|float|half|kdata|ktext|space|text|word|dword|dtprelword|dtpreldword|set\\\\s*(noat|at)|[su|]leb128|string|incbin|zero|rodata|comm|common)\\\\b","name":"storage.type.riscv"},{"match":"\\\\.(balign|align|p2align|extern|globl|global|local|pushsection|section|bss|insn|option|type|equ|macro|endm|file|ident)\\\\b","name":"storage.modifier.riscv"},{"captures":{"1":{"name":"entity.name.function.label.riscv"}},"match":"\\\\b([0-9A-Z_a-z]+):","name":"meta.function.label.riscv"},{"captures":{"1":{"name":"punctuation.definition.variable.riscv"}},"match":"\\\\b(x([0-9]|1[0-9]|2[0-9]|3[01]))\\\\b","name":"variable.other.register.usable.by-number.riscv"},{"captures":{"1":{"name":"punctuation.definition.variable.riscv"}},"match":"\\\\b(zero|ra|sp|gp|tp|t[0-6]|a[0-7]|s[0-9]|fp|s1[01])\\\\b","name":"variable.other.register.usable.by-name.riscv"},{"captures":{"1":{"name":"punctuation.definition.variable.riscv"}},"match":"\\\\b(([hmsu]|vs)status|([hmsu]|vs)ie|([msu]|vs)tvec|([msu]|vs)scratch|([msu]|vs)epc|([msu]|vs)cause|([hmsu]|vs)tval|([hmsu]|vs)ip|fflags|frm|fcsr|m?cycleh?|timeh?|m?instreth?|m?hpmcounter([3-9]|[12][0-9]|3[01])h?|[hms][ei]deleg|[hms]counteren|v?satp|hgeie|hgeip|[hm]tinst|hvip|hgatp|htimedeltah?|mvendorid|marchid|mimpid|mhartid|misa|mstatush|mtval2|pmpcfg[0-3]|pmpaddr([0-9]|1[0-5])|mcountinhibit|mhpmevent([3-9]|[12][0-9]|3[01])|tselect|tdata[123]|dcsr|dpc|dscratch[01])\\\\b","name":"variable.other.csr.names.riscv"},{"captures":{"1":{"name":"punctuation.definition.variable.riscv"}},"match":"\\\\bf([0-9]|1[0-9]|2[0-9]|3[01])\\\\b","name":"variable.other.register.usable.floating-point.riscv"},{"match":"\\\\b\\\\d+\\\\.\\\\d+\\\\b","name":"constant.numeric.float.riscv"},{"match":"\\\\b(\\\\d+|0([Xx])\\\\h+)\\\\b","name":"constant.numeric.integer.riscv"},{"begin":"\\"","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.riscv"}},"end":"\\"","endCaptures":{"0":{"name":"punctuation.definition.string.end.riscv"}},"name":"string.quoted.double.riscv","patterns":[{"match":"\\\\\\\\[\\"\\\\\\\\nrt]","name":"constant.character.escape.riscv"}]},{"begin":"'","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.riscv"}},"end":"'","endCaptures":{"0":{"name":"punctuation.definition.string.end.riscv"}},"name":"string.quoted.single.riscv","patterns":[{"match":"\\\\\\\\[\\"\\\\\\\\nrt]","name":"constant.character.escape.riscv"}]},{"begin":"/\\\\*","end":"\\\\*/","name":"comment.block"},{"begin":"//","end":"\\\\n","name":"comment.line.double-slash"},{"begin":"^\\\\s*#\\\\s*(define)\\\\s+((?<id>[A-Z_a-z][0-9A-Z_a-z]*))(?:(\\\\()(\\\\s*\\\\g<id>\\\\s*((,)\\\\s*\\\\g<id>\\\\s*)*(?:\\\\.\\\\.\\\\.)?)(\\\\)))?","beginCaptures":{"1":{"name":"keyword.control.import.define.c"},"2":{"name":"entity.name.function.preprocessor.c"},"4":{"name":"punctuation.definition.parameters.c"},"5":{"name":"variable.parameter.preprocessor.c"},"7":{"name":"punctuation.separator.parameters.c"},"8":{"name":"punctuation.definition.parameters.c"}},"end":"(?=/[*/])|$","name":"meta.preprocessor.macro.c","patterns":[{"match":"(?>\\\\\\\\\\\\s*\\\\n)","name":"punctuation.separator.continuation.c"},{"include":"$base"}]},{"begin":"^\\\\s*#\\\\s*(error|warning)\\\\b","captures":{"1":{"name":"keyword.control.import.error.c"}},"end":"$","name":"meta.preprocessor.diagnostic.c","patterns":[{"match":"(?>\\\\\\\\\\\\s*\\\\n)","name":"punctuation.separator.continuation.c"}]},{"begin":"^\\\\s*#\\\\s*(i(?:nclude|mport))\\\\b\\\\s+","captures":{"1":{"name":"keyword.control.import.include.c"}},"end":"(?=/[*/])|$","name":"meta.preprocessor.c.include","patterns":[{"match":"(?>\\\\\\\\\\\\s*\\\\n)","name":"punctuation.separator.continuation.c"},{"begin":"\\"","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.c"}},"end":"\\"","endCaptures":{"0":{"name":"punctuation.definition.string.end.c"}},"name":"string.quoted.double.include.c"},{"begin":"<","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.c"}},"end":">","endCaptures":{"0":{"name":"punctuation.definition.string.end.c"}},"name":"string.quoted.other.lt-gt.include.c"}]},{"begin":"^\\\\s*#\\\\s*(defined??|elif|else|if|ifdef|ifndef|line|pragma|undef|endif)\\\\b","captures":{"1":{"name":"keyword.control.import.c"}},"end":"(?=/[*/])|$","name":"meta.preprocessor.c","patterns":[{"match":"(?>\\\\\\\\\\\\s*\\\\n)","name":"punctuation.separator.continuation.c"}]},{"begin":"(^[\\\\t ]+)?(?=#)","beginCaptures":{"1":{"name":"punctuation.whitespace.comment.leading.riscv"}},"end":"(?!\\\\G)","patterns":[{"begin":"#|(//)","beginCaptures":{"0":{"name":"punctuation.definition.comment.riscv"}},"end":"\\\\n","name":"comment.line.number-sign.riscv"}]}],"scopeName":"source.riscv"}`));
var riscv = [
  lang
];
export {
  riscv as default
};
//# sourceMappingURL=riscv-BNQ9cVpw-SVRPA4XF.js.map
