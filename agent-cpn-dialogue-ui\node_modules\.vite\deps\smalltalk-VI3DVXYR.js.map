{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/smalltalk.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Smalltalk\", \"fileTypes\": [\"st\"], \"foldingStartMarker\": \"\\\\[\", \"foldingStopMarker\": \"^\\\\s*\\\\]|^\\\\s\\\\]\", \"name\": \"smalltalk\", \"patterns\": [{ \"match\": \"\\\\$.\", \"name\": \"constant.character.smalltalk\" }, { \"match\": \"\\\\b(class)\\\\b\", \"name\": \"storage.type.$1.smalltalk\" }, { \"match\": \"\\\\b(extend|super|self)\\\\b\", \"name\": \"storage.modifier.$1.smalltalk\" }, { \"match\": \"\\\\b(yourself|new|Smalltalk)\\\\b\", \"name\": \"keyword.control.$1.smalltalk\" }, { \"match\": \":=\", \"name\": \"keyword.operator.assignment.smalltalk\" }, { \"comment\": \"Parse the variable declaration like: |a b c|\", \"match\": \"/^:\\\\w*\\\\s*\\\\|/\", \"name\": \"constant.other.block.smalltalk\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.instance-variables.begin.smalltalk\" }, \"2\": { \"patterns\": [{ \"match\": \"\\\\w+\", \"name\": \"support.type.variable.declaration.smalltalk\" }] }, \"3\": { \"name\": \"punctuation.definition.instance-variables.end.smalltalk\" } }, \"match\": \"(\\\\|)(\\\\s*\\\\w[\\\\w ]*)(\\\\|)\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"match\": \":\\\\w+\", \"name\": \"entity.name.function.block.smalltalk\" }] } }, \"comment\": \"Parse the blocks like: [ :a :b | ...... ]\", \"match\": \"\\\\[((\\\\s+|:\\\\w+)*)\\\\|\" }, { \"include\": \"#numeric\" }, { \"match\": \"<(?!<|=)|>(?!<|=|>)|<=|>=|=|==|~=|~~|>>|\\\\^\", \"name\": \"keyword.operator.comparison.smalltalk\" }, { \"match\": \"(\\\\*|\\\\+|\\\\-|/|\\\\\\\\)\", \"name\": \"keyword.operator.arithmetic.smalltalk\" }, { \"match\": \"(?<=[ \\\\t])!+|\\\\bnot\\\\b|&|\\\\band\\\\b|\\\\||\\\\bor\\\\b\", \"name\": \"keyword.operator.logical.smalltalk\" }, { \"comment\": \"Fake reserved word -> main Smalltalk messages\", \"match\": \"(?<!\\\\.)\\\\b(ensure|resume|retry|signal)\\\\b(?![?!])\", \"name\": \"keyword.control.smalltalk\" }, { \"comment\": \"Fake conditionals. Smalltalk Methods.\", \"match\": \"ifCurtailed:|ifTrue:|ifFalse:|whileFalse:|whileTrue:\", \"name\": \"keyword.control.conditionals.smalltalk\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.inherited-class.smalltalk\" }, \"3\": { \"name\": \"keyword.control.smalltalk\" }, \"4\": { \"name\": \"entity.name.type.class.smalltalk\" } }, \"match\": \"(\\\\w+)(\\\\s+(subclass:))\\\\s*(\\\\w*)\", \"name\": \"meta.class.smalltalk\" }, { \"begin\": '\"', \"beginCaptures\": [{ \"name\": \"punctuation.definition.comment.begin.smalltalk\" }], \"end\": '\"', \"endCaptures\": [{ \"name\": \"punctuation.definition.comment.end.smalltalk\" }], \"name\": \"comment.block.smalltalk\" }, { \"match\": \"\\\\b(true|false)\\\\b\", \"name\": \"constant.language.boolean.smalltalk\" }, { \"match\": \"\\\\b(nil)\\\\b\", \"name\": \"constant.language.nil.smalltalk\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.smalltalk\" } }, \"comment\": \"messages/methods\", \"match\": \"(?>[a-zA-Z_]\\\\w*(?>[?!])?)(:)(?!:)\", \"name\": \"constant.other.messages.smalltalk\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.smalltalk\" } }, \"comment\": \"symbols\", \"match\": \"(#)[a-zA-Z_][a-zA-Z0-9_:]*\", \"name\": \"constant.other.symbol.smalltalk\" }, { \"begin\": \"#\\\\[\", \"beginCaptures\": [{ \"name\": \"punctuation.definition.constant.begin.smalltalk\" }], \"end\": \"\\\\]\", \"endCaptures\": [{ \"name\": \"punctuation.definition.constant.end.smalltalk\" }], \"name\": \"meta.array.byte.smalltalk\", \"patterns\": [{ \"match\": \"[0-9]+(r[a-zA-Z0-9]+)?\", \"name\": \"constant.numeric.integer.smalltalk\" }, { \"match\": \"[^\\\\s\\\\]]+\", \"name\": \"invalid.illegal.character-not-allowed-here.smalltalk\" }] }, { \"begin\": \"#\\\\(\", \"beginCaptures\": [{ \"name\": \"punctuation.definition.constant.begin.smalltalk\" }], \"comment\": \"Array Constructor\", \"end\": \"\\\\)\", \"endCaptures\": [{ \"name\": \"punctuation.definition.constant.end.smalltalk\" }], \"name\": \"constant.other.array.smalltalk\" }, { \"begin\": \"'\", \"beginCaptures\": [{ \"name\": \"punctuation.definition.string.begin.smalltalk\" }], \"end\": \"'\", \"endCaptures\": [{ \"name\": \"punctuation.definition.string.end.smalltalk\" }], \"name\": \"string.quoted.single.smalltalk\" }, { \"match\": \"\\\\b[A-Z]\\\\w*\\\\b\", \"name\": \"variable.other.constant.smalltalk\" }], \"repository\": { \"numeric\": { \"patterns\": [{ \"match\": \"(?<!\\\\w)[0-9]+\\\\.[0-9]+s[0-9]*\", \"name\": \"constant.numeric.float.scaled.smalltalk\" }, { \"match\": \"(?<!\\\\w)[0-9]+\\\\.[0-9]+([edq]-?[0-9]+)?\", \"name\": \"constant.numeric.float.smalltalk\" }, { \"match\": \"(?<!\\\\w)-?[0-9]+r[a-zA-Z0-9]+\", \"name\": \"constant.numeric.integer.radix.smalltalk\" }, { \"match\": \"(?<!\\\\w)-?[0-9]+([edq]-?[0-9]+)?\", \"name\": \"constant.numeric.integer.smalltalk\" }] } }, \"scopeName\": \"source.smalltalk\" });\nvar smalltalk = [\n  lang\n];\n\nexport { smalltalk as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,aAAa,aAAa,CAAC,IAAI,GAAG,sBAAsB,OAAO,qBAAqB,oBAAoB,QAAQ,aAAa,YAAY,CAAC,EAAE,SAAS,QAAQ,QAAQ,+BAA+B,GAAG,EAAE,SAAS,iBAAiB,QAAQ,4BAA4B,GAAG,EAAE,SAAS,6BAA6B,QAAQ,gCAAgC,GAAG,EAAE,SAAS,kCAAkC,QAAQ,+BAA+B,GAAG,EAAE,SAAS,MAAM,QAAQ,wCAAwC,GAAG,EAAE,WAAW,gDAAgD,SAAS,mBAAmB,QAAQ,iCAAiC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4DAA4D,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,QAAQ,QAAQ,8CAA8C,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,0DAA0D,EAAE,GAAG,SAAS,6BAA6B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,SAAS,QAAQ,uCAAuC,CAAC,EAAE,EAAE,GAAG,WAAW,6CAA6C,SAAS,wBAAwB,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,+CAA+C,QAAQ,wCAAwC,GAAG,EAAE,SAAS,wBAAwB,QAAQ,wCAAwC,GAAG,EAAE,SAAS,oDAAoD,QAAQ,qCAAqC,GAAG,EAAE,WAAW,iDAAiD,SAAS,sDAAsD,QAAQ,4BAA4B,GAAG,EAAE,WAAW,yCAAyC,SAAS,wDAAwD,QAAQ,yCAAyC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,qCAAqC,QAAQ,uBAAuB,GAAG,EAAE,SAAS,KAAK,iBAAiB,CAAC,EAAE,QAAQ,iDAAiD,CAAC,GAAG,OAAO,KAAK,eAAe,CAAC,EAAE,QAAQ,+CAA+C,CAAC,GAAG,QAAQ,0BAA0B,GAAG,EAAE,SAAS,sBAAsB,QAAQ,sCAAsC,GAAG,EAAE,SAAS,eAAe,QAAQ,kCAAkC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,WAAW,oBAAoB,SAAS,sCAAsC,QAAQ,oCAAoC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,WAAW,WAAW,SAAS,8BAA8B,QAAQ,kCAAkC,GAAG,EAAE,SAAS,QAAQ,iBAAiB,CAAC,EAAE,QAAQ,kDAAkD,CAAC,GAAG,OAAO,OAAO,eAAe,CAAC,EAAE,QAAQ,gDAAgD,CAAC,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,SAAS,0BAA0B,QAAQ,qCAAqC,GAAG,EAAE,SAAS,cAAc,QAAQ,uDAAuD,CAAC,EAAE,GAAG,EAAE,SAAS,QAAQ,iBAAiB,CAAC,EAAE,QAAQ,kDAAkD,CAAC,GAAG,WAAW,qBAAqB,OAAO,OAAO,eAAe,CAAC,EAAE,QAAQ,gDAAgD,CAAC,GAAG,QAAQ,iCAAiC,GAAG,EAAE,SAAS,KAAK,iBAAiB,CAAC,EAAE,QAAQ,gDAAgD,CAAC,GAAG,OAAO,KAAK,eAAe,CAAC,EAAE,QAAQ,8CAA8C,CAAC,GAAG,QAAQ,iCAAiC,GAAG,EAAE,SAAS,mBAAmB,QAAQ,oCAAoC,CAAC,GAAG,cAAc,EAAE,WAAW,EAAE,YAAY,CAAC,EAAE,SAAS,kCAAkC,QAAQ,0CAA0C,GAAG,EAAE,SAAS,2CAA2C,QAAQ,mCAAmC,GAAG,EAAE,SAAS,iCAAiC,QAAQ,2CAA2C,GAAG,EAAE,SAAS,oCAAoC,QAAQ,qCAAqC,CAAC,EAAE,EAAE,GAAG,aAAa,mBAAmB,CAAC;AACnwI,IAAI,YAAY;AAAA,EACd;AACF;", "names": []}