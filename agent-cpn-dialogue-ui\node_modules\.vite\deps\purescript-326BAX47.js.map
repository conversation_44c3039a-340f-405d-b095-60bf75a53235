{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/purescript.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"PureScript\", \"fileTypes\": [\"purs\"], \"name\": \"purescript\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.purescript\" }, \"2\": { \"name\": \"punctuation.definition.entity.purescript\" } }, \"match\": \"(`)(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.)?[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(`)\", \"name\": \"keyword.operator.function.infix.purescript\" }, { \"begin\": \"^\\\\s*\\\\b(module)(?!')\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.purescript\" } }, \"end\": \"(where)\", \"endCaptures\": { \"1\": { \"name\": \"keyword.other.purescript\" } }, \"name\": \"meta.declaration.module.purescript\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#module_name\" }, { \"include\": \"#module_exports\" }, { \"match\": \"[a-z]+\", \"name\": \"invalid.purescript\" }] }, { \"begin\": \"^\\\\s*\\\\b(class)(?!')\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.class.purescript\" } }, \"end\": \"\\\\b(where)\\\\b|$\", \"endCaptures\": { \"1\": { \"name\": \"keyword.other.purescript\" } }, \"name\": \"meta.declaration.typeclass.purescript\", \"patterns\": [{ \"include\": \"#type_signature\" }] }, { \"begin\": \"^\\\\s*\\\\b(else\\\\s+)?(derive\\\\s+)?(newtype\\\\s+)?(instance)(?!')\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.purescript\" }, \"2\": { \"name\": \"keyword.other.purescript\" }, \"3\": { \"name\": \"keyword.other.purescript\" }, \"4\": { \"name\": \"keyword.other.purescript\" } }, \"contentName\": \"meta.type-signature.purescript\", \"end\": \"\\\\b(where)\\\\b|$\", \"endCaptures\": { \"1\": { \"name\": \"keyword.other.purescript\" } }, \"name\": \"meta.declaration.instance.purescript\", \"patterns\": [{ \"include\": \"#type_signature\" }] }, { \"begin\": \"^(\\\\s*)(foreign)\\\\s+(import)\\\\s+(data)\\\\s+([\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.other.purescript\" }, \"3\": { \"name\": \"keyword.other.purescript\" }, \"4\": { \"name\": \"keyword.other.purescript\" }, \"5\": { \"name\": \"entity.name.type.purescript\" }, \"6\": { \"name\": \"keyword.other.double-colon.purescript\" } }, \"contentName\": \"meta.kind-signature.purescript\", \"end\": \"^(?!\\\\1[ \\\\t]|[ \\\\t]*$)\", \"name\": \"meta.foreign.data.purescript\", \"patterns\": [{ \"include\": \"#double_colon\" }, { \"include\": \"#kind_signature\" }] }, { \"begin\": \"^(\\\\s*)(foreign)\\\\s+(import)\\\\s+([\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.other.purescript\" }, \"3\": { \"name\": \"keyword.other.purescript\" }, \"4\": { \"name\": \"entity.name.function.purescript\" } }, \"contentName\": \"meta.type-signature.purescript\", \"end\": \"^(?!\\\\1[ \\\\t]|[ \\\\t]*$)\", \"name\": \"meta.foreign.purescript\", \"patterns\": [{ \"include\": \"#double_colon\" }, { \"include\": \"#type_signature\" }] }, { \"begin\": \"^\\\\s*\\\\b(import)(?!')\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.purescript\" } }, \"end\": \"($|(?=--))\", \"name\": \"meta.import.purescript\", \"patterns\": [{ \"include\": \"#module_name\" }, { \"include\": \"#module_exports\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.purescript\" } }, \"match\": \"\\\\b(as|hiding)\\\\b\" }] }, { \"begin\": \"^(\\\\s)*(data|newtype)\\\\s+(.+?)\\\\s*(?=\\\\=|$)\", \"beginCaptures\": { \"2\": { \"name\": \"storage.type.data.purescript\" }, \"3\": { \"name\": \"meta.type-signature.purescript\", \"patterns\": [{ \"include\": \"#type_signature\" }] } }, \"end\": \"^(?!\\\\1[ \\\\t]|[ \\\\t]*$)\", \"name\": \"meta.declaration.type.data.purescript\", \"patterns\": [{ \"include\": \"#comments\" }, { \"captures\": { \"0\": { \"name\": \"keyword.operator.assignment.purescript\" } }, \"match\": \"=\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#data_ctor\" }] }, \"2\": { \"name\": \"meta.type-signature.purescript\", \"patterns\": [{ \"include\": \"#type_signature\" }] } }, \"match\": \"(?:(?:\\\\b([\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*)\\\\s+)(?:(?<ctorArgs>(?:(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*|(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.)?[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*|(?:(?:[\\\\w()'\\u2192\\u21D2\\\\[\\\\],]|->|=>)+\\\\s*)+))(?:\\\\s*(?:\\\\s+)\\\\s*\\\\g<ctorArgs>)?)?))\" }, { \"captures\": { \"0\": { \"name\": \"punctuation.separator.pipe.purescript\" } }, \"match\": \"\\\\|\" }, { \"include\": \"#record_types\" }] }, { \"begin\": \"^(\\\\s)*(type)\\\\s+(.+?)\\\\s*(?=\\\\=|$)\", \"beginCaptures\": { \"2\": { \"name\": \"storage.type.data.purescript\" }, \"3\": { \"name\": \"meta.type-signature.purescript\", \"patterns\": [{ \"include\": \"#type_signature\" }] } }, \"contentName\": \"meta.type-signature.purescript\", \"end\": \"^(?!\\\\1[ \\\\t]|[ \\\\t]*$)\", \"name\": \"meta.declaration.type.type.purescript\", \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"keyword.operator.assignment.purescript\" } }, \"match\": \"=\" }, { \"include\": \"#type_signature\" }, { \"include\": \"#record_types\" }, { \"include\": \"#comments\" }] }, { \"match\": \"^\\\\s*\\\\b(derive|where|data|type|newtype|infix[lr]?|foreign(\\\\s+import)?(\\\\s+data)?)(?!')\\\\b\", \"name\": \"keyword.other.purescript\" }, { \"match\": \"\\\\?(?:[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*|[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)\", \"name\": \"entity.name.function.typed-hole.purescript\" }, { \"match\": \"^\\\\s*\\\\b(data|type|newtype)(?!')\\\\b\", \"name\": \"storage.type.purescript\" }, { \"match\": \"\\\\b(do|ado|if|then|else|case|of|let|in)(?!('|\\\\s*(:|=)))\\\\b\", \"name\": \"keyword.control.purescript\" }, { \"match\": \"\\\\b(?<!\\\\$)0(x|X)[0-9a-fA-F]+\\\\b(?!\\\\$)\", \"name\": \"constant.numeric.hex.purescript\" }, { \"captures\": { \"0\": { \"name\": \"constant.numeric.decimal.purescript\" }, \"1\": { \"name\": \"meta.delimiter.decimal.period.purescript\" }, \"2\": { \"name\": \"meta.delimiter.decimal.period.purescript\" }, \"3\": { \"name\": \"meta.delimiter.decimal.period.purescript\" }, \"4\": { \"name\": \"meta.delimiter.decimal.period.purescript\" }, \"5\": { \"name\": \"meta.delimiter.decimal.period.purescript\" }, \"6\": { \"name\": \"meta.delimiter.decimal.period.purescript\" } }, \"match\": \"(?x)\\n(?<!\\\\$)(?:\\n(?:\\\\b[0-9]+(\\\\.)[0-9]+[eE][+-]?[0-9]+\\\\b)|\\n(?:\\\\b[0-9]+[eE][+-]?[0-9]+\\\\b)|\\n(?:\\\\b[0-9]+(\\\\.)[0-9]+\\\\b)|\\n(?:\\\\b[0-9]+\\\\b(?!\\\\.))\\n)(?!\\\\$)\", \"name\": \"constant.numeric.decimal.purescript\" }, { \"match\": \"\\\\b(true|false)\\\\b\", \"name\": \"constant.language.boolean.purescript\" }, { \"match\": \"\\\\b(([0-9]+_?)*[0-9]+|0([xX][0-9a-fA-F]+|[oO][0-7]+))\\\\b\", \"name\": \"constant.numeric.purescript\" }, { \"begin\": '\"\"\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.purescript\" } }, \"end\": '\"\"\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.purescript\" } }, \"name\": \"string.quoted.triple.purescript\" }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.purescript\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.purescript\" } }, \"name\": \"string.quoted.double.purescript\", \"patterns\": [{ \"include\": \"#characters\" }, { \"begin\": \"\\\\\\\\\\\\s\", \"beginCaptures\": { \"0\": { \"name\": \"markup.other.escape.newline.begin.purescript\" } }, \"end\": \"\\\\\\\\\", \"endCaptures\": { \"0\": { \"name\": \"markup.other.escape.newline.end.purescript\" } }, \"patterns\": [{ \"match\": \"\\\\S+\", \"name\": \"invalid.illegal.character-not-allowed-here.purescript\" }] }] }, { \"match\": \"\\\\\\\\$\", \"name\": \"markup.other.escape.newline.purescript\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.purescript\" }, \"2\": { \"patterns\": [{ \"include\": \"#characters\" }] }, \"7\": { \"name\": \"punctuation.definition.string.end.purescript\" } }, \"match\": `(')((?:[ -\\\\[\\\\]-~]|(\\\\\\\\(?:NUL|SOH|STX|ETX|EOT|ENQ|ACK|BEL|BS|HT|LF|VT|FF|CR|SO|SI|DLE|DC1|DC2|DC3|DC4|NAK|SYN|ETB|CAN|EM|SUB|ESC|FS|GS|RS|US|SP|DEL|[abfnrtv\\\\\\\\\\\\\"'\\\\&]))|(\\\\\\\\o[0-7]+)|(\\\\\\\\x[0-9A-Fa-f]+)|(\\\\^[A-Z@\\\\[\\\\]\\\\\\\\\\\\^_])))(')`, \"name\": \"string.quoted.single.purescript\" }, { \"include\": \"#function_type_declaration\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"$self\" }] }, \"2\": { \"name\": \"keyword.other.double-colon.purescript\" }, \"3\": { \"name\": \"meta.type-signature.purescript\", \"patterns\": [{ \"include\": \"#type_signature\" }] } }, \"match\": \"\\\\((?<paren>(?:[^()]|\\\\(\\\\g<paren>\\\\))*)(::|\\u2237)(?<paren2>(?:[^()]|\\\\(\\\\g<paren2>\\\\))*)\\\\)\" }, { \"begin\": \"^(\\\\s*)(?:(::|\\u2237))\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.other.double-colon.purescript\" } }, \"end\": \"^(?!\\\\1[ \\\\t]*|[ \\\\t]*$)\", \"patterns\": [{ \"include\": \"#type_signature\" }] }, { \"include\": \"#data_ctor\" }, { \"include\": \"#comments\" }, { \"include\": \"#infix_op\" }, { \"match\": \"\\\\<-|-\\\\>\", \"name\": \"keyword.other.arrow.purescript\" }, { \"match\": \"[\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]+\", \"name\": \"keyword.operator.purescript\" }, { \"match\": \",\", \"name\": \"punctuation.separator.comma.purescript\" }], \"repository\": { \"block_comment\": { \"patterns\": [{ \"applyEndPatternLast\": 1, \"begin\": \"\\\\{-\\\\s*\\\\|\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.documentation.purescript\" } }, \"end\": \"-\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.documentation.purescript\" } }, \"name\": \"comment.block.documentation.purescript\", \"patterns\": [{ \"include\": \"#block_comment\" }] }, { \"applyEndPatternLast\": 1, \"begin\": \"\\\\{-\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.purescript\" } }, \"end\": \"-\\\\}\", \"name\": \"comment.block.purescript\", \"patterns\": [{ \"include\": \"#block_comment\" }] }] }, \"characters\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.character.escape.purescript\" }, \"2\": { \"name\": \"constant.character.escape.octal.purescript\" }, \"3\": { \"name\": \"constant.character.escape.hexadecimal.purescript\" }, \"4\": { \"name\": \"constant.character.escape.control.purescript\" } }, \"match\": `(?:[ -\\\\[\\\\]-~]|(\\\\\\\\(?:NUL|SOH|STX|ETX|EOT|ENQ|ACK|BEL|BS|HT|LF|VT|FF|CR|SO|SI|DLE|DC1|DC2|DC3|DC4|NAK|SYN|ETB|CAN|EM|SUB|ESC|FS|GS|RS|US|SP|DEL|[abfnrtv\\\\\\\\\\\\\"'\\\\&]))|(\\\\\\\\o[0-7]+)|(\\\\\\\\x[0-9A-Fa-f]+)|(\\\\^[A-Z@\\\\[\\\\]\\\\\\\\\\\\^_]))` }] }, \"class_constraint\": { \"patterns\": [{ \"captures\": { \"1\": { \"patterns\": [{ \"match\": \"\\\\b[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\", \"name\": \"entity.name.type.purescript\" }] }, \"2\": { \"patterns\": [{ \"include\": \"#type_name\" }, { \"include\": \"#generic_type\" }] } }, \"match\": \"(?:(?:([\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*)\\\\s+)(?:(?<classConstraint>(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*|(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.)?[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)(?:\\\\s*(?:\\\\s+)\\\\s*\\\\g<classConstraint>)?)))\", \"name\": \"meta.class-constraint.purescript\" }] }, \"comments\": { \"patterns\": [{ \"begin\": \"(^[ \\\\t]+)?(?=--+\\\\s+\\\\|)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.purescript\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"(--+)\\\\s+(\\\\|)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.purescript\" }, \"2\": { \"name\": \"punctuation.definition.comment.documentation.purescript\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.double-dash.documentation.purescript\" }] }, { \"begin\": \"(^[ \\\\t]+)?(?=--+(?![\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.purescript\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"--\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.purescript\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.double-dash.purescript\" }] }, { \"include\": \"#block_comment\" }] }, \"data_ctor\": { \"patterns\": [{ \"match\": \"\\\\b[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\", \"name\": \"entity.name.tag.purescript\" }] }, \"double_colon\": { \"patterns\": [{ \"match\": \"(?:::|\\u2237)\", \"name\": \"keyword.other.double-colon.purescript\" }] }, \"function_type_declaration\": { \"patterns\": [{ \"begin\": \"^(\\\\s*)([\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)\\\\s*(?:(::|\\u2237)(?!.*<-))\", \"beginCaptures\": { \"2\": { \"name\": \"entity.name.function.purescript\" }, \"3\": { \"name\": \"keyword.other.double-colon.purescript\" } }, \"contentName\": \"meta.type-signature.purescript\", \"end\": \"^(?!\\\\1[ \\\\t]|[ \\\\t]*$)\", \"name\": \"meta.function.type-declaration.purescript\", \"patterns\": [{ \"include\": \"#double_colon\" }, { \"include\": \"#type_signature\" }] }] }, \"generic_type\": { \"patterns\": [{ \"match\": \"\\\\b(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.)?[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*\", \"name\": \"variable.other.generic-type.purescript\" }] }, \"infix_op\": { \"patterns\": [{ \"match\": \"(?:\\\\((?!--+\\\\))[\\\\p{S}\\\\p{P}&&[^(),;\\\\[\\\\]`{}_\\\"']]+\\\\))\", \"name\": \"entity.name.function.infix.purescript\" }] }, \"kind_signature\": { \"patterns\": [{ \"match\": \"\\\\*\", \"name\": \"keyword.other.star.purescript\" }, { \"match\": \"!\", \"name\": \"keyword.other.exclaimation-point.purescript\" }, { \"match\": \"#\", \"name\": \"keyword.other.pound-sign.purescript\" }, { \"match\": \"->|\\u2192\", \"name\": \"keyword.other.arrow.purescript\" }] }, \"module_exports\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"end\": \"\\\\)\", \"name\": \"meta.declaration.exports.purescript\", \"patterns\": [{ \"include\": \"#comments\" }, { \"match\": \"\\\\b(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.)?[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*\", \"name\": \"entity.name.function.purescript\" }, { \"include\": \"#type_name\" }, { \"match\": \",\", \"name\": \"punctuation.separator.comma.purescript\" }, { \"include\": \"#infix_op\" }, { \"match\": \"\\\\(.*?\\\\)\", \"name\": \"meta.other.constructor-list.purescript\" }] }] }, \"module_name\": { \"patterns\": [{ \"match\": \"(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.)*[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.?\", \"name\": \"support.other.module.purescript\" }] }, \"record_field_declaration\": { \"patterns\": [{ \"begin\": \"([\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)\\\\s*(::|\\u2237)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"match\": \"(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.)?[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*\", \"name\": \"entity.other.attribute-name.purescript\" }] }, \"2\": { \"name\": \"keyword.other.double-colon.purescript\" } }, \"contentName\": \"meta.type-signature.purescript\", \"end\": \"(?=([\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)\\\\s*(::|\\u2237)|})\", \"name\": \"meta.record-field.type-declaration.purescript\", \"patterns\": [{ \"include\": \"#type_signature\" }, { \"include\": \"#record_types\" }] }] }, \"record_types\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.type.record.begin.purescript\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"keyword.operator.type.record.end.purescript\" } }, \"name\": \"meta.type.record.purescript\", \"patterns\": [{ \"match\": \",\", \"name\": \"punctuation.separator.comma.purescript\" }, { \"include\": \"#record_field_declaration\" }, { \"include\": \"#comments\" }] }] }, \"type_name\": { \"patterns\": [{ \"match\": \"\\\\b[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\", \"name\": \"entity.name.type.purescript\" }] }, \"type_signature\": { \"patterns\": [{ \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#class_constraint\" }] }, \"4\": { \"name\": \"keyword.other.big-arrow.purescript\" } }, \"match\": \"(?:(?:\\\\()(?:(?<classConstraints>(?:(?:(?:([\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*)\\\\s+)(?:(?<classConstraint>(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*|(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.)?[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)(?:\\\\s*(?:\\\\s+)\\\\s*\\\\g<classConstraint>)?))))(?:\\\\s*(?:,)\\\\s*\\\\g<classConstraints>)?))(?:\\\\))(?:\\\\s*(=>|<=|\\u21D0|\\u21D2)))\", \"name\": \"meta.class-constraints.purescript\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#class_constraint\" }] }, \"4\": { \"name\": \"keyword.other.big-arrow.purescript\" } }, \"match\": \"((?:(?:([\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*)\\\\s+)(?:(?<classConstraint>(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*|(?:[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*(?:\\\\.[\\\\p{Lu}\\\\p{Lt}][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)*\\\\.)?[\\\\p{Ll}_][\\\\p{Ll}_\\\\p{Lu}\\\\p{Lt}\\\\p{Nd}']*)(?:\\\\s*(?:\\\\s+)\\\\s*\\\\g<classConstraint>)?))))\\\\s*(=>|<=|\\u21D0|\\u21D2)\", \"name\": \"meta.class-constraints.purescript\" }, { \"match\": \"->|\\u2192\", \"name\": \"keyword.other.arrow.purescript\" }, { \"match\": \"=>|\\u21D2\", \"name\": \"keyword.other.big-arrow.purescript\" }, { \"match\": \"<=|\\u21D0\", \"name\": \"keyword.other.big-arrow-left.purescript\" }, { \"match\": \"forall|\\u2200\", \"name\": \"keyword.other.forall.purescript\" }, { \"include\": \"#generic_type\" }, { \"include\": \"#type_name\" }, { \"include\": \"#comments\" }] } }, \"scopeName\": \"source.purescript\" });\nvar purescript = [\n  lang\n];\n\nexport { purescript as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,cAAc,aAAa,CAAC,MAAM,GAAG,QAAQ,cAAc,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,SAAS,uKAAuK,QAAQ,6CAA6C,GAAG,EAAE,SAAS,4BAA4B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,QAAQ,sCAAsC,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,SAAS,UAAU,QAAQ,qBAAqB,CAAC,EAAE,GAAG,EAAE,SAAS,2BAA2B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,mBAAmB,eAAe,EAAE,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,QAAQ,yCAAyC,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,EAAE,SAAS,oEAAoE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,eAAe,kCAAkC,OAAO,mBAAmB,eAAe,EAAE,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,QAAQ,wCAAwC,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,EAAE,SAAS,iGAAiG,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,eAAe,kCAAkC,OAAO,2BAA2B,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,EAAE,SAAS,iFAAiF,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,eAAe,kCAAkC,OAAO,2BAA2B,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,EAAE,SAAS,4BAA4B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,OAAO,cAAc,QAAQ,0BAA0B,YAAY,CAAC,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,SAAS,oBAAoB,CAAC,EAAE,GAAG,EAAE,SAAS,+CAA+C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,kCAAkC,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,EAAE,GAAG,OAAO,2BAA2B,QAAQ,yCAAyC,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,SAAS,IAAI,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,kCAAkC,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,EAAE,GAAG,SAAS,weAAkf,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,SAAS,MAAM,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,uCAAuC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,kCAAkC,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,EAAE,GAAG,eAAe,kCAAkC,OAAO,2BAA2B,QAAQ,yCAAyC,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,SAAS,IAAI,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,+FAA+F,QAAQ,2BAA2B,GAAG,EAAE,SAAS,wGAAwG,QAAQ,6CAA6C,GAAG,EAAE,SAAS,uCAAuC,QAAQ,0BAA0B,GAAG,EAAE,SAAS,+DAA+D,QAAQ,6BAA6B,GAAG,EAAE,SAAS,2CAA2C,QAAQ,kCAAkC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,SAAS,qKAAqK,QAAQ,sCAAsC,GAAG,EAAE,SAAS,sBAAsB,QAAQ,uCAAuC,GAAG,EAAE,SAAS,4DAA4D,QAAQ,8BAA8B,GAAG,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,QAAQ,kCAAkC,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,QAAQ,mCAAmC,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,OAAO,QAAQ,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,QAAQ,QAAQ,wDAAwD,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,SAAS,QAAQ,yCAAyC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iDAAiD,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,SAAS,iPAAiP,QAAQ,kCAAkC,GAAG,EAAE,WAAW,6BAA6B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,kCAAkC,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,EAAE,GAAG,SAAS,2FAAgG,GAAG,EAAE,SAAS,qBAA0B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,OAAO,4BAA4B,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,SAAS,aAAa,QAAQ,iCAAiC,GAAG,EAAE,SAAS,yCAAyC,QAAQ,8BAA8B,GAAG,EAAE,SAAS,KAAK,QAAQ,yCAAyC,CAAC,GAAG,cAAc,EAAE,iBAAiB,EAAE,YAAY,CAAC,EAAE,uBAAuB,GAAG,SAAS,eAAe,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0DAA0D,EAAE,GAAG,OAAO,QAAQ,eAAe,EAAE,KAAK,EAAE,QAAQ,0DAA0D,EAAE,GAAG,QAAQ,0CAA0C,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,uBAAuB,GAAG,SAAS,QAAQ,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,QAAQ,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,CAAC,EAAE,GAAG,cAAc,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,mDAAmD,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,SAAS,wOAAwO,CAAC,EAAE,GAAG,oBAAoB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,iHAAiH,QAAQ,8BAA8B,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,EAAE,GAAG,SAAS,ycAAyc,QAAQ,mCAAmC,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,6BAA6B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oDAAoD,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,SAAS,kBAAkB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,0DAA0D,EAAE,GAAG,OAAO,OAAO,QAAQ,oDAAoD,CAAC,EAAE,GAAG,EAAE,SAAS,8DAA8D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oDAAoD,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,SAAS,MAAM,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,OAAO,QAAQ,sCAAsC,CAAC,EAAE,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,SAAS,iHAAiH,QAAQ,6BAA6B,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,CAAC,EAAE,SAAS,YAAiB,QAAQ,wCAAwC,CAAC,EAAE,GAAG,6BAA6B,EAAE,YAAY,CAAC,EAAE,SAAS,8EAAmF,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,eAAe,kCAAkC,OAAO,2BAA2B,QAAQ,6CAA6C,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,kBAAkB,CAAC,EAAE,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,CAAC,EAAE,SAAS,oKAAoK,QAAQ,yCAAyC,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,6DAA6D,QAAQ,wCAAwC,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,QAAQ,gCAAgC,GAAG,EAAE,SAAS,KAAK,QAAQ,8CAA8C,GAAG,EAAE,SAAS,KAAK,QAAQ,sCAAsC,GAAG,EAAE,SAAS,QAAa,QAAQ,iCAAiC,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,OAAO,OAAO,QAAQ,uCAAuC,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,SAAS,oKAAoK,QAAQ,kCAAkC,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,KAAK,QAAQ,yCAAyC,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,SAAS,aAAa,QAAQ,yCAAyC,CAAC,EAAE,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,SAAS,oOAAoO,QAAQ,kCAAkC,CAAC,EAAE,GAAG,4BAA4B,EAAE,YAAY,CAAC,EAAE,SAAS,2DAAgE,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,iKAAiK,QAAQ,yCAAyC,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,eAAe,kCAAkC,OAAO,iEAAsE,QAAQ,iDAAiD,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,QAAQ,+BAA+B,YAAY,CAAC,EAAE,SAAS,KAAK,QAAQ,yCAAyC,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,SAAS,iHAAiH,QAAQ,8BAA8B,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,SAAS,kjBAA4jB,QAAQ,oCAAoC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,SAAS,0dAAoe,QAAQ,oCAAoC,GAAG,EAAE,SAAS,QAAa,QAAQ,iCAAiC,GAAG,EAAE,SAAS,QAAa,QAAQ,qCAAqC,GAAG,EAAE,SAAS,QAAa,QAAQ,0CAA0C,GAAG,EAAE,SAAS,YAAiB,QAAQ,kCAAkC,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,EAAE,GAAG,aAAa,oBAAoB,CAAC;AAC1whB,IAAI,aAAa;AAAA,EACf;AACF;", "names": []}