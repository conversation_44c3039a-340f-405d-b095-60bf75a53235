{"version": 3, "sources": ["../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/shiki/node_modules/@shikijs/langs/dist/docker.mjs"], "sourcesContent": ["const lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Dockerfile\\\",\\\"name\\\":\\\"docker\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.other.special-method.dockerfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.special-method.dockerfile\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*\\\\\\\\b(?i:(FROM))\\\\\\\\b.*?\\\\\\\\b(?i:(AS))\\\\\\\\b\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.control.dockerfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.special-method.dockerfile\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(?i:(ONBUILD)\\\\\\\\s+)?(?i:(ADD|ARG|CMD|COPY|ENTRYPOINT|ENV|EXPOSE|FROM|HEALTHCHECK|LABEL|MAINTAINER|RUN|SHELL|STOPSIGNAL|USER|VOLUME|WORKDIR))\\\\\\\\s\\\"},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"keyword.operator.dockerfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"keyword.other.special-method.dockerfile\\\"}},\\\"match\\\":\\\"^\\\\\\\\s*(?i:(ONBUILD)\\\\\\\\s+)?(?i:(CMD|ENTRYPOINT))\\\\\\\\s\\\"},{\\\"include\\\":\\\"#string-character-escape\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.dockerfile\\\"}},\\\"end\\\":\\\"\\\\\\\"\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.dockerfile\\\"}},\\\"name\\\":\\\"string.quoted.double.dockerfile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-character-escape\\\"}]},{\\\"begin\\\":\\\"'\\\",\\\"beginCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.begin.dockerfile\\\"}},\\\"end\\\":\\\"'\\\",\\\"endCaptures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.definition.string.end.dockerfile\\\"}},\\\"name\\\":\\\"string.quoted.single.dockerfile\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#string-character-escape\\\"}]},{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"punctuation.whitespace.comment.leading.dockerfile\\\"},\\\"2\\\":{\\\"name\\\":\\\"comment.line.number-sign.dockerfile\\\"},\\\"3\\\":{\\\"name\\\":\\\"punctuation.definition.comment.dockerfile\\\"}},\\\"match\\\":\\\"^(\\\\\\\\s*)((#).*$\\\\\\\\n?)\\\"}],\\\"repository\\\":{\\\"string-character-escape\\\":{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escaped.dockerfile\\\"}},\\\"scopeName\\\":\\\"source.dockerfile\\\",\\\"aliases\\\":[\\\"dockerfile\\\"]}\"))\n\nexport default [\nlang\n]\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,KAAK,MAAM,6oDAAm0D,CAAC;AAE12D,IAAA,WAAe;EACf;AACA;;;;;;;;;", "names": []}