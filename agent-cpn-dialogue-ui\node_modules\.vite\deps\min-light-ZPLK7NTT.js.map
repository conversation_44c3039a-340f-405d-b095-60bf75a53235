{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/themes/min-light.mjs"], "sourcesContent": ["var minLight = Object.freeze({\n  \"colors\": {\n    \"activityBar.background\": \"#f6f6f6\",\n    \"activityBar.foreground\": \"#9E9E9E\",\n    \"activityBarBadge.background\": \"#616161\",\n    \"badge.background\": \"#E0E0E0\",\n    \"badge.foreground\": \"#616161\",\n    \"button.background\": \"#757575\",\n    \"button.hoverBackground\": \"#616161\",\n    \"debugIcon.breakpointCurrentStackframeForeground\": \"#1976D2\",\n    \"debugIcon.breakpointDisabledForeground\": \"#848484\",\n    \"debugIcon.breakpointForeground\": \"#D32F2F\",\n    \"debugIcon.breakpointStackframeForeground\": \"#1976D2\",\n    \"debugIcon.continueForeground\": \"#6f42c1\",\n    \"debugIcon.disconnectForeground\": \"#6f42c1\",\n    \"debugIcon.pauseForeground\": \"#6f42c1\",\n    \"debugIcon.restartForeground\": \"#1976D2\",\n    \"debugIcon.startForeground\": \"#1976D2\",\n    \"debugIcon.stepBackForeground\": \"#6f42c1\",\n    \"debugIcon.stepIntoForeground\": \"#6f42c1\",\n    \"debugIcon.stepOutForeground\": \"#6f42c1\",\n    \"debugIcon.stepOverForeground\": \"#6f42c1\",\n    \"debugIcon.stopForeground\": \"#1976D2\",\n    \"diffEditor.insertedTextBackground\": \"#b7e7a44b\",\n    \"diffEditor.removedTextBackground\": \"#e597af52\",\n    \"editor.background\": \"#ffffff\",\n    \"editor.foreground\": \"#212121\",\n    \"editor.lineHighlightBorder\": \"#f2f2f2\",\n    \"editorBracketMatch.background\": \"#E7F3FF\",\n    \"editorBracketMatch.border\": \"#c8e1ff\",\n    \"editorGroupHeader.tabsBackground\": \"#f6f6f6\",\n    \"editorGroupHeader.tabsBorder\": \"#fff\",\n    \"editorIndentGuide.background\": \"#EEE\",\n    \"editorLineNumber.activeForeground\": \"#757575\",\n    \"editorLineNumber.foreground\": \"#CCC\",\n    \"editorSuggestWidget.background\": \"#F3F3F3\",\n    \"extensionButton.prominentBackground\": \"#000000AA\",\n    \"extensionButton.prominentHoverBackground\": \"#000000BB\",\n    \"focusBorder\": \"#D0D0D0\",\n    \"foreground\": \"#757575\",\n    \"gitDecoration.ignoredResourceForeground\": \"#AAAAAA\",\n    \"input.border\": \"#E9E9E9\",\n    \"inputOption.activeBackground\": \"#EDEDED\",\n    \"list.activeSelectionBackground\": \"#EEE\",\n    \"list.activeSelectionForeground\": \"#212121\",\n    \"list.focusBackground\": \"#ddd\",\n    \"list.focusForeground\": \"#212121\",\n    \"list.highlightForeground\": \"#212121\",\n    \"list.inactiveSelectionBackground\": \"#E0E0E0\",\n    \"list.inactiveSelectionForeground\": \"#212121\",\n    \"panel.background\": \"#fff\",\n    \"panel.border\": \"#f4f4f4\",\n    \"panelTitle.activeBorder\": \"#fff\",\n    \"panelTitle.inactiveForeground\": \"#BDBDBD\",\n    \"peekView.border\": \"#E0E0E0\",\n    \"peekViewEditor.background\": \"#f8f8f8\",\n    \"pickerGroup.foreground\": \"#000\",\n    \"progressBar.background\": \"#000\",\n    \"scrollbar.shadow\": \"#FFF\",\n    \"sideBar.background\": \"#f6f6f6\",\n    \"sideBar.border\": \"#f6f6f6\",\n    \"sideBarSectionHeader.background\": \"#EEE\",\n    \"sideBarTitle.foreground\": \"#999\",\n    \"statusBar.background\": \"#f6f6f6\",\n    \"statusBar.border\": \"#f6f6f6\",\n    \"statusBar.debuggingBackground\": \"#f6f6f6\",\n    \"statusBar.foreground\": \"#7E7E7E\",\n    \"statusBar.noFolderBackground\": \"#f6f6f6\",\n    \"statusBarItem.prominentBackground\": \"#0000001a\",\n    \"statusBarItem.remoteBackground\": \"#f6f6f600\",\n    \"statusBarItem.remoteForeground\": \"#7E7E7E\",\n    \"symbolIcon.classForeground\": \"#dd8500\",\n    \"symbolIcon.constructorForeground\": \"#6f42c1\",\n    \"symbolIcon.enumeratorForeground\": \"#dd8500\",\n    \"symbolIcon.enumeratorMemberForeground\": \"#1976D2\",\n    \"symbolIcon.eventForeground\": \"#dd8500\",\n    \"symbolIcon.fieldForeground\": \"#1976D2\",\n    \"symbolIcon.functionForeground\": \"#6f42c1\",\n    \"symbolIcon.interfaceForeground\": \"#1976D2\",\n    \"symbolIcon.methodForeground\": \"#6f42c1\",\n    \"symbolIcon.variableForeground\": \"#1976D2\",\n    \"tab.activeBorder\": \"#FFF\",\n    \"tab.activeForeground\": \"#424242\",\n    \"tab.border\": \"#f6f6f6\",\n    \"tab.inactiveBackground\": \"#f6f6f6\",\n    \"tab.inactiveForeground\": \"#BDBDBD\",\n    \"tab.unfocusedActiveBorder\": \"#fff\",\n    \"terminal.ansiBlack\": \"#333\",\n    \"terminal.ansiBlue\": \"#e0e0e0\",\n    \"terminal.ansiBrightBlack\": \"#a1a1a1\",\n    \"terminal.ansiBrightBlue\": \"#6871ff\",\n    \"terminal.ansiBrightCyan\": \"#57d9ad\",\n    \"terminal.ansiBrightGreen\": \"#a3d900\",\n    \"terminal.ansiBrightMagenta\": \"#a37acc\",\n    \"terminal.ansiBrightRed\": \"#d6656a\",\n    \"terminal.ansiBrightWhite\": \"#7E7E7E\",\n    \"terminal.ansiBrightYellow\": \"#e7c547\",\n    \"terminal.ansiCyan\": \"#4dbf99\",\n    \"terminal.ansiGreen\": \"#77cc00\",\n    \"terminal.ansiMagenta\": \"#9966cc\",\n    \"terminal.ansiRed\": \"#D32F2F\",\n    \"terminal.ansiWhite\": \"#c7c7c7\",\n    \"terminal.ansiYellow\": \"#f29718\",\n    \"terminal.background\": \"#fff\",\n    \"textLink.activeForeground\": \"#000\",\n    \"textLink.foreground\": \"#000\",\n    \"titleBar.activeBackground\": \"#f6f6f6\",\n    \"titleBar.border\": \"#FFFFFF00\",\n    \"titleBar.inactiveBackground\": \"#f6f6f6\"\n  },\n  \"displayName\": \"Min Light\",\n  \"name\": \"min-light\",\n  \"tokenColors\": [\n    {\n      \"settings\": {\n        \"foreground\": \"#24292eff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.accessor\",\n        \"meta.group.braces.round.function.arguments\",\n        \"meta.template.expression\",\n        \"markup.fenced_code meta.embedded.block\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#24292eff\"\n      }\n    },\n    {\n      \"scope\": \"emphasis\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": [\n        \"strong\",\n        \"markup.heading.markdown\",\n        \"markup.bold.markdown\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.italic.markdown\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": \"meta.link.inline.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"underline\",\n        \"foreground\": \"#1976D2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\",\n        \"markup.fenced_code\",\n        \"markup.inline\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#2b5581\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment\",\n        \"string.quoted.docstring.multi\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c2c3c5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.numeric\",\n        \"constant.language\",\n        \"constant.other.placeholder\",\n        \"constant.character.format.placeholder\",\n        \"variable.language.this\",\n        \"variable.other.object\",\n        \"variable.other.class\",\n        \"variable.other.constant\",\n        \"meta.property-name\",\n        \"meta.property-value\",\n        \"support\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#1976D2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword\",\n        \"storage.modifier\",\n        \"storage.type\",\n        \"storage.control.clojure\",\n        \"entity.name.function.clojure\",\n        \"entity.name.tag.yaml\",\n        \"support.function.node\",\n        \"support.type.property-name.json\",\n        \"punctuation.separator.key-value\",\n        \"punctuation.definition.template-expression\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#D32F2F\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.function\",\n      \"settings\": {\n        \"foreground\": \"#FF9800\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.function\",\n        \"entity.name.type\",\n        \"entity.other.inherited-class\",\n        \"meta.function-call\",\n        \"meta.instance.constructor\",\n        \"entity.other.attribute-name\",\n        \"entity.name.function\",\n        \"constant.keyword.clojure\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#6f42c1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag\",\n        \"string.quoted\",\n        \"string.regexp\",\n        \"string.interpolated\",\n        \"string.template\",\n        \"string.unquoted.plain.out.yaml\",\n        \"keyword.other.template\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#22863a\"\n      }\n    },\n    {\n      \"scope\": \"token.info-token\",\n      \"settings\": {\n        \"foreground\": \"#316bcd\"\n      }\n    },\n    {\n      \"scope\": \"token.warn-token\",\n      \"settings\": {\n        \"foreground\": \"#cd9731\"\n      }\n    },\n    {\n      \"scope\": \"token.error-token\",\n      \"settings\": {\n        \"foreground\": \"#cd3131\"\n      }\n    },\n    {\n      \"scope\": \"token.debug-token\",\n      \"settings\": {\n        \"foreground\": \"#800080\"\n      }\n    },\n    {\n      \"scope\": [\n        \"strong\",\n        \"markup.heading.markdown\",\n        \"markup.bold.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#6f42c1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.arguments\",\n        \"punctuation.definition.dict\",\n        \"punctuation.separator\",\n        \"meta.function-call.arguments\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#212121\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline.link\",\n        \"punctuation.definition.metadata.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#22863a\"\n      }\n    },\n    {\n      \"scope\": [\n        \"beginning.punctuation.definition.list.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#6f42c1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.string.begin.markdown\",\n        \"punctuation.definition.string.end.markdown\",\n        \"string.other.link.title.markdown\",\n        \"string.other.link.description.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d32f2f\"\n      }\n    }\n  ],\n  \"type\": \"light\"\n});\n\nexport { minLight as default };\n"], "mappings": ";;;AAAA,IAAI,WAAW,OAAO,OAAO;AAAA,EAC3B,UAAU;AAAA,IACR,0BAA0B;AAAA,IAC1B,0BAA0B;AAAA,IAC1B,+BAA+B;AAAA,IAC/B,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,0BAA0B;AAAA,IAC1B,mDAAmD;AAAA,IACnD,0CAA0C;AAAA,IAC1C,kCAAkC;AAAA,IAClC,4CAA4C;AAAA,IAC5C,gCAAgC;AAAA,IAChC,kCAAkC;AAAA,IAClC,6BAA6B;AAAA,IAC7B,+BAA+B;AAAA,IAC/B,6BAA6B;AAAA,IAC7B,gCAAgC;AAAA,IAChC,gCAAgC;AAAA,IAChC,+BAA+B;AAAA,IAC/B,gCAAgC;AAAA,IAChC,4BAA4B;AAAA,IAC5B,qCAAqC;AAAA,IACrC,oCAAoC;AAAA,IACpC,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,iCAAiC;AAAA,IACjC,6BAA6B;AAAA,IAC7B,oCAAoC;AAAA,IACpC,gCAAgC;AAAA,IAChC,gCAAgC;AAAA,IAChC,qCAAqC;AAAA,IACrC,+BAA+B;AAAA,IAC/B,kCAAkC;AAAA,IAClC,uCAAuC;AAAA,IACvC,4CAA4C;AAAA,IAC5C,eAAe;AAAA,IACf,cAAc;AAAA,IACd,2CAA2C;AAAA,IAC3C,gBAAgB;AAAA,IAChB,gCAAgC;AAAA,IAChC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,4BAA4B;AAAA,IAC5B,oCAAoC;AAAA,IACpC,oCAAoC;AAAA,IACpC,oBAAoB;AAAA,IACpB,gBAAgB;AAAA,IAChB,2BAA2B;AAAA,IAC3B,iCAAiC;AAAA,IACjC,mBAAmB;AAAA,IACnB,6BAA6B;AAAA,IAC7B,0BAA0B;AAAA,IAC1B,0BAA0B;AAAA,IAC1B,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,kBAAkB;AAAA,IAClB,mCAAmC;AAAA,IACnC,2BAA2B;AAAA,IAC3B,wBAAwB;AAAA,IACxB,oBAAoB;AAAA,IACpB,iCAAiC;AAAA,IACjC,wBAAwB;AAAA,IACxB,gCAAgC;AAAA,IAChC,qCAAqC;AAAA,IACrC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,oCAAoC;AAAA,IACpC,mCAAmC;AAAA,IACnC,yCAAyC;AAAA,IACzC,8BAA8B;AAAA,IAC9B,8BAA8B;AAAA,IAC9B,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,+BAA+B;AAAA,IAC/B,iCAAiC;AAAA,IACjC,oBAAoB;AAAA,IACpB,wBAAwB;AAAA,IACxB,cAAc;AAAA,IACd,0BAA0B;AAAA,IAC1B,0BAA0B;AAAA,IAC1B,6BAA6B;AAAA,IAC7B,sBAAsB;AAAA,IACtB,qBAAqB;AAAA,IACrB,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,4BAA4B;AAAA,IAC5B,8BAA8B;AAAA,IAC9B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,6BAA6B;AAAA,IAC7B,qBAAqB;AAAA,IACrB,sBAAsB;AAAA,IACtB,wBAAwB;AAAA,IACxB,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,IACvB,6BAA6B;AAAA,IAC7B,uBAAuB;AAAA,IACvB,6BAA6B;AAAA,IAC7B,mBAAmB;AAAA,IACnB,+BAA+B;AAAA,EACjC;AAAA,EACA,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,eAAe;AAAA,IACb;AAAA,MACE,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AACV,CAAC;", "names": []}