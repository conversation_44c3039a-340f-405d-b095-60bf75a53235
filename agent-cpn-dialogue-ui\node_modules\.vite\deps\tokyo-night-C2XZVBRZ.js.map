{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/themes/tokyo-night.mjs"], "sourcesContent": ["var tokyoNight = Object.freeze({\n  \"colors\": {\n    \"activityBar.background\": \"#16161e\",\n    \"activityBar.border\": \"#16161e\",\n    \"activityBar.foreground\": \"#787c99\",\n    \"activityBar.inactiveForeground\": \"#3b3e52\",\n    \"activityBarBadge.background\": \"#3d59a1\",\n    \"activityBarBadge.foreground\": \"#fff\",\n    \"badge.background\": \"#7e83b230\",\n    \"badge.foreground\": \"#acb0d0\",\n    \"breadcrumb.activeSelectionForeground\": \"#a9b1d6\",\n    \"breadcrumb.background\": \"#16161e\",\n    \"breadcrumb.focusForeground\": \"#a9b1d6\",\n    \"breadcrumb.foreground\": \"#515670\",\n    \"breadcrumbPicker.background\": \"#16161e\",\n    \"button.background\": \"#3d59a1dd\",\n    \"button.foreground\": \"#ffffff\",\n    \"button.hoverBackground\": \"#3d59a1AA\",\n    \"button.secondaryBackground\": \"#3b3e52\",\n    \"charts.blue\": \"#7aa2f7\",\n    \"charts.foreground\": \"#9AA5CE\",\n    \"charts.green\": \"#41a6b5\",\n    \"charts.lines\": \"#16161e\",\n    \"charts.orange\": \"#ff9e64\",\n    \"charts.purple\": \"#9d7cd8\",\n    \"charts.red\": \"#f7768e\",\n    \"charts.yellow\": \"#e0af68\",\n    \"debugConsole.errorForeground\": \"#bb616b\",\n    \"debugConsole.infoForeground\": \"#787c99\",\n    \"debugConsole.sourceForeground\": \"#787c99\",\n    \"debugConsole.warningForeground\": \"#c49a5a\",\n    \"debugConsoleInputIcon.foreground\": \"#73daca\",\n    \"debugExceptionWidget.background\": \"#101014\",\n    \"debugExceptionWidget.border\": \"#963c47\",\n    \"debugIcon.breakpointDisabledForeground\": \"#414761\",\n    \"debugIcon.breakpointForeground\": \"#db4b4b\",\n    \"debugIcon.breakpointUnverifiedForeground\": \"#c24242\",\n    \"debugTokenExpression.boolean\": \"#ff9e64\",\n    \"debugTokenExpression.error\": \"#bb616b\",\n    \"debugTokenExpression.name\": \"#7dcfff\",\n    \"debugTokenExpression.number\": \"#ff9e64\",\n    \"debugTokenExpression.string\": \"#9ece6a\",\n    \"debugTokenExpression.value\": \"#9aa5ce\",\n    \"debugToolBar.background\": \"#101014\",\n    \"debugView.stateLabelBackground\": \"#14141b\",\n    \"debugView.stateLabelForeground\": \"#787c99\",\n    \"debugView.valueChangedHighlight\": \"#3d59a1aa\",\n    \"descriptionForeground\": \"#515670\",\n    \"diffEditor.diagonalFill\": \"#292e42\",\n    \"diffEditor.insertedLineBackground\": \"#41a6b520\",\n    \"diffEditor.insertedTextBackground\": \"#41a6b520\",\n    \"diffEditor.removedLineBackground\": \"#db4b4b22\",\n    \"diffEditor.removedTextBackground\": \"#db4b4b22\",\n    \"diffEditorGutter.insertedLineBackground\": \"#41a6b525\",\n    \"diffEditorGutter.removedLineBackground\": \"#db4b4b22\",\n    \"diffEditorOverview.insertedForeground\": \"#41a6b525\",\n    \"diffEditorOverview.removedForeground\": \"#db4b4b22\",\n    \"dropdown.background\": \"#14141b\",\n    \"dropdown.foreground\": \"#787c99\",\n    \"dropdown.listBackground\": \"#14141b\",\n    \"editor.background\": \"#1a1b26\",\n    \"editor.findMatchBackground\": \"#3d59a166\",\n    \"editor.findMatchBorder\": \"#e0af68\",\n    \"editor.findMatchHighlightBackground\": \"#3d59a166\",\n    \"editor.findRangeHighlightBackground\": \"#515c7e33\",\n    \"editor.focusedStackFrameHighlightBackground\": \"#73daca20\",\n    \"editor.foldBackground\": \"#1111174a\",\n    \"editor.foreground\": \"#a9b1d6\",\n    \"editor.inactiveSelectionBackground\": \"#515c7e25\",\n    \"editor.lineHighlightBackground\": \"#1e202e\",\n    \"editor.rangeHighlightBackground\": \"#515c7e20\",\n    \"editor.selectionBackground\": \"#515c7e4d\",\n    \"editor.selectionHighlightBackground\": \"#515c7e44\",\n    \"editor.stackFrameHighlightBackground\": \"#E2BD3A20\",\n    \"editor.wordHighlightBackground\": \"#515c7e44\",\n    \"editor.wordHighlightStrongBackground\": \"#515c7e55\",\n    \"editorBracketHighlight.foreground1\": \"#698cd6\",\n    \"editorBracketHighlight.foreground2\": \"#68b3de\",\n    \"editorBracketHighlight.foreground3\": \"#9a7ecc\",\n    \"editorBracketHighlight.foreground4\": \"#25aac2\",\n    \"editorBracketHighlight.foreground5\": \"#80a856\",\n    \"editorBracketHighlight.foreground6\": \"#c49a5a\",\n    \"editorBracketHighlight.unexpectedBracket.foreground\": \"#db4b4b\",\n    \"editorBracketMatch.background\": \"#16161e\",\n    \"editorBracketMatch.border\": \"#42465d\",\n    \"editorBracketPairGuide.activeBackground1\": \"#698cd6\",\n    \"editorBracketPairGuide.activeBackground2\": \"#68b3de\",\n    \"editorBracketPairGuide.activeBackground3\": \"#9a7ecc\",\n    \"editorBracketPairGuide.activeBackground4\": \"#25aac2\",\n    \"editorBracketPairGuide.activeBackground5\": \"#80a856\",\n    \"editorBracketPairGuide.activeBackground6\": \"#c49a5a\",\n    \"editorCodeLens.foreground\": \"#51597d\",\n    \"editorCursor.foreground\": \"#c0caf5\",\n    \"editorError.foreground\": \"#db4b4b\",\n    \"editorGhostText.foreground\": \"#646e9c\",\n    \"editorGroup.border\": \"#101014\",\n    \"editorGroup.dropBackground\": \"#1e202e\",\n    \"editorGroupHeader.border\": \"#101014\",\n    \"editorGroupHeader.noTabsBackground\": \"#16161e\",\n    \"editorGroupHeader.tabsBackground\": \"#16161e\",\n    \"editorGroupHeader.tabsBorder\": \"#101014\",\n    \"editorGutter.addedBackground\": \"#164846\",\n    \"editorGutter.deletedBackground\": \"#823c41\",\n    \"editorGutter.modifiedBackground\": \"#394b70\",\n    \"editorHint.foreground\": \"#0da0ba\",\n    \"editorHoverWidget.background\": \"#16161e\",\n    \"editorHoverWidget.border\": \"#101014\",\n    \"editorIndentGuide.activeBackground\": \"#363b54\",\n    \"editorIndentGuide.background\": \"#1e202e\",\n    \"editorInfo.foreground\": \"#0da0ba\",\n    \"editorLightBulb.foreground\": \"#e0af68\",\n    \"editorLightBulbAutoFix.foreground\": \"#e0af68\",\n    \"editorLineNumber.activeForeground\": \"#737aa2\",\n    \"editorLineNumber.foreground\": \"#363b54\",\n    \"editorLink.activeForeground\": \"#acb0d0\",\n    \"editorMarkerNavigation.background\": \"#16161e\",\n    \"editorOverviewRuler.addedForeground\": \"#164846\",\n    \"editorOverviewRuler.border\": \"#101014\",\n    \"editorOverviewRuler.bracketMatchForeground\": \"#101014\",\n    \"editorOverviewRuler.deletedForeground\": \"#703438\",\n    \"editorOverviewRuler.errorForeground\": \"#db4b4b\",\n    \"editorOverviewRuler.findMatchForeground\": \"#a9b1d644\",\n    \"editorOverviewRuler.infoForeground\": \"#1abc9c\",\n    \"editorOverviewRuler.modifiedForeground\": \"#394b70\",\n    \"editorOverviewRuler.rangeHighlightForeground\": \"#a9b1d644\",\n    \"editorOverviewRuler.selectionHighlightForeground\": \"#a9b1d622\",\n    \"editorOverviewRuler.warningForeground\": \"#e0af68\",\n    \"editorOverviewRuler.wordHighlightForeground\": \"#bb9af755\",\n    \"editorOverviewRuler.wordHighlightStrongForeground\": \"#bb9af766\",\n    \"editorPane.background\": \"#16161e\",\n    \"editorRuler.foreground\": \"#101014\",\n    \"editorSuggestWidget.background\": \"#16161e\",\n    \"editorSuggestWidget.border\": \"#101014\",\n    \"editorSuggestWidget.highlightForeground\": \"#6183bb\",\n    \"editorSuggestWidget.selectedBackground\": \"#20222c\",\n    \"editorWarning.foreground\": \"#e0af68\",\n    \"editorWhitespace.foreground\": \"#363b54\",\n    \"editorWidget.background\": \"#16161e\",\n    \"editorWidget.foreground\": \"#787c99\",\n    \"editorWidget.resizeBorder\": \"#545c7e33\",\n    \"errorForeground\": \"#515670\",\n    \"extensionBadge.remoteBackground\": \"#3d59a1\",\n    \"extensionBadge.remoteForeground\": \"#ffffff\",\n    \"extensionButton.prominentBackground\": \"#3d59a1DD\",\n    \"extensionButton.prominentForeground\": \"#ffffff\",\n    \"extensionButton.prominentHoverBackground\": \"#3d59a1AA\",\n    \"focusBorder\": \"#545c7e33\",\n    \"foreground\": \"#787c99\",\n    \"gitDecoration.addedResourceForeground\": \"#449dab\",\n    \"gitDecoration.conflictingResourceForeground\": \"#e0af68cc\",\n    \"gitDecoration.deletedResourceForeground\": \"#914c54\",\n    \"gitDecoration.ignoredResourceForeground\": \"#515670\",\n    \"gitDecoration.modifiedResourceForeground\": \"#6183bb\",\n    \"gitDecoration.renamedResourceForeground\": \"#449dab\",\n    \"gitDecoration.stageDeletedResourceForeground\": \"#914c54\",\n    \"gitDecoration.stageModifiedResourceForeground\": \"#6183bb\",\n    \"gitDecoration.untrackedResourceForeground\": \"#449dab\",\n    \"gitlens.gutterBackgroundColor\": \"#16161e\",\n    \"gitlens.gutterForegroundColor\": \"#787c99\",\n    \"gitlens.gutterUncommittedForegroundColor\": \"#7aa2f7\",\n    \"gitlens.trailingLineForegroundColor\": \"#646e9c\",\n    \"icon.foreground\": \"#787c99\",\n    \"input.background\": \"#14141b\",\n    \"input.border\": \"#0f0f14\",\n    \"input.foreground\": \"#a9b1d6\",\n    \"input.placeholderForeground\": \"#787c998A\",\n    \"inputOption.activeBackground\": \"#3d59a144\",\n    \"inputOption.activeForeground\": \"#c0caf5\",\n    \"inputValidation.errorBackground\": \"#85353e\",\n    \"inputValidation.errorBorder\": \"#963c47\",\n    \"inputValidation.errorForeground\": \"#bbc2e0\",\n    \"inputValidation.infoBackground\": \"#3d59a15c\",\n    \"inputValidation.infoBorder\": \"#3d59a1\",\n    \"inputValidation.infoForeground\": \"#bbc2e0\",\n    \"inputValidation.warningBackground\": \"#c2985b\",\n    \"inputValidation.warningBorder\": \"#e0af68\",\n    \"inputValidation.warningForeground\": \"#000000\",\n    \"list.activeSelectionBackground\": \"#202330\",\n    \"list.activeSelectionForeground\": \"#a9b1d6\",\n    \"list.deemphasizedForeground\": \"#787c99\",\n    \"list.dropBackground\": \"#1e202e\",\n    \"list.errorForeground\": \"#bb616b\",\n    \"list.focusBackground\": \"#1c1d29\",\n    \"list.focusForeground\": \"#a9b1d6\",\n    \"list.highlightForeground\": \"#668ac4\",\n    \"list.hoverBackground\": \"#13131a\",\n    \"list.hoverForeground\": \"#a9b1d6\",\n    \"list.inactiveSelectionBackground\": \"#1c1d29\",\n    \"list.inactiveSelectionForeground\": \"#a9b1d6\",\n    \"list.invalidItemForeground\": \"#c97018\",\n    \"list.warningForeground\": \"#c49a5a\",\n    \"listFilterWidget.background\": \"#101014\",\n    \"listFilterWidget.noMatchesOutline\": \"#a6333f\",\n    \"listFilterWidget.outline\": \"#3d59a1\",\n    \"menu.background\": \"#16161e\",\n    \"menu.border\": \"#101014\",\n    \"menu.foreground\": \"#787c99\",\n    \"menu.selectionBackground\": \"#1e202e\",\n    \"menu.selectionForeground\": \"#a9b1d6\",\n    \"menu.separatorBackground\": \"#101014\",\n    \"menubar.selectionBackground\": \"#1e202e\",\n    \"menubar.selectionBorder\": \"#1b1e2e\",\n    \"menubar.selectionForeground\": \"#a9b1d6\",\n    \"merge.currentContentBackground\": \"#007a7544\",\n    \"merge.currentHeaderBackground\": \"#41a6b525\",\n    \"merge.incomingContentBackground\": \"#3d59a144\",\n    \"merge.incomingHeaderBackground\": \"#3d59a1aa\",\n    \"mergeEditor.change.background\": \"#41a6b525\",\n    \"mergeEditor.change.word.background\": \"#41a6b540\",\n    \"mergeEditor.conflict.handled.minimapOverViewRuler\": \"#449dab\",\n    \"mergeEditor.conflict.handledFocused.border\": \"#41a6b565\",\n    \"mergeEditor.conflict.handledUnfocused.border\": \"#41a6b525\",\n    \"mergeEditor.conflict.unhandled.minimapOverViewRuler\": \"#e0af68\",\n    \"mergeEditor.conflict.unhandledFocused.border\": \"#e0af68b0\",\n    \"mergeEditor.conflict.unhandledUnfocused.border\": \"#e0af6888\",\n    \"minimapGutter.addedBackground\": \"#1C5957\",\n    \"minimapGutter.deletedBackground\": \"#944449\",\n    \"minimapGutter.modifiedBackground\": \"#425882\",\n    \"notebook.cellBorderColor\": \"#101014\",\n    \"notebook.cellEditorBackground\": \"#16161e\",\n    \"notebook.cellStatusBarItemHoverBackground\": \"#1c1d29\",\n    \"notebook.editorBackground\": \"#1a1b26\",\n    \"notebook.focusedCellBorder\": \"#29355a\",\n    \"notificationCenterHeader.background\": \"#101014\",\n    \"notificationLink.foreground\": \"#6183bb\",\n    \"notifications.background\": \"#101014\",\n    \"notificationsErrorIcon.foreground\": \"#bb616b\",\n    \"notificationsInfoIcon.foreground\": \"#0da0ba\",\n    \"notificationsWarningIcon.foreground\": \"#bba461\",\n    \"panel.background\": \"#16161e\",\n    \"panel.border\": \"#101014\",\n    \"panelInput.border\": \"#16161e\",\n    \"panelTitle.activeBorder\": \"#16161e\",\n    \"panelTitle.activeForeground\": \"#787c99\",\n    \"panelTitle.inactiveForeground\": \"#42465d\",\n    \"peekView.border\": \"#101014\",\n    \"peekViewEditor.background\": \"#16161e\",\n    \"peekViewEditor.matchHighlightBackground\": \"#3d59a166\",\n    \"peekViewResult.background\": \"#101014\",\n    \"peekViewResult.fileForeground\": \"#787c99\",\n    \"peekViewResult.lineForeground\": \"#a9b1d6\",\n    \"peekViewResult.matchHighlightBackground\": \"#3d59a166\",\n    \"peekViewResult.selectionBackground\": \"#3d59a133\",\n    \"peekViewResult.selectionForeground\": \"#a9b1d6\",\n    \"peekViewTitle.background\": \"#101014\",\n    \"peekViewTitleDescription.foreground\": \"#787c99\",\n    \"peekViewTitleLabel.foreground\": \"#a9b1d6\",\n    \"pickerGroup.border\": \"#101014\",\n    \"pickerGroup.foreground\": \"#a9b1d6\",\n    \"progressBar.background\": \"#3d59a1\",\n    \"sash.hoverBorder\": \"#29355a\",\n    \"scrollbar.shadow\": \"#00000033\",\n    \"scrollbarSlider.activeBackground\": \"#868bc422\",\n    \"scrollbarSlider.background\": \"#868bc415\",\n    \"scrollbarSlider.hoverBackground\": \"#868bc410\",\n    \"selection.background\": \"#515c7e40\",\n    \"settings.headerForeground\": \"#6183bb\",\n    \"sideBar.background\": \"#16161e\",\n    \"sideBar.border\": \"#101014\",\n    \"sideBar.dropBackground\": \"#1e202e\",\n    \"sideBar.foreground\": \"#787c99\",\n    \"sideBarSectionHeader.background\": \"#16161e\",\n    \"sideBarSectionHeader.border\": \"#101014\",\n    \"sideBarSectionHeader.foreground\": \"#a9b1d6\",\n    \"sideBarTitle.foreground\": \"#787c99\",\n    \"statusBar.background\": \"#16161e\",\n    \"statusBar.border\": \"#101014\",\n    \"statusBar.debuggingBackground\": \"#16161e\",\n    \"statusBar.debuggingForeground\": \"#787c99\",\n    \"statusBar.foreground\": \"#787c99\",\n    \"statusBar.noFolderBackground\": \"#16161e\",\n    \"statusBarItem.activeBackground\": \"#101014\",\n    \"statusBarItem.hoverBackground\": \"#20222c\",\n    \"statusBarItem.prominentBackground\": \"#101014\",\n    \"statusBarItem.prominentHoverBackground\": \"#20222c\",\n    \"tab.activeBackground\": \"#16161e\",\n    \"tab.activeBorder\": \"#3d59a1\",\n    \"tab.activeForeground\": \"#a9b1d6\",\n    \"tab.activeModifiedBorder\": \"#1a1b26\",\n    \"tab.border\": \"#101014\",\n    \"tab.hoverForeground\": \"#a9b1d6\",\n    \"tab.inactiveBackground\": \"#16161e\",\n    \"tab.inactiveForeground\": \"#787c99\",\n    \"tab.inactiveModifiedBorder\": \"#1f202e\",\n    \"tab.lastPinnedBorder\": \"#222333\",\n    \"tab.unfocusedActiveBorder\": \"#1f202e\",\n    \"tab.unfocusedActiveForeground\": \"#a9b1d6\",\n    \"tab.unfocusedHoverForeground\": \"#a9b1d6\",\n    \"tab.unfocusedInactiveForeground\": \"#787c99\",\n    \"terminal.ansiBlack\": \"#363b54\",\n    \"terminal.ansiBlue\": \"#7aa2f7\",\n    \"terminal.ansiBrightBlack\": \"#363b54\",\n    \"terminal.ansiBrightBlue\": \"#7aa2f7\",\n    \"terminal.ansiBrightCyan\": \"#7dcfff\",\n    \"terminal.ansiBrightGreen\": \"#41a6b5\",\n    \"terminal.ansiBrightMagenta\": \"#bb9af7\",\n    \"terminal.ansiBrightRed\": \"#f7768e\",\n    \"terminal.ansiBrightWhite\": \"#acb0d0\",\n    \"terminal.ansiBrightYellow\": \"#e0af68\",\n    \"terminal.ansiCyan\": \"#7dcfff\",\n    \"terminal.ansiGreen\": \"#73daca\",\n    \"terminal.ansiMagenta\": \"#bb9af7\",\n    \"terminal.ansiRed\": \"#f7768e\",\n    \"terminal.ansiWhite\": \"#787c99\",\n    \"terminal.ansiYellow\": \"#e0af68\",\n    \"terminal.background\": \"#16161e\",\n    \"terminal.foreground\": \"#787c99\",\n    \"terminal.selectionBackground\": \"#515c7e4d\",\n    \"textBlockQuote.background\": \"#16161e\",\n    \"textCodeBlock.background\": \"#16161e\",\n    \"textLink.activeForeground\": \"#7dcfff\",\n    \"textLink.foreground\": \"#6183bb\",\n    \"textPreformat.foreground\": \"#9699a8\",\n    \"textSeparator.foreground\": \"#363b54\",\n    \"titleBar.activeBackground\": \"#16161e\",\n    \"titleBar.activeForeground\": \"#787c99\",\n    \"titleBar.border\": \"#101014\",\n    \"titleBar.inactiveBackground\": \"#16161e\",\n    \"titleBar.inactiveForeground\": \"#787c99\",\n    \"toolbar.activeBackground\": \"#202330\",\n    \"toolbar.hoverBackground\": \"#202330\",\n    \"tree.indentGuidesStroke\": \"#2b2b3b\",\n    \"walkThrough.embeddedEditorBackground\": \"#16161e\",\n    \"widget.shadow\": \"#ffffff00\",\n    \"window.activeBorder\": \"#0d0f17\",\n    \"window.inactiveBorder\": \"#0d0f17\"\n  },\n  \"displayName\": \"Tokyo Night\",\n  \"name\": \"tokyo-night\",\n  \"semanticTokenColors\": {\n    \"*.defaultLibrary\": {\n      \"foreground\": \"#2ac3de\"\n    },\n    \"parameter\": {\n      \"foreground\": \"#d9d4cd\"\n    },\n    \"parameter.declaration\": {\n      \"foreground\": \"#e0af68\"\n    },\n    \"property.declaration\": {\n      \"foreground\": \"#73daca\"\n    },\n    \"property.defaultLibrary\": {\n      \"foreground\": \"#2ac3de\"\n    },\n    \"variable\": {\n      \"foreground\": \"#c0caf5\"\n    },\n    \"variable.declaration\": {\n      \"foreground\": \"#bb9af7\"\n    },\n    \"variable.defaultLibrary\": {\n      \"foreground\": \"#2ac3de\"\n    }\n  },\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"comment\",\n        \"meta.var.expr storage.type\",\n        \"keyword.control.flow\",\n        \"keyword.control.return\",\n        \"meta.directive.vue punctuation.separator.key-value.html\",\n        \"meta.directive.vue entity.other.attribute-name.html\",\n        \"tag.decorator.js entity.name.tag.js\",\n        \"tag.decorator.js punctuation.definition.tag.js\",\n        \"storage.modifier\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.flow.block-scalar.literal\",\n        \"keyword.control.flow.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment\",\n        \"comment.block.documentation\",\n        \"punctuation.definition.comment\",\n        \"comment.block.documentation punctuation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#51597d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.assignment.jsdoc\",\n        \"comment.block.documentation variable\",\n        \"comment.block.documentation storage\",\n        \"comment.block.documentation keyword\",\n        \"comment.block.documentation support\",\n        \"comment.block.documentation markup\",\n        \"comment.block.documentation markup.inline.raw.string.markdown\",\n        \"meta.other.type.phpdoc.php keyword.other.type.php\",\n        \"meta.other.type.phpdoc.php support.other.namespace.php\",\n        \"meta.other.type.phpdoc.php punctuation.separator.inheritance.php\",\n        \"meta.other.type.phpdoc.php support.class\",\n        \"keyword.other.phpdoc.php\",\n        \"log.date\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5a638c\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.other.type.phpdoc.php support.class\",\n        \"comment.block.documentation storage.type\",\n        \"comment.block.documentation punctuation.definition.block.tag\",\n        \"comment.block.documentation entity.name.type.instance\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#646e9c\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.constant\",\n        \"punctuation.definition.constant\",\n        \"constant.language\",\n        \"constant.numeric\",\n        \"support.constant\",\n        \"constant.other.caps\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff9e64\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\",\n        \"constant.other.symbol\",\n        \"constant.other.key\",\n        \"meta.attribute-selector\",\n        \"string constant.character\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#9ece6a\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.color\",\n        \"constant.other.color.rgb-value.hex punctuation.definition.constant\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9aa5ce\"\n      }\n    },\n    {\n      \"scope\": [\n        \"invalid\",\n        \"invalid.illegal\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff5370\"\n      }\n    },\n    {\n      \"scope\": \"invalid.deprecated\",\n      \"settings\": {\n        \"foreground\": \"#bb9af7\"\n      }\n    },\n    {\n      \"scope\": \"storage.type\",\n      \"settings\": {\n        \"foreground\": \"#bb9af7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.var.expr storage.type\",\n        \"storage.modifier\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9d7cd8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.template-expression\",\n        \"punctuation.section.embedded\",\n        \"meta.embedded.line.tag.smarty\",\n        \"support.constant.handlebars\",\n        \"punctuation.section.tag.twig\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7dcfff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.smarty\",\n        \"keyword.control.twig\",\n        \"support.constant.handlebars keyword.control\",\n        \"keyword.operator.comparison.twig\",\n        \"keyword.blade\",\n        \"entity.name.function.blade\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#0db9d7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.spread\",\n        \"keyword.operator.rest\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#f7768e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator\",\n        \"keyword.control.as\",\n        \"keyword.other\",\n        \"keyword.operator.bitwise.shift\",\n        \"punctuation\",\n        \"expression.embbeded.vue punctuation.definition.tag\",\n        \"text.html.twig meta.tag.inline.any.html\",\n        \"meta.tag.template.value.twig meta.function.arguments.twig\",\n        \"meta.directive.vue punctuation.separator.key-value.html\",\n        \"punctuation.definition.constant.markdown\",\n        \"punctuation.definition.string\",\n        \"punctuation.support.type.property-name\",\n        \"text.html.vue-html meta.tag\",\n        \"meta.attribute.directive\",\n        \"punctuation.definition.keyword\",\n        \"punctuation.terminator.rule\",\n        \"punctuation.definition.entity\",\n        \"punctuation.separator.inheritance.php\",\n        \"keyword.other.template\",\n        \"keyword.other.substitution\",\n        \"entity.name.operator\",\n        \"meta.property-list punctuation.separator.key-value\",\n        \"meta.at-rule.mixin punctuation.separator.key-value\",\n        \"meta.at-rule.function variable.parameter.url\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#89ddff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.module.js\",\n        \"keyword.control.import\",\n        \"keyword.control.export\",\n        \"keyword.control.from\",\n        \"keyword.control.default\",\n        \"meta.import keyword.other\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7dcfff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword\",\n        \"keyword.control\",\n        \"keyword.other.important\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bb9af7\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.DML\",\n      \"settings\": {\n        \"foreground\": \"#7dcfff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.logical\",\n        \"storage.type.function\",\n        \"keyword.operator.bitwise\",\n        \"keyword.operator.ternary\",\n        \"keyword.operator.comparison\",\n        \"keyword.operator.relational\",\n        \"keyword.operator.or.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bb9af7\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag\",\n      \"settings\": {\n        \"foreground\": \"#f7768e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag support.class.component\",\n        \"meta.tag.custom entity.name.tag\",\n        \"meta.tag.other.unrecognized.html.derivative entity.name.tag\",\n        \"meta.tag\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#de5971\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.tag\",\n      \"settings\": {\n        \"foreground\": \"#ba3c97\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.php\",\n        \"variable.other.global.safer\",\n        \"variable.other.global.safer punctuation.definition.variable\",\n        \"variable.other.global\",\n        \"variable.other.global punctuation.definition.variable\",\n        \"constant.other\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e0af68\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable\",\n        \"support.variable\",\n        \"string constant.other.placeholder\",\n        \"variable.parameter.handlebars\",\n        \"variable.other.object\",\n        \"meta.fstring\",\n        \"meta.function-call meta.function-call.arguments\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c0caf5\"\n      }\n    },\n    {\n      \"scope\": \"meta.array.literal variable\",\n      \"settings\": {\n        \"foreground\": \"#7dcfff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.object-literal.key\",\n        \"entity.name.type.hcl\",\n        \"string.alias.graphql\",\n        \"string.unquoted.graphql\",\n        \"string.unquoted.alias.graphql\",\n        \"meta.group.braces.curly constant.other.object.key.js string.unquoted.label.js\",\n        \"meta.field.declaration.ts variable.object.property\",\n        \"meta.block entity.name.label\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#73daca\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.property\",\n        \"support.variable.property\",\n        \"support.variable.property.dom\",\n        \"meta.function-call variable.other.object.property\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7dcfff\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.object.property\",\n      \"settings\": {\n        \"foreground\": \"#c0caf5\"\n      }\n    },\n    {\n      \"scope\": \"meta.objectliteral meta.object.member meta.objectliteral meta.object.member meta.objectliteral meta.object.member meta.object-literal.key\",\n      \"settings\": {\n        \"foreground\": \"#41a6b5\"\n      }\n    },\n    {\n      \"scope\": \"source.cpp meta.block variable.other\",\n      \"settings\": {\n        \"foreground\": \"#f7768e\"\n      }\n    },\n    {\n      \"scope\": \"support.other.variable\",\n      \"settings\": {\n        \"foreground\": \"#f7768e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.class-method.js entity.name.function.js\",\n        \"entity.name.method.js\",\n        \"variable.function.constructor\",\n        \"keyword.other.special-method\",\n        \"storage.type.cs\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7aa2f7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function\",\n        \"variable.other.enummember\",\n        \"meta.function-call\",\n        \"meta.function-call entity.name.function\",\n        \"variable.function\",\n        \"meta.definition.method entity.name.function\",\n        \"meta.object-literal entity.name.function\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7aa2f7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.function.language.special\",\n        \"variable.parameter\",\n        \"meta.function.parameters punctuation.definition.variable\",\n        \"meta.function.parameter variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e0af68\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.type.php\",\n        \"storage.type.php\",\n        \"constant.character\",\n        \"constant.escape\",\n        \"keyword.other.unit\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bb9af7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.definition.variable variable.other.constant\",\n        \"meta.definition.variable variable.other.readwrite\",\n        \"variable.declaration.hcl variable.other.readwrite.hcl\",\n        \"meta.mapping.key.hcl variable.other.readwrite.hcl\",\n        \"variable.other.declaration\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bb9af7\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.inherited-class\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#bb9af7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.class\",\n        \"support.type\",\n        \"variable.other.readwrite.alias\",\n        \"support.orther.namespace.use.php\",\n        \"meta.use.php\",\n        \"support.other.namespace.php\",\n        \"support.type.sys-types\",\n        \"support.variable.dom\",\n        \"support.constant.math\",\n        \"support.type.object.module\",\n        \"support.constant.json\",\n        \"entity.name.namespace\",\n        \"meta.import.qualifier\",\n        \"variable.other.constant.object\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#0db9d7\"\n      }\n    },\n    {\n      \"scope\": \"entity.name\",\n      \"settings\": {\n        \"foreground\": \"#c0caf5\"\n      }\n    },\n    {\n      \"scope\": \"support.function\",\n      \"settings\": {\n        \"foreground\": \"#0db9d7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css support.type.property-name\",\n        \"source.sass support.type.property-name\",\n        \"source.scss support.type.property-name\",\n        \"source.less support.type.property-name\",\n        \"source.stylus support.type.property-name\",\n        \"source.postcss support.type.property-name\",\n        \"support.type.property-name.css\",\n        \"support.type.vendored.property-name\",\n        \"support.type.map.key\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7aa2f7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant.font-name\",\n        \"meta.definition.variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9ece6a\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name.class\",\n        \"meta.at-rule.mixin.scss entity.name.function.scss\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9ece6a\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.id\",\n      \"settings\": {\n        \"foreground\": \"#fc7b7b\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.css\",\n      \"settings\": {\n        \"foreground\": \"#0db9d7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name.pseudo-class punctuation.definition.entity\",\n        \"entity.other.attribute-name.pseudo-element punctuation.definition.entity\",\n        \"entity.other.attribute-name.class punctuation.definition.entity\",\n        \"entity.name.tag.reference\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e0af68\"\n      }\n    },\n    {\n      \"scope\": \"meta.property-list\",\n      \"settings\": {\n        \"foreground\": \"#9abdf5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.property-list meta.at-rule.if\",\n        \"meta.at-rule.return variable.parameter.url\",\n        \"meta.property-list meta.at-rule.else\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff9e64\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name.parent-selector-suffix punctuation.definition.entity.css\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#73daca\"\n      }\n    },\n    {\n      \"scope\": \"meta.property-list meta.property-list\",\n      \"settings\": {\n        \"foreground\": \"#9abdf5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.at-rule.mixin keyword.control.at-rule.mixin\",\n        \"meta.at-rule.include entity.name.function.scss\",\n        \"meta.at-rule.include keyword.control.at-rule.include\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bb9af7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.at-rule.include punctuation.definition.keyword\",\n        \"keyword.control.at-rule.mixin punctuation.definition.keyword\",\n        \"meta.at-rule.include keyword.control.at-rule.include\",\n        \"keyword.control.at-rule.extend punctuation.definition.keyword\",\n        \"meta.at-rule.extend keyword.control.at-rule.extend\",\n        \"entity.other.attribute-name.placeholder.css punctuation.definition.entity.css\",\n        \"meta.at-rule.media keyword.control.at-rule.media\",\n        \"meta.at-rule.mixin keyword.control.at-rule.mixin\",\n        \"meta.at-rule.function keyword.control.at-rule.function\",\n        \"keyword.control punctuation.definition.keyword\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9d7cd8\"\n      }\n    },\n    {\n      \"scope\": \"meta.property-list meta.at-rule.include\",\n      \"settings\": {\n        \"foreground\": \"#c0caf5\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.property-value\",\n      \"settings\": {\n        \"foreground\": \"#ff9e64\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.module.js\",\n        \"variable.import.parameter.js\",\n        \"variable.other.class.js\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c0caf5\"\n      }\n    },\n    {\n      \"scope\": \"variable.language\",\n      \"settings\": {\n        \"foreground\": \"#f7768e\"\n      }\n    },\n    {\n      \"scope\": \"variable.other punctuation.definition.variable\",\n      \"settings\": {\n        \"foreground\": \"#c0caf5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.js constant.other.object.key.js string.unquoted.label.js\",\n        \"variable.language.this punctuation.definition.variable\",\n        \"keyword.other.this\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f7768e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name\",\n        \"text.html.basic entity.other.attribute-name.html\",\n        \"text.html.basic entity.other.attribute-name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bb9af7\"\n      }\n    },\n    {\n      \"scope\": \"text.html constant.character.entity\",\n      \"settings\": {\n        \"foreground\": \"#0DB9D7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name.id.html\",\n        \"meta.directive.vue entity.other.attribute-name.html\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bb9af7\"\n      }\n    },\n    {\n      \"scope\": \"source.sass keyword.control\",\n      \"settings\": {\n        \"foreground\": \"#7aa2f7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name.pseudo-class\",\n        \"entity.other.attribute-name.pseudo-element\",\n        \"entity.other.attribute-name.placeholder\",\n        \"meta.property-list meta.property-value\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bb9af7\"\n      }\n    },\n    {\n      \"scope\": \"markup.inserted\",\n      \"settings\": {\n        \"foreground\": \"#449dab\"\n      }\n    },\n    {\n      \"scope\": \"markup.deleted\",\n      \"settings\": {\n        \"foreground\": \"#914c54\"\n      }\n    },\n    {\n      \"scope\": \"markup.changed\",\n      \"settings\": {\n        \"foreground\": \"#6183bb\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp\",\n      \"settings\": {\n        \"foreground\": \"#b4f9f8\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.group\",\n      \"settings\": {\n        \"foreground\": \"#f7768e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.character-class.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bb9af7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.character-class.set.regexp\",\n        \"punctuation.definition.character-class.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e0af68\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.quantifier.regexp\",\n      \"settings\": {\n        \"foreground\": \"#89ddff\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape.backslash\",\n      \"settings\": {\n        \"foreground\": \"#c0caf5\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape\",\n      \"settings\": {\n        \"foreground\": \"#89ddff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"tag.decorator.js entity.name.tag.js\",\n        \"tag.decorator.js punctuation.definition.tag.js\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7aa2f7\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.unit\",\n      \"settings\": {\n        \"foreground\": \"#f7768e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7aa2f7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#0db9d7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7dcfff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bb9af7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e0af68\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#0db9d7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#73daca\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f7768e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9ece6a\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.list_item.markdown\",\n      \"settings\": {\n        \"foreground\": \"#9abdf5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.block\",\n        \"meta.brace\",\n        \"punctuation.definition.block\",\n        \"punctuation.definition.use\",\n        \"punctuation.definition.class\",\n        \"punctuation.definition.begin.bracket\",\n        \"punctuation.definition.end.bracket\",\n        \"punctuation.definition.switch-expression.begin.bracket\",\n        \"punctuation.definition.switch-expression.end.bracket\",\n        \"punctuation.definition.section.switch-block.begin.bracket\",\n        \"punctuation.definition.section.switch-block.end.bracket\",\n        \"punctuation.definition.group.shell\",\n        \"punctuation.definition.parameters\",\n        \"punctuation.definition.arguments\",\n        \"punctuation.definition.dictionary\",\n        \"punctuation.definition.array\",\n        \"punctuation.section\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9abdf5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.embedded.block\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c0caf5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.tag JSXNested\",\n        \"meta.jsx.children\",\n        \"text.html\",\n        \"text.log\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9aa5ce\"\n      }\n    },\n    {\n      \"scope\": \"text.html.markdown markup.inline.raw.markdown\",\n      \"settings\": {\n        \"foreground\": \"#bb9af7\"\n      }\n    },\n    {\n      \"scope\": \"text.html.markdown markup.inline.raw.markdown punctuation.definition.raw.markdown\",\n      \"settings\": {\n        \"foreground\": \"#4E5579\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.1.markdown entity.name\",\n        \"heading.1.markdown punctuation.definition.heading.markdown\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#89ddff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.2.markdown entity.name\",\n        \"heading.2.markdown punctuation.definition.heading.markdown\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#61bdf2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.3.markdown entity.name\",\n        \"heading.3.markdown punctuation.definition.heading.markdown\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#7aa2f7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.4.markdown entity.name\",\n        \"heading.4.markdown punctuation.definition.heading.markdown\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#6d91de\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.5.markdown entity.name\",\n        \"heading.5.markdown punctuation.definition.heading.markdown\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#9aa5ce\"\n      }\n    },\n    {\n      \"scope\": [\n        \"heading.6.markdown entity.name\",\n        \"heading.6.markdown punctuation.definition.heading.markdown\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#747ca1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.italic\",\n        \"markup.italic punctuation\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c0caf5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.bold\",\n        \"markup.bold punctuation\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#c0caf5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.bold markup.italic\",\n        \"markup.bold markup.italic punctuation\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold italic\",\n        \"foreground\": \"#c0caf5\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline\",\n        \"markup.underline punctuation\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline\"\n      }\n    },\n    {\n      \"scope\": \"markup.quote punctuation.definition.blockquote.markdown\",\n      \"settings\": {\n        \"foreground\": \"#4e5579\"\n      }\n    },\n    {\n      \"scope\": \"markup.quote\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.other.link\",\n        \"markup.underline.link\",\n        \"constant.other.reference.link.markdown\",\n        \"string.other.link.description.title.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#73daca\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.fenced_code.block.markdown\",\n        \"markup.inline.raw.string.markdown\",\n        \"variable.language.fenced.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#89ddff\"\n      }\n    },\n    {\n      \"scope\": \"meta.separator\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#51597d\"\n      }\n    },\n    {\n      \"scope\": \"markup.table\",\n      \"settings\": {\n        \"foreground\": \"#c0cefc\"\n      }\n    },\n    {\n      \"scope\": \"token.info-token\",\n      \"settings\": {\n        \"foreground\": \"#0db9d7\"\n      }\n    },\n    {\n      \"scope\": \"token.warn-token\",\n      \"settings\": {\n        \"foreground\": \"#ffdb69\"\n      }\n    },\n    {\n      \"scope\": \"token.error-token\",\n      \"settings\": {\n        \"foreground\": \"#db4b4b\"\n      }\n    },\n    {\n      \"scope\": \"token.debug-token\",\n      \"settings\": {\n        \"foreground\": \"#b267e6\"\n      }\n    },\n    {\n      \"scope\": \"entity.tag.apacheconf\",\n      \"settings\": {\n        \"foreground\": \"#f7768e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.preprocessor\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#73daca\"\n      }\n    },\n    {\n      \"scope\": \"source.env\",\n      \"settings\": {\n        \"foreground\": \"#7aa2f7\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { tokyoNight as default };\n"], "mappings": ";;;AAAA,IAAI,aAAa,OAAO,OAAO;AAAA,EAC7B,UAAU;AAAA,IACR,0BAA0B;AAAA,IAC1B,sBAAsB;AAAA,IACtB,0BAA0B;AAAA,IAC1B,kCAAkC;AAAA,IAClC,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,wCAAwC;AAAA,IACxC,yBAAyB;AAAA,IACzB,8BAA8B;AAAA,IAC9B,yBAAyB;AAAA,IACzB,+BAA+B;AAAA,IAC/B,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,0BAA0B;AAAA,IAC1B,8BAA8B;AAAA,IAC9B,eAAe;AAAA,IACf,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,gCAAgC;AAAA,IAChC,+BAA+B;AAAA,IAC/B,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,mCAAmC;AAAA,IACnC,+BAA+B;AAAA,IAC/B,0CAA0C;AAAA,IAC1C,kCAAkC;AAAA,IAClC,4CAA4C;AAAA,IAC5C,gCAAgC;AAAA,IAChC,8BAA8B;AAAA,IAC9B,6BAA6B;AAAA,IAC7B,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,8BAA8B;AAAA,IAC9B,2BAA2B;AAAA,IAC3B,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,mCAAmC;AAAA,IACnC,yBAAyB;AAAA,IACzB,2BAA2B;AAAA,IAC3B,qCAAqC;AAAA,IACrC,qCAAqC;AAAA,IACrC,oCAAoC;AAAA,IACpC,oCAAoC;AAAA,IACpC,2CAA2C;AAAA,IAC3C,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,wCAAwC;AAAA,IACxC,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,IACvB,2BAA2B;AAAA,IAC3B,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,0BAA0B;AAAA,IAC1B,uCAAuC;AAAA,IACvC,uCAAuC;AAAA,IACvC,+CAA+C;AAAA,IAC/C,yBAAyB;AAAA,IACzB,qBAAqB;AAAA,IACrB,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,mCAAmC;AAAA,IACnC,8BAA8B;AAAA,IAC9B,uCAAuC;AAAA,IACvC,wCAAwC;AAAA,IACxC,kCAAkC;AAAA,IAClC,wCAAwC;AAAA,IACxC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,uDAAuD;AAAA,IACvD,iCAAiC;AAAA,IACjC,6BAA6B;AAAA,IAC7B,4CAA4C;AAAA,IAC5C,4CAA4C;AAAA,IAC5C,4CAA4C;AAAA,IAC5C,4CAA4C;AAAA,IAC5C,4CAA4C;AAAA,IAC5C,4CAA4C;AAAA,IAC5C,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,0BAA0B;AAAA,IAC1B,8BAA8B;AAAA,IAC9B,sBAAsB;AAAA,IACtB,8BAA8B;AAAA,IAC9B,4BAA4B;AAAA,IAC5B,sCAAsC;AAAA,IACtC,oCAAoC;AAAA,IACpC,gCAAgC;AAAA,IAChC,gCAAgC;AAAA,IAChC,kCAAkC;AAAA,IAClC,mCAAmC;AAAA,IACnC,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,4BAA4B;AAAA,IAC5B,sCAAsC;AAAA,IACtC,gCAAgC;AAAA,IAChC,yBAAyB;AAAA,IACzB,8BAA8B;AAAA,IAC9B,qCAAqC;AAAA,IACrC,qCAAqC;AAAA,IACrC,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,qCAAqC;AAAA,IACrC,uCAAuC;AAAA,IACvC,8BAA8B;AAAA,IAC9B,8CAA8C;AAAA,IAC9C,yCAAyC;AAAA,IACzC,uCAAuC;AAAA,IACvC,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,0CAA0C;AAAA,IAC1C,gDAAgD;AAAA,IAChD,oDAAoD;AAAA,IACpD,yCAAyC;AAAA,IACzC,+CAA+C;AAAA,IAC/C,qDAAqD;AAAA,IACrD,yBAAyB;AAAA,IACzB,0BAA0B;AAAA,IAC1B,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,2CAA2C;AAAA,IAC3C,0CAA0C;AAAA,IAC1C,4BAA4B;AAAA,IAC5B,+BAA+B;AAAA,IAC/B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,6BAA6B;AAAA,IAC7B,mBAAmB;AAAA,IACnB,mCAAmC;AAAA,IACnC,mCAAmC;AAAA,IACnC,uCAAuC;AAAA,IACvC,uCAAuC;AAAA,IACvC,4CAA4C;AAAA,IAC5C,eAAe;AAAA,IACf,cAAc;AAAA,IACd,yCAAyC;AAAA,IACzC,+CAA+C;AAAA,IAC/C,2CAA2C;AAAA,IAC3C,2CAA2C;AAAA,IAC3C,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,gDAAgD;AAAA,IAChD,iDAAiD;AAAA,IACjD,6CAA6C;AAAA,IAC7C,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,4CAA4C;AAAA,IAC5C,uCAAuC;AAAA,IACvC,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,gCAAgC;AAAA,IAChC,gCAAgC;AAAA,IAChC,mCAAmC;AAAA,IACnC,+BAA+B;AAAA,IAC/B,mCAAmC;AAAA,IACnC,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,kCAAkC;AAAA,IAClC,qCAAqC;AAAA,IACrC,iCAAiC;AAAA,IACjC,qCAAqC;AAAA,IACrC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,+BAA+B;AAAA,IAC/B,uBAAuB;AAAA,IACvB,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,4BAA4B;AAAA,IAC5B,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,oCAAoC;AAAA,IACpC,oCAAoC;AAAA,IACpC,8BAA8B;AAAA,IAC9B,0BAA0B;AAAA,IAC1B,+BAA+B;AAAA,IAC/B,qCAAqC;AAAA,IACrC,4BAA4B;AAAA,IAC5B,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,+BAA+B;AAAA,IAC/B,2BAA2B;AAAA,IAC3B,+BAA+B;AAAA,IAC/B,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,mCAAmC;AAAA,IACnC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,sCAAsC;AAAA,IACtC,qDAAqD;AAAA,IACrD,8CAA8C;AAAA,IAC9C,gDAAgD;AAAA,IAChD,uDAAuD;AAAA,IACvD,gDAAgD;AAAA,IAChD,kDAAkD;AAAA,IAClD,iCAAiC;AAAA,IACjC,mCAAmC;AAAA,IACnC,oCAAoC;AAAA,IACpC,4BAA4B;AAAA,IAC5B,iCAAiC;AAAA,IACjC,6CAA6C;AAAA,IAC7C,6BAA6B;AAAA,IAC7B,8BAA8B;AAAA,IAC9B,uCAAuC;AAAA,IACvC,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,qCAAqC;AAAA,IACrC,oCAAoC;AAAA,IACpC,uCAAuC;AAAA,IACvC,oBAAoB;AAAA,IACpB,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,2BAA2B;AAAA,IAC3B,+BAA+B;AAAA,IAC/B,iCAAiC;AAAA,IACjC,mBAAmB;AAAA,IACnB,6BAA6B;AAAA,IAC7B,2CAA2C;AAAA,IAC3C,6BAA6B;AAAA,IAC7B,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,4BAA4B;AAAA,IAC5B,uCAAuC;AAAA,IACvC,iCAAiC;AAAA,IACjC,sBAAsB;AAAA,IACtB,0BAA0B;AAAA,IAC1B,0BAA0B;AAAA,IAC1B,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,oCAAoC;AAAA,IACpC,8BAA8B;AAAA,IAC9B,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,sBAAsB;AAAA,IACtB,kBAAkB;AAAA,IAClB,0BAA0B;AAAA,IAC1B,sBAAsB;AAAA,IACtB,mCAAmC;AAAA,IACnC,+BAA+B;AAAA,IAC/B,mCAAmC;AAAA,IACnC,2BAA2B;AAAA,IAC3B,wBAAwB;AAAA,IACxB,oBAAoB;AAAA,IACpB,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,wBAAwB;AAAA,IACxB,gCAAgC;AAAA,IAChC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,qCAAqC;AAAA,IACrC,0CAA0C;AAAA,IAC1C,wBAAwB;AAAA,IACxB,oBAAoB;AAAA,IACpB,wBAAwB;AAAA,IACxB,4BAA4B;AAAA,IAC5B,cAAc;AAAA,IACd,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,0BAA0B;AAAA,IAC1B,8BAA8B;AAAA,IAC9B,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,iCAAiC;AAAA,IACjC,gCAAgC;AAAA,IAChC,mCAAmC;AAAA,IACnC,sBAAsB;AAAA,IACtB,qBAAqB;AAAA,IACrB,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,4BAA4B;AAAA,IAC5B,8BAA8B;AAAA,IAC9B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,6BAA6B;AAAA,IAC7B,qBAAqB;AAAA,IACrB,sBAAsB;AAAA,IACtB,wBAAwB;AAAA,IACxB,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,IACvB,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,6BAA6B;AAAA,IAC7B,uBAAuB;AAAA,IACvB,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,6BAA6B;AAAA,IAC7B,6BAA6B;AAAA,IAC7B,mBAAmB;AAAA,IACnB,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,wCAAwC;AAAA,IACxC,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,yBAAyB;AAAA,EAC3B;AAAA,EACA,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,uBAAuB;AAAA,IACrB,oBAAoB;AAAA,MAClB,cAAc;AAAA,IAChB;AAAA,IACA,aAAa;AAAA,MACX,cAAc;AAAA,IAChB;AAAA,IACA,yBAAyB;AAAA,MACvB,cAAc;AAAA,IAChB;AAAA,IACA,wBAAwB;AAAA,MACtB,cAAc;AAAA,IAChB;AAAA,IACA,2BAA2B;AAAA,MACzB,cAAc;AAAA,IAChB;AAAA,IACA,YAAY;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,IACA,wBAAwB;AAAA,MACtB,cAAc;AAAA,IAChB;AAAA,IACA,2BAA2B;AAAA,MACzB,cAAc;AAAA,IAChB;AAAA,EACF;AAAA,EACA,eAAe;AAAA,IACb;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AACV,CAAC;", "names": []}