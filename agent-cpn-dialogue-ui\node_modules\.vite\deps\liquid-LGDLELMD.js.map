{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/liquid.mjs"], "sourcesContent": ["import html from './html.mjs';\nimport css from './css.mjs';\nimport json from './json.mjs';\nimport javascript from './javascript.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"Liquid\", \"fileTypes\": [\"liquid\"], \"foldingStartMarker\": \"(?x)\\n{%\\n  -?\\n  \\\\s*\\n  (capture|case|comment|for|form|if|javascript|paginate|schema|style)\\n  [^(%})]+\\n%}\\n\", \"foldingStopMarker\": \"(?x)\\n{%\\n  \\\\s*\\n  (endcapture|endcase|endcomment|endfor|endform|endif|endjavascript|endpaginate|endschema|endstyle)\\n  [^(%})]+\\n%}\\n\", \"injections\": { \"L:meta.embedded.block.js, L:meta.embedded.block.css, L:meta.embedded.block.html, L:string.quoted\": { \"patterns\": [{ \"include\": \"#injection\" }] } }, \"name\": \"liquid\", \"patterns\": [{ \"include\": \"#core\" }], \"repository\": { \"attribute\": { \"begin\": \"\\\\w+:\", \"beginCaptures\": { \"0\": { \"name\": \"entity.other.attribute-name.liquid\" } }, \"end\": \"(?=,|%}|}}|\\\\|)\", \"patterns\": [{ \"include\": \"#value_expression\" }] }, \"attribute_liquid\": { \"begin\": \"\\\\w+:\", \"beginCaptures\": { \"0\": { \"name\": \"entity.other.attribute-name.liquid\" } }, \"end\": \"(?=,|\\\\|)|$\", \"patterns\": [{ \"include\": \"#value_expression\" }] }, \"comment_block\": { \"begin\": \"{%-?\\\\s*comment\\\\s*-?%}\", \"end\": \"{%-?\\\\s*endcomment\\\\s*-?%}\", \"name\": \"comment.block.liquid\", \"patterns\": [{ \"include\": \"#comment_block\" }, { \"match\": \"(.(?!{%-?\\\\s*(comment|endcomment)\\\\s*-?%}))*.\" }] }, \"core\": { \"patterns\": [{ \"include\": \"#raw_tag\" }, { \"include\": \"#comment_block\" }, { \"include\": \"#style_codefence\" }, { \"include\": \"#stylesheet_codefence\" }, { \"include\": \"#json_codefence\" }, { \"include\": \"#javascript_codefence\" }, { \"include\": \"#object\" }, { \"include\": \"#tag\" }, { \"include\": \"text.html.basic\" }] }, \"filter\": { \"captures\": { \"1\": { \"name\": \"support.function.liquid\" } }, \"match\": \"\\\\|\\\\s*((?![\\\\.0-9])[a-zA-Z0-9_-]+\\\\:?)\\\\s*\" }, \"injection\": { \"patterns\": [{ \"include\": \"#raw_tag\" }, { \"include\": \"#comment_block\" }, { \"include\": \"#object\" }, { \"include\": \"#tag_injection\" }] }, \"invalid_range\": { \"match\": \"\\\\((.(?!\\\\.\\\\.))+\\\\)\", \"name\": \"invalid.illegal.range.liquid\" }, \"javascript_codefence\": { \"begin\": \"({%-?)\\\\s*(javascript)\\\\s*(-?%})\", \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.javascript.start.liquid\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.liquid\" }, \"2\": { \"name\": \"entity.name.tag.javascript.liquid\" }, \"3\": { \"name\": \"punctuation.definition.tag.begin.liquid\" } }, \"contentName\": \"meta.embedded.block.js\", \"end\": \"({%-?)\\\\s*(endjavascript)\\\\s*(-?%})\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.javascript.end.liquid\" }, \"1\": { \"name\": \"punctuation.definition.tag.end.liquid\" }, \"2\": { \"name\": \"entity.name.tag.javascript.liquid\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.liquid\" } }, \"name\": \"meta.block.javascript.liquid\", \"patterns\": [{ \"include\": \"source.js\" }] }, \"json_codefence\": { \"begin\": \"({%-?)\\\\s*(schema)\\\\s*(-?%})\", \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.schema.start.liquid\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.liquid\" }, \"2\": { \"name\": \"entity.name.tag.schema.liquid\" }, \"3\": { \"name\": \"punctuation.definition.tag.begin.liquid\" } }, \"contentName\": \"meta.embedded.block.json\", \"end\": \"({%-?)\\\\s*(endschema)\\\\s*(-?%})\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.schema.end.liquid\" }, \"1\": { \"name\": \"punctuation.definition.tag.end.liquid\" }, \"2\": { \"name\": \"entity.name.tag.schema.liquid\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.liquid\" } }, \"name\": \"meta.block.schema.liquid\", \"patterns\": [{ \"include\": \"source.json\" }] }, \"language_constant\": { \"match\": \"\\\\b(false|true|nil|blank)\\\\b|empty(?!\\\\?)\", \"name\": \"constant.language.liquid\" }, \"number\": { \"match\": \"((-|\\\\+)\\\\s*)?[0-9]+(\\\\.[0-9]+)?\", \"name\": \"constant.numeric.liquid\" }, \"object\": { \"begin\": \"(?<!comment %})(?<!comment -%})(?<!comment%})(?<!comment-%})(?<!raw %})(?<!raw -%})(?<!raw%})(?<!raw-%}){{-?\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.begin.liquid\" } }, \"end\": \"-?}}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.liquid\" } }, \"name\": \"meta.object.liquid\", \"patterns\": [{ \"include\": \"#filter\" }, { \"include\": \"#attribute\" }, { \"include\": \"#value_expression\" }] }, \"operator\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.expression.liquid\" } }, \"match\": \"(?:(?<=\\\\s)|\\\\b)(\\\\=\\\\=|!\\\\=|\\\\>|\\\\<|\\\\>\\\\=|\\\\<\\\\=|or|and|contains)(?:(?=\\\\s)|\\\\b)\" }, \"range\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.parens.begin.liquid\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.parens.end.liquid\" } }, \"name\": \"meta.range.liquid\", \"patterns\": [{ \"match\": \"\\\\.\\\\.\", \"name\": \"punctuation.range.liquid\" }, { \"include\": \"#variable_lookup\" }, { \"include\": \"#number\" }] }, \"raw_tag\": { \"begin\": \"{%-?\\\\s*(raw)\\\\s*-?%}\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.liquid\" } }, \"contentName\": \"string.unquoted.liquid\", \"end\": \"{%-?\\\\s*(endraw)\\\\s*-?%}\", \"endCaptures\": { \"1\": { \"name\": \"entity.name.tag.liquid\" } }, \"name\": \"meta.entity.tag.raw.liquid\", \"patterns\": [{ \"match\": \"(.(?!{%-?\\\\s*endraw\\\\s*-?%}))*.\" }] }, \"string\": { \"patterns\": [{ \"include\": \"#string_single\" }, { \"include\": \"#string_double\" }] }, \"string_double\": { \"begin\": '\"', \"end\": '\"', \"name\": \"string.quoted.double.liquid\" }, \"string_single\": { \"begin\": \"'\", \"end\": \"'\", \"name\": \"string.quoted.single.liquid\" }, \"style_codefence\": { \"begin\": \"({%-?)\\\\s*(style)\\\\s*(-?%})\", \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.style.start.liquid\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.liquid\" }, \"2\": { \"name\": \"entity.name.tag.style.liquid\" }, \"3\": { \"name\": \"punctuation.definition.tag.begin.liquid\" } }, \"contentName\": \"meta.embedded.block.css\", \"end\": \"({%-?)\\\\s*(endstyle)\\\\s*(-?%})\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.style.end.liquid\" }, \"1\": { \"name\": \"punctuation.definition.tag.end.liquid\" }, \"2\": { \"name\": \"entity.name.tag.style.liquid\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.liquid\" } }, \"name\": \"meta.block.style.liquid\", \"patterns\": [{ \"include\": \"source.css\" }] }, \"stylesheet_codefence\": { \"begin\": \"({%-?)\\\\s*(stylesheet)\\\\s*(-?%})\", \"beginCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.style.start.liquid\" }, \"1\": { \"name\": \"punctuation.definition.tag.begin.liquid\" }, \"2\": { \"name\": \"entity.name.tag.style.liquid\" }, \"3\": { \"name\": \"punctuation.definition.tag.begin.liquid\" } }, \"contentName\": \"meta.embedded.block.css\", \"end\": \"({%-?)\\\\s*(endstylesheet)\\\\s*(-?%})\", \"endCaptures\": { \"0\": { \"name\": \"meta.tag.metadata.style.end.liquid\" }, \"1\": { \"name\": \"punctuation.definition.tag.end.liquid\" }, \"2\": { \"name\": \"entity.name.tag.style.liquid\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.liquid\" } }, \"name\": \"meta.block.style.liquid\", \"patterns\": [{ \"include\": \"source.css\" }] }, \"tag\": { \"begin\": \"(?<!comment %})(?<!comment -%})(?<!comment%})(?<!comment-%})(?<!raw %})(?<!raw -%})(?<!raw%})(?<!raw-%}){%-?\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.begin.liquid\" } }, \"end\": \"-?%}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.liquid\" } }, \"name\": \"meta.tag.liquid\", \"patterns\": [{ \"include\": \"#tag_body\" }] }, \"tag_assign\": { \"begin\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(assign|echo)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.liquid\" } }, \"end\": \"(?=%})\", \"name\": \"meta.entity.tag.liquid\", \"patterns\": [{ \"include\": \"#filter\" }, { \"include\": \"#attribute\" }, { \"include\": \"#value_expression\" }] }, \"tag_assign_liquid\": { \"begin\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(assign|echo)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.liquid\" } }, \"end\": \"$\", \"name\": \"meta.entity.tag.liquid\", \"patterns\": [{ \"include\": \"#filter\" }, { \"include\": \"#attribute_liquid\" }, { \"include\": \"#value_expression\" }] }, \"tag_body\": { \"patterns\": [{ \"include\": \"#tag_liquid\" }, { \"include\": \"#tag_assign\" }, { \"include\": \"#tag_comment_inline\" }, { \"include\": \"#tag_case\" }, { \"include\": \"#tag_conditional\" }, { \"include\": \"#tag_for\" }, { \"include\": \"#tag_paginate\" }, { \"include\": \"#tag_render\" }, { \"include\": \"#tag_tablerow\" }, { \"include\": \"#tag_expression\" }] }, \"tag_case\": { \"begin\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(case|when)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.case.liquid\" } }, \"end\": \"(?=%})\", \"name\": \"meta.entity.tag.case.liquid\", \"patterns\": [{ \"include\": \"#value_expression\" }] }, \"tag_case_liquid\": { \"begin\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(case|when)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.case.liquid\" } }, \"end\": \"$\", \"name\": \"meta.entity.tag.case.liquid\", \"patterns\": [{ \"include\": \"#value_expression\" }] }, \"tag_comment_block_liquid\": { \"begin\": \"(?:^\\\\s*)(comment)\\\\b\", \"end\": \"(?:^\\\\s*)(endcomment)\\\\b\", \"name\": \"comment.block.liquid\", \"patterns\": [{ \"include\": \"#tag_comment_block_liquid\" }, { \"match\": \"(?:^\\\\s*)(?!(comment|endcomment)).*\" }] }, \"tag_comment_inline\": { \"begin\": \"#\", \"end\": \"(?=%})\", \"name\": \"comment.line.number-sign.liquid\" }, \"tag_comment_inline_liquid\": { \"begin\": \"(?:^\\\\s*)#.*\", \"end\": \"$\", \"name\": \"comment.line.number-sign.liquid\" }, \"tag_conditional\": { \"begin\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(if|elsif|unless)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.conditional.liquid\" } }, \"end\": \"(?=%})\", \"name\": \"meta.entity.tag.conditional.liquid\", \"patterns\": [{ \"include\": \"#value_expression\" }] }, \"tag_conditional_liquid\": { \"begin\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(if|elsif|unless)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.conditional.liquid\" } }, \"end\": \"$\", \"name\": \"meta.entity.tag.conditional.liquid\", \"patterns\": [{ \"include\": \"#value_expression\" }] }, \"tag_expression\": { \"patterns\": [{ \"include\": \"#tag_expression_without_arguments\" }, { \"begin\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(\\\\w+)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.liquid\" } }, \"end\": \"(?=%})\", \"name\": \"meta.entity.tag.liquid\", \"patterns\": [{ \"include\": \"#value_expression\" }] }] }, \"tag_expression_liquid\": { \"patterns\": [{ \"include\": \"#tag_expression_without_arguments\" }, { \"begin\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(\\\\w+)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.liquid\" } }, \"end\": \"$\", \"name\": \"meta.entity.tag.liquid\", \"patterns\": [{ \"include\": \"#value_expression\" }] }] }, \"tag_expression_without_arguments\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.control.conditional.liquid\" } }, \"match\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(endunless|endif)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.loop.liquid\" } }, \"match\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(endfor|endtablerow|endpaginate)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.case.liquid\" } }, \"match\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(endcase)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.other.liquid\" } }, \"match\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(capture|case|comment|for|form|if|javascript|paginate|schema|style)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.other.liquid\" } }, \"match\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(endcapture|endcase|endcomment|endfor|endform|endif|endjavascript|endpaginate|endschema|endstyle)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.other.liquid\" } }, \"match\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(else|break|continue)\\\\b\" }] }, \"tag_for\": { \"begin\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(for)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.for.liquid\" } }, \"end\": \"(?=%})\", \"name\": \"meta.entity.tag.for.liquid\", \"patterns\": [{ \"include\": \"#tag_for_body\" }] }, \"tag_for_body\": { \"patterns\": [{ \"match\": \"\\\\b(in|reversed)\\\\b\", \"name\": \"keyword.control.liquid\" }, { \"match\": \"\\\\b(offset|limit):\", \"name\": \"keyword.control.liquid\" }, { \"include\": \"#value_expression\" }] }, \"tag_for_liquid\": { \"begin\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(for)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.for.liquid\" } }, \"end\": \"$\", \"name\": \"meta.entity.tag.for.liquid\", \"patterns\": [{ \"include\": \"#tag_for_body\" }] }, \"tag_injection\": { \"begin\": \"(?<!comment %})(?<!comment -%})(?<!comment%})(?<!comment-%})(?<!raw %})(?<!raw -%})(?<!raw%})(?<!raw-%}){%-?(?!-?\\\\s*(endstyle|endjavascript|endcomment|endraw))\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.liquid\" } }, \"end\": \"-?%}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.liquid\" } }, \"name\": \"meta.tag.liquid\", \"patterns\": [{ \"include\": \"#tag_body\" }] }, \"tag_liquid\": { \"begin\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(liquid)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.liquid.liquid\" } }, \"end\": \"(?=%})\", \"name\": \"meta.entity.tag.liquid.liquid\", \"patterns\": [{ \"include\": \"#tag_comment_block_liquid\" }, { \"include\": \"#tag_comment_inline_liquid\" }, { \"include\": \"#tag_assign_liquid\" }, { \"include\": \"#tag_case_liquid\" }, { \"include\": \"#tag_conditional_liquid\" }, { \"include\": \"#tag_for_liquid\" }, { \"include\": \"#tag_paginate_liquid\" }, { \"include\": \"#tag_render_liquid\" }, { \"include\": \"#tag_tablerow_liquid\" }, { \"include\": \"#tag_expression_liquid\" }] }, \"tag_paginate\": { \"begin\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(paginate)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.paginate.liquid\" } }, \"end\": \"(?=%})\", \"name\": \"meta.entity.tag.paginate.liquid\", \"patterns\": [{ \"include\": \"#tag_paginate_body\" }] }, \"tag_paginate_body\": { \"patterns\": [{ \"match\": \"\\\\b(by)\\\\b\", \"name\": \"keyword.control.liquid\" }, { \"include\": \"#value_expression\" }] }, \"tag_paginate_liquid\": { \"begin\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(paginate)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.paginate.liquid\" } }, \"end\": \"$\", \"name\": \"meta.entity.tag.paginate.liquid\", \"patterns\": [{ \"include\": \"#tag_paginate_body\" }] }, \"tag_render\": { \"begin\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(render)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.render.liquid\" } }, \"end\": \"(?=%})\", \"name\": \"meta.entity.tag.render.liquid\", \"patterns\": [{ \"include\": \"#tag_render_special_keywords\" }, { \"include\": \"#attribute\" }, { \"include\": \"#value_expression\" }] }, \"tag_render_liquid\": { \"begin\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(render)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.render.liquid\" } }, \"end\": \"$\", \"name\": \"meta.entity.tag.render.liquid\", \"patterns\": [{ \"include\": \"#tag_render_special_keywords\" }, { \"include\": \"#attribute_liquid\" }, { \"include\": \"#value_expression\" }] }, \"tag_render_special_keywords\": { \"match\": \"\\\\b(with|as|for)\\\\b\", \"name\": \"keyword.control.other.liquid\" }, \"tag_tablerow\": { \"begin\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(tablerow)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.tablerow.liquid\" } }, \"end\": \"(?=%})\", \"name\": \"meta.entity.tag.tablerow.liquid\", \"patterns\": [{ \"include\": \"#tag_tablerow_body\" }] }, \"tag_tablerow_body\": { \"patterns\": [{ \"match\": \"\\\\b(in)\\\\b\", \"name\": \"keyword.control.liquid\" }, { \"match\": \"\\\\b(cols|offset|limit):\", \"name\": \"keyword.control.liquid\" }, { \"include\": \"#value_expression\" }] }, \"tag_tablerow_liquid\": { \"begin\": \"(?:(?:(?<={%)|(?<={%-)|^)\\\\s*)(tablerow)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.tablerow.liquid\" } }, \"end\": \"$\", \"name\": \"meta.entity.tag.tablerow.liquid\", \"patterns\": [{ \"include\": \"#tag_tablerow_body\" }] }, \"value_expression\": { \"patterns\": [{ \"captures\": { \"2\": { \"name\": \"invalid.illegal.filter.liquid\" }, \"3\": { \"name\": \"invalid.illegal.filter.liquid\" } }, \"match\": \"(\\\\[)(\\\\|)(?=[^\\\\]]*)(?=\\\\])\" }, { \"match\": \"(?<=\\\\s)(\\\\+|\\\\-|\\\\/|\\\\*)(?=\\\\s)\", \"name\": \"invalid.illegal.filter.liquid\" }, { \"include\": \"#language_constant\" }, { \"include\": \"#operator\" }, { \"include\": \"#invalid_range\" }, { \"include\": \"#range\" }, { \"include\": \"#number\" }, { \"include\": \"#string\" }, { \"include\": \"#variable_lookup\" }] }, \"variable_lookup\": { \"patterns\": [{ \"match\": \"\\\\b(additional_checkout_buttons|address|all_country_option_tags|all_products|article|articles|block|blog|blogs|canonical_url|cart|checkout|collection|collections|comment|content_for_additional_checkout_buttons|content_for_header|content_for_index|content_for_layout|country_option_tags|currency|current_page|current_tags|customer|customer_address|discount_allocation|discount_application|external_video|font|forloop|form|fulfillment|gift_card|handle|image|images|line_item|link|linklist|linklists|location|localization|metafield|model|model_source|order|page|page_description|page_image|page_title|pages|paginate|part|policy|powered_by_link|predictive_search|product|product_option|product_variant|recommendations|request|routes|script|scripts|search|section|selling_plan|selling_plan_allocation|selling_plan_group|settings|shipping_method|shop|shop_locale|store_availability|tablerow|tax_line|template|theme|transaction|unit_price_measurement|variant|video|video_source)\\\\b\", \"name\": \"variable.language.liquid\" }, { \"match\": \"((?<=\\\\w\\\\:\\\\s)\\\\w+)\", \"name\": \"variable.parameter.liquid\" }, { \"begin\": \"(?<=\\\\w)\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.brackets.begin.liquid\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.brackets.end.liquid\" } }, \"name\": \"meta.brackets.liquid\", \"patterns\": [{ \"include\": \"#string\" }] }, { \"match\": \"(?<=(\\\\w|\\\\])\\\\.)([-\\\\w]+\\\\??)\", \"name\": \"variable.other.member.liquid\" }, { \"match\": \"(?<=\\\\w)\\\\.(?=\\\\w)\", \"name\": \"punctuation.accessor.liquid\" }, { \"match\": \"(?i)[a-z_](\\\\w|(?:-(?!\\\\}\\\\})))*\", \"name\": \"variable.other.liquid\" }] } }, \"scopeName\": \"text.html.liquid\", \"embeddedLangs\": [\"html\", \"css\", \"json\", \"javascript\"] });\nvar liquid = [\n  ...html,\n  ...css,\n  ...json,\n  ...javascript,\n  lang\n];\n\nexport { liquid as default };\n"], "mappings": ";;;;;;;;;;;;;;;AAKA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,UAAU,aAAa,CAAC,QAAQ,GAAG,sBAAsB,mHAAmH,qBAAqB,2IAA2I,cAAc,EAAE,oGAAoG,EAAE,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,EAAE,GAAG,QAAQ,UAAU,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,GAAG,cAAc,EAAE,aAAa,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,mBAAmB,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,oBAAoB,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,eAAe,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,2BAA2B,OAAO,8BAA8B,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,iBAAiB,GAAG,EAAE,SAAS,gDAAgD,CAAC,EAAE,GAAG,QAAQ,EAAE,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,OAAO,GAAG,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,SAAS,8CAA8C,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,wBAAwB,QAAQ,+BAA+B,GAAG,wBAAwB,EAAE,SAAS,oCAAoC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,eAAe,0BAA0B,OAAO,uCAAuC,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,kBAAkB,EAAE,SAAS,gCAAgC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,eAAe,4BAA4B,OAAO,mCAAmC,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,qBAAqB,EAAE,SAAS,6CAA6C,QAAQ,2BAA2B,GAAG,UAAU,EAAE,SAAS,oCAAoC,QAAQ,0BAA0B,GAAG,UAAU,EAAE,SAAS,gHAAgH,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,QAAQ,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,sBAAsB,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,SAAS,qFAAqF,GAAG,SAAS,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,SAAS,UAAU,QAAQ,2BAA2B,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,WAAW,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,eAAe,0BAA0B,OAAO,4BAA4B,eAAe,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,SAAS,kCAAkC,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,KAAK,OAAO,KAAK,QAAQ,8BAA8B,GAAG,iBAAiB,EAAE,SAAS,KAAK,OAAO,KAAK,QAAQ,8BAA8B,GAAG,mBAAmB,EAAE,SAAS,+BAA+B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,eAAe,2BAA2B,OAAO,kCAAkC,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,wBAAwB,EAAE,SAAS,oCAAoC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,eAAe,2BAA2B,OAAO,uCAAuC,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,SAAS,gHAAgH,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,QAAQ,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,mBAAmB,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,kDAAkD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,UAAU,QAAQ,0BAA0B,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,qBAAqB,EAAE,SAAS,kDAAkD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,KAAK,QAAQ,0BAA0B,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,YAAY,EAAE,SAAS,gDAAgD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,UAAU,QAAQ,+BAA+B,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,gDAAgD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,KAAK,QAAQ,+BAA+B,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,4BAA4B,EAAE,SAAS,yBAAyB,OAAO,4BAA4B,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,4BAA4B,GAAG,EAAE,SAAS,sCAAsC,CAAC,EAAE,GAAG,sBAAsB,EAAE,SAAS,KAAK,OAAO,UAAU,QAAQ,kCAAkC,GAAG,6BAA6B,EAAE,SAAS,gBAAgB,OAAO,KAAK,QAAQ,kCAAkC,GAAG,mBAAmB,EAAE,SAAS,sDAAsD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,UAAU,QAAQ,sCAAsC,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,0BAA0B,EAAE,SAAS,sDAAsD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,KAAK,QAAQ,sCAAsC,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,CAAC,EAAE,WAAW,oCAAoC,GAAG,EAAE,SAAS,wCAAwC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,UAAU,QAAQ,0BAA0B,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,CAAC,EAAE,GAAG,yBAAyB,EAAE,YAAY,CAAC,EAAE,WAAW,oCAAoC,GAAG,EAAE,SAAS,wCAAwC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,KAAK,QAAQ,0BAA0B,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,CAAC,EAAE,GAAG,oCAAoC,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,SAAS,qDAAqD,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,SAAS,oEAAoE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,SAAS,6CAA6C,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,SAAS,uGAAuG,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,SAAS,qIAAqI,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,SAAS,yDAAyD,CAAC,EAAE,GAAG,WAAW,EAAE,SAAS,0CAA0C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,UAAU,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,CAAC,EAAE,SAAS,uBAAuB,QAAQ,yBAAyB,GAAG,EAAE,SAAS,sBAAsB,QAAQ,yBAAyB,GAAG,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,kBAAkB,EAAE,SAAS,0CAA0C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,KAAK,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,oKAAoK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,OAAO,QAAQ,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,mBAAmB,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,6CAA6C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,UAAU,QAAQ,iCAAiC,YAAY,CAAC,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,6BAA6B,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,+CAA+C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,UAAU,QAAQ,mCAAmC,YAAY,CAAC,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,qBAAqB,EAAE,YAAY,CAAC,EAAE,SAAS,cAAc,QAAQ,yBAAyB,GAAG,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,+CAA+C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,KAAK,QAAQ,mCAAmC,YAAY,CAAC,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,6CAA6C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,UAAU,QAAQ,iCAAiC,YAAY,CAAC,EAAE,WAAW,+BAA+B,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,qBAAqB,EAAE,SAAS,6CAA6C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,KAAK,QAAQ,iCAAiC,YAAY,CAAC,EAAE,WAAW,+BAA+B,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,+BAA+B,EAAE,SAAS,uBAAuB,QAAQ,+BAA+B,GAAG,gBAAgB,EAAE,SAAS,+CAA+C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,UAAU,QAAQ,mCAAmC,YAAY,CAAC,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,qBAAqB,EAAE,YAAY,CAAC,EAAE,SAAS,cAAc,QAAQ,yBAAyB,GAAG,EAAE,SAAS,2BAA2B,QAAQ,yBAAyB,GAAG,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,+CAA+C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,KAAK,QAAQ,mCAAmC,YAAY,CAAC,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,oBAAoB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,SAAS,+BAA+B,GAAG,EAAE,SAAS,oCAAoC,QAAQ,gCAAgC,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,SAAS,k9BAAk9B,QAAQ,2BAA2B,GAAG,EAAE,SAAS,wBAAwB,QAAQ,4BAA4B,GAAG,EAAE,SAAS,eAAe,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,kCAAkC,QAAQ,+BAA+B,GAAG,EAAE,SAAS,sBAAsB,QAAQ,8BAA8B,GAAG,EAAE,SAAS,oCAAoC,QAAQ,wBAAwB,CAAC,EAAE,EAAE,GAAG,aAAa,oBAAoB,iBAAiB,CAAC,QAAQ,OAAO,QAAQ,YAAY,EAAE,CAAC;AAC7yhB,IAAI,SAAS;AAAA,EACX,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACF;", "names": []}