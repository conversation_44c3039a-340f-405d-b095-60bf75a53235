{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/http.mjs"], "sourcesContent": ["import shellscript from './shellscript.mjs';\nimport json from './json.mjs';\nimport xml from './xml.mjs';\nimport graphql from './graphql.mjs';\nimport './java.mjs';\nimport './javascript.mjs';\nimport './typescript.mjs';\nimport './jsx.mjs';\nimport './tsx.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"HTTP\", \"fileTypes\": [\"http\", \"rest\"], \"name\": \"http\", \"patterns\": [{ \"begin\": \"^\\\\s*(?=curl)\", \"end\": \"^\\\\s*(\\\\#{3,}.*?)?\\\\s*$\", \"endCaptures\": { \"0\": { \"name\": \"comment.line.sharp.http\" } }, \"name\": \"http.request.curl\", \"patterns\": [{ \"include\": \"source.shell\" }] }, { \"begin\": \"\\\\s*(?=(\\\\[|{[^{]))\", \"end\": \"^\\\\s*(\\\\#{3,}.*?)?\\\\s*$\", \"endCaptures\": { \"0\": { \"name\": \"comment.line.sharp.http\" } }, \"name\": \"http.request.body.json\", \"patterns\": [{ \"include\": \"source.json\" }] }, { \"begin\": \"^\\\\s*(?=<\\\\S)\", \"end\": \"^\\\\s*(\\\\#{3,}.*?)?\\\\s*$\", \"endCaptures\": { \"0\": { \"name\": \"comment.line.sharp.http\" } }, \"name\": \"http.request.body.xml\", \"patterns\": [{ \"include\": \"text.xml\" }] }, { \"begin\": \"\\\\s*(?=(query|mutation))\", \"end\": \"^\\\\s*(\\\\#{3,}.*?)?\\\\s*$\", \"endCaptures\": { \"0\": { \"name\": \"comment.line.sharp.http\" } }, \"name\": \"http.request.body.graphql\", \"patterns\": [{ \"include\": \"source.graphql\" }] }, { \"begin\": \"\\\\s*(?=(query|mutation))\", \"end\": \"^\\\\{\\\\s*$\", \"name\": \"http.request.body.graphql\", \"patterns\": [{ \"include\": \"source.graphql\" }] }, { \"include\": \"#metadata\" }, { \"include\": \"#comments\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.http\" }, \"2\": { \"name\": \"variable.other.http\" }, \"3\": { \"name\": \"string.other.http\" } }, \"match\": \"^\\\\s*(@)([^\\\\s=]+)\\\\s*=\\\\s*(.*?)\\\\s*$\", \"name\": \"http.filevariable\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.http\" }, \"2\": { \"name\": \"variable.other.http\" }, \"3\": { \"name\": \"string.other.http\" } }, \"match\": \"^\\\\s*(\\\\?|&)([^=\\\\s]+)=(.*)$\", \"name\": \"http.query\" }, { \"captures\": { \"1\": { \"name\": \"entity.name.tag.http\" }, \"2\": { \"name\": \"keyword.other.http\" }, \"3\": { \"name\": \"string.other.http\" } }, \"match\": \"^([\\\\w\\\\-]+)\\\\s*(\\\\:)\\\\s*([^/].*?)\\\\s*$\", \"name\": \"http.headers\" }, { \"include\": \"#request-line\" }, { \"include\": \"#response-line\" }], \"repository\": { \"comments\": { \"patterns\": [{ \"match\": \"^\\\\s*\\\\#{1,}.*$\", \"name\": \"comment.line.sharp.http\" }, { \"match\": \"^\\\\s*\\\\/{2,}.*$\", \"name\": \"comment.line.double-slash.http\" }] }, \"metadata\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.metadata\" }, \"3\": { \"name\": \"entity.name.type.http\" } }, \"match\": \"^\\\\s*\\\\#{1,}\\\\s+(?:((@)name)\\\\s+([^\\\\s\\\\.]+))$\", \"name\": \"comment.line.sharp.http\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.metadata\" }, \"3\": { \"name\": \"entity.name.type.http\" } }, \"match\": \"^\\\\s*\\\\/{2,}\\\\s+(?:((@)name)\\\\s+([^\\\\s\\\\.]+))$\", \"name\": \"comment.line.double-slash.http\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.metadata\" } }, \"match\": \"^\\\\s*\\\\#{1,}\\\\s+((@)note)\\\\s*$\", \"name\": \"comment.line.sharp.http\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.metadata\" } }, \"match\": \"^\\\\s*\\\\/{2,}\\\\s+((@)note)\\\\s*$\", \"name\": \"comment.line.double-slash.http\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.metadata\" }, \"3\": { \"name\": \"variable.other.http\" }, \"4\": { \"name\": \"string.other.http\" } }, \"match\": \"^\\\\s*\\\\#{1,}\\\\s+(?:((@)prompt)\\\\s+([^\\\\s]+)(?:\\\\s+(.*))?\\\\s*)$\", \"name\": \"comment.line.sharp.http\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.metadata\" }, \"3\": { \"name\": \"variable.other.http\" }, \"4\": { \"name\": \"string.other.http\" } }, \"match\": \"^\\\\s*\\\\/{2,}\\\\s+(?:((@)prompt)\\\\s+([^\\\\s]+)(?:\\\\s+(.*))?\\\\s*)$\", \"name\": \"comment.line.double-slash.http\" }] }, \"protocol\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.other.http\" }, \"2\": { \"name\": \"constant.numeric.http\" } }, \"match\": \"(HTTP)/(\\\\d+.\\\\d+)\", \"name\": \"http.version\" }] }, \"request-line\": { \"captures\": { \"1\": { \"name\": \"keyword.control.http\" }, \"2\": { \"name\": \"const.language.http\" }, \"3\": { \"patterns\": [{ \"include\": \"#protocol\" }] } }, \"match\": \"(?i)^(?:(get|post|put|delete|patch|head|options|connect|trace|lock|unlock|propfind|proppatch|copy|move|mkcol|mkcalendar|acl|search)\\\\s+)?\\\\s*(.+?)(?:\\\\s+(HTTP\\\\/\\\\S+))?$\", \"name\": \"http.requestline\" }, \"response-line\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#protocol\" }] }, \"2\": { \"name\": \"constant.numeric.http\" }, \"3\": { \"name\": \"string.other.http\" } }, \"match\": \"(?i)^\\\\s*(HTTP\\\\/\\\\S+)\\\\s([1-5][0-9][0-9])\\\\s(.*)$\", \"name\": \"http.responseLine\" } }, \"scopeName\": \"source.http\", \"embeddedLangs\": [\"shellscript\", \"json\", \"xml\", \"graphql\"] });\nvar http = [\n  ...shellscript,\n  ...json,\n  ...xml,\n  ...graphql,\n  lang\n];\n\nexport { http as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAUA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,QAAQ,aAAa,CAAC,QAAQ,MAAM,GAAG,QAAQ,QAAQ,YAAY,CAAC,EAAE,SAAS,iBAAiB,OAAO,2BAA2B,eAAe,EAAE,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,EAAE,SAAS,uBAAuB,OAAO,2BAA2B,eAAe,EAAE,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,QAAQ,0BAA0B,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,iBAAiB,OAAO,2BAA2B,eAAe,EAAE,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,QAAQ,yBAAyB,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,EAAE,SAAS,4BAA4B,OAAO,2BAA2B,eAAe,EAAE,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,SAAS,4BAA4B,OAAO,aAAa,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qBAAqB,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,SAAS,yCAAyC,QAAQ,oBAAoB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,SAAS,gCAAgC,QAAQ,aAAa,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,qBAAqB,GAAG,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,SAAS,2CAA2C,QAAQ,eAAe,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,iBAAiB,CAAC,GAAG,cAAc,EAAE,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,mBAAmB,QAAQ,0BAA0B,GAAG,EAAE,SAAS,mBAAmB,QAAQ,iCAAiC,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,SAAS,kDAAkD,QAAQ,0BAA0B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,SAAS,kDAAkD,QAAQ,iCAAiC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,SAAS,kCAAkC,QAAQ,0BAA0B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,SAAS,kCAAkC,QAAQ,iCAAiC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,SAAS,kEAAkE,QAAQ,0BAA0B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,SAAS,kEAAkE,QAAQ,iCAAiC,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qBAAqB,GAAG,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,SAAS,sBAAsB,QAAQ,eAAe,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,EAAE,GAAG,SAAS,6KAA6K,QAAQ,mBAAmB,GAAG,iBAAiB,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,SAAS,sDAAsD,QAAQ,oBAAoB,EAAE,GAAG,aAAa,eAAe,iBAAiB,CAAC,eAAe,QAAQ,OAAO,SAAS,EAAE,CAAC;AACv+I,IAAI,OAAO;AAAA,EACT,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACF;", "names": []}