{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/jssm.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"JSSM\", \"fileTypes\": [\"jssm\", \"jssm_state\"], \"name\": \"jssm\", \"patterns\": [{ \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.mn\" } }, \"comment\": \"block comment\", \"end\": \"\\\\*/\", \"name\": \"comment.block.jssm\" }, { \"begin\": \"//\", \"comment\": \"block comment\", \"end\": \"$\", \"name\": \"comment.line.jssm\" }, { \"begin\": \"\\\\${\", \"captures\": { \"0\": { \"name\": \"entity.name.function\" } }, \"comment\": \"js outcalls\", \"end\": \"}\", \"name\": \"keyword.other\" }, { \"comment\": \"semver\", \"match\": \"([0-9]*)(\\\\.)([0-9]*)(\\\\.)([0-9]*)\", \"name\": \"constant.numeric\" }, { \"comment\": \"jssm language tokens\", \"match\": \"graph_layout(\\\\s*)(:)\", \"name\": \"constant.language.jssmLanguage\" }, { \"comment\": \"jssm language tokens\", \"match\": \"machine_name(\\\\s*)(:)\", \"name\": \"constant.language.jssmLanguage\" }, { \"comment\": \"jssm language tokens\", \"match\": \"machine_version(\\\\s*)(:)\", \"name\": \"constant.language.jssmLanguage\" }, { \"comment\": \"jssm language tokens\", \"match\": \"jssm_version(\\\\s*)(:)\", \"name\": \"constant.language.jssmLanguage\" }, { \"comment\": \"transitions\", \"match\": \"<->\", \"name\": \"keyword.control.transition.jssmArrow.legal_legal\" }, { \"comment\": \"transitions\", \"match\": \"<-\", \"name\": \"keyword.control.transition.jssmArrow.legal_none\" }, { \"comment\": \"transitions\", \"match\": \"->\", \"name\": \"keyword.control.transition.jssmArrow.none_legal\" }, { \"comment\": \"transitions\", \"match\": \"<=>\", \"name\": \"keyword.control.transition.jssmArrow.main_main\" }, { \"comment\": \"transitions\", \"match\": \"=>\", \"name\": \"keyword.control.transition.jssmArrow.none_main\" }, { \"comment\": \"transitions\", \"match\": \"<=\", \"name\": \"keyword.control.transition.jssmArrow.main_none\" }, { \"comment\": \"transitions\", \"match\": \"<~>\", \"name\": \"keyword.control.transition.jssmArrow.forced_forced\" }, { \"comment\": \"transitions\", \"match\": \"~>\", \"name\": \"keyword.control.transition.jssmArrow.none_forced\" }, { \"comment\": \"transitions\", \"match\": \"<~\", \"name\": \"keyword.control.transition.jssmArrow.forced_none\" }, { \"comment\": \"transitions\", \"match\": \"<-=>\", \"name\": \"keyword.control.transition.jssmArrow.legal_main\" }, { \"comment\": \"transitions\", \"match\": \"<=->\", \"name\": \"keyword.control.transition.jssmArrow.main_legal\" }, { \"comment\": \"transitions\", \"match\": \"<-~>\", \"name\": \"keyword.control.transition.jssmArrow.legal_forced\" }, { \"comment\": \"transitions\", \"match\": \"<~->\", \"name\": \"keyword.control.transition.jssmArrow.forced_legal\" }, { \"comment\": \"transitions\", \"match\": \"<=~>\", \"name\": \"keyword.control.transition.jssmArrow.main_forced\" }, { \"comment\": \"transitions\", \"match\": \"<~=>\", \"name\": \"keyword.control.transition.jssmArrow.forced_main\" }, { \"comment\": \"edge probability annotation\", \"match\": \"([0-9]+)%\", \"name\": \"constant.numeric.jssmProbability\" }, { \"comment\": \"action annotation\", \"match\": \"\\\\'[^']*\\\\'\", \"name\": \"constant.character.jssmAction\" }, { \"comment\": \"jssm label annotation\", \"match\": '\\\\\"[^\"]*\\\\\"', \"name\": \"entity.name.tag.jssmLabel.doublequoted\" }, { \"comment\": \"jssm label annotation\", \"match\": \"([a-zA-Z0-9_.+&()#@!?,])\", \"name\": \"entity.name.tag.jssmLabel.atom\" }], \"scopeName\": \"source.jssm\", \"aliases\": [\"fsl\"] });\nvar jssm = [\n  lang\n];\n\nexport { jssm as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,QAAQ,aAAa,CAAC,QAAQ,YAAY,GAAG,QAAQ,QAAQ,YAAY,CAAC,EAAE,SAAS,QAAQ,YAAY,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,WAAW,iBAAiB,OAAO,QAAQ,QAAQ,qBAAqB,GAAG,EAAE,SAAS,MAAM,WAAW,iBAAiB,OAAO,KAAK,QAAQ,oBAAoB,GAAG,EAAE,SAAS,QAAQ,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,WAAW,eAAe,OAAO,KAAK,QAAQ,gBAAgB,GAAG,EAAE,WAAW,UAAU,SAAS,sCAAsC,QAAQ,mBAAmB,GAAG,EAAE,WAAW,wBAAwB,SAAS,yBAAyB,QAAQ,iCAAiC,GAAG,EAAE,WAAW,wBAAwB,SAAS,yBAAyB,QAAQ,iCAAiC,GAAG,EAAE,WAAW,wBAAwB,SAAS,4BAA4B,QAAQ,iCAAiC,GAAG,EAAE,WAAW,wBAAwB,SAAS,yBAAyB,QAAQ,iCAAiC,GAAG,EAAE,WAAW,eAAe,SAAS,OAAO,QAAQ,mDAAmD,GAAG,EAAE,WAAW,eAAe,SAAS,MAAM,QAAQ,kDAAkD,GAAG,EAAE,WAAW,eAAe,SAAS,MAAM,QAAQ,kDAAkD,GAAG,EAAE,WAAW,eAAe,SAAS,OAAO,QAAQ,iDAAiD,GAAG,EAAE,WAAW,eAAe,SAAS,MAAM,QAAQ,iDAAiD,GAAG,EAAE,WAAW,eAAe,SAAS,MAAM,QAAQ,iDAAiD,GAAG,EAAE,WAAW,eAAe,SAAS,OAAO,QAAQ,qDAAqD,GAAG,EAAE,WAAW,eAAe,SAAS,MAAM,QAAQ,mDAAmD,GAAG,EAAE,WAAW,eAAe,SAAS,MAAM,QAAQ,mDAAmD,GAAG,EAAE,WAAW,eAAe,SAAS,QAAQ,QAAQ,kDAAkD,GAAG,EAAE,WAAW,eAAe,SAAS,QAAQ,QAAQ,kDAAkD,GAAG,EAAE,WAAW,eAAe,SAAS,QAAQ,QAAQ,oDAAoD,GAAG,EAAE,WAAW,eAAe,SAAS,QAAQ,QAAQ,oDAAoD,GAAG,EAAE,WAAW,eAAe,SAAS,QAAQ,QAAQ,mDAAmD,GAAG,EAAE,WAAW,eAAe,SAAS,QAAQ,QAAQ,mDAAmD,GAAG,EAAE,WAAW,+BAA+B,SAAS,aAAa,QAAQ,mCAAmC,GAAG,EAAE,WAAW,qBAAqB,SAAS,eAAe,QAAQ,gCAAgC,GAAG,EAAE,WAAW,yBAAyB,SAAS,eAAe,QAAQ,yCAAyC,GAAG,EAAE,WAAW,yBAAyB,SAAS,4BAA4B,QAAQ,iCAAiC,CAAC,GAAG,aAAa,eAAe,WAAW,CAAC,KAAK,EAAE,CAAC;AACjlG,IAAI,OAAO;AAAA,EACT;AACF;", "names": []}