{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/ini.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"INI\", \"name\": \"ini\", \"patterns\": [{ \"begin\": \"(^[ \\\\t]+)?(?=#)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.ini\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.ini\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.number-sign.ini\" }] }, { \"begin\": \"(^[ \\\\t]+)?(?=;)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.ini\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \";\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.ini\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.semicolon.ini\" }] }, { \"captures\": { \"1\": { \"name\": \"keyword.other.definition.ini\" }, \"2\": { \"name\": \"punctuation.separator.key-value.ini\" } }, \"match\": \"\\\\b([a-zA-Z0-9_.-]+)\\\\b\\\\s*(=)\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.ini\" }, \"3\": { \"name\": \"punctuation.definition.entity.ini\" } }, \"match\": \"^(\\\\[)(.*?)(\\\\])\", \"name\": \"entity.name.section.group-title.ini\" }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ini\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ini\" } }, \"name\": \"string.quoted.single.ini\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.ini\" }] }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.ini\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.ini\" } }, \"name\": \"string.quoted.double.ini\" }], \"scopeName\": \"source.ini\", \"aliases\": [\"properties\"] });\nvar ini = [\n  lang\n];\n\nexport { ini as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,OAAO,QAAQ,OAAO,YAAY,CAAC,EAAE,SAAS,oBAAoB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,OAAO,QAAQ,+BAA+B,CAAC,EAAE,GAAG,EAAE,SAAS,oBAAoB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,OAAO,QAAQ,6BAA6B,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,SAAS,iCAAiC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,SAAS,oBAAoB,QAAQ,sCAAsC,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,SAAS,SAAS,QAAQ,gCAAgC,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,2BAA2B,CAAC,GAAG,aAAa,cAAc,WAAW,CAAC,YAAY,EAAE,CAAC;AACvlD,IAAI,MAAM;AAAA,EACR;AACF;", "names": []}