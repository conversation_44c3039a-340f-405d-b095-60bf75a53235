{"version": 3, "sources": ["../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-MI5ZYTHO.mjs"], "sourcesContent": ["import {\n  StateDB,\n  stateDiagram_default,\n  styles_default\n} from \"./chunk-OW32GOEJ.mjs\";\nimport \"./chunk-BFAMUDN2.mjs\";\nimport \"./chunk-SKB7J2MH.mjs\";\nimport \"./chunk-IWUHOULB.mjs\";\nimport \"./chunk-M6DAPIYF.mjs\";\nimport \"./chunk-MXNHSMXR.mjs\";\nimport \"./chunk-JW4RIYDF.mjs\";\nimport \"./chunk-AC5SNWB5.mjs\";\nimport \"./chunk-UWXLY5YG.mjs\";\nimport \"./chunk-QESNASVV.mjs\";\nimport {\n  utils_default\n} from \"./chunk-55PJQP7W.mjs\";\nimport {\n  __name,\n  common_default,\n  configureSvgSize,\n  getConfig2 as getConfig,\n  getUrl,\n  log\n} from \"./chunk-3XYRH5AP.mjs\";\n\n// src/diagrams/state/stateRenderer.js\nimport { select } from \"d3\";\nimport { layout as dagreLayout } from \"dagre-d3-es/src/dagre/index.js\";\nimport * as graphlib from \"dagre-d3-es/src/graphlib/index.js\";\n\n// src/diagrams/state/shapes.js\nimport { line, curveBasis } from \"d3\";\nvar drawStartState = /* @__PURE__ */ __name((g) => g.append(\"circle\").attr(\"class\", \"start-state\").attr(\"r\", getConfig().state.sizeUnit).attr(\"cx\", getConfig().state.padding + getConfig().state.sizeUnit).attr(\"cy\", getConfig().state.padding + getConfig().state.sizeUnit), \"drawStartState\");\nvar drawDivider = /* @__PURE__ */ __name((g) => g.append(\"line\").style(\"stroke\", \"grey\").style(\"stroke-dasharray\", \"3\").attr(\"x1\", getConfig().state.textHeight).attr(\"class\", \"divider\").attr(\"x2\", getConfig().state.textHeight * 2).attr(\"y1\", 0).attr(\"y2\", 0), \"drawDivider\");\nvar drawSimpleState = /* @__PURE__ */ __name((g, stateDef) => {\n  const state = g.append(\"text\").attr(\"x\", 2 * getConfig().state.padding).attr(\"y\", getConfig().state.textHeight + 2 * getConfig().state.padding).attr(\"font-size\", getConfig().state.fontSize).attr(\"class\", \"state-title\").text(stateDef.id);\n  const classBox = state.node().getBBox();\n  g.insert(\"rect\", \":first-child\").attr(\"x\", getConfig().state.padding).attr(\"y\", getConfig().state.padding).attr(\"width\", classBox.width + 2 * getConfig().state.padding).attr(\"height\", classBox.height + 2 * getConfig().state.padding).attr(\"rx\", getConfig().state.radius);\n  return state;\n}, \"drawSimpleState\");\nvar drawDescrState = /* @__PURE__ */ __name((g, stateDef) => {\n  const addTspan = /* @__PURE__ */ __name(function(textEl, txt, isFirst2) {\n    const tSpan = textEl.append(\"tspan\").attr(\"x\", 2 * getConfig().state.padding).text(txt);\n    if (!isFirst2) {\n      tSpan.attr(\"dy\", getConfig().state.textHeight);\n    }\n  }, \"addTspan\");\n  const title = g.append(\"text\").attr(\"x\", 2 * getConfig().state.padding).attr(\"y\", getConfig().state.textHeight + 1.3 * getConfig().state.padding).attr(\"font-size\", getConfig().state.fontSize).attr(\"class\", \"state-title\").text(stateDef.descriptions[0]);\n  const titleBox = title.node().getBBox();\n  const titleHeight = titleBox.height;\n  const description = g.append(\"text\").attr(\"x\", getConfig().state.padding).attr(\n    \"y\",\n    titleHeight + getConfig().state.padding * 0.4 + getConfig().state.dividerMargin + getConfig().state.textHeight\n  ).attr(\"class\", \"state-description\");\n  let isFirst = true;\n  let isSecond = true;\n  stateDef.descriptions.forEach(function(descr) {\n    if (!isFirst) {\n      addTspan(description, descr, isSecond);\n      isSecond = false;\n    }\n    isFirst = false;\n  });\n  const descrLine = g.append(\"line\").attr(\"x1\", getConfig().state.padding).attr(\"y1\", getConfig().state.padding + titleHeight + getConfig().state.dividerMargin / 2).attr(\"y2\", getConfig().state.padding + titleHeight + getConfig().state.dividerMargin / 2).attr(\"class\", \"descr-divider\");\n  const descrBox = description.node().getBBox();\n  const width = Math.max(descrBox.width, titleBox.width);\n  descrLine.attr(\"x2\", width + 3 * getConfig().state.padding);\n  g.insert(\"rect\", \":first-child\").attr(\"x\", getConfig().state.padding).attr(\"y\", getConfig().state.padding).attr(\"width\", width + 2 * getConfig().state.padding).attr(\"height\", descrBox.height + titleHeight + 2 * getConfig().state.padding).attr(\"rx\", getConfig().state.radius);\n  return g;\n}, \"drawDescrState\");\nvar addTitleAndBox = /* @__PURE__ */ __name((g, stateDef, altBkg) => {\n  const pad = getConfig().state.padding;\n  const dblPad = 2 * getConfig().state.padding;\n  const orgBox = g.node().getBBox();\n  const orgWidth = orgBox.width;\n  const orgX = orgBox.x;\n  const title = g.append(\"text\").attr(\"x\", 0).attr(\"y\", getConfig().state.titleShift).attr(\"font-size\", getConfig().state.fontSize).attr(\"class\", \"state-title\").text(stateDef.id);\n  const titleBox = title.node().getBBox();\n  const titleWidth = titleBox.width + dblPad;\n  let width = Math.max(titleWidth, orgWidth);\n  if (width === orgWidth) {\n    width = width + dblPad;\n  }\n  let startX;\n  const graphBox = g.node().getBBox();\n  if (stateDef.doc) {\n  }\n  startX = orgX - pad;\n  if (titleWidth > orgWidth) {\n    startX = (orgWidth - width) / 2 + pad;\n  }\n  if (Math.abs(orgX - graphBox.x) < pad && titleWidth > orgWidth) {\n    startX = orgX - (titleWidth - orgWidth) / 2;\n  }\n  const lineY = 1 - getConfig().state.textHeight;\n  g.insert(\"rect\", \":first-child\").attr(\"x\", startX).attr(\"y\", lineY).attr(\"class\", altBkg ? \"alt-composit\" : \"composit\").attr(\"width\", width).attr(\n    \"height\",\n    graphBox.height + getConfig().state.textHeight + getConfig().state.titleShift + 1\n  ).attr(\"rx\", \"0\");\n  title.attr(\"x\", startX + pad);\n  if (titleWidth <= orgWidth) {\n    title.attr(\"x\", orgX + (width - dblPad) / 2 - titleWidth / 2 + pad);\n  }\n  g.insert(\"rect\", \":first-child\").attr(\"x\", startX).attr(\n    \"y\",\n    getConfig().state.titleShift - getConfig().state.textHeight - getConfig().state.padding\n  ).attr(\"width\", width).attr(\"height\", getConfig().state.textHeight * 3).attr(\"rx\", getConfig().state.radius);\n  g.insert(\"rect\", \":first-child\").attr(\"x\", startX).attr(\n    \"y\",\n    getConfig().state.titleShift - getConfig().state.textHeight - getConfig().state.padding\n  ).attr(\"width\", width).attr(\"height\", graphBox.height + 3 + 2 * getConfig().state.textHeight).attr(\"rx\", getConfig().state.radius);\n  return g;\n}, \"addTitleAndBox\");\nvar drawEndState = /* @__PURE__ */ __name((g) => {\n  g.append(\"circle\").attr(\"class\", \"end-state-outer\").attr(\"r\", getConfig().state.sizeUnit + getConfig().state.miniPadding).attr(\n    \"cx\",\n    getConfig().state.padding + getConfig().state.sizeUnit + getConfig().state.miniPadding\n  ).attr(\n    \"cy\",\n    getConfig().state.padding + getConfig().state.sizeUnit + getConfig().state.miniPadding\n  );\n  return g.append(\"circle\").attr(\"class\", \"end-state-inner\").attr(\"r\", getConfig().state.sizeUnit).attr(\"cx\", getConfig().state.padding + getConfig().state.sizeUnit + 2).attr(\"cy\", getConfig().state.padding + getConfig().state.sizeUnit + 2);\n}, \"drawEndState\");\nvar drawForkJoinState = /* @__PURE__ */ __name((g, stateDef) => {\n  let width = getConfig().state.forkWidth;\n  let height = getConfig().state.forkHeight;\n  if (stateDef.parentId) {\n    let tmp = width;\n    width = height;\n    height = tmp;\n  }\n  return g.append(\"rect\").style(\"stroke\", \"black\").style(\"fill\", \"black\").attr(\"width\", width).attr(\"height\", height).attr(\"x\", getConfig().state.padding).attr(\"y\", getConfig().state.padding);\n}, \"drawForkJoinState\");\nvar _drawLongText = /* @__PURE__ */ __name((_text, x, y, g) => {\n  let textHeight = 0;\n  const textElem = g.append(\"text\");\n  textElem.style(\"text-anchor\", \"start\");\n  textElem.attr(\"class\", \"noteText\");\n  let text = _text.replace(/\\r\\n/g, \"<br/>\");\n  text = text.replace(/\\n/g, \"<br/>\");\n  const lines = text.split(common_default.lineBreakRegex);\n  let tHeight = 1.25 * getConfig().state.noteMargin;\n  for (const line2 of lines) {\n    const txt = line2.trim();\n    if (txt.length > 0) {\n      const span = textElem.append(\"tspan\");\n      span.text(txt);\n      if (tHeight === 0) {\n        const textBounds = span.node().getBBox();\n        tHeight += textBounds.height;\n      }\n      textHeight += tHeight;\n      span.attr(\"x\", x + getConfig().state.noteMargin);\n      span.attr(\"y\", y + textHeight + 1.25 * getConfig().state.noteMargin);\n    }\n  }\n  return { textWidth: textElem.node().getBBox().width, textHeight };\n}, \"_drawLongText\");\nvar drawNote = /* @__PURE__ */ __name((text, g) => {\n  g.attr(\"class\", \"state-note\");\n  const note = g.append(\"rect\").attr(\"x\", 0).attr(\"y\", getConfig().state.padding);\n  const rectElem = g.append(\"g\");\n  const { textWidth, textHeight } = _drawLongText(text, 0, 0, rectElem);\n  note.attr(\"height\", textHeight + 2 * getConfig().state.noteMargin);\n  note.attr(\"width\", textWidth + getConfig().state.noteMargin * 2);\n  return note;\n}, \"drawNote\");\nvar drawState = /* @__PURE__ */ __name(function(elem, stateDef) {\n  const id = stateDef.id;\n  const stateInfo = {\n    id,\n    label: stateDef.id,\n    width: 0,\n    height: 0\n  };\n  const g = elem.append(\"g\").attr(\"id\", id).attr(\"class\", \"stateGroup\");\n  if (stateDef.type === \"start\") {\n    drawStartState(g);\n  }\n  if (stateDef.type === \"end\") {\n    drawEndState(g);\n  }\n  if (stateDef.type === \"fork\" || stateDef.type === \"join\") {\n    drawForkJoinState(g, stateDef);\n  }\n  if (stateDef.type === \"note\") {\n    drawNote(stateDef.note.text, g);\n  }\n  if (stateDef.type === \"divider\") {\n    drawDivider(g);\n  }\n  if (stateDef.type === \"default\" && stateDef.descriptions.length === 0) {\n    drawSimpleState(g, stateDef);\n  }\n  if (stateDef.type === \"default\" && stateDef.descriptions.length > 0) {\n    drawDescrState(g, stateDef);\n  }\n  const stateBox = g.node().getBBox();\n  stateInfo.width = stateBox.width + 2 * getConfig().state.padding;\n  stateInfo.height = stateBox.height + 2 * getConfig().state.padding;\n  return stateInfo;\n}, \"drawState\");\nvar edgeCount = 0;\nvar drawEdge = /* @__PURE__ */ __name(function(elem, path, relation) {\n  const getRelationType = /* @__PURE__ */ __name(function(type) {\n    switch (type) {\n      case StateDB.relationType.AGGREGATION:\n        return \"aggregation\";\n      case StateDB.relationType.EXTENSION:\n        return \"extension\";\n      case StateDB.relationType.COMPOSITION:\n        return \"composition\";\n      case StateDB.relationType.DEPENDENCY:\n        return \"dependency\";\n    }\n  }, \"getRelationType\");\n  path.points = path.points.filter((p) => !Number.isNaN(p.y));\n  const lineData = path.points;\n  const lineFunction = line().x(function(d) {\n    return d.x;\n  }).y(function(d) {\n    return d.y;\n  }).curve(curveBasis);\n  const svgPath = elem.append(\"path\").attr(\"d\", lineFunction(lineData)).attr(\"id\", \"edge\" + edgeCount).attr(\"class\", \"transition\");\n  let url = \"\";\n  if (getConfig().state.arrowMarkerAbsolute) {\n    url = getUrl(true);\n  }\n  svgPath.attr(\n    \"marker-end\",\n    \"url(\" + url + \"#\" + getRelationType(StateDB.relationType.DEPENDENCY) + \"End)\"\n  );\n  if (relation.title !== void 0) {\n    const label = elem.append(\"g\").attr(\"class\", \"stateLabel\");\n    const { x, y } = utils_default.calcLabelPosition(path.points);\n    const rows = common_default.getRows(relation.title);\n    let titleHeight = 0;\n    const titleRows = [];\n    let maxWidth = 0;\n    let minX = 0;\n    for (let i = 0; i <= rows.length; i++) {\n      const title = label.append(\"text\").attr(\"text-anchor\", \"middle\").text(rows[i]).attr(\"x\", x).attr(\"y\", y + titleHeight);\n      const boundsTmp = title.node().getBBox();\n      maxWidth = Math.max(maxWidth, boundsTmp.width);\n      minX = Math.min(minX, boundsTmp.x);\n      log.info(boundsTmp.x, x, y + titleHeight);\n      if (titleHeight === 0) {\n        const titleBox = title.node().getBBox();\n        titleHeight = titleBox.height;\n        log.info(\"Title height\", titleHeight, y);\n      }\n      titleRows.push(title);\n    }\n    let boxHeight = titleHeight * rows.length;\n    if (rows.length > 1) {\n      const heightAdj = (rows.length - 1) * titleHeight * 0.5;\n      titleRows.forEach((title, i) => title.attr(\"y\", y + i * titleHeight - heightAdj));\n      boxHeight = titleHeight * rows.length;\n    }\n    const bounds = label.node().getBBox();\n    label.insert(\"rect\", \":first-child\").attr(\"class\", \"box\").attr(\"x\", x - maxWidth / 2 - getConfig().state.padding / 2).attr(\"y\", y - boxHeight / 2 - getConfig().state.padding / 2 - 3.5).attr(\"width\", maxWidth + getConfig().state.padding).attr(\"height\", boxHeight + getConfig().state.padding);\n    log.info(bounds);\n  }\n  edgeCount++;\n}, \"drawEdge\");\n\n// src/diagrams/state/stateRenderer.js\nvar conf;\nvar transformationLog = {};\nvar setConf = /* @__PURE__ */ __name(function() {\n}, \"setConf\");\nvar insertMarkers = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"dependencyEnd\").attr(\"refX\", 19).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 19,7 L9,13 L14,7 L9,1 Z\");\n}, \"insertMarkers\");\nvar draw = /* @__PURE__ */ __name(function(text, id, _version, diagObj) {\n  conf = getConfig().state;\n  const securityLevel = getConfig().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const doc = securityLevel === \"sandbox\" ? sandboxElement.nodes()[0].contentDocument : document;\n  log.debug(\"Rendering diagram \" + text);\n  const diagram2 = root.select(`[id='${id}']`);\n  insertMarkers(diagram2);\n  const rootDoc = diagObj.db.getRootDoc();\n  renderDoc(rootDoc, diagram2, void 0, false, root, doc, diagObj);\n  const padding = conf.padding;\n  const bounds = diagram2.node().getBBox();\n  const width = bounds.width + padding * 2;\n  const height = bounds.height + padding * 2;\n  const svgWidth = width * 1.75;\n  configureSvgSize(diagram2, height, svgWidth, conf.useMaxWidth);\n  diagram2.attr(\n    \"viewBox\",\n    `${bounds.x - conf.padding}  ${bounds.y - conf.padding} ` + width + \" \" + height\n  );\n}, \"draw\");\nvar getLabelWidth = /* @__PURE__ */ __name((text) => {\n  return text ? text.length * conf.fontSizeFactor : 1;\n}, \"getLabelWidth\");\nvar renderDoc = /* @__PURE__ */ __name((doc, diagram2, parentId, altBkg, root, domDocument, diagObj) => {\n  const graph = new graphlib.Graph({\n    compound: true,\n    multigraph: true\n  });\n  let i;\n  let edgeFreeDoc = true;\n  for (i = 0; i < doc.length; i++) {\n    if (doc[i].stmt === \"relation\") {\n      edgeFreeDoc = false;\n      break;\n    }\n  }\n  if (parentId) {\n    graph.setGraph({\n      rankdir: \"LR\",\n      multigraph: true,\n      compound: true,\n      // acyclicer: 'greedy',\n      ranker: \"tight-tree\",\n      ranksep: edgeFreeDoc ? 1 : conf.edgeLengthFactor,\n      nodeSep: edgeFreeDoc ? 1 : 50,\n      isMultiGraph: true\n      // ranksep: 5,\n      // nodesep: 1\n    });\n  } else {\n    graph.setGraph({\n      rankdir: \"TB\",\n      multigraph: true,\n      compound: true,\n      // isCompound: true,\n      // acyclicer: 'greedy',\n      // ranker: 'longest-path'\n      ranksep: edgeFreeDoc ? 1 : conf.edgeLengthFactor,\n      nodeSep: edgeFreeDoc ? 1 : 50,\n      ranker: \"tight-tree\",\n      // ranker: 'network-simplex'\n      isMultiGraph: true\n    });\n  }\n  graph.setDefaultEdgeLabel(function() {\n    return {};\n  });\n  const states = diagObj.db.getStates();\n  const relations = diagObj.db.getRelations();\n  const keys = Object.keys(states);\n  let first = true;\n  for (const key of keys) {\n    const stateDef = states[key];\n    if (parentId) {\n      stateDef.parentId = parentId;\n    }\n    let node;\n    if (stateDef.doc) {\n      let sub = diagram2.append(\"g\").attr(\"id\", stateDef.id).attr(\"class\", \"stateGroup\");\n      node = renderDoc(stateDef.doc, sub, stateDef.id, !altBkg, root, domDocument, diagObj);\n      if (first) {\n        sub = addTitleAndBox(sub, stateDef, altBkg);\n        let boxBounds = sub.node().getBBox();\n        node.width = boxBounds.width;\n        node.height = boxBounds.height + conf.padding / 2;\n        transformationLog[stateDef.id] = { y: conf.compositTitleSize };\n      } else {\n        let boxBounds = sub.node().getBBox();\n        node.width = boxBounds.width;\n        node.height = boxBounds.height;\n      }\n    } else {\n      node = drawState(diagram2, stateDef, graph);\n    }\n    if (stateDef.note) {\n      const noteDef = {\n        descriptions: [],\n        id: stateDef.id + \"-note\",\n        note: stateDef.note,\n        type: \"note\"\n      };\n      const note = drawState(diagram2, noteDef, graph);\n      if (stateDef.note.position === \"left of\") {\n        graph.setNode(node.id + \"-note\", note);\n        graph.setNode(node.id, node);\n      } else {\n        graph.setNode(node.id, node);\n        graph.setNode(node.id + \"-note\", note);\n      }\n      graph.setParent(node.id, node.id + \"-group\");\n      graph.setParent(node.id + \"-note\", node.id + \"-group\");\n    } else {\n      graph.setNode(node.id, node);\n    }\n  }\n  log.debug(\"Count=\", graph.nodeCount(), graph);\n  let cnt = 0;\n  relations.forEach(function(relation) {\n    cnt++;\n    log.debug(\"Setting edge\", relation);\n    graph.setEdge(\n      relation.id1,\n      relation.id2,\n      {\n        relation,\n        width: getLabelWidth(relation.title),\n        height: conf.labelHeight * common_default.getRows(relation.title).length,\n        labelpos: \"c\"\n      },\n      \"id\" + cnt\n    );\n  });\n  dagreLayout(graph);\n  log.debug(\"Graph after layout\", graph.nodes());\n  const svgElem = diagram2.node();\n  graph.nodes().forEach(function(v) {\n    if (v !== void 0 && graph.node(v) !== void 0) {\n      log.warn(\"Node \" + v + \": \" + JSON.stringify(graph.node(v)));\n      root.select(\"#\" + svgElem.id + \" #\" + v).attr(\n        \"transform\",\n        \"translate(\" + (graph.node(v).x - graph.node(v).width / 2) + \",\" + (graph.node(v).y + (transformationLog[v] ? transformationLog[v].y : 0) - graph.node(v).height / 2) + \" )\"\n      );\n      root.select(\"#\" + svgElem.id + \" #\" + v).attr(\"data-x-shift\", graph.node(v).x - graph.node(v).width / 2);\n      const dividers = domDocument.querySelectorAll(\"#\" + svgElem.id + \" #\" + v + \" .divider\");\n      dividers.forEach((divider) => {\n        const parent = divider.parentElement;\n        let pWidth = 0;\n        let pShift = 0;\n        if (parent) {\n          if (parent.parentElement) {\n            pWidth = parent.parentElement.getBBox().width;\n          }\n          pShift = parseInt(parent.getAttribute(\"data-x-shift\"), 10);\n          if (Number.isNaN(pShift)) {\n            pShift = 0;\n          }\n        }\n        divider.setAttribute(\"x1\", 0 - pShift + 8);\n        divider.setAttribute(\"x2\", pWidth - pShift - 8);\n      });\n    } else {\n      log.debug(\"No Node \" + v + \": \" + JSON.stringify(graph.node(v)));\n    }\n  });\n  let stateBox = svgElem.getBBox();\n  graph.edges().forEach(function(e) {\n    if (e !== void 0 && graph.edge(e) !== void 0) {\n      log.debug(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(graph.edge(e)));\n      drawEdge(diagram2, graph.edge(e), graph.edge(e).relation);\n    }\n  });\n  stateBox = svgElem.getBBox();\n  const stateInfo = {\n    id: parentId ? parentId : \"root\",\n    label: parentId ? parentId : \"root\",\n    width: 0,\n    height: 0\n  };\n  stateInfo.width = stateBox.width + 2 * conf.padding;\n  stateInfo.height = stateBox.height + 2 * conf.padding;\n  log.debug(\"Doc rendered\", stateInfo, graph);\n  return stateInfo;\n}, \"renderDoc\");\nvar stateRenderer_default = {\n  setConf,\n  draw\n};\n\n// src/diagrams/state/stateDiagram.ts\nvar diagram = {\n  parser: stateDiagram_default,\n  get db() {\n    return new StateDB(1);\n  },\n  renderer: stateRenderer_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ __name((cnf) => {\n    if (!cnf.state) {\n      cnf.state = {};\n    }\n    cnf.state.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;\n  }, \"init\")\n};\nexport {\n  diagram\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA,IAAI,iBAAiC,OAAO,CAAC,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,SAAS,aAAa,EAAE,KAAK,KAAKA,WAAS,EAAG,MAAM,QAAQ,EAAE,KAAK,MAAMA,WAAS,EAAG,MAAM,UAAUA,WAAS,EAAG,MAAM,QAAQ,EAAE,KAAK,MAAMA,WAAS,EAAG,MAAM,UAAUA,WAAS,EAAG,MAAM,QAAQ,GAAG,gBAAgB;AAChS,IAAI,cAA8B,OAAO,CAAC,MAAM,EAAE,OAAO,MAAM,EAAE,MAAM,UAAU,MAAM,EAAE,MAAM,oBAAoB,GAAG,EAAE,KAAK,MAAMA,WAAS,EAAG,MAAM,UAAU,EAAE,KAAK,SAAS,SAAS,EAAE,KAAK,MAAMA,WAAS,EAAG,MAAM,aAAa,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,GAAG,aAAa;AACjR,IAAI,kBAAkC,OAAO,CAAC,GAAG,aAAa;AAC5D,QAAM,QAAQ,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,IAAIA,WAAS,EAAG,MAAM,OAAO,EAAE,KAAK,KAAKA,WAAS,EAAG,MAAM,aAAa,IAAIA,WAAS,EAAG,MAAM,OAAO,EAAE,KAAK,aAAaA,WAAS,EAAG,MAAM,QAAQ,EAAE,KAAK,SAAS,aAAa,EAAE,KAAK,SAAS,EAAE;AAC3O,QAAM,WAAW,MAAM,KAAI,EAAG,QAAO;AACrC,IAAE,OAAO,QAAQ,cAAc,EAAE,KAAK,KAAKA,WAAS,EAAG,MAAM,OAAO,EAAE,KAAK,KAAKA,WAAS,EAAG,MAAM,OAAO,EAAE,KAAK,SAAS,SAAS,QAAQ,IAAIA,WAAS,EAAG,MAAM,OAAO,EAAE,KAAK,UAAU,SAAS,SAAS,IAAIA,WAAAA,EAAY,MAAM,OAAO,EAAE,KAAK,MAAMA,WAAS,EAAG,MAAM,MAAM;AAC5Q,SAAO;AACT,GAAG,iBAAiB;AACpB,IAAI,iBAAiC,OAAO,CAAC,GAAG,aAAa;AAC3D,QAAM,WAA2B,OAAO,SAAS,QAAQ,KAAK,UAAU;AACtE,UAAM,QAAQ,OAAO,OAAO,OAAO,EAAE,KAAK,KAAK,IAAIA,WAAS,EAAG,MAAM,OAAO,EAAE,KAAK,GAAG;AACtF,QAAI,CAAC,UAAU;AACb,YAAM,KAAK,MAAMA,WAAS,EAAG,MAAM,UAAU;IAC/C;EACF,GAAG,UAAU;AACb,QAAM,QAAQ,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,IAAIA,WAAS,EAAG,MAAM,OAAO,EAAE,KAAK,KAAKA,WAAS,EAAG,MAAM,aAAa,MAAMA,WAAS,EAAG,MAAM,OAAO,EAAE,KAAK,aAAaA,WAAS,EAAG,MAAM,QAAQ,EAAE,KAAK,SAAS,aAAa,EAAE,KAAK,SAAS,aAAa,CAAC,CAAC;AAC1P,QAAM,WAAW,MAAM,KAAI,EAAG,QAAO;AACrC,QAAM,cAAc,SAAS;AAC7B,QAAM,cAAc,EAAE,OAAO,MAAM,EAAE,KAAK,KAAKA,WAAS,EAAG,MAAM,OAAO,EAAE;IACxE;IACA,cAAcA,WAAS,EAAG,MAAM,UAAU,MAAMA,WAAS,EAAG,MAAM,gBAAgBA,WAAS,EAAG,MAAM;EACxG,EAAI,KAAK,SAAS,mBAAmB;AACnC,MAAI,UAAU;AACd,MAAI,WAAW;AACf,WAAS,aAAa,QAAQ,SAAS,OAAO;AAC5C,QAAI,CAAC,SAAS;AACZ,eAAS,aAAa,OAAO,QAAQ;AACrC,iBAAW;IACb;AACA,cAAU;EACZ,CAAC;AACD,QAAM,YAAY,EAAE,OAAO,MAAM,EAAE,KAAK,MAAMA,WAAS,EAAG,MAAM,OAAO,EAAE,KAAK,MAAMA,WAAS,EAAG,MAAM,UAAU,cAAcA,WAAS,EAAG,MAAM,gBAAgB,CAAC,EAAE,KAAK,MAAMA,WAAS,EAAG,MAAM,UAAU,cAAcA,WAAS,EAAG,MAAM,gBAAgB,CAAC,EAAE,KAAK,SAAS,eAAe;AAC1R,QAAM,WAAW,YAAY,KAAI,EAAG,QAAO;AAC3C,QAAM,QAAQ,KAAK,IAAI,SAAS,OAAO,SAAS,KAAK;AACrD,YAAU,KAAK,MAAM,QAAQ,IAAIA,WAAS,EAAG,MAAM,OAAO;AAC1D,IAAE,OAAO,QAAQ,cAAc,EAAE,KAAK,KAAKA,WAAS,EAAG,MAAM,OAAO,EAAE,KAAK,KAAKA,WAAS,EAAG,MAAM,OAAO,EAAE,KAAK,SAAS,QAAQ,IAAIA,WAAS,EAAG,MAAM,OAAO,EAAE,KAAK,UAAU,SAAS,SAAS,cAAc,IAAIA,WAAAA,EAAY,MAAM,OAAO,EAAE,KAAK,MAAMA,WAAS,EAAG,MAAM,MAAM;AACjR,SAAO;AACT,GAAG,gBAAgB;AACnB,IAAI,iBAAiC,OAAO,CAAC,GAAG,UAAU,WAAW;AACnE,QAAM,MAAMA,WAAAA,EAAY,MAAM;AAC9B,QAAM,SAAS,IAAIA,WAAS,EAAG,MAAM;AACrC,QAAM,SAAS,EAAE,KAAI,EAAG,QAAO;AAC/B,QAAM,WAAW,OAAO;AACxB,QAAM,OAAO,OAAO;AACpB,QAAM,QAAQ,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAKA,WAAS,EAAG,MAAM,UAAU,EAAE,KAAK,aAAaA,WAAS,EAAG,MAAM,QAAQ,EAAE,KAAK,SAAS,aAAa,EAAE,KAAK,SAAS,EAAE;AAC/K,QAAM,WAAW,MAAM,KAAI,EAAG,QAAO;AACrC,QAAM,aAAa,SAAS,QAAQ;AACpC,MAAI,QAAQ,KAAK,IAAI,YAAY,QAAQ;AACzC,MAAI,UAAU,UAAU;AACtB,YAAQ,QAAQ;EAClB;AACA,MAAI;AACJ,QAAM,WAAW,EAAE,KAAI,EAAG,QAAO;AACjC,MAAI,SAAS;AAAK;AAElB,WAAS,OAAO;AAChB,MAAI,aAAa,UAAU;AACzB,cAAU,WAAW,SAAS,IAAI;EACpC;AACA,MAAI,KAAK,IAAI,OAAO,SAAS,CAAC,IAAI,OAAO,aAAa,UAAU;AAC9D,aAAS,QAAQ,aAAa,YAAY;EAC5C;AACA,QAAM,QAAQ,IAAIA,WAAS,EAAG,MAAM;AACpC,IAAE,OAAO,QAAQ,cAAc,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,KAAK,EAAE,KAAK,SAAS,SAAS,iBAAiB,UAAU,EAAE,KAAK,SAAS,KAAK,EAAE;IAC3I;IACA,SAAS,SAASA,WAAAA,EAAY,MAAM,aAAaA,WAAS,EAAG,MAAM,aAAa;EACpF,EAAI,KAAK,MAAM,GAAG;AAChB,QAAM,KAAK,KAAK,SAAS,GAAG;AAC5B,MAAI,cAAc,UAAU;AAC1B,UAAM,KAAK,KAAK,QAAQ,QAAQ,UAAU,IAAI,aAAa,IAAI,GAAG;EACpE;AACA,IAAE,OAAO,QAAQ,cAAc,EAAE,KAAK,KAAK,MAAM,EAAE;IACjD;IACAA,WAAS,EAAG,MAAM,aAAaA,WAAS,EAAG,MAAM,aAAaA,WAAS,EAAG,MAAM;EACpF,EAAI,KAAK,SAAS,KAAK,EAAE,KAAK,UAAUA,WAAAA,EAAY,MAAM,aAAa,CAAC,EAAE,KAAK,MAAMA,WAAS,EAAG,MAAM,MAAM;AAC3G,IAAE,OAAO,QAAQ,cAAc,EAAE,KAAK,KAAK,MAAM,EAAE;IACjD;IACAA,WAAS,EAAG,MAAM,aAAaA,WAAS,EAAG,MAAM,aAAaA,WAAS,EAAG,MAAM;EACpF,EAAI,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,SAAS,SAAS,IAAI,IAAIA,WAAAA,EAAY,MAAM,UAAU,EAAE,KAAK,MAAMA,WAAS,EAAG,MAAM,MAAM;AACjI,SAAO;AACT,GAAG,gBAAgB;AACnB,IAAI,eAA+B,OAAO,CAAC,MAAM;AAC/C,IAAE,OAAO,QAAQ,EAAE,KAAK,SAAS,iBAAiB,EAAE,KAAK,KAAKA,WAAS,EAAG,MAAM,WAAWA,WAAS,EAAG,MAAM,WAAW,EAAE;IACxH;IACAA,WAAS,EAAG,MAAM,UAAUA,WAAS,EAAG,MAAM,WAAWA,WAAS,EAAG,MAAM;EAC/E,EAAI;IACA;IACAA,WAAS,EAAG,MAAM,UAAUA,WAAS,EAAG,MAAM,WAAWA,WAAS,EAAG,MAAM;EAC/E;AACE,SAAO,EAAE,OAAO,QAAQ,EAAE,KAAK,SAAS,iBAAiB,EAAE,KAAK,KAAKA,WAAS,EAAG,MAAM,QAAQ,EAAE,KAAK,MAAMA,WAAS,EAAG,MAAM,UAAUA,WAAAA,EAAY,MAAM,WAAW,CAAC,EAAE,KAAK,MAAMA,WAAS,EAAG,MAAM,UAAUA,WAAS,EAAG,MAAM,WAAW,CAAC;AAC/O,GAAG,cAAc;AACjB,IAAI,oBAAoC,OAAO,CAAC,GAAG,aAAa;AAC9D,MAAI,QAAQA,WAAAA,EAAY,MAAM;AAC9B,MAAI,SAASA,WAAAA,EAAY,MAAM;AAC/B,MAAI,SAAS,UAAU;AACrB,QAAI,MAAM;AACV,YAAQ;AACR,aAAS;EACX;AACA,SAAO,EAAE,OAAO,MAAM,EAAE,MAAM,UAAU,OAAO,EAAE,MAAM,QAAQ,OAAO,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,MAAM,EAAE,KAAK,KAAKA,WAAS,EAAG,MAAM,OAAO,EAAE,KAAK,KAAKA,WAAS,EAAG,MAAM,OAAO;AAC9L,GAAG,mBAAmB;AACtB,IAAI,gBAAgC,OAAO,CAAC,OAAO,GAAG,GAAG,MAAM;AAC7D,MAAI,aAAa;AACjB,QAAM,WAAW,EAAE,OAAO,MAAM;AAChC,WAAS,MAAM,eAAe,OAAO;AACrC,WAAS,KAAK,SAAS,UAAU;AACjC,MAAI,OAAO,MAAM,QAAQ,SAAS,OAAO;AACzC,SAAO,KAAK,QAAQ,OAAO,OAAO;AAClC,QAAM,QAAQ,KAAK,MAAM,eAAe,cAAc;AACtD,MAAI,UAAU,OAAOA,WAAS,EAAG,MAAM;AACvC,aAAW,SAAS,OAAO;AACzB,UAAM,MAAM,MAAM,KAAI;AACtB,QAAI,IAAI,SAAS,GAAG;AAClB,YAAM,OAAO,SAAS,OAAO,OAAO;AACpC,WAAK,KAAK,GAAG;AACb,UAAI,YAAY,GAAG;AACjB,cAAM,aAAa,KAAK,KAAI,EAAG,QAAO;AACtC,mBAAW,WAAW;MACxB;AACA,oBAAc;AACd,WAAK,KAAK,KAAK,IAAIA,WAAS,EAAG,MAAM,UAAU;AAC/C,WAAK,KAAK,KAAK,IAAI,aAAa,OAAOA,WAAS,EAAG,MAAM,UAAU;IACrE;EACF;AACA,SAAO,EAAE,WAAW,SAAS,KAAI,EAAG,QAAO,EAAG,OAAO,WAAU;AACjE,GAAG,eAAe;AAClB,IAAI,WAA2B,OAAO,CAAC,MAAM,MAAM;AACjD,IAAE,KAAK,SAAS,YAAY;AAC5B,QAAM,OAAO,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAKA,WAAS,EAAG,MAAM,OAAO;AAC9E,QAAM,WAAW,EAAE,OAAO,GAAG;AAC7B,QAAM,EAAE,WAAW,WAAA,IAAe,cAAc,MAAM,GAAG,GAAG,QAAQ;AACpE,OAAK,KAAK,UAAU,aAAa,IAAIA,WAAS,EAAG,MAAM,UAAU;AACjE,OAAK,KAAK,SAAS,YAAYA,WAAS,EAAG,MAAM,aAAa,CAAC;AAC/D,SAAO;AACT,GAAG,UAAU;AACb,IAAI,YAA4B,OAAO,SAAS,MAAM,UAAU;AAC9D,QAAM,KAAK,SAAS;AACpB,QAAM,YAAY;IAChB;IACA,OAAO,SAAS;IAChB,OAAO;IACP,QAAQ;EACZ;AACE,QAAM,IAAI,KAAK,OAAO,GAAG,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,SAAS,YAAY;AACpE,MAAI,SAAS,SAAS,SAAS;AAC7B,mBAAe,CAAC;EAClB;AACA,MAAI,SAAS,SAAS,OAAO;AAC3B,iBAAa,CAAC;EAChB;AACA,MAAI,SAAS,SAAS,UAAU,SAAS,SAAS,QAAQ;AACxD,sBAAkB,GAAG,QAAQ;EAC/B;AACA,MAAI,SAAS,SAAS,QAAQ;AAC5B,aAAS,SAAS,KAAK,MAAM,CAAC;EAChC;AACA,MAAI,SAAS,SAAS,WAAW;AAC/B,gBAAY,CAAC;EACf;AACA,MAAI,SAAS,SAAS,aAAa,SAAS,aAAa,WAAW,GAAG;AACrE,oBAAgB,GAAG,QAAQ;EAC7B;AACA,MAAI,SAAS,SAAS,aAAa,SAAS,aAAa,SAAS,GAAG;AACnE,mBAAe,GAAG,QAAQ;EAC5B;AACA,QAAM,WAAW,EAAE,KAAI,EAAG,QAAO;AACjC,YAAU,QAAQ,SAAS,QAAQ,IAAIA,WAAS,EAAG,MAAM;AACzD,YAAU,SAAS,SAAS,SAAS,IAAIA,WAAS,EAAG,MAAM;AAC3D,SAAO;AACT,GAAG,WAAW;AACd,IAAI,YAAY;AAChB,IAAI,WAA2B,OAAO,SAAS,MAAM,MAAM,UAAU;AACnE,QAAM,kBAAkC,OAAO,SAAS,MAAM;AAC5D,YAAQ,MAAI;MACV,KAAK,QAAQ,aAAa;AACxB,eAAO;MACT,KAAK,QAAQ,aAAa;AACxB,eAAO;MACT,KAAK,QAAQ,aAAa;AACxB,eAAO;MACT,KAAK,QAAQ,aAAa;AACxB,eAAO;IACf;EACE,GAAG,iBAAiB;AACpB,OAAK,SAAS,KAAK,OAAO,OAAO,CAAC,MAAM,CAAC,OAAO,MAAM,EAAE,CAAC,CAAC;AAC1D,QAAM,WAAW,KAAK;AACtB,QAAM,eAAe,OAAI,EAAG,EAAE,SAAS,GAAG;AACxC,WAAO,EAAE;EACX,CAAC,EAAE,EAAE,SAAS,GAAG;AACf,WAAO,EAAE;EACX,CAAC,EAAE,MAAM,UAAU;AACnB,QAAM,UAAU,KAAK,OAAO,MAAM,EAAE,KAAK,KAAK,aAAa,QAAQ,CAAC,EAAE,KAAK,MAAM,SAAS,SAAS,EAAE,KAAK,SAAS,YAAY;AAC/H,MAAI,MAAM;AACV,MAAIA,WAAS,EAAG,MAAM,qBAAqB;AACzC,UAAM,OAAO,IAAI;EACnB;AACA,UAAQ;IACN;IACA,SAAS,MAAM,MAAM,gBAAgB,QAAQ,aAAa,UAAU,IAAI;EAC5E;AACE,MAAI,SAAS,UAAU,QAAQ;AAC7B,UAAM,QAAQ,KAAK,OAAO,GAAG,EAAE,KAAK,SAAS,YAAY;AACzD,UAAM,EAAE,GAAG,EAAC,IAAK,cAAc,kBAAkB,KAAK,MAAM;AAC5D,UAAM,OAAO,eAAe,QAAQ,SAAS,KAAK;AAClD,QAAI,cAAc;AAClB,UAAM,YAAY,CAAA;AAClB,QAAI,WAAW;AACf,QAAI,OAAO;AACX,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,KAAK;AACrC,YAAM,QAAQ,MAAM,OAAO,MAAM,EAAE,KAAK,eAAe,QAAQ,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,IAAI,WAAW;AACrH,YAAM,YAAY,MAAM,KAAI,EAAG,QAAO;AACtC,iBAAW,KAAK,IAAI,UAAU,UAAU,KAAK;AAC7C,aAAO,KAAK,IAAI,MAAM,UAAU,CAAC;AACjC,UAAI,KAAK,UAAU,GAAG,GAAG,IAAI,WAAW;AACxC,UAAI,gBAAgB,GAAG;AACrB,cAAM,WAAW,MAAM,KAAI,EAAG,QAAO;AACrC,sBAAc,SAAS;AACvB,YAAI,KAAK,gBAAgB,aAAa,CAAC;MACzC;AACA,gBAAU,KAAK,KAAK;IACtB;AACA,QAAI,YAAY,cAAc,KAAK;AACnC,QAAI,KAAK,SAAS,GAAG;AACnB,YAAM,aAAa,KAAK,SAAS,KAAK,cAAc;AACpD,gBAAU,QAAQ,CAAC,OAAO,MAAM,MAAM,KAAK,KAAK,IAAI,IAAI,cAAc,SAAS,CAAC;AAChF,kBAAY,cAAc,KAAK;IACjC;AACA,UAAM,SAAS,MAAM,KAAI,EAAG,QAAO;AACnC,UAAM,OAAO,QAAQ,cAAc,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,KAAK,IAAI,WAAW,IAAIA,WAAS,EAAG,MAAM,UAAU,CAAC,EAAE,KAAK,KAAK,IAAI,YAAY,IAAIA,WAAAA,EAAY,MAAM,UAAU,IAAI,GAAG,EAAE,KAAK,SAAS,WAAWA,WAAS,EAAG,MAAM,OAAO,EAAE,KAAK,UAAU,YAAYA,WAAS,EAAG,MAAM,OAAO;AACjS,QAAI,KAAK,MAAM;EACjB;AACA;AACF,GAAG,UAAU;AAGb,IAAI;AACJ,IAAI,oBAAoB,CAAA;AACxB,IAAI,UAA0B,OAAO,WAAW;AAChD,GAAG,SAAS;AACZ,IAAI,gBAAgC,OAAO,SAAS,MAAM;AACxD,OAAK,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,KAAK,MAAM,eAAe,EAAE,KAAK,QAAQ,EAAE,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,EAAE,EAAE,KAAK,gBAAgB,EAAE,EAAE,KAAK,UAAU,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,2BAA2B;AAChO,GAAG,eAAe;AAClB,IAAI,OAAuB,OAAO,SAAS,MAAM,IAAI,UAAU,SAAS;AACtE,SAAOA,WAAS,EAAG;AACnB,QAAM,gBAAgBA,WAAS,EAAG;AAClC,MAAI;AACJ,MAAI,kBAAkB,WAAW;AAC/B,qBAAiB,OAAO,OAAO,EAAE;EACnC;AACA,QAAM,OAAO,kBAAkB,YAAY,OAAO,eAAe,MAAA,EAAQ,CAAC,EAAE,gBAAgB,IAAI,IAAI,OAAO,MAAM;AACjH,QAAM,MAAM,kBAAkB,YAAY,eAAe,MAAA,EAAQ,CAAC,EAAE,kBAAkB;AACtF,MAAI,MAAM,uBAAuB,IAAI;AACrC,QAAM,WAAW,KAAK,OAAO,QAAQ,EAAE,IAAI;AAC3C,gBAAc,QAAQ;AACtB,QAAM,UAAU,QAAQ,GAAG,WAAU;AACrC,YAAU,SAAS,UAAU,QAAQ,OAAO,MAAM,KAAK,OAAO;AAC9D,QAAM,UAAU,KAAK;AACrB,QAAM,SAAS,SAAS,KAAI,EAAG,QAAO;AACtC,QAAM,QAAQ,OAAO,QAAQ,UAAU;AACvC,QAAM,SAAS,OAAO,SAAS,UAAU;AACzC,QAAM,WAAW,QAAQ;AACzB,mBAAiB,UAAU,QAAQ,UAAU,KAAK,WAAW;AAC7D,WAAS;IACP;IACA,GAAG,OAAO,IAAI,KAAK,OAAO,KAAK,OAAO,IAAI,KAAK,OAAO,MAAM,QAAQ,MAAM;EAC9E;AACA,GAAG,MAAM;AACT,IAAI,gBAAgC,OAAO,CAAC,SAAS;AACnD,SAAO,OAAO,KAAK,SAAS,KAAK,iBAAiB;AACpD,GAAG,eAAe;AAClB,IAAI,YAA4B,OAAO,CAAC,KAAK,UAAU,UAAU,QAAQ,MAAM,aAAa,YAAY;AACtG,QAAM,QAAQ,IAAIC,MAAe;IAC/B,UAAU;IACV,YAAY;EAChB,CAAG;AACD,MAAI;AACJ,MAAI,cAAc;AAClB,OAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAC/B,QAAI,IAAI,CAAC,EAAE,SAAS,YAAY;AAC9B,oBAAc;AACd;IACF;EACF;AACA,MAAI,UAAU;AACZ,UAAM,SAAS;MACb,SAAS;MACT,YAAY;MACZ,UAAU;;MAEV,QAAQ;MACR,SAAS,cAAc,IAAI,KAAK;MAChC,SAAS,cAAc,IAAI;MAC3B,cAAc;;;IAGpB,CAAK;EACH,OAAO;AACL,UAAM,SAAS;MACb,SAAS;MACT,YAAY;MACZ,UAAU;;;;MAIV,SAAS,cAAc,IAAI,KAAK;MAChC,SAAS,cAAc,IAAI;MAC3B,QAAQ;;MAER,cAAc;IACpB,CAAK;EACH;AACA,QAAM,oBAAoB,WAAW;AACnC,WAAO,CAAA;EACT,CAAC;AACD,QAAM,SAAS,QAAQ,GAAG,UAAS;AACnC,QAAM,YAAY,QAAQ,GAAG,aAAY;AACzC,QAAM,OAAO,OAAO,KAAK,MAAM;AAE/B,aAAW,OAAO,MAAM;AACtB,UAAM,WAAW,OAAO,GAAG;AAC3B,QAAI,UAAU;AACZ,eAAS,WAAW;IACtB;AACA,QAAI;AACJ,QAAI,SAAS,KAAK;AAChB,UAAI,MAAM,SAAS,OAAO,GAAG,EAAE,KAAK,MAAM,SAAS,EAAE,EAAE,KAAK,SAAS,YAAY;AACjF,aAAO,UAAU,SAAS,KAAK,KAAK,SAAS,IAAI,CAAC,QAAQ,MAAM,aAAa,OAAO;AACzE;AACT,cAAM,eAAe,KAAK,UAAU,MAAM;AAC1C,YAAI,YAAY,IAAI,KAAI,EAAG,QAAO;AAClC,aAAK,QAAQ,UAAU;AACvB,aAAK,SAAS,UAAU,SAAS,KAAK,UAAU;AAChD,0BAAkB,SAAS,EAAE,IAAI,EAAE,GAAG,KAAK,kBAAiB;MAC9D;IAKF,OAAO;AACL,aAAO,UAAU,UAAU,UAAU,KAAK;IAC5C;AACA,QAAI,SAAS,MAAM;AACjB,YAAM,UAAU;QACd,cAAc,CAAA;QACd,IAAI,SAAS,KAAK;QAClB,MAAM,SAAS;QACf,MAAM;MACd;AACM,YAAM,OAAO,UAAU,UAAU,SAAS,KAAK;AAC/C,UAAI,SAAS,KAAK,aAAa,WAAW;AACxC,cAAM,QAAQ,KAAK,KAAK,SAAS,IAAI;AACrC,cAAM,QAAQ,KAAK,IAAI,IAAI;MAC7B,OAAO;AACL,cAAM,QAAQ,KAAK,IAAI,IAAI;AAC3B,cAAM,QAAQ,KAAK,KAAK,SAAS,IAAI;MACvC;AACA,YAAM,UAAU,KAAK,IAAI,KAAK,KAAK,QAAQ;AAC3C,YAAM,UAAU,KAAK,KAAK,SAAS,KAAK,KAAK,QAAQ;IACvD,OAAO;AACL,YAAM,QAAQ,KAAK,IAAI,IAAI;IAC7B;EACF;AACA,MAAI,MAAM,UAAU,MAAM,UAAS,GAAI,KAAK;AAC5C,MAAI,MAAM;AACV,YAAU,QAAQ,SAAS,UAAU;AACnC;AACA,QAAI,MAAM,gBAAgB,QAAQ;AAClC,UAAM;MACJ,SAAS;MACT,SAAS;MACT;QACE;QACA,OAAO,cAAc,SAAS,KAAK;QACnC,QAAQ,KAAK,cAAc,eAAe,QAAQ,SAAS,KAAK,EAAE;QAClE,UAAU;MAClB;MACM,OAAO;IACb;EACE,CAAC;AACDC,SAAY,KAAK;AACjB,MAAI,MAAM,sBAAsB,MAAM,MAAK,CAAE;AAC7C,QAAM,UAAU,SAAS,KAAI;AAC7B,QAAM,MAAK,EAAG,QAAQ,SAAS,GAAG;AAChC,QAAI,MAAM,UAAU,MAAM,KAAK,CAAC,MAAM,QAAQ;AAC5C,UAAI,KAAK,UAAU,IAAI,OAAO,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC,CAAC;AAC3D,WAAK,OAAO,MAAM,QAAQ,KAAK,OAAO,CAAC,EAAE;QACvC;QACA,gBAAgB,MAAM,KAAK,CAAC,EAAE,IAAI,MAAM,KAAK,CAAC,EAAE,QAAQ,KAAK,OAAO,MAAM,KAAK,CAAC,EAAE,KAAK,kBAAkB,CAAC,IAAI,kBAAkB,CAAC,EAAE,IAAI,KAAK,MAAM,KAAK,CAAC,EAAE,SAAS,KAAK;MAChL;AACM,WAAK,OAAO,MAAM,QAAQ,KAAK,OAAO,CAAC,EAAE,KAAK,gBAAgB,MAAM,KAAK,CAAC,EAAE,IAAI,MAAM,KAAK,CAAC,EAAE,QAAQ,CAAC;AACvG,YAAM,WAAW,YAAY,iBAAiB,MAAM,QAAQ,KAAK,OAAO,IAAI,WAAW;AACvF,eAAS,QAAQ,CAAC,YAAY;AAC5B,cAAM,SAAS,QAAQ;AACvB,YAAI,SAAS;AACb,YAAI,SAAS;AACb,YAAI,QAAQ;AACV,cAAI,OAAO,eAAe;AACxB,qBAAS,OAAO,cAAc,QAAO,EAAG;UAC1C;AACA,mBAAS,SAAS,OAAO,aAAa,cAAc,GAAG,EAAE;AACzD,cAAI,OAAO,MAAM,MAAM,GAAG;AACxB,qBAAS;UACX;QACF;AACA,gBAAQ,aAAa,MAAM,IAAI,SAAS,CAAC;AACzC,gBAAQ,aAAa,MAAM,SAAS,SAAS,CAAC;MAChD,CAAC;IACH,OAAO;AACL,UAAI,MAAM,aAAa,IAAI,OAAO,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC,CAAC;IACjE;EACF,CAAC;AACD,MAAI,WAAW,QAAQ,QAAO;AAC9B,QAAM,MAAK,EAAG,QAAQ,SAAS,GAAG;AAChC,QAAI,MAAM,UAAU,MAAM,KAAK,CAAC,MAAM,QAAQ;AAC5C,UAAI,MAAM,UAAU,EAAE,IAAI,SAAS,EAAE,IAAI,OAAO,KAAK,UAAU,MAAM,KAAK,CAAC,CAAC,CAAC;AAC7E,eAAS,UAAU,MAAM,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC,EAAE,QAAQ;IAC1D;EACF,CAAC;AACD,aAAW,QAAQ,QAAO;AAC1B,QAAM,YAAY;IAChB,IAAI,WAAW,WAAW;IAC1B,OAAO,WAAW,WAAW;IAC7B,OAAO;IACP,QAAQ;EACZ;AACE,YAAU,QAAQ,SAAS,QAAQ,IAAI,KAAK;AAC5C,YAAU,SAAS,SAAS,SAAS,IAAI,KAAK;AAC9C,MAAI,MAAM,gBAAgB,WAAW,KAAK;AAC1C,SAAO;AACT,GAAG,WAAW;AACd,IAAI,wBAAwB;EAC1B;EACA;AACF;AAGG,IAAC,UAAU;EACZ,QAAQ;EACR,IAAI,KAAK;AACP,WAAO,IAAI,QAAQ,CAAC;EACtB;EACA,UAAU;EACV,QAAQ;EACR,MAAsB,OAAO,CAAC,QAAQ;AACpC,QAAI,CAAC,IAAI,OAAO;AACd,UAAI,QAAQ,CAAA;IACd;AACA,QAAI,MAAM,sBAAsB,IAAI;EACtC,GAAG,MAAM;AACX;", "names": ["getConfig", "graphlib.Graph", "dagreLayout"]}