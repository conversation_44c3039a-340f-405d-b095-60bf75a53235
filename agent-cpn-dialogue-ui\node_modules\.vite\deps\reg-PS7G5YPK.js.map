{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/reg.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Windows Registry Script\", \"fileTypes\": [\"reg\", \"REG\"], \"name\": \"reg\", \"patterns\": [{ \"match\": \"Windows Registry Editor Version 5\\\\.00|REGEDIT4\", \"name\": \"keyword.control.import.reg\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.reg\" } }, \"match\": \"(;).*$\", \"name\": \"comment.line.semicolon.reg\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.section.reg\" }, \"2\": { \"name\": \"entity.section.reg\" }, \"3\": { \"name\": \"punctuation.definition.section.reg\" } }, \"match\": \"^\\\\s*(\\\\[(?!-))(.*?)(\\\\])\", \"name\": \"entity.name.function.section.add.reg\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.section.reg\" }, \"2\": { \"name\": \"entity.section.reg\" }, \"3\": { \"name\": \"punctuation.definition.section.reg\" } }, \"match\": \"^\\\\s*(\\\\[-)(.*?)(\\\\])\", \"name\": \"entity.name.function.section.delete.reg\" }, { \"captures\": { \"2\": { \"name\": \"punctuation.definition.quote.reg\" }, \"3\": { \"name\": \"support.function.regname.ini\" }, \"4\": { \"name\": \"punctuation.definition.quote.reg\" }, \"5\": { \"name\": \"punctuation.definition.equals.reg\" }, \"7\": { \"name\": \"keyword.operator.arithmetic.minus.reg\" }, \"9\": { \"name\": \"punctuation.definition.quote.reg\" }, \"10\": { \"name\": \"string.name.regdata.reg\" }, \"11\": { \"name\": \"punctuation.definition.quote.reg\" }, \"13\": { \"name\": \"support.type.dword.reg\" }, \"14\": { \"name\": \"keyword.operator.arithmetic.colon.reg\" }, \"15\": { \"name\": \"constant.numeric.dword.reg\" }, \"17\": { \"name\": \"support.type.dword.reg\" }, \"18\": { \"name\": \"keyword.operator.arithmetic.parenthesis.reg\" }, \"19\": { \"name\": \"keyword.operator.arithmetic.parenthesis.reg\" }, \"20\": { \"name\": \"constant.numeric.hex.size.reg\" }, \"21\": { \"name\": \"keyword.operator.arithmetic.parenthesis.reg\" }, \"22\": { \"name\": \"keyword.operator.arithmetic.colon.reg\" }, \"23\": { \"name\": \"constant.numeric.hex.reg\" }, \"24\": { \"name\": \"keyword.operator.arithmetic.linecontinuation.reg\" }, \"25\": { \"name\": \"comment.declarationline.semicolon.reg\" } }, \"match\": `^(\\\\s*([\"']?)(.+?)([\"']?)\\\\s*(=))?\\\\s*((-)|(([\"'])(.*?)([\"']))|(((?i:dword))(\\\\:)\\\\s*([\\\\dabcdefABCDEF]{1,8}))|(((?i:hex))((\\\\()([\\\\d]*)(\\\\)))?(\\\\:)(.*?)(\\\\\\\\?)))\\\\s*(;.*)?$`, \"name\": \"meta.declaration.reg\" }, { \"match\": \"[0-9]+\", \"name\": \"constant.numeric.reg\" }, { \"match\": \"[a-fA-F]+\", \"name\": \"constant.numeric.hex.reg\" }, { \"match\": \",+\", \"name\": \"constant.numeric.hex.comma.reg\" }, { \"match\": \"\\\\\\\\\", \"name\": \"keyword.operator.arithmetic.linecontinuation.reg\" }], \"scopeName\": \"source.reg\" });\nvar reg = [\n  lang\n];\n\nexport { reg as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,2BAA2B,aAAa,CAAC,OAAO,KAAK,GAAG,QAAQ,OAAO,YAAY,CAAC,EAAE,SAAS,mDAAmD,QAAQ,6BAA6B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,SAAS,UAAU,QAAQ,6BAA6B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,qBAAqB,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,SAAS,6BAA6B,QAAQ,uCAAuC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,qBAAqB,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,SAAS,yBAAyB,QAAQ,0CAA0C,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,MAAM,EAAE,QAAQ,0BAA0B,GAAG,MAAM,EAAE,QAAQ,mCAAmC,GAAG,MAAM,EAAE,QAAQ,yBAAyB,GAAG,MAAM,EAAE,QAAQ,wCAAwC,GAAG,MAAM,EAAE,QAAQ,6BAA6B,GAAG,MAAM,EAAE,QAAQ,yBAAyB,GAAG,MAAM,EAAE,QAAQ,8CAA8C,GAAG,MAAM,EAAE,QAAQ,8CAA8C,GAAG,MAAM,EAAE,QAAQ,gCAAgC,GAAG,MAAM,EAAE,QAAQ,8CAA8C,GAAG,MAAM,EAAE,QAAQ,wCAAwC,GAAG,MAAM,EAAE,QAAQ,2BAA2B,GAAG,MAAM,EAAE,QAAQ,mDAAmD,GAAG,MAAM,EAAE,QAAQ,wCAAwC,EAAE,GAAG,SAAS,iLAAiL,QAAQ,uBAAuB,GAAG,EAAE,SAAS,UAAU,QAAQ,uBAAuB,GAAG,EAAE,SAAS,aAAa,QAAQ,2BAA2B,GAAG,EAAE,SAAS,MAAM,QAAQ,iCAAiC,GAAG,EAAE,SAAS,QAAQ,QAAQ,mDAAmD,CAAC,GAAG,aAAa,aAAa,CAAC;AAC17E,IAAI,MAAM;AAAA,EACR;AACF;", "names": []}