{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/ocaml.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"OCaml\", \"fileTypes\": [\".ml\", \".mli\"], \"name\": \"ocaml\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"include\": \"#decl\" }], \"repository\": { \"attribute\": { \"begin\": \"(\\\\[)[[:space:]]*((?<![#\\\\-:!?.@*/&%^+<=>|~$])@{1,3}(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"beginCaptures\": { \"1\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" }, \"2\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" } }, \"patterns\": [{ \"include\": \"#attributePayload\" }] }, \"attributeIdentifier\": { \"captures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"punctuation.definition.tag\" } }, \"match\": `((?<![#\\\\-:!?.@*/&%^+<=>|~$])%(?![#\\\\-:!?.@*/&%^+<=>|~$]))((?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*))` }, \"attributePayload\": { \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]%|^%))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"((?<![#\\\\-:!?.@*/&%^+<=>|~$])[:\\\\?](?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?<=[[:space:]])|(?=\\\\])\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"include\": \"#pathModuleExtended\" }, { \"include\": \"#pathRecord\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(?=\\\\])\", \"patterns\": [{ \"include\": \"#signature\" }, { \"include\": \"#type\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]\\\\?|^\\\\?))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(?=\\\\])\", \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]\\\\?|^\\\\?))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(?=\\\\])|\\\\bwhen\\\\b\", \"endCaptures\": { \"1\": {} }, \"patterns\": [{ \"include\": \"#pattern\" }] }, { \"begin\": \"(?:(?<=(?:[^[:word:]]when|^when))(?![[:word:]]))\", \"end\": \"(?=\\\\])\", \"patterns\": [{ \"include\": \"#term\" }] }] }, { \"include\": \"#term\" }] }, \"bindClassTerm\": { \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^[:word:]]and|^and|[^[:word:]]class|^class|[^[:word:]]type|^type))(?![[:word:]]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])(:)|(=)(?![#\\\\-:!?.@*/&%^+<=>|~$])|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^[:word:]]and|^and|[^[:word:]]class|^class|[^[:word:]]type|^type))(?![[:word:]]))\", \"end\": `(?=(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)[[:space:]]*,|[^[:space:][:lower:]%])|(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)|(?=\\\\btype\\\\b)`, \"endCaptures\": { \"0\": { \"name\": \"entity.name.function strong emphasis\" } }, \"patterns\": [{ \"include\": \"#attributeIdentifier\" }] }, { \"begin\": \"\\\\[\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#type\" }] }, { \"include\": \"#bindTermArgs\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])=(?![#\\\\-:!?.@*/&%^+<=>|~$])|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#literalClassType\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\band\\\\b|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" } }, \"patterns\": [{ \"include\": \"#term\" }] }] }, \"bindClassType\": { \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^[:word:]]and|^and|[^[:word:]]class|^class|[^[:word:]]type|^type))(?![[:word:]]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])(:)|(=)(?![#\\\\-:!?.@*/&%^+<=>|~$])|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^[:word:]]and|^and|[^[:word:]]class|^class|[^[:word:]]type|^type))(?![[:word:]]))\", \"end\": `(?=(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)[[:space:]]*,|[^[:space:][:lower:]%])|(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)|(?=\\\\btype\\\\b)`, \"endCaptures\": { \"0\": { \"name\": \"entity.name.function strong emphasis\" } }, \"patterns\": [{ \"include\": \"#attributeIdentifier\" }] }, { \"begin\": \"\\\\[\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#type\" }] }, { \"include\": \"#bindTermArgs\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])=(?![#\\\\-:!?.@*/&%^+<=>|~$])|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#literalClassType\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\band\\\\b|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" } }, \"patterns\": [{ \"include\": \"#literalClassType\" }] }] }, \"bindConstructor\": { \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^[:word:]]exception|^exception))(?![[:word:]]))|(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]\\\\+=|^\\\\+=|[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=|[^#\\\\-:!?.@*/&%^+<=>|~$]\\\\||^\\\\|))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(:)|(\\\\bof\\\\b)|((?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\|(?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"punctuation.definition.tag\" }, \"3\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#attributeIdentifier\" }, { \"match\": \"\\\\.\\\\.\", \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, { \"match\": \"\\\\b(?:\\\\b(?=[[:upper:]])[[:alpha:]_][[:word:]']*)\\\\b(?![[:space:]]*(?:\\\\.|\\\\([^\\\\*]))\", \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" }, { \"include\": \"#type\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?:(?<=(?:[^[:word:]]of|^of))(?![[:word:]]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\|(?![#\\\\-:!?.@*/&%^+<=>|~$])|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#type\" }] }] }, \"bindSignature\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?:(?<=(?:[^[:word:]]type|^type))(?![[:word:]]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])=(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"endCaptures\": { \"0\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pathModuleExtended\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\band\\\\b|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" } }, \"patterns\": [{ \"include\": \"#signature\" }] }] }, \"bindStructure\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?:(?<=(?:[^[:word:]]and|^and))(?![[:word:]]))|(?=[[:upper:]])\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])(:(?!=))|(:?=)(?![#\\\\-:!?.@*/&%^+<=>|~$])|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|open|type|val)\\\\b)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"match\": \"\\\\bmodule\\\\b\", \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" }, { \"match\": \"(?:\\\\b(?=[[:upper:]])[[:alpha:]_][[:word:]']*)\", \"name\": \"entity.name.function strong emphasis\" }, { \"begin\": \"\\\\((?!\\\\))\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$]):(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" } }, \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#signature\" }] }, { \"include\": \"#variableModule\" }] }, { \"include\": \"#literalUnit\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\b(and)\\\\b|((?<![#\\\\-:!?.@*/&%^+<=>|~$])=(?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#signature\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:=|^:=|[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\b(?:(and)|(with))\\\\b|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" }, \"2\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" } }, \"patterns\": [{ \"include\": \"#structure\" }] }] }, \"bindTerm\": { \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]!|^!))(?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?:(?<=(?:[^[:word:]]and|^and|[^[:word:]]external|^external|[^[:word:]]let|^let|[^[:word:]]method|^method|[^[:word:]]val|^val))(?![[:word:]]))\", \"end\": \"(\\\\bmodule\\\\b)|(\\\\bopen\\\\b)|(?<![#\\\\-:!?.@*/&%^+<=>|~$])(:)|((?<![#\\\\-:!?.@*/&%^+<=>|~$])=(?![#\\\\-:!?.@*/&%^+<=>|~$]))(?![#\\\\-:!?.@*/&%^+<=>|~$])|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"1\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" }, \"2\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"3\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"4\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]!|^!))(?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?:(?<=(?:[^[:word:]]and|^and|[^[:word:]]external|^external|[^[:word:]]let|^let|[^[:word:]]method|^method|[^[:word:]]val|^val))(?![[:word:]]))\", \"end\": `(?=\\\\b(?:module|open)\\\\b)|(?=(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)[[:space:]]*,|[^[:space:][:lower:]%])|(\\\\brec\\\\b)|((?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*))`, \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"2\": { \"name\": \"entity.name.function strong emphasis\" } }, \"patterns\": [{ \"include\": \"#attributeIdentifier\" }, { \"include\": \"#comment\" }] }, { \"begin\": \"(?:(?<=(?:[^[:word:]]rec|^rec))(?![[:word:]]))\", \"end\": `((?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*))|(?=[^[:space:][:alpha:]])`, \"endCaptures\": { \"0\": { \"name\": \"entity.name.function strong emphasis\" } }, \"patterns\": [{ \"include\": \"#bindTermArgs\" }] }, { \"include\": \"#bindTermArgs\" }] }, { \"begin\": \"(?:(?<=(?:[^[:word:]]module|^module))(?![[:word:]]))\", \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#declModule\" }] }, { \"begin\": \"(?:(?<=(?:[^[:word:]]open|^open))(?![[:word:]]))\", \"end\": \"(?=\\\\bin\\\\b)|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#pathModuleSimple\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])=(?![#\\\\-:!?.@*/&%^+<=>|~$])|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\btype\\\\b|(?=[^[:space:]])\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control\" } } }, { \"begin\": \"(?:(?<=(?:[^[:word:]]type|^type))(?![[:word:]]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\.(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"include\": \"#pattern\" }] }, { \"include\": \"#type\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\band\\\\b|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" } }, \"patterns\": [{ \"include\": \"#term\" }] }] }, \"bindTermArgs\": { \"patterns\": [{ \"applyEndPatternLast\": true, \"begin\": \"~|\\\\?\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \":|(?=[^[:space:]])\", \"endCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]~|^~|[^#\\\\-:!?.@*/&%^+<=>|~$]\\\\?|^\\\\?))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)|(?<=\\\\))`, \"endCaptures\": { \"0\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"\\\\((?!\\\\*)\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"begin\": \"(?<=\\\\()\", \"end\": \":|=\", \"endCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"patterns\": [{ \"match\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)`, \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" }] }, { \"begin\": \"(?<=:)\", \"end\": \"=|(?=\\\\))\", \"endCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"patterns\": [{ \"include\": \"#type\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#term\" }] }] }] }] }, { \"include\": \"#pattern\" }] }, \"bindType\": { \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^[:word:]]and|^and|[^[:word:]]type|^type))(?![[:word:]]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\+=|=(?![#\\\\-:!?.@*/&%^+<=>|~$])|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#attributeIdentifier\" }, { \"include\": \"#pathType\" }, { \"match\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)`, \"name\": \"entity.name.function strong\" }, { \"include\": \"#type\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]\\\\+=|^\\\\+=|[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\band\\\\b|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" } }, \"patterns\": [{ \"include\": \"#bindConstructor\" }] }] }, \"comment\": { \"patterns\": [{ \"include\": \"#attribute\" }, { \"include\": \"#extension\" }, { \"include\": \"#commentBlock\" }, { \"include\": \"#commentDoc\" }] }, \"commentBlock\": { \"begin\": \"\\\\(\\\\*(?!\\\\*[^\\\\)])\", \"contentName\": \"emphasis\", \"end\": \"\\\\*\\\\)\", \"name\": \"comment constant.regexp meta.separator.markdown\", \"patterns\": [{ \"include\": \"#commentBlock\" }, { \"include\": \"#commentDoc\" }] }, \"commentDoc\": { \"begin\": \"\\\\(\\\\*\\\\*\", \"end\": \"\\\\*\\\\)\", \"name\": \"comment constant.regexp meta.separator.markdown\", \"patterns\": [{ \"match\": \"\\\\*\" }, { \"include\": \"#comment\" }] }, \"decl\": { \"patterns\": [{ \"include\": \"#declClass\" }, { \"include\": \"#declException\" }, { \"include\": \"#declInclude\" }, { \"include\": \"#declModule\" }, { \"include\": \"#declOpen\" }, { \"include\": \"#declTerm\" }, { \"include\": \"#declType\" }] }, \"declClass\": { \"begin\": \"\\\\bclass\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"entity.name.class constant.numeric markup.underline\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"begin\": \"(?:(?<=(?:[^[:word:]]class|^class))(?![[:word:]]))\", \"beginCaptures\": { \"0\": { \"name\": \"entity.name.class constant.numeric markup.underline\" } }, \"end\": \"\\\\btype\\\\b|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"patterns\": [{ \"include\": \"#bindClassTerm\" }] }, { \"begin\": \"(?:(?<=(?:[^[:word:]]type|^type))(?![[:word:]]))\", \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#bindClassType\" }] }] }, \"declException\": { \"begin\": \"\\\\bexception\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword markup.underline\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#attributeIdentifier\" }, { \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"include\": \"#bindConstructor\" }] }, \"declInclude\": { \"begin\": \"\\\\binclude\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#attributeIdentifier\" }, { \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"include\": \"#signature\" }] }, \"declModule\": { \"begin\": \"(?:(?<=(?:[^[:word:]]module|^module))(?![[:word:]]))|\\\\bmodule\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename markup.underline\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"begin\": \"(?:(?<=(?:[^[:word:]]module|^module))(?![[:word:]]))\", \"end\": \"(\\\\btype\\\\b)|(?=[[:upper:]])\", \"endCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"patterns\": [{ \"include\": \"#attributeIdentifier\" }, { \"include\": \"#comment\" }, { \"match\": \"\\\\brec\\\\b\", \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }] }, { \"begin\": \"(?:(?<=(?:[^[:word:]]type|^type))(?![[:word:]]))\", \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#bindSignature\" }] }, { \"begin\": \"(?=[[:upper:]])\", \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#bindStructure\" }] }] }, \"declOpen\": { \"begin\": \"\\\\bopen\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#attributeIdentifier\" }, { \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"include\": \"#pathModuleExtended\" }] }, \"declTerm\": { \"begin\": \"\\\\b(?:(external|val)|(method)|(let))\\\\b(!?)\", \"beginCaptures\": { \"1\": { \"name\": \"support.type markup.underline\" }, \"2\": { \"name\": \"storage.type markup.underline\" }, \"3\": { \"name\": \"keyword.control markup.underline\" }, \"4\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"include\": \"#bindTerm\" }] }, \"declType\": { \"begin\": \"(?:(?<=(?:[^[:word:]]type|^type))(?![[:word:]]))|\\\\btype\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword markup.underline\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"include\": \"#bindType\" }] }, \"extension\": { \"begin\": \"(\\\\[)((?<![#\\\\-:!?.@*/&%^+<=>|~$])%{1,3}(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"beginCaptures\": { \"1\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" }, \"2\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" } }, \"patterns\": [{ \"include\": \"#attributePayload\" }] }, \"literal\": { \"patterns\": [{ \"include\": \"#termConstructor\" }, { \"include\": \"#literalArray\" }, { \"include\": \"#literalBoolean\" }, { \"include\": \"#literalCharacter\" }, { \"include\": \"#literalList\" }, { \"include\": \"#literalNumber\" }, { \"include\": \"#literalObjectTerm\" }, { \"include\": \"#literalString\" }, { \"include\": \"#literalRecord\" }, { \"include\": \"#literalUnit\" }] }, \"literalArray\": { \"begin\": \"\\\\[\\\\|\", \"captures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" } }, \"end\": \"\\\\|\\\\]\", \"patterns\": [{ \"include\": \"#term\" }] }, \"literalBoolean\": { \"match\": \"\\\\bfalse|true\\\\b\", \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" }, \"literalCharacter\": { \"begin\": \"(?<![[:word:]])'\", \"end\": \"'\", \"name\": \"markup.punctuation.quote.beginning\", \"patterns\": [{ \"include\": \"#literalCharacterEscape\" }] }, \"literalCharacterEscape\": { \"match\": `\\\\\\\\(?:[\\\\\\\\\"'ntbr]|[[:digit:]][[:digit:]][[:digit:]]|x[[:xdigit:]][[:xdigit:]]|o[0-3][0-7][0-7])` }, \"literalClassType\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"\\\\bobject\\\\b\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag emphasis\" } }, \"end\": \"\\\\bend\\\\b\", \"patterns\": [{ \"begin\": \"\\\\binherit\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"begin\": \"\\\\bas\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#variablePattern\" }] }, { \"include\": \"#type\" }] }, { \"include\": \"#pattern\" }, { \"include\": \"#declTerm\" }] }, { \"begin\": \"\\\\[\", \"end\": \"\\\\]\" }] }, \"literalList\": { \"patterns\": [{ \"begin\": \"\\\\[\", \"captures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" } }, \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#term\" }] }] }, \"literalNumber\": { \"match\": \"(?<![[:alpha:]])[[:digit:]][[:digit:]]*(\\\\.[[:digit:]][[:digit:]]*)?\", \"name\": \"constant.numeric\" }, \"literalObjectTerm\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"\\\\bobject\\\\b\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag emphasis\" } }, \"end\": \"\\\\bend\\\\b\", \"patterns\": [{ \"begin\": \"\\\\binherit\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"begin\": \"\\\\bas\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \";;|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#variablePattern\" }] }, { \"include\": \"#term\" }] }, { \"include\": \"#pattern\" }, { \"include\": \"#declTerm\" }] }, { \"begin\": \"\\\\[\", \"end\": \"\\\\]\" }] }, \"literalRecord\": { \"begin\": \"\\\\{\", \"captures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong strong\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"begin\": \"(?<=\\\\{|;)\", \"end\": \"(:)|(=)|(;)|(with)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"support.type strong\" }, \"3\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"4\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pathModulePrefixSimple\" }, { \"match\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)`, \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\" }] }, { \"begin\": \"(?:(?<=(?:[^[:word:]]with|^with))(?![[:word:]]))\", \"end\": \"(:)|(=)|(;)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"support.type strong\" }, \"3\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"match\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)`, \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(;)|(=)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#type\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \";|(?=\\\\})\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"include\": \"#term\" }] }] }, \"literalString\": { \"patterns\": [{ \"begin\": '\"', \"end\": '\"', \"name\": \"string beginning.punctuation.definition.quote.markdown\", \"patterns\": [{ \"include\": \"#literalStringEscape\" }] }, { \"begin\": \"(\\\\{)([_[:lower:]]*?)(\\\\|)\", \"end\": \"(\\\\|)(\\\\2)(\\\\})\", \"name\": \"string beginning.punctuation.definition.quote.markdown\", \"patterns\": [{ \"include\": \"#literalStringEscape\" }] }] }, \"literalStringEscape\": { \"match\": '\\\\\\\\(?:[\\\\\\\\\"ntbr]|[[:digit:]][[:digit:]][[:digit:]]|x[[:xdigit:]][[:xdigit:]]|o[0-3][0-7][0-7])' }, \"literalUnit\": { \"match\": \"\\\\(\\\\)\", \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" }, \"pathModuleExtended\": { \"patterns\": [{ \"include\": \"#pathModulePrefixExtended\" }, { \"match\": \"(?:\\\\b(?=[[:upper:]])[[:alpha:]_][[:word:]']*)\", \"name\": \"entity.name.class constant.numeric\" }] }, \"pathModulePrefixExtended\": { \"begin\": \"(?:\\\\b(?=[[:upper:]])[[:alpha:]_][[:word:]']*)(?=[[:space:]]*\\\\.|$|\\\\()\", \"beginCaptures\": { \"0\": { \"name\": \"entity.name.class constant.numeric\" } }, \"end\": \"(?![[:space:]\\\\.]|$|\\\\()\", \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"\\\\(\", \"captures\": { \"0\": { \"name\": \"keyword.control\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"match\": \"((?:\\\\b(?=[[:upper:]])[[:alpha:]_][[:word:]']*)(?=[[:space:]]*\\\\)))\", \"name\": \"string.other.link variable.language variable.parameter emphasis\" }, { \"include\": \"#structure\" }] }, { \"begin\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\.(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"keyword strong\" } }, \"end\": \"((?:\\\\b(?=[[:upper:]])[[:alpha:]_][[:word:]']*)(?=[[:space:]]*\\\\.|$))|((?:\\\\b(?=[[:upper:]])[[:alpha:]_][[:word:]']*)(?=[[:space:]]*(?:$|\\\\()))|((?:\\\\b(?=[[:upper:]])[[:alpha:]_][[:word:]']*)(?=[[:space:]]*\\\\)))|(?![[:space:]\\\\.[:upper:]]|$|\\\\()\", \"endCaptures\": { \"1\": { \"name\": \"entity.name.class constant.numeric\" }, \"2\": { \"name\": \"entity.name.function strong\" }, \"3\": { \"name\": \"string.other.link variable.language variable.parameter emphasis\" } } }] }, \"pathModulePrefixExtendedParens\": { \"begin\": \"\\\\(\", \"captures\": { \"0\": { \"name\": \"keyword.control\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"match\": \"((?:\\\\b(?=[[:upper:]])[[:alpha:]_][[:word:]']*)(?=[[:space:]]*\\\\)))\", \"name\": \"string.other.link variable.language variable.parameter emphasis\" }, { \"include\": \"#structure\" }] }, \"pathModulePrefixSimple\": { \"begin\": \"(?:\\\\b(?=[[:upper:]])[[:alpha:]_][[:word:]']*)(?=[[:space:]]*\\\\.)\", \"beginCaptures\": { \"0\": { \"name\": \"entity.name.class constant.numeric\" } }, \"end\": \"(?![[:space:]\\\\.])\", \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\.(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"keyword strong\" } }, \"end\": \"((?:\\\\b(?=[[:upper:]])[[:alpha:]_][[:word:]']*)(?=[[:space:]]*\\\\.))|((?:\\\\b(?=[[:upper:]])[[:alpha:]_][[:word:]']*)(?=[[:space:]]*))|(?![[:space:]\\\\.[:upper:]])\", \"endCaptures\": { \"1\": { \"name\": \"entity.name.class constant.numeric\" }, \"2\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" } } }] }, \"pathModuleSimple\": { \"patterns\": [{ \"include\": \"#pathModulePrefixSimple\" }, { \"match\": \"(?:\\\\b(?=[[:upper:]])[[:alpha:]_][[:word:]']*)\", \"name\": \"entity.name.class constant.numeric\" }] }, \"pathRecord\": { \"patterns\": [{ \"begin\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)`, \"end\": \"(?=[^[:space:]\\\\.])(?!\\\\(\\\\*)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]\\\\.|^\\\\.))(?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\.(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"keyword strong\" } }, \"end\": `((?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\.(?![#\\\\-:!?.@*/&%^+<=>|~$]))|((?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|mutable|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*))|(?<=\\\\))|(?<=\\\\])`, \"endCaptures\": { \"1\": { \"name\": \"keyword strong\" }, \"2\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pathModulePrefixSimple\" }, { \"begin\": \"\\\\((?!\\\\*)\", \"captures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#term\" }] }, { \"begin\": \"\\\\[\", \"captures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#pattern\" }] }] }] }] }, \"pattern\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#patternArray\" }, { \"include\": \"#patternLazy\" }, { \"include\": \"#patternList\" }, { \"include\": \"#patternMisc\" }, { \"include\": \"#patternModule\" }, { \"include\": \"#patternRecord\" }, { \"include\": \"#literal\" }, { \"include\": \"#patternParens\" }, { \"include\": \"#patternType\" }, { \"include\": \"#variablePattern\" }, { \"include\": \"#termOperator\" }] }, \"patternArray\": { \"begin\": \"\\\\[\\\\|\", \"captures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" } }, \"end\": \"\\\\|\\\\]\", \"patterns\": [{ \"include\": \"#pattern\" }] }, \"patternLazy\": { \"match\": \"lazy\", \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"patternList\": { \"begin\": \"\\\\[\", \"captures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" } }, \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#pattern\" }] }, \"patternMisc\": { \"captures\": { \"1\": { \"name\": \"string.regexp strong\" }, \"2\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"3\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"match\": \"((?<![#\\\\-:!?.@*/&%^+<=>|~$]),(?![#\\\\-:!?.@*/&%^+<=>|~$]))|([#\\\\-:!?.@*/&%^+<=>|~$]+)|\\\\b(as)\\\\b\" }, \"patternModule\": { \"begin\": \"\\\\bmodule\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" } }, \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#declModule\" }] }, \"patternParens\": { \"begin\": \"\\\\((?!\\\\))\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$]):(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" } }, \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#type\" }] }, { \"include\": \"#pattern\" }] }, \"patternRecord\": { \"begin\": \"\\\\{\", \"captures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong strong\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"begin\": \"(?<=\\\\{|;)\", \"end\": \"(:)|(=)|(;)|(with)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"support.type strong\" }, \"3\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"4\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pathModulePrefixSimple\" }, { \"match\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)`, \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\" }] }, { \"begin\": \"(?:(?<=(?:[^[:word:]]with|^with))(?![[:word:]]))\", \"end\": \"(:)|(=)|(;)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"support.type strong\" }, \"3\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"match\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)`, \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(;)|(=)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#type\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \";|(?=\\\\})\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"include\": \"#pattern\" }] }] }, \"patternType\": { \"begin\": \"\\\\btype\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#declType\" }] }, \"pragma\": { \"begin\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])#(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#literalNumber\" }, { \"include\": \"#literalString\" }] }, \"signature\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#signatureLiteral\" }, { \"include\": \"#signatureFunctor\" }, { \"include\": \"#pathModuleExtended\" }, { \"include\": \"#signatureParens\" }, { \"include\": \"#signatureRecovered\" }, { \"include\": \"#signatureConstraints\" }] }, \"signatureConstraints\": { \"begin\": \"\\\\bwith\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" } }, \"end\": \"(?=\\\\))|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^[:word:]]with|^with))(?![[:word:]]))\", \"end\": \"\\\\b(?:(module)|(type))\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" }, \"2\": { \"name\": \"keyword\" } } }, { \"include\": \"#declModule\" }, { \"include\": \"#declType\" }] }, \"signatureFunctor\": { \"patterns\": [{ \"begin\": \"\\\\bfunctor\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^[:word:]]functor|^functor))(?![[:word:]]))\", \"end\": \"(\\\\(\\\\))|(\\\\((?!\\\\)))\", \"endCaptures\": { \"1\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" }, \"2\": { \"name\": \"punctuation.definition.tag\" } } }, { \"begin\": \"(?<=\\\\()\", \"end\": \"(:)|(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#variableModule\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#signature\" }] }, { \"begin\": \"(?<=\\\\))\", \"end\": \"(\\\\()|((?<![#\\\\-:!?.@*/&%^+<=>|~$])->(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag\" }, \"2\": { \"name\": \"support.type strong\" } } }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]->|^->))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#signature\" }] }] }, { \"match\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])->(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"name\": \"support.type strong\" }] }, \"signatureLiteral\": { \"begin\": \"\\\\bsig\\\\b\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag emphasis\" } }, \"end\": \"\\\\bend\\\\b\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"include\": \"#decl\" }] }, \"signatureParens\": { \"begin\": \"\\\\((?!\\\\))\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$]):(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" } }, \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#signature\" }] }, { \"include\": \"#signature\" }] }, \"signatureRecovered\": { \"patterns\": [{ \"begin\": \"\\\\(|(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:|[^#\\\\-:!?.@*/&%^+<=>|~$]->|^->))(?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?:(?<=(?:[^[:word:]]include|^include|[^[:word:]]open|^open))(?![[:word:]]))\", \"end\": \"\\\\bmodule\\\\b|(?!$|[[:space:]]|\\\\bmodule\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" } } }, { \"begin\": \"(?:(?<=(?:[^[:word:]]module|^module))(?![[:word:]]))\", \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^[:word:]]module|^module))(?![[:word:]]))\", \"end\": \"\\\\btype\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"keyword\" } } }, { \"begin\": \"(?:(?<=(?:[^[:word:]]type|^type))(?![[:word:]]))\", \"end\": \"\\\\bof\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } } }, { \"begin\": \"(?:(?<=(?:[^[:word:]]of|^of))(?![[:word:]]))\", \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#signature\" }] }] }] }, \"structure\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#structureLiteral\" }, { \"include\": \"#structureFunctor\" }, { \"include\": \"#pathModuleExtended\" }, { \"include\": \"#structureParens\" }] }, \"structureFunctor\": { \"patterns\": [{ \"begin\": \"\\\\bfunctor\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^[:word:]]functor|^functor))(?![[:word:]]))\", \"end\": \"(\\\\(\\\\))|(\\\\((?!\\\\)))\", \"endCaptures\": { \"1\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" }, \"2\": { \"name\": \"punctuation.definition.tag\" } } }, { \"begin\": \"(?<=\\\\()\", \"end\": \"(:)|(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#variableModule\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"patterns\": [{ \"include\": \"#signature\" }] }, { \"begin\": \"(?<=\\\\))\", \"end\": \"(\\\\()|((?<![#\\\\-:!?.@*/&%^+<=>|~$])->(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag\" }, \"2\": { \"name\": \"support.type strong\" } } }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]->|^->))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"patterns\": [{ \"include\": \"#structure\" }] }] }, { \"match\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])->(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"name\": \"support.type strong\" }] }, \"structureLiteral\": { \"begin\": \"\\\\bstruct\\\\b\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag emphasis\" } }, \"end\": \"\\\\bend\\\\b\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pragma\" }, { \"include\": \"#decl\" }] }, \"structureParens\": { \"begin\": \"\\\\(\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#structureUnpack\" }, { \"include\": \"#structure\" }] }, \"structureUnpack\": { \"begin\": \"\\\\bval\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \"(?=\\\\))\" }, \"term\": { \"patterns\": [{ \"include\": \"#termLet\" }, { \"include\": \"#termAtomic\" }] }, \"termAtomic\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#termConditional\" }, { \"include\": \"#termConstructor\" }, { \"include\": \"#termDelim\" }, { \"include\": \"#termFor\" }, { \"include\": \"#termFunction\" }, { \"include\": \"#literal\" }, { \"include\": \"#termMatch\" }, { \"include\": \"#termMatchRule\" }, { \"include\": \"#termPun\" }, { \"include\": \"#termOperator\" }, { \"include\": \"#termTry\" }, { \"include\": \"#termWhile\" }, { \"include\": \"#pathRecord\" }] }, \"termConditional\": { \"match\": \"\\\\b(?:if|then|else)\\\\b\", \"name\": \"keyword.control\" }, \"termConstructor\": { \"patterns\": [{ \"include\": \"#pathModulePrefixSimple\" }, { \"match\": \"(?:\\\\b(?=[[:upper:]])[[:alpha:]_][[:word:]']*)\", \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong\" }] }, \"termDelim\": { \"patterns\": [{ \"begin\": \"\\\\((?!\\\\))\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#term\" }] }, { \"begin\": \"\\\\bbegin\\\\b\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\bend\\\\b\", \"patterns\": [{ \"include\": \"#attributeIdentifier\" }, { \"include\": \"#term\" }] }] }, \"termFor\": { \"patterns\": [{ \"begin\": \"\\\\bfor\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control\" } }, \"end\": \"\\\\bdone\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control\" } }, \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^[:word:]]for|^for))(?![[:word:]]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])=(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"endCaptures\": { \"0\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#pattern\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"\\\\b(?:downto|to)\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control\" } }, \"patterns\": [{ \"include\": \"#term\" }] }, { \"begin\": \"(?:(?<=(?:[^[:word:]]to|^to))(?![[:word:]]))\", \"end\": \"\\\\bdo\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control\" } }, \"patterns\": [{ \"include\": \"#term\" }] }, { \"begin\": \"(?:(?<=(?:[^[:word:]]do|^do))(?![[:word:]]))\", \"end\": \"(?=\\\\bdone\\\\b)\", \"patterns\": [{ \"include\": \"#term\" }] }] }] }, \"termFunction\": { \"captures\": { \"1\": { \"name\": \"storage.type\" }, \"2\": { \"name\": \"storage.type\" } }, \"match\": \"\\\\b(?:(fun)|(function))\\\\b\" }, \"termLet\": { \"patterns\": [{ \"begin\": \"(?:(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=|[^#\\\\-:!?.@*/&%^+<=>|~$]->|^->))(?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?<=;|\\\\())(?=[[:space:]]|\\\\blet\\\\b)|(?:(?<=(?:[^[:word:]]begin|^begin|[^[:word:]]do|^do|[^[:word:]]else|^else|[^[:word:]]in|^in|[^[:word:]]struct|^struct|[^[:word:]]then|^then|[^[:word:]]try|^try))(?![[:word:]]))|(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]@@|^@@))(?![#\\\\-:!?.@*/&%^+<=>|~$]))[[:space:]]+\", \"end\": \"\\\\b(?:(and)|(let))\\\\b|(?=[^[:space:]])(?!\\\\(\\\\*)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" }, \"2\": { \"name\": \"storage.type markup.underline\" } }, \"patterns\": [{ \"include\": \"#comment\" }] }, { \"begin\": \"(?:(?<=(?:[^[:word:]]and|^and|[^[:word:]]let|^let))(?![[:word:]]))|(let)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type markup.underline\" } }, \"end\": \"\\\\b(?:(and)|(in))\\\\b|(?=\\\\}|\\\\)|\\\\]|\\\\b(?:end|class|exception|external|include|inherit|initializer|let|method|module|open|type|val)\\\\b)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp markup.underline\" }, \"2\": { \"name\": \"storage.type markup.underline\" } }, \"patterns\": [{ \"include\": \"#bindTerm\" }] }] }, \"termMatch\": { \"begin\": \"\\\\bmatch\\\\b\", \"captures\": { \"0\": { \"name\": \"keyword.control\" } }, \"end\": \"\\\\bwith\\\\b\", \"patterns\": [{ \"include\": \"#term\" }] }, \"termMatchRule\": { \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^[:word:]]fun|^fun|[^[:word:]]function|^function|[^[:word:]]with|^with))(?![[:word:]]))\", \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])(\\\\|)|(->)(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"endCaptures\": { \"1\": { \"name\": \"support.type strong\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#attributeIdentifier\" }, { \"include\": \"#pattern\" }] }, { \"begin\": \"(?:(?<=(?:[^\\\\[#\\\\-:!?.@*/&%^+<=>|~$]\\\\||^\\\\|))(?![#\\\\-:!?.@*/&%^+<=>|~$]))|(?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\|(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"support.type strong\" } }, \"end\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])(\\\\|)|(->)(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"endCaptures\": { \"1\": { \"name\": \"support.type strong\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#pattern\" }, { \"begin\": \"\\\\bwhen\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \"(?=(?<![#\\\\-:!?.@*/&%^+<=>|~$])->(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"patterns\": [{ \"include\": \"#term\" }] }] }] }, \"termOperator\": { \"patterns\": [{ \"begin\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])#(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"end\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)`, \"endCaptures\": { \"0\": { \"name\": \"entity.name.function\" } } }, { \"captures\": { \"0\": { \"name\": \"keyword.control strong\" } }, \"match\": \"<-\" }, { \"captures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"2\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"match\": \"(,|[#\\\\-:!?.@*/&%^+<=>|~$]+)|(;)\" }, { \"match\": \"\\\\b(?:and|assert|asr|land|lazy|lsr|lxor|mod|new|or)\\\\b\", \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }] }, \"termPun\": { \"applyEndPatternLast\": true, \"begin\": \"(?<![#\\\\-:!?.@*/&%^+<=>|~$])\\\\?|~(?![#\\\\-:!?.@*/&%^+<=>|~$])\", \"beginCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"end\": \":|(?=[^[:space:]:])\", \"endCaptures\": { \"0\": { \"name\": \"keyword\" } }, \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]\\\\?|^\\\\?|[^#\\\\-:!?.@*/&%^+<=>|~$]~|^~))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)`, \"endCaptures\": { \"0\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" } } }] }, \"termTry\": { \"begin\": \"\\\\btry\\\\b\", \"captures\": { \"0\": { \"name\": \"keyword.control\" } }, \"end\": \"\\\\bwith\\\\b\", \"patterns\": [{ \"include\": \"#term\" }] }, \"termWhile\": { \"patterns\": [{ \"begin\": \"\\\\bwhile\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control\" } }, \"end\": \"\\\\bdone\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control\" } }, \"patterns\": [{ \"begin\": \"(?:(?<=(?:[^[:word:]]while|^while))(?![[:word:]]))\", \"end\": \"\\\\bdo\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control\" } }, \"patterns\": [{ \"include\": \"#term\" }] }, { \"begin\": \"(?:(?<=(?:[^[:word:]]do|^do))(?![[:word:]]))\", \"end\": \"(?=\\\\bdone\\\\b)\", \"patterns\": [{ \"include\": \"#term\" }] }] }] }, \"type\": { \"patterns\": [{ \"include\": \"#comment\" }, { \"match\": \"\\\\bnonrec\\\\b\", \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, { \"include\": \"#pathModulePrefixExtended\" }, { \"include\": \"#typeLabel\" }, { \"include\": \"#typeObject\" }, { \"include\": \"#typeOperator\" }, { \"include\": \"#typeParens\" }, { \"include\": \"#typePolymorphicVariant\" }, { \"include\": \"#typeRecord\" }, { \"include\": \"#typeConstructor\" }] }, \"typeConstructor\": { \"patterns\": [{ \"begin\": `(_)|((?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*))|(')((?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*))|(?<=[^\\\\*]\\\\)|\\\\])`, \"beginCaptures\": { \"1\": { \"name\": \"comment constant.regexp meta.separator.markdown\" }, \"3\": { \"name\": \"string.other.link variable.language variable.parameter emphasis strong emphasis\" }, \"4\": { \"name\": \"keyword.control emphasis\" } }, \"end\": `(?=\\\\((?!\\\\*)|\\\\*|:|,|=|\\\\.|>|-|\\\\{|\\\\[|\\\\+|\\\\}|\\\\)|\\\\]|;|\\\\|)|((?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*))[:space:]*(?!\\\\(\\\\*|[[:word:]])|(?=;;|\\\\}|\\\\)|\\\\]|\\\\b(?:end|and|class|exception|external|in|include|inherit|initializer|let|method|module|open|type|val)\\\\b)`, \"endCaptures\": { \"1\": { \"name\": \"entity.name.function strong\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pathModulePrefixExtended\" }] }] }, \"typeLabel\": { \"patterns\": [{ \"begin\": `(\\\\??)((?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*))[[:space:]]*((?<![#\\\\-:!?.@*/&%^+<=>|~$]):(?![#\\\\-:!?.@*/&%^+<=>|~$]))`, \"captures\": { \"1\": { \"name\": \"keyword strong emphasis\" }, \"2\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\" }, \"3\": { \"name\": \"keyword\" } }, \"end\": \"(?=(?<![#\\\\-:!?.@*/&%^+<=>|~$])->(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"patterns\": [{ \"include\": \"#type\" }] }] }, \"typeModule\": { \"begin\": \"\\\\bmodule\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename\" } }, \"end\": \"(?=\\\\))\", \"patterns\": [{ \"include\": \"#pathModuleExtended\" }, { \"include\": \"#signatureConstraints\" }] }, \"typeObject\": { \"begin\": \"<\", \"captures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong strong\" } }, \"end\": \">\", \"patterns\": [{ \"begin\": \"(?<=<|;)\", \"end\": \"(:)|(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"3\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"4\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pathModulePrefixSimple\" }, { \"match\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)`, \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(;)|(?=>)\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#type\" }] }] }, \"typeOperator\": { \"patterns\": [{ \"match\": \",|;|[#\\\\-:!?.@*/&%^+<=>|~$]+\", \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }] }, \"typeParens\": { \"begin\": \"\\\\(\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.tag\" } }, \"end\": \"\\\\)\", \"patterns\": [{ \"match\": \",\", \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, { \"include\": \"#typeModule\" }, { \"include\": \"#type\" }] }, \"typePolymorphicVariant\": { \"begin\": \"\\\\[\", \"end\": \"\\\\]\", \"patterns\": [] }, \"typeRecord\": { \"begin\": \"\\\\{\", \"captures\": { \"0\": { \"name\": \"constant.language constant.numeric entity.other.attribute-name.id.css strong strong\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"begin\": \"(?<=\\\\{|;)\", \"end\": \"(:)|(=)|(;)|(with)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"support.type strong\" }, \"3\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"4\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#pathModulePrefixSimple\" }, { \"match\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)`, \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\" }] }, { \"begin\": \"(?:(?<=(?:[^[:word:]]with|^with))(?![[:word:]]))\", \"end\": \"(:)|(=)|(;)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp strong\" }, \"2\": { \"name\": \"support.type strong\" }, \"3\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"match\": `(?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*)`, \"name\": \"markup.inserted constant.language support.constant.property-value entity.name.filename emphasis\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]:|^:))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \"(;)|(=)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" }, \"2\": { \"name\": \"support.type strong\" } }, \"patterns\": [{ \"include\": \"#type\" }] }, { \"begin\": \"(?:(?<=(?:[^#\\\\-:!?.@*/&%^+<=>|~$]=|^=))(?![#\\\\-:!?.@*/&%^+<=>|~$]))\", \"end\": \";|(?=\\\\})\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.class.js message.error variable.interpolation string.regexp\" } }, \"patterns\": [{ \"include\": \"#type\" }] }] }, \"variableModule\": { \"captures\": { \"0\": { \"name\": \"string.other.link variable.language variable.parameter emphasis\" } }, \"match\": \"(?:\\\\b(?=[[:upper:]])[[:alpha:]_][[:word:]']*)\" }, \"variablePattern\": { \"captures\": { \"1\": { \"name\": \"comment constant.regexp meta.separator.markdown\" }, \"2\": { \"name\": \"string.other.link variable.language variable.parameter emphasis\" } }, \"match\": `(\\\\b_\\\\b)|((?:(?!\\\\b(?:and|'|as|asr|assert|\\\\*|begin|class|:|,|@|constraint|do|done|downto|else|end|=|exception|external|false|for|\\\\.|fun|function|functor|>|-|if|in|include|inherit|initializer|land|lazy|\\\\{|\\\\(|\\\\[|<|let|lor|lsl|lsr|lxor|match|method|mod|module|mutable|new|nonrec|#|object|of|open|or|%|\\\\+|private|\\\\?|\"|rec|\\\\\\\\|\\\\}|\\\\)|\\\\]|;|sig|/|struct|then|~|to|true|try|type|val|\\\\||virtual|when|while|with)\\\\b(?:[^']|$))\\\\b(?=[[:lower:]_])[[:alpha:]_][[:word:]']*))` } }, \"scopeName\": \"source.ocaml\" });\nvar ocaml = [\n  lang\n];\n\nexport { ocaml as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,SAAS,aAAa,CAAC,OAAO,MAAM,GAAG,QAAQ,SAAS,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,QAAQ,CAAC,GAAG,cAAc,EAAE,aAAa,EAAE,SAAS,oFAAoF,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+EAA+E,GAAG,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,+EAA+E,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,uBAAuB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,SAAS,4gBAA4gB,GAAG,oBAAoB,EAAE,YAAY,CAAC,EAAE,SAAS,wEAAwE,OAAO,4FAA4F,eAAe,EAAE,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,4EAA4E,OAAO,WAAW,YAAY,CAAC,EAAE,SAAS,4EAA4E,OAAO,sBAAsB,eAAe,EAAE,KAAK,CAAC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,EAAE,SAAS,oDAAoD,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,SAAS,gGAAgG,OAAO,+LAA+L,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,gGAAgG,OAAO,s9BAAs9B,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,iLAAiL,eAAe,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,0IAA0I,eAAe,EAAE,KAAK,EAAE,QAAQ,8FAA8F,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,SAAS,gGAAgG,OAAO,+LAA+L,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,gGAAgG,OAAO,s9BAAs9B,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,iLAAiL,eAAe,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,0IAA0I,eAAe,EAAE,KAAK,EAAE,QAAQ,8FAA8F,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,SAAS,uMAAuM,OAAO,4MAA4M,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,SAAS,UAAU,QAAQ,6EAA6E,GAAG,EAAE,SAAS,yFAAyF,QAAQ,+EAA+E,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,qHAAqH,OAAO,2LAA2L,eAAe,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,oDAAoD,OAAO,4DAA4D,eAAe,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,0IAA0I,eAAe,EAAE,KAAK,EAAE,QAAQ,8FAA8F,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,kEAAkE,OAAO,4LAA4L,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,gBAAgB,QAAQ,yFAAyF,GAAG,EAAE,SAAS,kDAAkD,QAAQ,uCAAuC,GAAG,EAAE,SAAS,cAAc,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,4DAA4D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oFAAoF,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,uMAAuM,eAAe,EAAE,KAAK,EAAE,QAAQ,8FAA8F,GAAG,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,uGAAuG,OAAO,uJAAuJ,eAAe,EAAE,KAAK,EAAE,QAAQ,8FAA8F,GAAG,KAAK,EAAE,QAAQ,8FAA8F,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,uNAAuN,OAAO,kRAAkR,eAAe,EAAE,KAAK,EAAE,QAAQ,yFAAyF,GAAG,KAAK,EAAE,QAAQ,6EAA6E,GAAG,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,uNAAuN,OAAO,++BAA++B,eAAe,EAAE,KAAK,EAAE,QAAQ,6EAA6E,GAAG,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,EAAE,SAAS,kDAAkD,OAAO,6eAA6e,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,wDAAwD,OAAO,gIAAgI,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,oDAAoD,OAAO,0IAA0I,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,yLAAyL,eAAe,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,wEAAwE,OAAO,+BAA+B,eAAe,EAAE,KAAK,EAAE,QAAQ,kBAAkB,EAAE,EAAE,GAAG,EAAE,SAAS,oDAAoD,OAAO,8DAA8D,eAAe,EAAE,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,0IAA0I,eAAe,EAAE,KAAK,EAAE,QAAQ,8FAA8F,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,CAAC,EAAE,uBAAuB,MAAM,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,UAAU,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,yGAAyG,OAAO,0dAA0d,eAAe,EAAE,KAAK,EAAE,QAAQ,yFAAyF,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,cAAc,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,SAAS,YAAY,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,UAAU,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,idAAid,QAAQ,yFAAyF,CAAC,EAAE,GAAG,EAAE,SAAS,UAAU,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,UAAU,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,wEAAwE,OAAO,8LAA8L,eAAe,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,SAAS,idAAid,QAAQ,8BAA8B,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,2GAA2G,OAAO,0IAA0I,eAAe,EAAE,KAAK,EAAE,QAAQ,8FAA8F,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mBAAmB,CAAC,EAAE,CAAC,EAAE,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,uBAAuB,eAAe,YAAY,OAAO,UAAU,QAAQ,mDAAmD,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,aAAa,OAAO,UAAU,QAAQ,mDAAmD,YAAY,CAAC,EAAE,SAAS,MAAM,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,QAAQ,EAAE,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,eAAe,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sDAAsD,EAAE,GAAG,OAAO,gIAAgI,eAAe,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,SAAS,sDAAsD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sDAAsD,EAAE,GAAG,OAAO,mIAAmI,eAAe,EAAE,KAAK,EAAE,QAAQ,UAAU,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,SAAS,oDAAoD,OAAO,gIAAgI,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,mBAAmB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,OAAO,gIAAgI,eAAe,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,eAAe,EAAE,SAAS,iBAAiB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,OAAO,gIAAgI,eAAe,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,qEAAqE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0GAA0G,EAAE,GAAG,OAAO,gIAAgI,eAAe,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,SAAS,wDAAwD,OAAO,gCAAgC,eAAe,EAAE,KAAK,EAAE,QAAQ,UAAU,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,aAAa,QAAQ,6EAA6E,CAAC,EAAE,GAAG,EAAE,SAAS,oDAAoD,OAAO,gIAAgI,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,SAAS,mBAAmB,OAAO,gIAAgI,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,OAAO,gIAAgI,eAAe,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,YAAY,EAAE,SAAS,+CAA+C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,OAAO,gIAAgI,eAAe,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,YAAY,EAAE,SAAS,+DAA+D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,OAAO,gIAAgI,eAAe,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,wEAAwE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+EAA+E,GAAG,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,+EAA+E,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,UAAU,YAAY,EAAE,KAAK,EAAE,QAAQ,+EAA+E,EAAE,GAAG,OAAO,UAAU,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,kBAAkB,EAAE,SAAS,oBAAoB,QAAQ,+EAA+E,GAAG,oBAAoB,EAAE,SAAS,oBAAoB,OAAO,KAAK,QAAQ,sCAAsC,YAAY,CAAC,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,0BAA0B,EAAE,SAAS,oGAAoG,GAAG,oBAAoB,EAAE,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,gBAAgB,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,aAAa,YAAY,CAAC,EAAE,SAAS,iBAAiB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,OAAO,gIAAgI,eAAe,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,YAAY,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,OAAO,gIAAgI,YAAY,CAAC,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,OAAO,MAAM,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,+EAA+E,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,wEAAwE,QAAQ,mBAAmB,GAAG,qBAAqB,EAAE,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,gBAAgB,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,aAAa,YAAY,CAAC,EAAE,SAAS,iBAAiB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,OAAO,gIAAgI,eAAe,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,YAAY,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,OAAO,gIAAgI,YAAY,CAAC,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,OAAO,MAAM,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,sFAAsF,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,SAAS,cAAc,OAAO,8BAA8B,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,6EAA6E,GAAG,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,SAAS,idAAid,QAAQ,kGAAkG,CAAC,EAAE,GAAG,EAAE,SAAS,oDAAoD,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,idAAid,QAAQ,kGAAkG,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,mBAAmB,eAAe,EAAE,KAAK,EAAE,QAAQ,6EAA6E,GAAG,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,SAAS,KAAK,OAAO,KAAK,QAAQ,0DAA0D,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,SAAS,8BAA8B,OAAO,mBAAmB,QAAQ,0DAA0D,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,mGAAmG,GAAG,eAAe,EAAE,SAAS,UAAU,QAAQ,+EAA+E,GAAG,sBAAsB,EAAE,YAAY,CAAC,EAAE,WAAW,4BAA4B,GAAG,EAAE,SAAS,kDAAkD,QAAQ,qCAAqC,CAAC,EAAE,GAAG,4BAA4B,EAAE,SAAS,2EAA2E,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,4BAA4B,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,kBAAkB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,SAAS,uEAAuE,QAAQ,kEAAkE,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,8DAA8D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iBAAiB,EAAE,GAAG,OAAO,yPAAyP,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,kEAAkE,EAAE,EAAE,CAAC,EAAE,GAAG,kCAAkC,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,kBAAkB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,SAAS,uEAAuE,QAAQ,kEAAkE,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,0BAA0B,EAAE,SAAS,qEAAqE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,sBAAsB,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,8DAA8D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iBAAiB,EAAE,GAAG,OAAO,oKAAoK,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,+EAA+E,EAAE,EAAE,CAAC,EAAE,GAAG,oBAAoB,EAAE,YAAY,CAAC,EAAE,WAAW,0BAA0B,GAAG,EAAE,SAAS,kDAAkD,QAAQ,qCAAqC,CAAC,EAAE,GAAG,cAAc,EAAE,YAAY,CAAC,EAAE,SAAS,idAAid,OAAO,iCAAiC,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,uIAAuI,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iBAAiB,EAAE,GAAG,OAAO,uhBAAuhB,eAAe,EAAE,KAAK,EAAE,QAAQ,iBAAiB,GAAG,KAAK,EAAE,QAAQ,yFAAyF,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,SAAS,cAAc,YAAY,EAAE,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,UAAU,YAAY,EAAE,KAAK,EAAE,QAAQ,+EAA+E,EAAE,GAAG,OAAO,UAAU,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,eAAe,EAAE,SAAS,QAAQ,QAAQ,6EAA6E,GAAG,eAAe,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,+EAA+E,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,6EAA6E,GAAG,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,SAAS,mGAAmG,GAAG,iBAAiB,EAAE,SAAS,gBAAgB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yFAAyF,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,cAAc,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,4DAA4D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oFAAoF,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,sFAAsF,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,SAAS,cAAc,OAAO,8BAA8B,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,6EAA6E,GAAG,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,SAAS,idAAid,QAAQ,kGAAkG,CAAC,EAAE,GAAG,EAAE,SAAS,oDAAoD,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,idAAid,QAAQ,kGAAkG,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,mBAAmB,eAAe,EAAE,KAAK,EAAE,QAAQ,6EAA6E,GAAG,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,CAAC,EAAE,GAAG,eAAe,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,UAAU,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,UAAU,EAAE,SAAS,4DAA4D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,gIAAgI,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,wBAAwB,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8FAA8F,EAAE,GAAG,OAAO,wIAAwI,YAAY,CAAC,EAAE,SAAS,oDAAoD,OAAO,6BAA6B,eAAe,EAAE,KAAK,EAAE,QAAQ,yFAAyF,GAAG,KAAK,EAAE,QAAQ,UAAU,EAAE,EAAE,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,oBAAoB,EAAE,YAAY,CAAC,EAAE,SAAS,iBAAiB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,UAAU,EAAE,GAAG,OAAO,gIAAgI,YAAY,CAAC,EAAE,SAAS,0DAA0D,OAAO,yBAAyB,eAAe,EAAE,KAAK,EAAE,QAAQ,+EAA+E,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,EAAE,GAAG,EAAE,SAAS,YAAY,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,YAAY,OAAO,qEAAqE,eAAe,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,sBAAsB,EAAE,EAAE,GAAG,EAAE,SAAS,0EAA0E,OAAO,gIAAgI,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,6DAA6D,QAAQ,sBAAsB,CAAC,EAAE,GAAG,oBAAoB,EAAE,SAAS,aAAa,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,aAAa,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,cAAc,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,4DAA4D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oFAAoF,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,sBAAsB,EAAE,YAAY,CAAC,EAAE,SAAS,wLAAwL,OAAO,+CAA+C,eAAe,EAAE,KAAK,EAAE,QAAQ,yFAAyF,EAAE,EAAE,GAAG,EAAE,SAAS,wDAAwD,OAAO,gIAAgI,YAAY,CAAC,EAAE,SAAS,wDAAwD,OAAO,cAAc,eAAe,EAAE,KAAK,EAAE,QAAQ,UAAU,EAAE,EAAE,GAAG,EAAE,SAAS,oDAAoD,OAAO,YAAY,eAAe,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,EAAE,GAAG,EAAE,SAAS,gDAAgD,OAAO,gIAAgI,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,oBAAoB,EAAE,YAAY,CAAC,EAAE,SAAS,iBAAiB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,UAAU,EAAE,GAAG,OAAO,gIAAgI,YAAY,CAAC,EAAE,SAAS,0DAA0D,OAAO,yBAAyB,eAAe,EAAE,KAAK,EAAE,QAAQ,+EAA+E,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,EAAE,GAAG,EAAE,SAAS,YAAY,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,YAAY,OAAO,qEAAqE,eAAe,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,sBAAsB,EAAE,EAAE,GAAG,EAAE,SAAS,0EAA0E,OAAO,gIAAgI,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,6DAA6D,QAAQ,sBAAsB,CAAC,EAAE,GAAG,oBAAoB,EAAE,SAAS,gBAAgB,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,aAAa,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,OAAO,UAAU,GAAG,QAAQ,EAAE,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,cAAc,EAAE,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,0BAA0B,QAAQ,kBAAkB,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,WAAW,0BAA0B,GAAG,EAAE,SAAS,kDAAkD,QAAQ,+EAA+E,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,SAAS,cAAc,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,eAAe,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,aAAa,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kBAAkB,EAAE,GAAG,OAAO,cAAc,eAAe,EAAE,KAAK,EAAE,QAAQ,kBAAkB,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,kDAAkD,OAAO,4DAA4D,eAAe,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,kBAAkB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,gDAAgD,OAAO,YAAY,eAAe,EAAE,KAAK,EAAE,QAAQ,kBAAkB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,gDAAgD,OAAO,kBAAkB,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,eAAe,GAAG,KAAK,EAAE,QAAQ,eAAe,EAAE,GAAG,SAAS,6BAA6B,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,SAAS,mZAAmZ,OAAO,oDAAoD,eAAe,EAAE,KAAK,EAAE,QAAQ,8FAA8F,GAAG,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,EAAE,SAAS,4EAA4E,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,2IAA2I,eAAe,EAAE,KAAK,EAAE,QAAQ,8FAA8F,GAAG,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,eAAe,YAAY,EAAE,KAAK,EAAE,QAAQ,kBAAkB,EAAE,GAAG,OAAO,cAAc,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,SAAS,sGAAsG,OAAO,qEAAqE,eAAe,EAAE,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,EAAE,SAAS,0IAA0I,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,OAAO,qEAAqE,eAAe,EAAE,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,OAAO,iEAAiE,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,CAAC,EAAE,SAAS,4DAA4D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,UAAU,EAAE,GAAG,OAAO,idAAid,eAAe,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,SAAS,KAAK,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6EAA6E,GAAG,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,SAAS,mCAAmC,GAAG,EAAE,SAAS,0DAA0D,QAAQ,6EAA6E,CAAC,EAAE,GAAG,WAAW,EAAE,uBAAuB,MAAM,SAAS,gEAAgE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,UAAU,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,yGAAyG,OAAO,idAAid,eAAe,EAAE,KAAK,EAAE,QAAQ,yFAAyF,EAAE,EAAE,CAAC,EAAE,GAAG,WAAW,EAAE,SAAS,aAAa,YAAY,EAAE,KAAK,EAAE,QAAQ,kBAAkB,EAAE,GAAG,OAAO,cAAc,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,SAAS,eAAe,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kBAAkB,EAAE,GAAG,OAAO,cAAc,eAAe,EAAE,KAAK,EAAE,QAAQ,kBAAkB,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,sDAAsD,OAAO,YAAY,eAAe,EAAE,KAAK,EAAE,QAAQ,kBAAkB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,gDAAgD,OAAO,kBAAkB,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,QAAQ,EAAE,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,gBAAgB,QAAQ,6EAA6E,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,SAAS,67BAA67B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kDAAkD,GAAG,KAAK,EAAE,QAAQ,kFAAkF,GAAG,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,OAAO,8qBAA8qB,eAAe,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,4BAA4B,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,SAAS,+hBAA+hB,YAAY,EAAE,KAAK,EAAE,QAAQ,0BAA0B,GAAG,KAAK,EAAE,QAAQ,kGAAkG,GAAG,KAAK,EAAE,QAAQ,UAAU,EAAE,GAAG,OAAO,iEAAiE,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,gBAAgB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yFAAyF,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,KAAK,YAAY,EAAE,KAAK,EAAE,QAAQ,sFAAsF,EAAE,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,SAAS,YAAY,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,6EAA6E,GAAG,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,SAAS,idAAid,QAAQ,kGAAkG,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,6EAA6E,GAAG,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,CAAC,EAAE,SAAS,gCAAgC,QAAQ,oFAAoF,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,SAAS,KAAK,QAAQ,6EAA6E,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,0BAA0B,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,OAAO,YAAY,EAAE,KAAK,EAAE,QAAQ,sFAAsF,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,SAAS,cAAc,OAAO,8BAA8B,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,6EAA6E,GAAG,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,SAAS,idAAid,QAAQ,kGAAkG,CAAC,EAAE,GAAG,EAAE,SAAS,oDAAoD,OAAO,uBAAuB,eAAe,EAAE,KAAK,EAAE,QAAQ,oFAAoF,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,idAAid,QAAQ,kGAAkG,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,mBAAmB,eAAe,EAAE,KAAK,EAAE,QAAQ,6EAA6E,GAAG,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,wEAAwE,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,6EAA6E,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,kEAAkE,EAAE,GAAG,SAAS,iDAAiD,GAAG,mBAAmB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,kDAAkD,GAAG,KAAK,EAAE,QAAQ,kEAAkE,EAAE,GAAG,SAAS,4dAA4d,EAAE,GAAG,aAAa,eAAe,CAAC;AAC34jE,IAAI,QAAQ;AAAA,EACV;AACF;", "names": []}