import {
  handlebars
} from "./chunk-5TULGCP7.js";
import "./chunk-5WCIUZ4S.js";
import {
  typescript
} from "./chunk-YIYGYI63.js";
import "./chunk-ESK7UDUX.js";
import "./chunk-B3KVBTAC.js";
import "./chunk-2DKB27CY.js";
import "./chunk-2LSFTFF7.js";

// node_modules/.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/glimmer-ts.mjs
var lang = Object.freeze({ "displayName": "Glimmer TS", "injections": { "L:source.gts -comment -string": { "patterns": [{ "begin": "\\s*(<)(template)\\s*(>)", "beginCaptures": { "1": { "name": "punctuation.definition.tag.html" }, "2": { "name": "entity.name.tag.other.html" }, "3": { "name": "punctuation.definition.tag.html" } }, "end": "(</)(template)(>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.html" }, "2": { "name": "entity.name.tag.other.html" }, "3": { "name": "punctuation.definition.tag.html" } }, "name": "meta.js.embeddedTemplateWithoutArgs", "patterns": [{ "include": "text.html.handlebars" }] }, { "begin": "(<)(template)", "beginCaptures": { "1": { "name": "punctuation.definition.tag.html" }, "2": { "name": "entity.name.tag.other.html" } }, "end": "(</)(template)(>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.html" }, "2": { "name": "entity.name.tag.other.html" }, "3": { "name": "punctuation.definition.tag.html" } }, "name": "meta.js.embeddedTemplateWithArgs", "patterns": [{ "begin": "(?<=\\<template)", "end": "(?=\\>)", "patterns": [{ "include": "text.html.handlebars#tag-stuff" }] }, { "begin": "(>)", "beginCaptures": { "1": { "name": "punctuation.definition.tag.end.js" } }, "contentName": "meta.html.embedded.block", "end": "(?=</template>)", "patterns": [{ "include": "text.html.handlebars" }] }] }] } }, "name": "glimmer-ts", "patterns": [{ "include": "source.ts" }], "scopeName": "source.gts", "embeddedLangs": ["typescript", "handlebars"], "aliases": ["gts"] });
var glimmerTs = [
  ...typescript,
  ...handlebars,
  lang
];
export {
  glimmerTs as default
};
//# sourceMappingURL=glimmer-ts-TGELMBN5.js.map
