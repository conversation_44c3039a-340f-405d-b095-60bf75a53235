{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/verilog.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Verilog\", \"fileTypes\": [\"v\", \"vh\"], \"name\": \"verilog\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#module_pattern\" }, { \"include\": \"#keywords\" }, { \"include\": \"#constants\" }, { \"include\": \"#strings\" }, { \"include\": \"#operators\" }], \"repository\": { \"comments\": { \"patterns\": [{ \"begin\": \"(^[ \\\\t]+)?(?=//)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.verilog\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"//\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.verilog\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.double-slash.verilog\" }] }, { \"begin\": \"/\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.block.c-style.verilog\" }] }, \"constants\": { \"patterns\": [{ \"match\": \"`(?!(celldefine|endcelldefine|default_nettype|define|undef|ifdef|ifndef|else|endif|include|resetall|timescale|unconnected_drive|nounconnected_drive))[a-z_A-Z][a-zA-Z0-9_$]*\", \"name\": \"variable.other.constant.verilog\" }, { \"match\": \"[0-9]*'[bBoOdDhH][a-fA-F0-9_xXzZ]+\\\\b\", \"name\": \"constant.numeric.sized_integer.verilog\" }, { \"captures\": { \"1\": { \"name\": \"constant.numeric.integer.verilog\" }, \"2\": { \"name\": \"punctuation.separator.range.verilog\" }, \"3\": { \"name\": \"constant.numeric.integer.verilog\" } }, \"match\": \"\\\\b(\\\\d+)(:)(\\\\d+)\\\\b\", \"name\": \"meta.block.numeric.range.verilog\" }, { \"match\": \"\\\\b\\\\d[\\\\d_]*(?i:e\\\\d+)?\\\\b\", \"name\": \"constant.numeric.integer.verilog\" }, { \"match\": \"\\\\b\\\\d+\\\\.\\\\d+(?i:e\\\\d+)?\\\\b\", \"name\": \"constant.numeric.real.verilog\" }, { \"match\": \"#\\\\d+\", \"name\": \"constant.numeric.delay.verilog\" }, { \"match\": \"\\\\b[01xXzZ]+\\\\b\", \"name\": \"constant.numeric.logic.verilog\" }] }, \"instantiation_patterns\": { \"patterns\": [{ \"include\": \"#keywords\" }, { \"begin\": \"^\\\\s*([a-zA-Z][a-zA-Z0-9_]*)\\\\s+([a-zA-Z][a-zA-Z0-9_]*)(?<!begin|if)\\\\s*(?=\\\\(|$)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.module.reference.verilog\" }, \"2\": { \"name\": \"entity.name.tag.module.identifier.verilog\" } }, \"end\": \";\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.expression.verilog\" } }, \"name\": \"meta.block.instantiation.parameterless.verilog\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#constants\" }, { \"include\": \"#strings\" }] }, { \"begin\": \"^\\\\s*([a-zA-Z][a-zA-Z0-9_]*)\\\\s*(#)(?=\\\\s*\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.tag.module.reference.verilog\" } }, \"end\": \";\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.expression.verilog\" } }, \"name\": \"meta.block.instantiation.with.parameters.verilog\", \"patterns\": [{ \"include\": \"#parenthetical_list\" }, { \"match\": \"[a-zA-Z][a-zA-Z0-9_]*\", \"name\": \"entity.name.tag.module.identifier.verilog\" }] }] }, \"keywords\": { \"patterns\": [{ \"match\": \"\\\\b(always|and|assign|attribute|begin|buf|bufif0|bufif1|case[xz]?|cmos|deassign|default|defparam|disable|edge|else|end(attribute|case|function|generate|module|primitive|specify|table|task)?|event|for|force|forever|fork|function|generate|genvar|highz(01)|if(none)?|initial|inout|input|integer|join|localparam|medium|module|large|macromodule|nand|negedge|nmos|nor|not|notif(01)|or|output|parameter|pmos|posedge|primitive|pull0|pull1|pulldown|pullup|rcmos|real|realtime|reg|release|repeat|rnmos|rpmos|rtran|rtranif(01)|scalared|signed|small|specify|specparam|strength|strong0|strong1|supply0|supply1|table|task|time|tran|tranif(01)|tri(01)?|tri(and|or|reg)|unsigned|vectored|wait|wand|weak(01)|while|wire|wor|xnor|xor)\\\\b\", \"name\": \"keyword.other.verilog\" }, { \"match\": \"^\\\\s*`((cell)?define|default_(decay_time|nettype|trireg_strength)|delay_mode_(path|unit|zero)|ifdef|ifndef|include|end(if|celldefine)|else|(no)?unconnected_drive|resetall|timescale|undef)\\\\b\", \"name\": \"keyword.other.compiler.directive.verilog\" }, { \"match\": \"\\\\$(f(open|close)|readmem(b|h)|timeformat|printtimescale|stop|finish|(s|real)?time|realtobits|bitstoreal|rtoi|itor|(f)?(display|write(h|b)))\\\\b\", \"name\": \"support.function.system.console.tasks.verilog\" }, { \"match\": \"\\\\$(random|dist_(chi_square|erlang|exponential|normal|poisson|t|uniform))\\\\b\", \"name\": \"support.function.system.random_number.tasks.verilog\" }, { \"match\": \"\\\\$((a)?sync\\\\$((n)?and|(n)or)\\\\$(array|plane))\\\\b\", \"name\": \"support.function.system.pld_modeling.tasks.verilog\" }, { \"match\": \"\\\\$(q_(initialize|add|remove|full|exam))\\\\b\", \"name\": \"support.function.system.stochastic.tasks.verilog\" }, { \"match\": \"\\\\$(hold|nochange|period|recovery|setup(hold)?|skew|width)\\\\b\", \"name\": \"support.function.system.timing.tasks.verilog\" }, { \"match\": \"\\\\$(dump(file|vars|off|on|all|limit|flush))\\\\b\", \"name\": \"support.function.system.vcd.tasks.verilog\" }, { \"match\": \"\\\\$(countdrivers|list|input|scope|showscopes|(no)?(key|log)|reset(_count|_value)?|(inc)?save|restart|showvars|getpattern|sreadmem(b|h)|scale)\", \"name\": \"support.function.non-standard.tasks.verilog\" }] }, \"module_pattern\": { \"patterns\": [{ \"begin\": \"\\\\b(module)\\\\s+([a-zA-Z][a-zA-Z0-9_]*)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.module.verilog\" }, \"2\": { \"name\": \"entity.name.type.module.verilog\" } }, \"end\": \"\\\\bendmodule\\\\b\", \"endCaptures\": { \"0\": { \"name\": \"storage.type.module.verilog\" } }, \"name\": \"meta.block.module.verilog\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#keywords\" }, { \"include\": \"#constants\" }, { \"include\": \"#strings\" }, { \"include\": \"#instantiation_patterns\" }, { \"include\": \"#operators\" }] }] }, \"operators\": { \"patterns\": [{ \"match\": \"\\\\+|-|\\\\*|/|%|(<|>)=?|(!|=)?==?|!|&&?|\\\\|\\\\|?|\\\\^?~|~\\\\^?\", \"name\": \"keyword.operator.verilog\" }] }, \"parenthetical_list\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.list.verilog\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.list.verilog\" } }, \"name\": \"meta.block.parenthetical_list.verilog\", \"patterns\": [{ \"include\": \"#parenthetical_list\" }, { \"include\": \"#comments\" }, { \"include\": \"#keywords\" }, { \"include\": \"#constants\" }, { \"include\": \"#strings\" }] }] }, \"strings\": { \"patterns\": [{ \"begin\": '\"', \"end\": '\"', \"name\": \"string.quoted.double.verilog\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.verilog\" }] }] } }, \"scopeName\": \"source.verilog\" });\nvar verilog = [\n  lang\n];\n\nexport { verilog as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,WAAW,aAAa,CAAC,KAAK,IAAI,GAAG,QAAQ,WAAW,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,aAAa,CAAC,GAAG,cAAc,EAAE,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,qBAAqB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,SAAS,MAAM,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,OAAO,QAAQ,oCAAoC,CAAC,EAAE,GAAG,EAAE,SAAS,QAAQ,OAAO,QAAQ,QAAQ,gCAAgC,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,SAAS,gLAAgL,QAAQ,kCAAkC,GAAG,EAAE,SAAS,yCAAyC,QAAQ,yCAAyC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,yBAAyB,QAAQ,mCAAmC,GAAG,EAAE,SAAS,+BAA+B,QAAQ,mCAAmC,GAAG,EAAE,SAAS,gCAAgC,QAAQ,gCAAgC,GAAG,EAAE,SAAS,SAAS,QAAQ,iCAAiC,GAAG,EAAE,SAAS,mBAAmB,QAAQ,iCAAiC,CAAC,EAAE,GAAG,0BAA0B,EAAE,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,SAAS,qFAAqF,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,QAAQ,kDAAkD,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,EAAE,SAAS,kDAAkD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,QAAQ,oDAAoD,YAAY,CAAC,EAAE,WAAW,sBAAsB,GAAG,EAAE,SAAS,yBAAyB,QAAQ,4CAA4C,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,ktBAAktB,QAAQ,wBAAwB,GAAG,EAAE,SAAS,kMAAkM,QAAQ,2CAA2C,GAAG,EAAE,SAAS,mJAAmJ,QAAQ,gDAAgD,GAAG,EAAE,SAAS,gFAAgF,QAAQ,sDAAsD,GAAG,EAAE,SAAS,sDAAsD,QAAQ,qDAAqD,GAAG,EAAE,SAAS,+CAA+C,QAAQ,mDAAmD,GAAG,EAAE,SAAS,iEAAiE,QAAQ,+CAA+C,GAAG,EAAE,SAAS,kDAAkD,QAAQ,4CAA4C,GAAG,EAAE,SAAS,iJAAiJ,QAAQ,8CAA8C,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,CAAC,EAAE,SAAS,0CAA0C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,mBAAmB,eAAe,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,SAAS,6DAA6D,QAAQ,2BAA2B,CAAC,EAAE,GAAG,sBAAsB,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,QAAQ,yCAAyC,YAAY,CAAC,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,CAAC,EAAE,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,SAAS,KAAK,OAAO,KAAK,QAAQ,gCAAgC,YAAY,CAAC,EAAE,SAAS,SAAS,QAAQ,oCAAoC,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,aAAa,iBAAiB,CAAC;AACv/L,IAAI,UAAU;AAAA,EACZ;AACF;", "names": []}