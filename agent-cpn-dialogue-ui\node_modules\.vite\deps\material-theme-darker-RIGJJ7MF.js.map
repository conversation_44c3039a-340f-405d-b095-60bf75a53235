{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/themes/material-theme-darker.mjs"], "sourcesContent": ["var materialThemeDarker = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBorder\": \"#80CBC4\",\n    \"activityBar.background\": \"#212121\",\n    \"activityBar.border\": \"#21212160\",\n    \"activityBar.dropBackground\": \"#f0717880\",\n    \"activityBar.foreground\": \"#EEFFFF\",\n    \"activityBarBadge.background\": \"#80CBC4\",\n    \"activityBarBadge.foreground\": \"#000000\",\n    \"badge.background\": \"#00000030\",\n    \"badge.foreground\": \"#545454\",\n    \"breadcrumb.activeSelectionForeground\": \"#80CBC4\",\n    \"breadcrumb.background\": \"#212121\",\n    \"breadcrumb.focusForeground\": \"#EEFFFF\",\n    \"breadcrumb.foreground\": \"#676767\",\n    \"breadcrumbPicker.background\": \"#212121\",\n    \"button.background\": \"#61616150\",\n    \"button.foreground\": \"#ffffff\",\n    \"debugConsole.errorForeground\": \"#f07178\",\n    \"debugConsole.infoForeground\": \"#89DDFF\",\n    \"debugConsole.warningForeground\": \"#FFCB6B\",\n    \"debugToolBar.background\": \"#212121\",\n    \"diffEditor.insertedTextBackground\": \"#89DDFF20\",\n    \"diffEditor.removedTextBackground\": \"#ff9cac20\",\n    \"dropdown.background\": \"#212121\",\n    \"dropdown.border\": \"#FFFFFF10\",\n    \"editor.background\": \"#212121\",\n    \"editor.findMatchBackground\": \"#000000\",\n    \"editor.findMatchBorder\": \"#80CBC4\",\n    \"editor.findMatchHighlight\": \"#EEFFFF\",\n    \"editor.findMatchHighlightBackground\": \"#00000050\",\n    \"editor.findMatchHighlightBorder\": \"#ffffff30\",\n    \"editor.findRangeHighlightBackground\": \"#FFCB6B30\",\n    \"editor.foreground\": \"#EEFFFF\",\n    \"editor.lineHighlightBackground\": \"#00000050\",\n    \"editor.lineHighlightBorder\": \"#00000000\",\n    \"editor.rangeHighlightBackground\": \"#FFFFFF0d\",\n    \"editor.selectionBackground\": \"#61616150\",\n    \"editor.selectionHighlightBackground\": \"#FFCC0020\",\n    \"editor.wordHighlightBackground\": \"#ff9cac30\",\n    \"editor.wordHighlightStrongBackground\": \"#C3E88D30\",\n    \"editorBracketMatch.background\": \"#212121\",\n    \"editorBracketMatch.border\": \"#FFCC0050\",\n    \"editorCursor.foreground\": \"#FFCC00\",\n    \"editorError.foreground\": \"#f0717870\",\n    \"editorGroup.border\": \"#00000030\",\n    \"editorGroup.dropBackground\": \"#f0717880\",\n    \"editorGroup.focusedEmptyBorder\": \"#f07178\",\n    \"editorGroupHeader.tabsBackground\": \"#212121\",\n    \"editorGutter.addedBackground\": \"#C3E88D60\",\n    \"editorGutter.deletedBackground\": \"#f0717860\",\n    \"editorGutter.modifiedBackground\": \"#82AAFF60\",\n    \"editorHoverWidget.background\": \"#212121\",\n    \"editorHoverWidget.border\": \"#FFFFFF10\",\n    \"editorIndentGuide.activeBackground\": \"#424242\",\n    \"editorIndentGuide.background\": \"#42424270\",\n    \"editorInfo.foreground\": \"#82AAFF70\",\n    \"editorLineNumber.activeForeground\": \"#676767\",\n    \"editorLineNumber.foreground\": \"#424242\",\n    \"editorLink.activeForeground\": \"#EEFFFF\",\n    \"editorMarkerNavigation.background\": \"#EEFFFF05\",\n    \"editorOverviewRuler.border\": \"#212121\",\n    \"editorOverviewRuler.errorForeground\": \"#f0717840\",\n    \"editorOverviewRuler.findMatchForeground\": \"#80CBC4\",\n    \"editorOverviewRuler.infoForeground\": \"#82AAFF40\",\n    \"editorOverviewRuler.warningForeground\": \"#FFCB6B40\",\n    \"editorRuler.foreground\": \"#424242\",\n    \"editorSuggestWidget.background\": \"#212121\",\n    \"editorSuggestWidget.border\": \"#FFFFFF10\",\n    \"editorSuggestWidget.foreground\": \"#EEFFFF\",\n    \"editorSuggestWidget.highlightForeground\": \"#80CBC4\",\n    \"editorSuggestWidget.selectedBackground\": \"#00000050\",\n    \"editorWarning.foreground\": \"#FFCB6B70\",\n    \"editorWhitespace.foreground\": \"#EEFFFF40\",\n    \"editorWidget.background\": \"#212121\",\n    \"editorWidget.border\": \"#80CBC4\",\n    \"editorWidget.resizeBorder\": \"#80CBC4\",\n    \"extensionBadge.remoteForeground\": \"#EEFFFF\",\n    \"extensionButton.prominentBackground\": \"#C3E88D90\",\n    \"extensionButton.prominentForeground\": \"#EEFFFF\",\n    \"extensionButton.prominentHoverBackground\": \"#C3E88D\",\n    \"focusBorder\": \"#FFFFFF00\",\n    \"foreground\": \"#EEFFFF\",\n    \"gitDecoration.conflictingResourceForeground\": \"#FFCB6B90\",\n    \"gitDecoration.deletedResourceForeground\": \"#f0717890\",\n    \"gitDecoration.ignoredResourceForeground\": \"#67676790\",\n    \"gitDecoration.modifiedResourceForeground\": \"#82AAFF90\",\n    \"gitDecoration.untrackedResourceForeground\": \"#C3E88D90\",\n    \"input.background\": \"#2B2B2B\",\n    \"input.border\": \"#FFFFFF10\",\n    \"input.foreground\": \"#EEFFFF\",\n    \"input.placeholderForeground\": \"#EEFFFF60\",\n    \"inputOption.activeBackground\": \"#EEFFFF30\",\n    \"inputOption.activeBorder\": \"#EEFFFF30\",\n    \"inputValidation.errorBorder\": \"#f07178\",\n    \"inputValidation.infoBorder\": \"#82AAFF\",\n    \"inputValidation.warningBorder\": \"#FFCB6B\",\n    \"list.activeSelectionBackground\": \"#212121\",\n    \"list.activeSelectionForeground\": \"#80CBC4\",\n    \"list.dropBackground\": \"#f0717880\",\n    \"list.focusBackground\": \"#EEFFFF20\",\n    \"list.focusForeground\": \"#EEFFFF\",\n    \"list.highlightForeground\": \"#80CBC4\",\n    \"list.hoverBackground\": \"#212121\",\n    \"list.hoverForeground\": \"#FFFFFF\",\n    \"list.inactiveSelectionBackground\": \"#00000030\",\n    \"list.inactiveSelectionForeground\": \"#80CBC4\",\n    \"listFilterWidget.background\": \"#00000030\",\n    \"listFilterWidget.noMatchesOutline\": \"#00000030\",\n    \"listFilterWidget.outline\": \"#00000030\",\n    \"menu.background\": \"#212121\",\n    \"menu.foreground\": \"#EEFFFF\",\n    \"menu.selectionBackground\": \"#00000050\",\n    \"menu.selectionBorder\": \"#00000030\",\n    \"menu.selectionForeground\": \"#80CBC4\",\n    \"menu.separatorBackground\": \"#EEFFFF\",\n    \"menubar.selectionBackground\": \"#00000030\",\n    \"menubar.selectionBorder\": \"#00000030\",\n    \"menubar.selectionForeground\": \"#80CBC4\",\n    \"notebook.focusedCellBorder\": \"#80CBC4\",\n    \"notebook.inactiveFocusedCellBorder\": \"#80CBC450\",\n    \"notificationLink.foreground\": \"#80CBC4\",\n    \"notifications.background\": \"#212121\",\n    \"notifications.foreground\": \"#EEFFFF\",\n    \"panel.background\": \"#212121\",\n    \"panel.border\": \"#21212160\",\n    \"panel.dropBackground\": \"#EEFFFF\",\n    \"panelTitle.activeBorder\": \"#80CBC4\",\n    \"panelTitle.activeForeground\": \"#FFFFFF\",\n    \"panelTitle.inactiveForeground\": \"#EEFFFF\",\n    \"peekView.border\": \"#00000030\",\n    \"peekViewEditor.background\": \"#EEFFFF05\",\n    \"peekViewEditor.matchHighlightBackground\": \"#61616150\",\n    \"peekViewEditorGutter.background\": \"#EEFFFF05\",\n    \"peekViewResult.background\": \"#EEFFFF05\",\n    \"peekViewResult.matchHighlightBackground\": \"#61616150\",\n    \"peekViewResult.selectionBackground\": \"#67676770\",\n    \"peekViewTitle.background\": \"#EEFFFF05\",\n    \"peekViewTitleDescription.foreground\": \"#EEFFFF60\",\n    \"pickerGroup.border\": \"#FFFFFF1a\",\n    \"pickerGroup.foreground\": \"#80CBC4\",\n    \"progressBar.background\": \"#80CBC4\",\n    \"quickInput.background\": \"#212121\",\n    \"quickInput.foreground\": \"#676767\",\n    \"quickInput.list.focusBackground\": \"#EEFFFF20\",\n    \"sash.hoverBorder\": \"#80CBC450\",\n    \"scrollbar.shadow\": \"#00000030\",\n    \"scrollbarSlider.activeBackground\": \"#80CBC4\",\n    \"scrollbarSlider.background\": \"#EEFFFF20\",\n    \"scrollbarSlider.hoverBackground\": \"#EEFFFF10\",\n    \"selection.background\": \"#00000080\",\n    \"settings.checkboxBackground\": \"#212121\",\n    \"settings.checkboxForeground\": \"#EEFFFF\",\n    \"settings.dropdownBackground\": \"#212121\",\n    \"settings.dropdownForeground\": \"#EEFFFF\",\n    \"settings.headerForeground\": \"#80CBC4\",\n    \"settings.modifiedItemIndicator\": \"#80CBC4\",\n    \"settings.numberInputBackground\": \"#212121\",\n    \"settings.numberInputForeground\": \"#EEFFFF\",\n    \"settings.textInputBackground\": \"#212121\",\n    \"settings.textInputForeground\": \"#EEFFFF\",\n    \"sideBar.background\": \"#212121\",\n    \"sideBar.border\": \"#21212160\",\n    \"sideBar.foreground\": \"#676767\",\n    \"sideBarSectionHeader.background\": \"#212121\",\n    \"sideBarSectionHeader.border\": \"#21212160\",\n    \"sideBarTitle.foreground\": \"#EEFFFF\",\n    \"statusBar.background\": \"#212121\",\n    \"statusBar.border\": \"#21212160\",\n    \"statusBar.debuggingBackground\": \"#C792EA\",\n    \"statusBar.debuggingForeground\": \"#ffffff\",\n    \"statusBar.foreground\": \"#616161\",\n    \"statusBar.noFolderBackground\": \"#212121\",\n    \"statusBarItem.activeBackground\": \"#f0717880\",\n    \"statusBarItem.hoverBackground\": \"#54545420\",\n    \"statusBarItem.remoteBackground\": \"#80CBC4\",\n    \"statusBarItem.remoteForeground\": \"#000000\",\n    \"tab.activeBackground\": \"#212121\",\n    \"tab.activeBorder\": \"#80CBC4\",\n    \"tab.activeForeground\": \"#FFFFFF\",\n    \"tab.activeModifiedBorder\": \"#676767\",\n    \"tab.border\": \"#212121\",\n    \"tab.inactiveBackground\": \"#212121\",\n    \"tab.inactiveForeground\": \"#676767\",\n    \"tab.inactiveModifiedBorder\": \"#904348\",\n    \"tab.unfocusedActiveBorder\": \"#545454\",\n    \"tab.unfocusedActiveForeground\": \"#EEFFFF\",\n    \"tab.unfocusedActiveModifiedBorder\": \"#c05a60\",\n    \"tab.unfocusedInactiveModifiedBorder\": \"#904348\",\n    \"terminal.ansiBlack\": \"#000000\",\n    \"terminal.ansiBlue\": \"#82AAFF\",\n    \"terminal.ansiBrightBlack\": \"#545454\",\n    \"terminal.ansiBrightBlue\": \"#82AAFF\",\n    \"terminal.ansiBrightCyan\": \"#89DDFF\",\n    \"terminal.ansiBrightGreen\": \"#C3E88D\",\n    \"terminal.ansiBrightMagenta\": \"#C792EA\",\n    \"terminal.ansiBrightRed\": \"#f07178\",\n    \"terminal.ansiBrightWhite\": \"#ffffff\",\n    \"terminal.ansiBrightYellow\": \"#FFCB6B\",\n    \"terminal.ansiCyan\": \"#89DDFF\",\n    \"terminal.ansiGreen\": \"#C3E88D\",\n    \"terminal.ansiMagenta\": \"#C792EA\",\n    \"terminal.ansiRed\": \"#f07178\",\n    \"terminal.ansiWhite\": \"#ffffff\",\n    \"terminal.ansiYellow\": \"#FFCB6B\",\n    \"terminalCursor.background\": \"#000000\",\n    \"terminalCursor.foreground\": \"#FFCB6B\",\n    \"textLink.activeForeground\": \"#EEFFFF\",\n    \"textLink.foreground\": \"#80CBC4\",\n    \"titleBar.activeBackground\": \"#212121\",\n    \"titleBar.activeForeground\": \"#EEFFFF\",\n    \"titleBar.border\": \"#21212160\",\n    \"titleBar.inactiveBackground\": \"#212121\",\n    \"titleBar.inactiveForeground\": \"#676767\",\n    \"tree.indentGuidesStroke\": \"#424242\",\n    \"widget.shadow\": \"#00000030\"\n  },\n  \"displayName\": \"Material Theme Darker\",\n  \"name\": \"material-theme-darker\",\n  \"semanticHighlighting\": true,\n  \"tokenColors\": [\n    {\n      \"settings\": {\n        \"background\": \"#212121\",\n        \"foreground\": \"#EEFFFF\"\n      }\n    },\n    {\n      \"scope\": \"string\",\n      \"settings\": {\n        \"foreground\": \"#C3E88D\"\n      }\n    },\n    {\n      \"scope\": \"punctuation, constant.other.symbol\",\n      \"settings\": {\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape, text.html constant.character.entity.named\",\n      \"settings\": {\n        \"foreground\": \"#EEFFFF\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.boolean\",\n      \"settings\": {\n        \"foreground\": \"#ff9cac\"\n      }\n    },\n    {\n      \"scope\": \"constant.numeric\",\n      \"settings\": {\n        \"foreground\": \"#F78C6C\"\n      }\n    },\n    {\n      \"scope\": \"variable, variable.parameter, support.variable, variable.language, support.constant, meta.definition.variable entity.name.function, meta.function-call.arguments\",\n      \"settings\": {\n        \"foreground\": \"#EEFFFF\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other\",\n      \"settings\": {\n        \"foreground\": \"#F78C6C\"\n      }\n    },\n    {\n      \"scope\": \"keyword, modifier, variable.language.this, support.type.object, constant.language\",\n      \"settings\": {\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function, support.function\",\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": \"storage.type, storage.modifier, storage.control\",\n      \"settings\": {\n        \"foreground\": \"#C792EA\"\n      }\n    },\n    {\n      \"scope\": \"support.module, support.node\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"support.type, constant.other.key\",\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type, entity.other.inherited-class, entity.other\",\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": \"comment\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#545454\"\n      }\n    },\n    {\n      \"scope\": \"comment punctuation.definition.comment, string.quoted.docstring\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#545454\"\n      }\n    },\n    {\n      \"scope\": \"punctuation\",\n      \"settings\": {\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"entity.name, entity.name.type.class, support.type, support.class, meta.use\",\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": \"variable.object.property, meta.field.declaration entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"meta.definition.method entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"meta.function entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": \"template.expression.begin, template.expression.end, punctuation.definition.template-expression.begin, punctuation.definition.template-expression.end\",\n      \"settings\": {\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"meta.embedded, source.groovy.embedded, meta.template.expression\",\n      \"settings\": {\n        \"foreground\": \"#EEFFFF\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.yaml\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"meta.object-literal.key, meta.object-literal.key string, support.type.property-name.json\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.json\",\n      \"settings\": {\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.class\",\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.id\",\n      \"settings\": {\n        \"foreground\": \"#F78C6C\"\n      }\n    },\n    {\n      \"scope\": \"source.css entity.name.tag\",\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.css\",\n      \"settings\": {\n        \"foreground\": \"#B2CCD6\"\n      }\n    },\n    {\n      \"scope\": \"meta.tag, punctuation.definition.tag\",\n      \"settings\": {\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name\",\n      \"settings\": {\n        \"foreground\": \"#C792EA\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.entity.html\",\n      \"settings\": {\n        \"foreground\": \"#EEFFFF\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading\",\n      \"settings\": {\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"text.html.markdown meta.link.inline, meta.link.reference\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"text.html.markdown beginning.punctuation.definition.list\",\n      \"settings\": {\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold markup.italic, markup.italic markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"italic bold\",\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"markup.fenced_code.block.markdown punctuation.definition.markdown\",\n      \"settings\": {\n        \"foreground\": \"#C3E88D\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw.string.markdown\",\n      \"settings\": {\n        \"foreground\": \"#C3E88D\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.definition.ini\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.section.group-title.ini\",\n      \"settings\": {\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"source.cs meta.class.identifier storage.type\",\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": \"source.cs meta.method.identifier entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"source.cs meta.method-call meta.method, source.cs entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": \"source.cs storage.type\",\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": \"source.cs meta.method.return-type\",\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": \"source.cs meta.preprocessor\",\n      \"settings\": {\n        \"foreground\": \"#545454\"\n      }\n    },\n    {\n      \"scope\": \"source.cs entity.name.type.namespace\",\n      \"settings\": {\n        \"foreground\": \"#EEFFFF\"\n      }\n    },\n    {\n      \"scope\": \"meta.jsx.children, SXNested\",\n      \"settings\": {\n        \"foreground\": \"#EEFFFF\"\n      }\n    },\n    {\n      \"scope\": \"support.class.component\",\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": \"source.cpp meta.block variable.other\",\n      \"settings\": {\n        \"foreground\": \"#EEFFFF\"\n      }\n    },\n    {\n      \"scope\": \"source.python meta.member.access.python\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"source.python meta.function-call.python, meta.function-call.arguments\",\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": \"meta.block\",\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.call\",\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": \"source.php support.other.namespace, source.php meta.use support.class\",\n      \"settings\": {\n        \"foreground\": \"#EEFFFF\"\n      }\n    },\n    {\n      \"scope\": \"constant.keyword\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"settings\": {\n        \"background\": \"#212121\",\n        \"foreground\": \"#EEFFFF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.placeholder\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.deleted\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inserted\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#C3E88D\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.function.language.special.self.python\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.character.format.placeholder.other.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F78C6C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.quote\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#89DDFF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.fenced_code.block\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#EEFFFF90\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.quote\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff9cac\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#C792EA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FFCB6B\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F78C6C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f07178\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#916b53\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff9cac\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#C792EA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#C3E88D\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { materialThemeDarker as default };\n"], "mappings": ";;;AAAA,IAAI,sBAAsB,OAAO,OAAO;AAAA,EACtC,UAAU;AAAA,IACR,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,sBAAsB;AAAA,IACtB,8BAA8B;AAAA,IAC9B,0BAA0B;AAAA,IAC1B,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,wCAAwC;AAAA,IACxC,yBAAyB;AAAA,IACzB,8BAA8B;AAAA,IAC9B,yBAAyB;AAAA,IACzB,+BAA+B;AAAA,IAC/B,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,gCAAgC;AAAA,IAChC,+BAA+B;AAAA,IAC/B,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,qCAAqC;AAAA,IACrC,oCAAoC;AAAA,IACpC,uBAAuB;AAAA,IACvB,mBAAmB;AAAA,IACnB,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,0BAA0B;AAAA,IAC1B,6BAA6B;AAAA,IAC7B,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,uCAAuC;AAAA,IACvC,qBAAqB;AAAA,IACrB,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,mCAAmC;AAAA,IACnC,8BAA8B;AAAA,IAC9B,uCAAuC;AAAA,IACvC,kCAAkC;AAAA,IAClC,wCAAwC;AAAA,IACxC,iCAAiC;AAAA,IACjC,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,0BAA0B;AAAA,IAC1B,sBAAsB;AAAA,IACtB,8BAA8B;AAAA,IAC9B,kCAAkC;AAAA,IAClC,oCAAoC;AAAA,IACpC,gCAAgC;AAAA,IAChC,kCAAkC;AAAA,IAClC,mCAAmC;AAAA,IACnC,gCAAgC;AAAA,IAChC,4BAA4B;AAAA,IAC5B,sCAAsC;AAAA,IACtC,gCAAgC;AAAA,IAChC,yBAAyB;AAAA,IACzB,qCAAqC;AAAA,IACrC,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,qCAAqC;AAAA,IACrC,8BAA8B;AAAA,IAC9B,uCAAuC;AAAA,IACvC,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,yCAAyC;AAAA,IACzC,0BAA0B;AAAA,IAC1B,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,kCAAkC;AAAA,IAClC,2CAA2C;AAAA,IAC3C,0CAA0C;AAAA,IAC1C,4BAA4B;AAAA,IAC5B,+BAA+B;AAAA,IAC/B,2BAA2B;AAAA,IAC3B,uBAAuB;AAAA,IACvB,6BAA6B;AAAA,IAC7B,mCAAmC;AAAA,IACnC,uCAAuC;AAAA,IACvC,uCAAuC;AAAA,IACvC,4CAA4C;AAAA,IAC5C,eAAe;AAAA,IACf,cAAc;AAAA,IACd,+CAA+C;AAAA,IAC/C,2CAA2C;AAAA,IAC3C,2CAA2C;AAAA,IAC3C,4CAA4C;AAAA,IAC5C,6CAA6C;AAAA,IAC7C,oBAAoB;AAAA,IACpB,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,gCAAgC;AAAA,IAChC,4BAA4B;AAAA,IAC5B,+BAA+B;AAAA,IAC/B,8BAA8B;AAAA,IAC9B,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,uBAAuB;AAAA,IACvB,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,4BAA4B;AAAA,IAC5B,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,oCAAoC;AAAA,IACpC,oCAAoC;AAAA,IACpC,+BAA+B;AAAA,IAC/B,qCAAqC;AAAA,IACrC,4BAA4B;AAAA,IAC5B,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,IACnB,4BAA4B;AAAA,IAC5B,wBAAwB;AAAA,IACxB,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,+BAA+B;AAAA,IAC/B,2BAA2B;AAAA,IAC3B,+BAA+B;AAAA,IAC/B,8BAA8B;AAAA,IAC9B,sCAAsC;AAAA,IACtC,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,oBAAoB;AAAA,IACpB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,IACxB,2BAA2B;AAAA,IAC3B,+BAA+B;AAAA,IAC/B,iCAAiC;AAAA,IACjC,mBAAmB;AAAA,IACnB,6BAA6B;AAAA,IAC7B,2CAA2C;AAAA,IAC3C,mCAAmC;AAAA,IACnC,6BAA6B;AAAA,IAC7B,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,4BAA4B;AAAA,IAC5B,uCAAuC;AAAA,IACvC,sBAAsB;AAAA,IACtB,0BAA0B;AAAA,IAC1B,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,IACzB,mCAAmC;AAAA,IACnC,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,oCAAoC;AAAA,IACpC,8BAA8B;AAAA,IAC9B,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,6BAA6B;AAAA,IAC7B,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,gCAAgC;AAAA,IAChC,gCAAgC;AAAA,IAChC,sBAAsB;AAAA,IACtB,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,mCAAmC;AAAA,IACnC,+BAA+B;AAAA,IAC/B,2BAA2B;AAAA,IAC3B,wBAAwB;AAAA,IACxB,oBAAoB;AAAA,IACpB,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,wBAAwB;AAAA,IACxB,gCAAgC;AAAA,IAChC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,wBAAwB;AAAA,IACxB,oBAAoB;AAAA,IACpB,wBAAwB;AAAA,IACxB,4BAA4B;AAAA,IAC5B,cAAc;AAAA,IACd,0BAA0B;AAAA,IAC1B,0BAA0B;AAAA,IAC1B,8BAA8B;AAAA,IAC9B,6BAA6B;AAAA,IAC7B,iCAAiC;AAAA,IACjC,qCAAqC;AAAA,IACrC,uCAAuC;AAAA,IACvC,sBAAsB;AAAA,IACtB,qBAAqB;AAAA,IACrB,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,4BAA4B;AAAA,IAC5B,8BAA8B;AAAA,IAC9B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,6BAA6B;AAAA,IAC7B,qBAAqB;AAAA,IACrB,sBAAsB;AAAA,IACtB,wBAAwB;AAAA,IACxB,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,uBAAuB;AAAA,IACvB,6BAA6B;AAAA,IAC7B,6BAA6B;AAAA,IAC7B,6BAA6B;AAAA,IAC7B,uBAAuB;AAAA,IACvB,6BAA6B;AAAA,IAC7B,6BAA6B;AAAA,IAC7B,mBAAmB;AAAA,IACnB,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,2BAA2B;AAAA,IAC3B,iBAAiB;AAAA,EACnB;AAAA,EACA,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,wBAAwB;AAAA,EACxB,eAAe;AAAA,IACb;AAAA,MACE,YAAY;AAAA,QACV,cAAc;AAAA,QACd,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,YAAY;AAAA,QACV,cAAc;AAAA,QACd,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AACV,CAAC;", "names": []}