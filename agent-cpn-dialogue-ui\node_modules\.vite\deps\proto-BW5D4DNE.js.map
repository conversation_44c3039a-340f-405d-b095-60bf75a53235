{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/proto.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Protocol Buffer 3\", \"fileTypes\": [\"proto\"], \"name\": \"proto\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#syntax\" }, { \"include\": \"#package\" }, { \"include\": \"#import\" }, { \"include\": \"#optionStmt\" }, { \"include\": \"#message\" }, { \"include\": \"#enum\" }, { \"include\": \"#service\" }], \"repository\": { \"comments\": { \"patterns\": [{ \"begin\": \"/\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.block.proto\" }, { \"begin\": \"//\", \"end\": \"$\\\\n?\", \"name\": \"comment.line.double-slash.proto\" }] }, \"constants\": { \"match\": \"\\\\b(true|false|max|[A-Z_]+)\\\\b\", \"name\": \"constant.language.proto\" }, \"enum\": { \"begin\": \"(enum)(\\\\s+)([A-Za-z][A-Za-z0-9_]*)(\\\\s*)(\\\\{)?\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"3\": { \"name\": \"entity.name.class.proto\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#reserved\" }, { \"include\": \"#optionStmt\" }, { \"include\": \"#comments\" }, { \"begin\": \"([A-Za-z][A-Za-z0-9_]*)\\\\s*(=)\\\\s*(0[xX][0-9a-fA-F]+|[0-9]+)\", \"beginCaptures\": { \"1\": { \"name\": \"variable.other.proto\" }, \"2\": { \"name\": \"keyword.operator.assignment.proto\" }, \"3\": { \"name\": \"constant.numeric.proto\" } }, \"end\": \"(;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.proto\" } }, \"patterns\": [{ \"include\": \"#fieldOptions\" }] }] }, \"field\": { \"begin\": \"\\\\s*(optional|repeated|required)?\\\\s*\\\\b([\\\\w.]+)\\\\s+(\\\\w+)\\\\s*(=)\\\\s*(0[xX][0-9a-fA-F]+|[0-9]+)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.proto\" }, \"2\": { \"name\": \"storage.type.proto\" }, \"3\": { \"name\": \"variable.other.proto\" }, \"4\": { \"name\": \"keyword.operator.assignment.proto\" }, \"5\": { \"name\": \"constant.numeric.proto\" } }, \"end\": \"(;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.proto\" } }, \"patterns\": [{ \"include\": \"#fieldOptions\" }] }, \"fieldOptions\": { \"begin\": \"\\\\[\", \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#constants\" }, { \"include\": \"#number\" }, { \"include\": \"#string\" }, { \"include\": \"#subMsgOption\" }, { \"include\": \"#optionName\" }] }, \"ident\": { \"match\": \"[A-Za-z][A-Za-z0-9_]*\", \"name\": \"entity.name.class.proto\" }, \"import\": { \"captures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"2\": { \"name\": \"keyword.other.proto\" }, \"3\": { \"name\": \"string.quoted.double.proto.import\" }, \"4\": { \"name\": \"punctuation.terminator.proto\" } }, \"match\": '\\\\s*(import)\\\\s+(weak|public)?\\\\s*(\"[^\"]+\")\\\\s*(;)' }, \"kv\": { \"begin\": \"(\\\\w+)\\\\s*(:)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"2\": { \"name\": \"punctuation.separator.key-value.proto\" } }, \"end\": \"(;)|,|(?=[}/_a-zA-Z])\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.proto\" } }, \"patterns\": [{ \"include\": \"#constants\" }, { \"include\": \"#number\" }, { \"include\": \"#string\" }, { \"include\": \"#subMsgOption\" }] }, \"mapfield\": { \"begin\": \"\\\\s*(map)\\\\s*(<)\\\\s*([\\\\w.]+)\\\\s*,\\\\s*([\\\\w.]+)\\\\s*(>)\\\\s+(\\\\w+)\\\\s*(=)\\\\s*(\\\\d+)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.proto\" }, \"2\": { \"name\": \"punctuation.definition.typeparameters.begin.proto\" }, \"3\": { \"name\": \"storage.type.proto\" }, \"4\": { \"name\": \"storage.type.proto\" }, \"5\": { \"name\": \"punctuation.definition.typeparameters.end.proto\" }, \"6\": { \"name\": \"variable.other.proto\" }, \"7\": { \"name\": \"keyword.operator.assignment.proto\" }, \"8\": { \"name\": \"constant.numeric.proto\" } }, \"end\": \"(;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.proto\" } }, \"patterns\": [{ \"include\": \"#fieldOptions\" }] }, \"message\": { \"begin\": \"(message|extend)(\\\\s+)([A-Za-z_][A-Za-z0-9_.]*)(\\\\s*)(\\\\{)?\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"3\": { \"name\": \"entity.name.class.message.proto\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#reserved\" }, { \"include\": \"$self\" }, { \"include\": \"#enum\" }, { \"include\": \"#optionStmt\" }, { \"include\": \"#comments\" }, { \"include\": \"#oneof\" }, { \"include\": \"#field\" }, { \"include\": \"#mapfield\" }] }, \"method\": { \"begin\": \"(rpc)\\\\s+([A-Za-z][A-Za-z0-9_]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"2\": { \"name\": \"entity.name.function\" } }, \"end\": \"\\\\}|(;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.proto\" } }, \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#optionStmt\" }, { \"include\": \"#rpcKeywords\" }, { \"include\": \"#ident\" }] }, \"number\": { \"match\": \"\\\\b((0(x|X)[0-9a-fA-F]*)|(([0-9]+\\\\.?[0-9]*)|(\\\\.[0-9]+))((e|E)(\\\\+|-)?[0-9]+)?)\\\\b\", \"name\": \"constant.numeric.proto\" }, \"oneof\": { \"begin\": \"(oneof)\\\\s+([A-Za-z][A-Za-z0-9_]*)\\\\s*\\\\{?\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"2\": { \"name\": \"variable.other.proto\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#optionStmt\" }, { \"include\": \"#comments\" }, { \"include\": \"#field\" }] }, \"optionName\": { \"captures\": { \"1\": { \"name\": \"support.other.proto\" }, \"2\": { \"name\": \"support.other.proto\" }, \"3\": { \"name\": \"support.other.proto\" } }, \"match\": \"(\\\\w+|\\\\(\\\\w+(\\\\.\\\\w+)*\\\\))(\\\\.\\\\w+)*\" }, \"optionStmt\": { \"begin\": \"(option)\\\\s+(\\\\w+|\\\\(\\\\w+(\\\\.\\\\w+)*\\\\))(\\\\.\\\\w+)*\\\\s*(=)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"2\": { \"name\": \"support.other.proto\" }, \"3\": { \"name\": \"support.other.proto\" }, \"4\": { \"name\": \"support.other.proto\" }, \"5\": { \"name\": \"keyword.operator.assignment.proto\" } }, \"end\": \"(;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.proto\" } }, \"patterns\": [{ \"include\": \"#constants\" }, { \"include\": \"#number\" }, { \"include\": \"#string\" }, { \"include\": \"#subMsgOption\" }] }, \"package\": { \"captures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"2\": { \"name\": \"string.unquoted.proto.package\" }, \"3\": { \"name\": \"punctuation.terminator.proto\" } }, \"match\": \"\\\\s*(package)\\\\s+([\\\\w.]+)\\\\s*(;)\" }, \"reserved\": { \"begin\": \"(reserved)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.proto\" } }, \"end\": \"(;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.proto\" } }, \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.numeric.proto\" }, \"3\": { \"name\": \"keyword.other.proto\" }, \"4\": { \"name\": \"constant.numeric.proto\" } }, \"match\": \"(\\\\d+)(\\\\s+(to)\\\\s+(\\\\d+))?\" }, { \"include\": \"#string\" }] }, \"rpcKeywords\": { \"match\": \"\\\\b(stream|returns)\\\\b\", \"name\": \"keyword.other.proto\" }, \"service\": { \"begin\": \"(service)\\\\s+([A-Za-z][A-Za-z0-9_.]*)\\\\s*\\\\{?\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"2\": { \"name\": \"entity.name.class.message.proto\" } }, \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#optionStmt\" }, { \"include\": \"#method\" }] }, \"storagetypes\": { \"match\": \"\\\\b(double|float|int32|int64|uint32|uint64|sint32|sint64|fixed32|fixed64|sfixed32|sfixed64|bool|string|bytes)\\\\b\", \"name\": \"storage.type.proto\" }, \"string\": { \"match\": `('([^']|\\\\')*')|(\"([^\"]|\\\\\")*\")`, \"name\": \"string.quoted.double.proto\" }, \"subMsgOption\": { \"begin\": \"\\\\{\", \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#kv\" }, { \"include\": \"#comments\" }] }, \"syntax\": { \"captures\": { \"1\": { \"name\": \"keyword.other.proto\" }, \"2\": { \"name\": \"keyword.operator.assignment.proto\" }, \"3\": { \"name\": \"string.quoted.double.proto.syntax\" }, \"4\": { \"name\": \"punctuation.terminator.proto\" } }, \"match\": '\\\\s*(syntax)\\\\s*(=)\\\\s*(\"proto[23]\")\\\\s*(;)' } }, \"scopeName\": \"source.proto\" });\nvar proto = [\n  lang\n];\n\nexport { proto as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,qBAAqB,aAAa,CAAC,OAAO,GAAG,QAAQ,SAAS,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,WAAW,CAAC,GAAG,cAAc,EAAE,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,QAAQ,OAAO,QAAQ,QAAQ,sBAAsB,GAAG,EAAE,SAAS,MAAM,OAAO,SAAS,QAAQ,kCAAkC,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,kCAAkC,QAAQ,0BAA0B,GAAG,QAAQ,EAAE,SAAS,mDAAmD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,SAAS,gEAAgE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,SAAS,EAAE,SAAS,oGAAoG,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yBAAyB,GAAG,KAAK,EAAE,QAAQ,qBAAqB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,SAAS,EAAE,SAAS,yBAAyB,QAAQ,0BAA0B,GAAG,UAAU,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,SAAS,qDAAqD,GAAG,MAAM,EAAE,SAAS,iBAAiB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,OAAO,yBAAyB,eAAe,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,YAAY,EAAE,SAAS,qFAAqF,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qBAAqB,GAAG,KAAK,EAAE,QAAQ,oDAAoD,GAAG,KAAK,EAAE,QAAQ,qBAAqB,GAAG,KAAK,EAAE,QAAQ,qBAAqB,GAAG,KAAK,EAAE,QAAQ,kDAAkD,GAAG,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,WAAW,EAAE,SAAS,+DAA+D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,UAAU,EAAE,SAAS,oCAAoC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,SAAS,CAAC,EAAE,GAAG,UAAU,EAAE,SAAS,uFAAuF,QAAQ,yBAAyB,GAAG,SAAS,EAAE,SAAS,8CAA8C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,SAAS,CAAC,EAAE,GAAG,cAAc,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,SAAS,wCAAwC,GAAG,cAAc,EAAE,SAAS,4DAA4D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,WAAW,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,SAAS,oCAAoC,GAAG,YAAY,EAAE,SAAS,kBAAkB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yBAAyB,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,SAAS,8BAA8B,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,eAAe,EAAE,SAAS,0BAA0B,QAAQ,sBAAsB,GAAG,WAAW,EAAE,SAAS,iDAAiD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,oHAAoH,QAAQ,qBAAqB,GAAG,UAAU,EAAE,SAAS,mCAAmC,QAAQ,6BAA6B,GAAG,gBAAgB,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,MAAM,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,SAAS,8CAA8C,EAAE,GAAG,aAAa,eAAe,CAAC;AAC34N,IAAI,QAAQ;AAAA,EACV;AACF;", "names": []}