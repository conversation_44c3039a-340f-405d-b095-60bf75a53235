{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/tasl.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Tasl\", \"fileTypes\": [\"tasl\"], \"name\": \"tasl\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#namespace\" }, { \"include\": \"#type\" }, { \"include\": \"#class\" }, { \"include\": \"#edge\" }], \"repository\": { \"class\": { \"begin\": \"(?:^\\\\s*)(class)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.tasl.class\" } }, \"end\": \"$\", \"patterns\": [{ \"include\": \"#key\" }, { \"include\": \"#export\" }, { \"include\": \"#expression\" }] }, \"comment\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.tasl\" } }, \"match\": \"(#).*$\", \"name\": \"comment.line.number-sign.tasl\" }, \"component\": { \"begin\": \"->\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.tasl.component\" } }, \"end\": \"$\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"coproduct\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.tasl.coproduct\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.tasl.coproduct\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#term\" }, { \"include\": \"#option\" }] }, \"datatype\": { \"match\": \"[a-zA-Z][a-zA-Z0-9]*:(?:[A-Za-z0-9\\\\-._~!$&'()*+,;=:@/?]|%[0-9A-Fa-f]{2})+\", \"name\": \"string.regexp\" }, \"edge\": { \"begin\": \"(?:^\\\\s*)(edge)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.tasl.edge\" } }, \"end\": \"$\", \"patterns\": [{ \"include\": \"#key\" }, { \"include\": \"#export\" }, { \"match\": \"=/\", \"name\": \"punctuation.separator.tasl.edge.source\" }, { \"match\": \"/=>\", \"name\": \"punctuation.separator.tasl.edge.target\" }, { \"match\": \"=>\", \"name\": \"punctuation.separator.tasl.edge\" }, { \"include\": \"#expression\" }] }, \"export\": { \"match\": \"::\", \"name\": \"keyword.operator.tasl.export\" }, \"expression\": { \"patterns\": [{ \"include\": \"#literal\" }, { \"include\": \"#uri\" }, { \"include\": \"#product\" }, { \"include\": \"#coproduct\" }, { \"include\": \"#reference\" }, { \"include\": \"#optional\" }, { \"include\": \"#identifier\" }] }, \"identifier\": { \"captures\": { \"1\": { \"name\": \"variable\" } }, \"match\": \"([a-zA-Z][a-zA-Z0-9]*)\\\\b\" }, \"key\": { \"match\": \"[a-zA-Z][a-zA-Z0-9]*:(?:[A-Za-z0-9\\\\-._~!$&'()*+,;=:@/?]|%[0-9A-Fa-f]{2})+\", \"name\": \"markup.bold entity.name.class\" }, \"literal\": { \"patterns\": [{ \"include\": \"#datatype\" }] }, \"namespace\": { \"captures\": { \"1\": { \"name\": \"keyword.control.tasl.namespace\" }, \"2\": { \"patterns\": [{ \"include\": \"#namespaceURI\" }, { \"match\": \"[a-zA-Z][a-zA-Z0-9]*\\\\b\", \"name\": \"entity.name\" }] } }, \"match\": \"(?:^\\\\s*)(namespace)\\\\b(.*)\" }, \"namespaceURI\": { \"match\": \"[a-z]+:[a-zA-Z0-9-._~:\\\\/?#\\\\[\\\\]@!$&'()*+,;%=]+\", \"name\": \"markup.underline.link\" }, \"option\": { \"begin\": \"<-\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.tasl.option\" } }, \"end\": \"$\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"optional\": { \"begin\": \"\\\\?\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator\" } }, \"end\": \"$\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"product\": { \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.tasl.product\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.block.tasl.product\" } }, \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#term\" }, { \"include\": \"#component\" }] }, \"reference\": { \"captures\": { \"1\": { \"name\": \"markup.bold keyword.operator\" }, \"2\": { \"patterns\": [{ \"include\": \"#key\" }] } }, \"match\": \"(\\\\*)\\\\s*(.*)\" }, \"term\": { \"match\": \"[a-zA-Z][a-zA-Z0-9]*:(?:[A-Za-z0-9\\\\-._~!$&'()*+,;=:@/?]|%[0-9A-Fa-f]{2})+\", \"name\": \"entity.other.tasl.key\" }, \"type\": { \"begin\": \"(?:^\\\\s*)(type)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.tasl.type\" } }, \"end\": \"$\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"uri\": { \"match\": \"<>\", \"name\": \"variable.other.constant\" } }, \"scopeName\": \"source.tasl\" });\nvar tasl = [\n  lang\n];\n\nexport { tasl as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,QAAQ,aAAa,CAAC,MAAM,GAAG,QAAQ,QAAQ,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,QAAQ,CAAC,GAAG,cAAc,EAAE,SAAS,EAAE,SAAS,uBAAuB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,OAAO,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,WAAW,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,SAAS,UAAU,QAAQ,gCAAgC,GAAG,aAAa,EAAE,SAAS,MAAM,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,YAAY,EAAE,SAAS,8EAA8E,QAAQ,gBAAgB,GAAG,QAAQ,EAAE,SAAS,sBAAsB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,OAAO,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,SAAS,MAAM,QAAQ,yCAAyC,GAAG,EAAE,SAAS,OAAO,QAAQ,yCAAyC,GAAG,EAAE,SAAS,MAAM,QAAQ,kCAAkC,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,UAAU,EAAE,SAAS,MAAM,QAAQ,+BAA+B,GAAG,cAAc,EAAE,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,OAAO,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,cAAc,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,WAAW,EAAE,GAAG,SAAS,4BAA4B,GAAG,OAAO,EAAE,SAAS,8EAA8E,QAAQ,gCAAgC,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iCAAiC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,SAAS,2BAA2B,QAAQ,cAAc,CAAC,EAAE,EAAE,GAAG,SAAS,8BAA8B,GAAG,gBAAgB,EAAE,SAAS,oDAAoD,QAAQ,wBAAwB,GAAG,UAAU,EAAE,SAAS,MAAM,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,YAAY,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mBAAmB,EAAE,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,WAAW,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,OAAO,CAAC,EAAE,EAAE,GAAG,SAAS,gBAAgB,GAAG,QAAQ,EAAE,SAAS,8EAA8E,QAAQ,wBAAwB,GAAG,QAAQ,EAAE,SAAS,sBAAsB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,OAAO,EAAE,SAAS,MAAM,QAAQ,0BAA0B,EAAE,GAAG,aAAa,cAAc,CAAC;AAC9oH,IAAI,OAAO;AAAA,EACT;AACF;", "names": []}