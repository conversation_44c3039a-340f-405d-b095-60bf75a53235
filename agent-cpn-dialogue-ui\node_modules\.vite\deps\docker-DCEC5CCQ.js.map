{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/docker.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Dockerfile\", \"name\": \"docker\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.other.special-method.dockerfile\" }, \"2\": { \"name\": \"keyword.other.special-method.dockerfile\" } }, \"match\": \"^\\\\s*\\\\b(?i:(FROM))\\\\b.*?\\\\b(?i:(AS))\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.dockerfile\" }, \"2\": { \"name\": \"keyword.other.special-method.dockerfile\" } }, \"match\": \"^\\\\s*(?i:(ONBUILD)\\\\s+)?(?i:(ADD|ARG|CMD|COPY|ENTRYPOINT|ENV|EXPOSE|FROM|HEALTHCHECK|LABEL|MAINTAINER|RUN|SHELL|STOPSIGNAL|USER|VOLUME|WORKDIR))\\\\s\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.dockerfile\" }, \"2\": { \"name\": \"keyword.other.special-method.dockerfile\" } }, \"match\": \"^\\\\s*(?i:(ONBUILD)\\\\s+)?(?i:(CMD|ENTRYPOINT))\\\\s\" }, { \"begin\": '\"', \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.dockerfile\" } }, \"end\": '\"', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.dockerfile\" } }, \"name\": \"string.quoted.double.dockerfile\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escaped.dockerfile\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.dockerfile\" } }, \"end\": \"'\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.dockerfile\" } }, \"name\": \"string.quoted.single.dockerfile\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escaped.dockerfile\" }] }, { \"captures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.dockerfile\" }, \"2\": { \"name\": \"comment.line.number-sign.dockerfile\" }, \"3\": { \"name\": \"punctuation.definition.comment.dockerfile\" } }, \"comment\": \"comment.line\", \"match\": \"^(\\\\s*)((#).*$\\\\n?)\" }], \"scopeName\": \"source.dockerfile\", \"aliases\": [\"dockerfile\"] });\nvar docker = [\n  lang\n];\n\nexport { docker as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,cAAc,QAAQ,UAAU,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,SAAS,2CAA2C,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,SAAS,sJAAsJ,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,SAAS,mDAAmD,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,QAAQ,mCAAmC,YAAY,CAAC,EAAE,SAAS,SAAS,QAAQ,wCAAwC,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,QAAQ,mCAAmC,YAAY,CAAC,EAAE,SAAS,SAAS,QAAQ,wCAAwC,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oDAAoD,GAAG,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,WAAW,gBAAgB,SAAS,sBAAsB,CAAC,GAAG,aAAa,qBAAqB,WAAW,CAAC,YAAY,EAAE,CAAC;AACntD,IAAI,SAAS;AAAA,EACX;AACF;", "names": []}