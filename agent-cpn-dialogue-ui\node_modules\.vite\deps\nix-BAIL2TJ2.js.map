{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/nix.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"<PERSON>\", \"fileTypes\": [\"nix\"], \"name\": \"nix\", \"patterns\": [{ \"include\": \"#expression\" }], \"repository\": { \"attribute-bind\": { \"patterns\": [{ \"include\": \"#attribute-name\" }, { \"include\": \"#attribute-bind-from-equals\" }] }, \"attribute-bind-from-equals\": { \"begin\": \"\\\\=\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.bind.nix\" } }, \"end\": \"\\\\;\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.bind.nix\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, \"attribute-inherit\": { \"begin\": \"\\\\binherit\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.inherit.nix\" } }, \"end\": \"\\\\;\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.inherit.nix\" } }, \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.function.arguments.nix\" } }, \"end\": \"(?=\\\\;)\", \"patterns\": [{ \"begin\": \"\\\\)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.function.arguments.nix\" } }, \"end\": \"(?=\\\\;)\", \"patterns\": [{ \"include\": \"#bad-reserved\" }, { \"include\": \"#attribute-name-single\" }, { \"include\": \"#others\" }] }, { \"include\": \"#expression\" }] }, { \"begin\": \"(?=[a-zA-Z\\\\_])\", \"end\": \"(?=\\\\;)\", \"patterns\": [{ \"include\": \"#bad-reserved\" }, { \"include\": \"#attribute-name-single\" }, { \"include\": \"#others\" }] }, { \"include\": \"#others\" }] }, \"attribute-name\": { \"patterns\": [{ \"match\": \"\\\\b[a-zA-Z\\\\_][a-zA-Z0-9\\\\_\\\\'\\\\-]*\", \"name\": \"entity.other.attribute-name.multipart.nix\" }, { \"match\": \"\\\\.\" }, { \"include\": \"#string-quoted\" }, { \"include\": \"#interpolation\" }] }, \"attribute-name-single\": { \"match\": \"\\\\b[a-zA-Z\\\\_][a-zA-Z0-9\\\\_\\\\'\\\\-]*\", \"name\": \"entity.other.attribute-name.single.nix\" }, \"attrset-contents\": { \"patterns\": [{ \"include\": \"#attribute-inherit\" }, { \"include\": \"#bad-reserved\" }, { \"include\": \"#attribute-bind\" }, { \"include\": \"#others\" }] }, \"attrset-definition\": { \"begin\": \"(?=\\\\{)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"begin\": \"(\\\\{)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.attrset.nix\" } }, \"end\": \"(\\\\})\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.attrset.nix\" } }, \"patterns\": [{ \"include\": \"#attrset-contents\" }] }, { \"begin\": \"(?<=\\\\})\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }] }, \"attrset-definition-brace-opened\": { \"patterns\": [{ \"begin\": \"(?<=\\\\})\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }, { \"begin\": \"(?=.?)\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.attrset.nix\" } }, \"patterns\": [{ \"include\": \"#attrset-contents\" }] }] }, \"attrset-for-sure\": { \"patterns\": [{ \"begin\": \"(?=\\\\brec\\\\b)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"begin\": \"\\\\brec\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"end\": \"(?=\\\\{)\", \"patterns\": [{ \"include\": \"#others\" }] }, { \"include\": \"#attrset-definition\" }, { \"include\": \"#others\" }] }, { \"begin\": \"(?=\\\\{\\\\s*(\\\\}|[^,?]*(=|;)))\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#attrset-definition\" }, { \"include\": \"#others\" }] }] }, \"attrset-or-function\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.attrset-or-function.nix\" } }, \"end\": \"(?=([\\\\])};]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"begin\": `(?=(\\\\s*\\\\}|\\\\\"|\\\\binherit\\\\b|\\\\b[a-zA-Z\\\\_][a-zA-Z0-9\\\\_\\\\'\\\\-]*(\\\\s*\\\\.|\\\\s*=[^=])))`, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#attrset-definition-brace-opened\" }] }, { \"begin\": \"(?=(\\\\.\\\\.\\\\.|\\\\b[a-zA-Z\\\\_][a-zA-Z0-9\\\\_\\\\'\\\\-]*\\\\s*[,?]))\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#function-definition-brace-opened\" }] }, { \"include\": \"#bad-reserved\" }, { \"begin\": \"\\\\b[a-zA-Z\\\\_][a-zA-Z0-9\\\\_\\\\'\\\\-]*\", \"beginCaptures\": { \"0\": { \"name\": \"variable.parameter.function.maybe.nix\" } }, \"end\": \"(?=([\\\\])};]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"begin\": \"(?=\\\\.)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#attrset-definition-brace-opened\" }] }, { \"begin\": \"\\\\s*(\\\\,)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#function-definition-brace-opened\" }] }, { \"begin\": \"(?=\\\\=)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#attribute-bind-from-equals\" }, { \"include\": \"#attrset-definition-brace-opened\" }] }, { \"begin\": \"(?=\\\\?)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#function-parameter-default\" }, { \"begin\": \"\\\\,\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#function-definition-brace-opened\" }] }] }, { \"include\": \"#others\" }] }, { \"include\": \"#others\" }] }, \"bad-reserved\": { \"match\": \"\\\\b(if|then|else|assert|with|let|in|rec|inherit)\\\\b\", \"name\": \"invalid.illegal.reserved.nix\" }, \"comment\": { \"patterns\": [{ \"begin\": \"/\\\\*([^*]|\\\\*[^\\\\/])*\", \"end\": \"\\\\*\\\\/\", \"name\": \"comment.block.nix\", \"patterns\": [{ \"include\": \"#comment-remark\" }] }, { \"begin\": \"\\\\#\", \"end\": \"$\", \"name\": \"comment.line.number-sign.nix\", \"patterns\": [{ \"include\": \"#comment-remark\" }] }] }, \"comment-remark\": { \"captures\": { \"1\": { \"name\": \"markup.bold.comment.nix\" } }, \"match\": \"(TODO|FIXME|BUG|\\\\!\\\\!\\\\!):?\" }, \"constants\": { \"patterns\": [{ \"begin\": \"\\\\b(builtins|true|false|null)\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"constant.language.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }, { \"begin\": \"\\\\b(scopedImport|import|isNull|abort|throw|baseNameOf|dirOf|removeAttrs|map|toString|derivationStrict|derivation)\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"support.function.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }, { \"begin\": \"\\\\b[0-9]+\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"constant.numeric.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }] }, \"expression\": { \"patterns\": [{ \"include\": \"#parens-and-cont\" }, { \"include\": \"#list-and-cont\" }, { \"include\": \"#string\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#with-assert\" }, { \"include\": \"#function-for-sure\" }, { \"include\": \"#attrset-for-sure\" }, { \"include\": \"#attrset-or-function\" }, { \"include\": \"#let\" }, { \"include\": \"#if\" }, { \"include\": \"#operator-unary\" }, { \"include\": \"#constants\" }, { \"include\": \"#bad-reserved\" }, { \"include\": \"#parameter-name-and-cont\" }, { \"include\": \"#others\" }] }, \"expression-cont\": { \"begin\": \"(?=.?)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#parens\" }, { \"include\": \"#list\" }, { \"include\": \"#string\" }, { \"include\": \"#interpolation\" }, { \"include\": \"#function-for-sure\" }, { \"include\": \"#attrset-for-sure\" }, { \"include\": \"#attrset-or-function\" }, { \"match\": \"(\\\\bor\\\\b|\\\\.|==|!=|!|\\\\<\\\\=|\\\\<|\\\\>\\\\=|\\\\>|&&|\\\\|\\\\||-\\\\>|//|\\\\?|\\\\+\\\\+|-|\\\\*|/(?=([^*]|$))|\\\\+)\", \"name\": \"keyword.operator.nix\" }, { \"include\": \"#constants\" }, { \"include\": \"#bad-reserved\" }, { \"include\": \"#parameter-name\" }, { \"include\": \"#others\" }] }, \"function-body\": { \"begin\": \"(@\\\\s*([a-zA-Z\\\\_][a-zA-Z0-9\\\\_\\\\'\\\\-]*)\\\\s*)?(\\\\:)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"function-body-from-colon\": { \"begin\": \"(\\\\:)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.function.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"function-contents\": { \"patterns\": [{ \"include\": \"#bad-reserved\" }, { \"include\": \"#function-parameter\" }, { \"include\": \"#others\" }] }, \"function-definition\": { \"begin\": \"(?=.?)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#function-body-from-colon\" }, { \"begin\": \"(?=.?)\", \"end\": \"(?=\\\\:)\", \"patterns\": [{ \"begin\": \"(\\\\b[a-zA-Z\\\\_][a-zA-Z0-9\\\\_\\\\'\\\\-]*)\", \"beginCaptures\": { \"0\": { \"name\": \"variable.parameter.function.4.nix\" } }, \"end\": \"(?=\\\\:)\", \"patterns\": [{ \"begin\": \"\\\\@\", \"end\": \"(?=\\\\:)\", \"patterns\": [{ \"include\": \"#function-header-until-colon-no-arg\" }, { \"include\": \"#others\" }] }, { \"include\": \"#others\" }] }, { \"begin\": \"(?=\\\\{)\", \"end\": \"(?=\\\\:)\", \"patterns\": [{ \"include\": \"#function-header-until-colon-with-arg\" }] }] }, { \"include\": \"#others\" }] }, \"function-definition-brace-opened\": { \"begin\": \"(?=.?)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#function-body-from-colon\" }, { \"begin\": \"(?=.?)\", \"end\": \"(?=\\\\:)\", \"patterns\": [{ \"include\": \"#function-header-close-brace-with-arg\" }, { \"begin\": \"(?=.?)\", \"end\": \"(?=\\\\})\", \"patterns\": [{ \"include\": \"#function-contents\" }] }] }, { \"include\": \"#others\" }] }, \"function-for-sure\": { \"patterns\": [{ \"begin\": `(?=(\\\\b[a-zA-Z\\\\_][a-zA-Z0-9\\\\_\\\\'\\\\-]*\\\\s*[:@]|\\\\{[^}]*\\\\}\\\\s*:|\\\\{[^#}\"'/=]*[,\\\\?]))`, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#function-definition\" }] }] }, \"function-header-close-brace-no-arg\": { \"begin\": \"\\\\}\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.entity.function.nix\" } }, \"end\": \"(?=\\\\:)\", \"patterns\": [{ \"include\": \"#others\" }] }, \"function-header-close-brace-with-arg\": { \"begin\": \"\\\\}\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.entity.function.nix\" } }, \"end\": \"(?=\\\\:)\", \"patterns\": [{ \"include\": \"#function-header-terminal-arg\" }, { \"include\": \"#others\" }] }, \"function-header-open-brace\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.entity.function.2.nix\" } }, \"end\": \"(?=\\\\})\", \"patterns\": [{ \"include\": \"#function-contents\" }] }, \"function-header-terminal-arg\": { \"begin\": \"(?=@)\", \"end\": \"(?=\\\\:)\", \"patterns\": [{ \"begin\": \"\\\\@\", \"end\": \"(?=\\\\:)\", \"patterns\": [{ \"begin\": \"(\\\\b[a-zA-Z\\\\_][a-zA-Z0-9\\\\_\\\\'\\\\-]*)\", \"end\": \"(?=\\\\:)\", \"name\": \"variable.parameter.function.3.nix\" }, { \"include\": \"#others\" }] }, { \"include\": \"#others\" }] }, \"function-header-until-colon-no-arg\": { \"begin\": \"(?=\\\\{)\", \"end\": \"(?=\\\\:)\", \"patterns\": [{ \"include\": \"#function-header-open-brace\" }, { \"include\": \"#function-header-close-brace-no-arg\" }] }, \"function-header-until-colon-with-arg\": { \"begin\": \"(?=\\\\{)\", \"end\": \"(?=\\\\:)\", \"patterns\": [{ \"include\": \"#function-header-open-brace\" }, { \"include\": \"#function-header-close-brace-with-arg\" }] }, \"function-parameter\": { \"patterns\": [{ \"begin\": \"(\\\\.\\\\.\\\\.)\", \"end\": \"(,|(?=\\\\}))\", \"name\": \"keyword.operator.nix\", \"patterns\": [{ \"include\": \"#others\" }] }, { \"begin\": \"\\\\b[a-zA-Z\\\\_][a-zA-Z0-9\\\\_\\\\'\\\\-]*\", \"beginCaptures\": { \"0\": { \"name\": \"variable.parameter.function.1.nix\" } }, \"end\": \"(,|(?=\\\\}))\", \"endCaptures\": { \"0\": { \"name\": \"keyword.operator.nix\" } }, \"patterns\": [{ \"include\": \"#whitespace\" }, { \"include\": \"#comment\" }, { \"include\": \"#function-parameter-default\" }, { \"include\": \"#expression\" }] }, { \"include\": \"#others\" }] }, \"function-parameter-default\": { \"begin\": \"\\\\?\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.nix\" } }, \"end\": \"(?=[,}])\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"if\": { \"begin\": \"(?=\\\\bif\\\\b)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"begin\": \"\\\\bif\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"end\": \"\\\\bth(?=en\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, { \"begin\": \"(?<=th)en\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"end\": \"\\\\bel(?=se\\\\b)\", \"endCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, { \"begin\": \"(?<=el)se\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"endCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }] }, \"illegal\": { \"match\": \".\", \"name\": \"invalid.illegal\" }, \"interpolation\": { \"begin\": \"\\\\$\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.begin.nix\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.nix\" } }, \"name\": \"markup.italic\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"let\": { \"begin\": \"(?=\\\\blet\\\\b)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"begin\": \"\\\\blet\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(in|else|then)\\\\b))\", \"patterns\": [{ \"begin\": \"(?=\\\\{)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"begin\": \"\\\\{\", \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#attrset-contents\" }] }, { \"begin\": \"(^|(?<=\\\\}))\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }, { \"include\": \"#others\" }] }, { \"include\": \"#attrset-contents\" }, { \"include\": \"#others\" }] }, { \"begin\": \"\\\\bin\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression\" }] }] }, \"list\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.list.nix\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.list.nix\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, \"list-and-cont\": { \"begin\": \"(?=\\\\[)\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#list\" }, { \"include\": \"#expression-cont\" }] }, \"operator-unary\": { \"match\": \"(!|-)\", \"name\": \"keyword.operator.unary.nix\" }, \"others\": { \"patterns\": [{ \"include\": \"#whitespace\" }, { \"include\": \"#comment\" }, { \"include\": \"#illegal\" }] }, \"parameter-name\": { \"captures\": { \"0\": { \"name\": \"variable.parameter.name.nix\" } }, \"match\": \"\\\\b[a-zA-Z\\\\_][a-zA-Z0-9\\\\_\\\\'\\\\-]*\" }, \"parameter-name-and-cont\": { \"begin\": \"\\\\b[a-zA-Z\\\\_][a-zA-Z0-9\\\\_\\\\'\\\\-]*\", \"beginCaptures\": { \"0\": { \"name\": \"variable.parameter.name.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }, \"parens\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.expression.nix\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.expression.nix\" } }, \"patterns\": [{ \"include\": \"#expression\" }] }, \"parens-and-cont\": { \"begin\": \"(?=\\\\()\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#parens\" }, { \"include\": \"#expression-cont\" }] }, \"string\": { \"patterns\": [{ \"begin\": \"(?=\\\\'\\\\')\", \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"begin\": \"\\\\'\\\\'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.other.start.nix\" } }, \"end\": \"\\\\'\\\\'(?!\\\\$|\\\\'|\\\\\\\\.)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.other.end.nix\" } }, \"name\": \"string.quoted.other.nix\", \"patterns\": [{ \"match\": \"\\\\'\\\\'(\\\\$|\\\\'|\\\\\\\\.)\", \"name\": \"constant.character.escape.nix\" }, { \"include\": \"#interpolation\" }] }, { \"include\": \"#expression-cont\" }] }, { \"begin\": '(?=\\\\\")', \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#string-quoted\" }, { \"include\": \"#expression-cont\" }] }, { \"begin\": \"([a-zA-Z0-9\\\\.\\\\_\\\\-\\\\+]*(\\\\/[a-zA-Z0-9\\\\.\\\\_\\\\-\\\\+]+)+)\", \"beginCaptures\": { \"0\": { \"name\": \"string.unquoted.path.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }, { \"begin\": \"(\\\\<[a-zA-Z0-9\\\\.\\\\_\\\\-\\\\+]+(\\\\/[a-zA-Z0-9\\\\.\\\\_\\\\-\\\\+]+)*\\\\>)\", \"beginCaptures\": { \"0\": { \"name\": \"string.unquoted.spath.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }, { \"begin\": \"([a-zA-Z][a-zA-Z0-9\\\\+\\\\-\\\\.]*\\\\:[a-zA-Z0-9\\\\%\\\\/\\\\?\\\\:\\\\@\\\\&\\\\=\\\\+\\\\$\\\\,\\\\-\\\\_\\\\.\\\\!\\\\~\\\\*\\\\']+)\", \"beginCaptures\": { \"0\": { \"name\": \"string.unquoted.url.nix\" } }, \"end\": \"(?=([\\\\])};,]|\\\\b(else|then)\\\\b))\", \"patterns\": [{ \"include\": \"#expression-cont\" }] }] }, \"string-quoted\": { \"begin\": '\\\\\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.double.start.nix\" } }, \"end\": '\\\\\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.double.end.nix\" } }, \"name\": \"string.quoted.double.nix\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.nix\" }, { \"include\": \"#interpolation\" }] }, \"whitespace\": { \"match\": \"\\\\s+\" }, \"with-assert\": { \"begin\": \"\\\\b(with|assert)\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.nix\" } }, \"end\": \"\\\\;\", \"patterns\": [{ \"include\": \"#expression\" }] } }, \"scopeName\": \"source.nix\" });\nvar nix = [\n  lang\n];\n\nexport { nix as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,OAAO,aAAa,CAAC,KAAK,GAAG,QAAQ,OAAO,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,GAAG,cAAc,EAAE,kBAAkB,EAAE,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,8BAA8B,CAAC,EAAE,GAAG,8BAA8B,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,qBAAqB,EAAE,SAAS,iBAAiB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,mBAAmB,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,CAAC,EAAE,SAAS,uCAAuC,QAAQ,4CAA4C,GAAG,EAAE,SAAS,MAAM,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,yBAAyB,EAAE,SAAS,uCAAuC,QAAQ,yCAAyC,GAAG,oBAAoB,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,sBAAsB,EAAE,SAAS,WAAW,OAAO,qCAAqC,YAAY,CAAC,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,EAAE,SAAS,YAAY,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,mBAAmB,CAAC,EAAE,CAAC,EAAE,GAAG,mCAAmC,EAAE,YAAY,CAAC,EAAE,SAAS,YAAY,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,EAAE,SAAS,UAAU,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,CAAC,EAAE,GAAG,oBAAoB,EAAE,YAAY,CAAC,EAAE,SAAS,iBAAiB,OAAO,qCAAqC,YAAY,CAAC,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,gCAAgC,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,oCAAoC,YAAY,CAAC,EAAE,SAAS,0FAA0F,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,mCAAmC,CAAC,EAAE,GAAG,EAAE,SAAS,+DAA+D,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,oCAAoC,CAAC,EAAE,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,SAAS,uCAAuC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,OAAO,oCAAoC,YAAY,CAAC,EAAE,SAAS,WAAW,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,mCAAmC,CAAC,EAAE,GAAG,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,oCAAoC,CAAC,EAAE,GAAG,EAAE,SAAS,WAAW,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,8BAA8B,GAAG,EAAE,WAAW,mCAAmC,CAAC,EAAE,GAAG,EAAE,SAAS,WAAW,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,8BAA8B,GAAG,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,oCAAoC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,uDAAuD,QAAQ,+BAA+B,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,SAAS,yBAAyB,OAAO,UAAU,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,OAAO,KAAK,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,SAAS,+BAA+B,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,SAAS,oCAAoC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,EAAE,SAAS,wHAAwH,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,EAAE,SAAS,gBAAgB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,mBAAmB,CAAC,EAAE,CAAC,EAAE,GAAG,cAAc,EAAE,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,OAAO,GAAG,EAAE,WAAW,MAAM,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,UAAU,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,SAAS,qGAAqG,QAAQ,uBAAuB,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,uDAAuD,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,4BAA4B,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,qBAAqB,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,UAAU,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,4BAA4B,GAAG,EAAE,SAAS,UAAU,OAAO,WAAW,YAAY,CAAC,EAAE,SAAS,yCAAyC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,SAAS,OAAO,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,sCAAsC,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,WAAW,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,wCAAwC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,oCAAoC,EAAE,SAAS,UAAU,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,4BAA4B,GAAG,EAAE,SAAS,UAAU,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,wCAAwC,GAAG,EAAE,SAAS,UAAU,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,qBAAqB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,qBAAqB,EAAE,YAAY,CAAC,EAAE,SAAS,0FAA0F,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,CAAC,EAAE,GAAG,sCAAsC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,wCAAwC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,8BAA8B,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,gCAAgC,EAAE,SAAS,SAAS,OAAO,WAAW,YAAY,CAAC,EAAE,SAAS,OAAO,OAAO,WAAW,YAAY,CAAC,EAAE,SAAS,yCAAyC,OAAO,WAAW,QAAQ,oCAAoC,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,sCAAsC,EAAE,SAAS,WAAW,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,8BAA8B,GAAG,EAAE,WAAW,sCAAsC,CAAC,EAAE,GAAG,wCAAwC,EAAE,SAAS,WAAW,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,8BAA8B,GAAG,EAAE,WAAW,wCAAwC,CAAC,EAAE,GAAG,sBAAsB,EAAE,YAAY,CAAC,EAAE,SAAS,eAAe,OAAO,eAAe,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,uCAAuC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,eAAe,eAAe,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,8BAA8B,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,8BAA8B,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,YAAY,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,MAAM,EAAE,SAAS,gBAAgB,OAAO,qCAAqC,YAAY,CAAC,EAAE,SAAS,YAAY,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,OAAO,kBAAkB,eAAe,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,gBAAgB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,OAAO,kBAAkB,eAAe,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,gBAAgB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,OAAO,qCAAqC,eAAe,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,WAAW,EAAE,SAAS,KAAK,QAAQ,kBAAkB,GAAG,iBAAiB,EAAE,SAAS,UAAU,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,QAAQ,iBAAiB,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,OAAO,EAAE,SAAS,iBAAiB,OAAO,qCAAqC,YAAY,CAAC,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,OAAO,wCAAwC,YAAY,CAAC,EAAE,SAAS,WAAW,OAAO,qCAAqC,YAAY,CAAC,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,EAAE,SAAS,gBAAgB,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,YAAY,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,QAAQ,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,WAAW,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,kBAAkB,EAAE,SAAS,SAAS,QAAQ,6BAA6B,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,SAAS,sCAAsC,GAAG,2BAA2B,EAAE,SAAS,uCAAuC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,UAAU,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,WAAW,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,SAAS,cAAc,OAAO,qCAAqC,YAAY,CAAC,EAAE,SAAS,UAAU,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,2BAA2B,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,SAAS,yBAAyB,QAAQ,gCAAgC,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,EAAE,SAAS,WAAW,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,EAAE,SAAS,4DAA4D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,EAAE,SAAS,kEAAkE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,EAAE,SAAS,qGAAqG,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,OAAO,qCAAqC,YAAY,CAAC,EAAE,WAAW,mBAAmB,CAAC,EAAE,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,SAAS,SAAS,QAAQ,gCAAgC,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,OAAO,GAAG,eAAe,EAAE,SAAS,uBAAuB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,EAAE,GAAG,aAAa,aAAa,CAAC;AACh7f,IAAI,MAAM;AAAA,EACR;AACF;", "names": []}