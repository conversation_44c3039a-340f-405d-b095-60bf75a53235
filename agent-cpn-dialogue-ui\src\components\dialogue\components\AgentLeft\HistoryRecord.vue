<template>
    <div class="history-container">
        <div
            class="history-header"
            @click="toggleHistory"
            :class="{ 'justify-between': !isTreeCollapsed, 'justify-center': isTreeCollapsed }"
        >
            <div class="header-left" :class="{ 'ml-4': !isTreeCollapsed, 'mr-2': isTreeCollapsed }">
                <Icon name="lsjl" size="20" />
                <span v-show="!isTreeCollapsed" class="ml-2">历史记录</span>
            </div>
            <div>
                <span class="toggle-btn" v-show="!isTreeCollapsed" @click="setRightHistory">
                    {{ showHistory ? '收起' : '展开' }}
                </span>
            </div>
        </div>

        <transition name="fade">
            <div
                v-if="showHistory && !isTreeShow"
                class="bg-transparent rounded-lg overflow-hidden flex flex-col gap-3 h-200px"
            >
                <Conversations
                    v-if="userStore.token && HistoryLists.length > 0"
                    v-model:active="active"
                    :items="HistoryLists"
                    row-key="id"
                    :show-tooltip="true"
                    @change="handleChange"
                    :label-max-width="140"
                    :tooltip-offset="35"
                    show-built-in-menu
                    popper-class="tooltip"
                    label-key="sessionTitle"
                    tooltip-placement="right"
                    class="conversations"
                    :style="{
                        padding: '0 10px',
                        backgroundColor: 'transparent',
                        borderRadius: '8px',
                        width: '230px',
                        boxShadow: 'none',
                    }"
                    @menu-command="handleMenuCommand"
                ></Conversations>
                <el-empty v-else class="h-full flex-center" description="暂无对话记录" />
            </div>
        </transition>
    </div>
</template>

<script setup>
/**
 * @description 历史记录组件，显示用户历史会话记录
 */
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import { ChatLineRound } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useSessionStore } from '@/components/dialogue/store/modules/session';
import { useUserStore } from '@/components/dialogue/store/modules/user';
import { useRouter, useRoute } from 'vue-router';
const sessionStore = useSessionStore();
const router = useRouter();
const route = useRoute();
// 历史记录显示状态
const showHistory = ref(true);
const active = computed(() => sessionStore.active);
import { isTreeShow } from '@/components/dialogue/store/main';
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';
const userStore = useUserStore();
defineOptions({
    name: 'HistoryRecord',
});

const props = defineProps({
    isTreeCollapsed: {
        type: Boolean,
        default: false,
    },
});
function setCurrentSession(id) {
    sessionStore.setActive(id);
    const target = sessionStore.sessionList.find(session => session.id === id);

    sessionStore.setCurrentSession({
        ...target,
        title: target.dialogueName,
    });
}
/**
 * @description 监听路由变化，同步更新高亮状态
 */
watch(
    () => route.params?.id,
    async newId => {
        if (newId) {
            setCurrentSession(newId);
        } else {
            sessionStore.setActive('');
        }
    }
);

/**
 * @description 监听会话列表变化，确保高亮状态正确
 */
watch(
    () => sessionStore.sessionList,
    newList => {
        if (newList && newList.length > 0 && route.params?.id) {
            // 检查当前路由的会话ID是否在列表中
            const currentSessionExists = newList.some(session => session.id === route.params.id);
            if (currentSessionExists) {
                sessionStore.setActive(route.params.id);
            }
        }
    },
    { immediate: true }
);

// 内置菜单点击方法
// 右键菜单
function handleMenuCommand(command, item) {
    switch (command) {
        case 'delete':
            ElMessageBox.confirm('删除后，聊天记录将不可恢复。', '确定删除对话？', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                confirmButtonClass: 'el-button--danger',
                cancelButtonClass: 'el-button--info',
                roundButton: true,
                autofocus: false,
                inputAttrs: {
                    maxlength: 20, // 限制最大输入长度
                },
            })
                .then(() => {
                    sessionStore.deleteSessions(item.id);
                    nextTick(() => {
                        if (item.id === active.value) {
                            // 如果删除当前会话 返回到默认页
                            sessionStore.createSessionBtn();
                        }
                    });
                })
                .catch(() => {
                    // 取消删除
                });
            break;
        case 'rename':
            ElMessageBox.prompt('', '编辑对话名称', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputErrorMessage: '请输入对话名称',
                confirmButtonClass: 'el-button--primary',
                cancelButtonClass: 'el-button--info',
                roundButton: true,
                inputValue: item.sessionTitle, // 设置默认值
                autofocus: false,
                inputValidator: value => {
                    if (!value) {
                        return false;
                    }
                    if (value.length > 21) {
                        return `请将此文本缩短到20个字符或更少(目前使用了${value.length}个字符)`;
                    }
                    return true;
                },
            }).then(({ value }) => {
                sessionStore.updateSession(item?.id, value);
            });
            break;
        default:
            break;
    }
}

// 页面加载时，从持久化存储中读取历史记录
onMounted(async () => {
    await sessionStore.requestSessionList();
    // 初始化时设置当前高亮状态
    if (route.params?.id) {
        setCurrentSession(route.params?.id);
    }
});

const { setRightPanel } = useRightPanelStore();
const setRightHistory = () => {
    // setRightPanel('RightHistory');
};
const truncateText = (text, maxLength = 20) => {
    if (!text) return ''; // 处理空值
    // 计算字符长度（中文等全角字符按1个字符计算）
    return text.length > maxLength ? text.slice(0, maxLength) + '...' : text;
};
/**
 * 将历史记录数据转换为Conversations组件所需的格式
 */
const HistoryLists = computed(() => {
    // 优先使用 sessionStore 中的会话列表数据
    const dataSource = sessionStore.sessionList || [];

    if (!dataSource || dataSource.length === 0) {
        console.log('历史记录为空');
        return [];
    }

    // 预处理会话分组 并添加前缀图标
    return dataSource.map((item, index) => ({
        id: item.id,
        label: item.sessionTitle || item.dialogueName,
        prefixIcon: ChatLineRound,
        index: index, // 保存索引用于点赞功能
        liked: item.liked || false,
        // 从 sessionStore 中获取的数据可能字段名不同，需要适配
        sessionContent: item.sessionContent,
        // 会话标题内容
        sessionTitle: truncateText(item.sessionTitle || item.dialogueName),
        // 根据时间戳添加分组
        group: getGroupByTimestamp(new Date(item.createTime || item.updateTime).getTime()),
    }));
});

/**
 * 根据时间戳获取分组名称
 * @param {Number} timestamp - 时间戳
 * @returns {String} 分组名称
 */
function getGroupByTimestamp(timestamp) {
    const now = Date.now();
    const oneDay = 86400000;
    const oneWeek = oneDay * 7;

    if (now - timestamp < oneDay) {
        return '今天';
    } else if (now - timestamp < oneDay * 2) {
        return '昨天';
    } else if (now - timestamp < oneWeek) {
        return '一周内';
    } else {
        return '更早';
    }
}

/**
 * 展开/收起历史记录
 */
const toggleHistory = () => {
    if (!props.isTreeCollapsed) {
        showHistory.value = !showHistory.value;
        console.log('历史记录显示状态:', showHistory.value);
    }
};

/**
 * @description 切换会话
 * @param {Object} item - 选中的会话项
 */
function handleChange(item) {
    // 保存当前会话消息列表到chatMap
    // sessionStore.setCurrentSession(item);

    // 导航到新会话
    router.replace({
        name: 'chatWithId',
        params: {
            id: item.id,
        },
    });
}
</script>

<style scoped lang="scss">
// .conversations {
//     padding: 0 10px;
//     background-color: transparent;
//     border-radius: 8px;
//     box-shadow: none;
//     width: 230px;
// }
:deep(.conversations-container) {
    box-shadow: none !important;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0 10px;
    background-color: transparent;
    border-radius: 8px;
    box-shadow: none;
}

/* 使用深度选择器修改对话项样式，实现文本超出显示省略号 */
:deep(.conversation-group-items),
:deep(.scroll-content) {
    /* 针对Item组件 */
    div[class*='item'] {
        /* 标签/文本容器 */
        div[class*='label'] {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
        }
    }
}
// 样式穿透
:deep() {
    // 群组标题样式 和 侧边栏菜单背景色一致
    .conversation-group-title {
        // padding-left: 12px !important;
        background-color: rgba(241, 244, 253) !important;
    }
}

/* 历史记录样式 */
.history-container {
    .custom-group-title {
        font-size: 13px;
        color: #666;
        font-weight: 500;
    }

    .history-header {
        display: flex;
        align-items: center;
        cursor: pointer;
        margin-bottom: 20px;

        .header-left {
            display: flex;
            align-items: center;

            span {
                font-size: 14px;
                color: #333;
            }
        }

        .toggle-btn {
            font-size: 12px;
            color: #999;

            &:hover {
                color: #1890ff;
            }
        }
    }

    .empty-history {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100px;
        color: #999;
        font-size: 14px;
    }
    .his-item {
        color: #667085;
        font-size: 14px;
        font-weight: 500;
    }
    .action-icons {
        display: flex;
        align-items: center;
        gap: 8px;

        .action-icon {
            width: 16px;
            height: 16px;
            cursor: pointer;
            opacity: 0.6;
            transition: all 0.2s;

            &:hover {
                opacity: 1;
            }

            &.like-icon {
                background: url('/static/dialogue/dianzan.svg') no-repeat center / contain;

                &.active {
                    background: url('/static/dialogue/dianzan-a.svg') no-repeat center / contain;
                }
            }

            &.delete-icon {
                background: url('/static/dialogue/shanchu.svg') no-repeat center / contain;
            }

            &.edit-icon {
                background: url('/static/dialogue/bianji.svg') no-repeat center / contain;
            }
        }
    }
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>
