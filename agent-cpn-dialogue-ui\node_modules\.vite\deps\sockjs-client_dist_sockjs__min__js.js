import {
  __commonJS,
  __require
} from "./chunk-2LSFTFF7.js";

// node_modules/.pnpm/sockjs-client@1.6.1/node_modules/sockjs-client/dist/sockjs.min.js
var require_sockjs_min = __commonJS({
  "node_modules/.pnpm/sockjs-client@1.6.1/node_modules/sockjs-client/dist/sockjs.min.js"(exports, module) {
    !function(e) {
      if ("object" == typeof exports && "undefined" != typeof module)
        module.exports = e();
      else if ("function" == typeof define && define.amd)
        define([], e);
      else {
        ("undefined" != typeof window ? window : "undefined" != typeof global ? global : "undefined" != typeof self ? self : this).SockJS = e();
      }
    }(function() {
      return function i(s, a, l) {
        function u(t, e2) {
          if (!a[t]) {
            if (!s[t]) {
              var n = "function" == typeof __require && __require;
              if (!e2 && n)
                return n(t, true);
              if (c)
                return c(t, true);
              var r = new Error("Cannot find module '" + t + "'");
              throw r.code = "MODULE_NOT_FOUND", r;
            }
            var o = a[t] = { exports: {} };
            s[t][0].call(o.exports, function(e3) {
              return u(s[t][1][e3] || e3);
            }, o, o.exports, i, s, a, l);
          }
          return a[t].exports;
        }
        for (var c = "function" == typeof __require && __require, e = 0; e < l.length; e++)
          u(l[e]);
        return u;
      }({ 1: [function(n, r, e) {
        (function(t) {
          (function() {
            "use strict";
            var e2 = n("./transport-list");
            r.exports = n("./main")(e2), "_sockjs_onload" in t && setTimeout(t._sockjs_onload, 1);
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, { "./main": 14, "./transport-list": 16 }], 2: [function(e, t, n) {
        "use strict";
        var r = e("inherits"), o = e("./event");
        function i() {
          o.call(this), this.initEvent("close", false, false), this.wasClean = false, this.code = 0, this.reason = "";
        }
        r(i, o), t.exports = i;
      }, { "./event": 4, "inherits": 54 }], 3: [function(e, t, n) {
        "use strict";
        var r = e("inherits"), o = e("./eventtarget");
        function i() {
          o.call(this);
        }
        r(i, o), i.prototype.removeAllListeners = function(e2) {
          e2 ? delete this._listeners[e2] : this._listeners = {};
        }, i.prototype.once = function(t2, n2) {
          var r2 = this, o2 = false;
          this.on(t2, function e2() {
            r2.removeListener(t2, e2), o2 || (o2 = true, n2.apply(this, arguments));
          });
        }, i.prototype.emit = function() {
          var e2 = arguments[0], t2 = this._listeners[e2];
          if (t2) {
            for (var n2 = arguments.length, r2 = new Array(n2 - 1), o2 = 1; o2 < n2; o2++)
              r2[o2 - 1] = arguments[o2];
            for (var i2 = 0; i2 < t2.length; i2++)
              t2[i2].apply(this, r2);
          }
        }, i.prototype.on = i.prototype.addListener = o.prototype.addEventListener, i.prototype.removeListener = o.prototype.removeEventListener, t.exports.EventEmitter = i;
      }, { "./eventtarget": 5, "inherits": 54 }], 4: [function(e, t, n) {
        "use strict";
        function r(e2) {
          this.type = e2;
        }
        r.prototype.initEvent = function(e2, t2, n2) {
          return this.type = e2, this.bubbles = t2, this.cancelable = n2, this.timeStamp = +/* @__PURE__ */ new Date(), this;
        }, r.prototype.stopPropagation = function() {
        }, r.prototype.preventDefault = function() {
        }, r.CAPTURING_PHASE = 1, r.AT_TARGET = 2, r.BUBBLING_PHASE = 3, t.exports = r;
      }, {}], 5: [function(e, t, n) {
        "use strict";
        function r() {
          this._listeners = {};
        }
        r.prototype.addEventListener = function(e2, t2) {
          e2 in this._listeners || (this._listeners[e2] = []);
          var n2 = this._listeners[e2];
          -1 === n2.indexOf(t2) && (n2 = n2.concat([t2])), this._listeners[e2] = n2;
        }, r.prototype.removeEventListener = function(e2, t2) {
          var n2 = this._listeners[e2];
          if (n2) {
            var r2 = n2.indexOf(t2);
            -1 === r2 || (1 < n2.length ? this._listeners[e2] = n2.slice(0, r2).concat(n2.slice(r2 + 1)) : delete this._listeners[e2]);
          }
        }, r.prototype.dispatchEvent = function() {
          var e2 = arguments[0], t2 = e2.type, n2 = 1 === arguments.length ? [e2] : Array.apply(null, arguments);
          if (this["on" + t2] && this["on" + t2].apply(this, n2), t2 in this._listeners)
            for (var r2 = this._listeners[t2], o = 0; o < r2.length; o++)
              r2[o].apply(this, n2);
        }, t.exports = r;
      }, {}], 6: [function(e, t, n) {
        "use strict";
        var r = e("inherits"), o = e("./event");
        function i(e2) {
          o.call(this), this.initEvent("message", false, false), this.data = e2;
        }
        r(i, o), t.exports = i;
      }, { "./event": 4, "inherits": 54 }], 7: [function(e, t, n) {
        "use strict";
        var r = e("./utils/iframe");
        function o(e2) {
          (this._transport = e2).on("message", this._transportMessage.bind(this)), e2.on("close", this._transportClose.bind(this));
        }
        o.prototype._transportClose = function(e2, t2) {
          r.postMessage("c", JSON.stringify([e2, t2]));
        }, o.prototype._transportMessage = function(e2) {
          r.postMessage("t", e2);
        }, o.prototype._send = function(e2) {
          this._transport.send(e2);
        }, o.prototype._close = function() {
          this._transport.close(), this._transport.removeAllListeners();
        }, t.exports = o;
      }, { "./utils/iframe": 47 }], 8: [function(e, t, n) {
        "use strict";
        var f = e("./utils/url"), r = e("./utils/event"), h = e("./facade"), o = e("./info-iframe-receiver"), d = e("./utils/iframe"), p = e("./location"), m = function() {
        };
        t.exports = function(l, e2) {
          var u, c = {};
          e2.forEach(function(e3) {
            e3.facadeTransport && (c[e3.facadeTransport.transportName] = e3.facadeTransport);
          }), c[o.transportName] = o, l.bootstrap_iframe = function() {
            var a;
            d.currentWindowId = p.hash.slice(1);
            r.attachEvent("message", function(t2) {
              if (t2.source === parent && (void 0 === u && (u = t2.origin), t2.origin === u)) {
                var n2;
                try {
                  n2 = JSON.parse(t2.data);
                } catch (e4) {
                  return void m("bad json", t2.data);
                }
                if (n2.windowId === d.currentWindowId)
                  switch (n2.type) {
                    case "s":
                      var e3;
                      try {
                        e3 = JSON.parse(n2.data);
                      } catch (e4) {
                        m("bad json", n2.data);
                        break;
                      }
                      var r2 = e3[0], o2 = e3[1], i = e3[2], s = e3[3];
                      if (m(r2, o2, i, s), r2 !== l.version)
                        throw new Error('Incompatible SockJS! Main site uses: "' + r2 + '", the iframe: "' + l.version + '".');
                      if (!f.isOriginEqual(i, p.href) || !f.isOriginEqual(s, p.href))
                        throw new Error("Can't connect to different domain from within an iframe. (" + p.href + ", " + i + ", " + s + ")");
                      a = new h(new c[o2](i, s));
                      break;
                    case "m":
                      a._send(n2.data);
                      break;
                    case "c":
                      a && a._close(), a = null;
                  }
              }
            }), d.postMessage("s");
          };
        };
      }, { "./facade": 7, "./info-iframe-receiver": 10, "./location": 13, "./utils/event": 46, "./utils/iframe": 47, "./utils/url": 52, "debug": void 0 }], 9: [function(e, t, n) {
        "use strict";
        var r = e("events").EventEmitter, o = e("inherits"), s = e("./utils/object"), a = function() {
        };
        function i(e2, t2) {
          r.call(this);
          var o2 = this, i2 = +/* @__PURE__ */ new Date();
          this.xo = new t2("GET", e2), this.xo.once("finish", function(e3, t3) {
            var n2, r2;
            if (200 === e3) {
              if (r2 = +/* @__PURE__ */ new Date() - i2, t3)
                try {
                  n2 = JSON.parse(t3);
                } catch (e4) {
                  a("bad json", t3);
                }
              s.isObject(n2) || (n2 = {});
            }
            o2.emit("finish", n2, r2), o2.removeAllListeners();
          });
        }
        o(i, r), i.prototype.close = function() {
          this.removeAllListeners(), this.xo.close();
        }, t.exports = i;
      }, { "./utils/object": 49, "debug": void 0, "events": 3, "inherits": 54 }], 10: [function(e, t, n) {
        "use strict";
        var r = e("inherits"), o = e("events").EventEmitter, i = e("./transport/sender/xhr-local"), s = e("./info-ajax");
        function a(e2) {
          var n2 = this;
          o.call(this), this.ir = new s(e2, i), this.ir.once("finish", function(e3, t2) {
            n2.ir = null, n2.emit("message", JSON.stringify([e3, t2]));
          });
        }
        r(a, o), a.transportName = "iframe-info-receiver", a.prototype.close = function() {
          this.ir && (this.ir.close(), this.ir = null), this.removeAllListeners();
        }, t.exports = a;
      }, { "./info-ajax": 9, "./transport/sender/xhr-local": 37, "events": 3, "inherits": 54 }], 11: [function(n, o, e) {
        (function(u) {
          (function() {
            "use strict";
            var r = n("events").EventEmitter, e2 = n("inherits"), i = n("./utils/event"), s = n("./transport/iframe"), a = n("./info-iframe-receiver"), l = function() {
            };
            function t(t2, n2) {
              var o2 = this;
              r.call(this);
              function e3() {
                var e4 = o2.ifr = new s(a.transportName, n2, t2);
                e4.once("message", function(t3) {
                  if (t3) {
                    var e5;
                    try {
                      e5 = JSON.parse(t3);
                    } catch (e6) {
                      return l("bad json", t3), o2.emit("finish"), void o2.close();
                    }
                    var n3 = e5[0], r2 = e5[1];
                    o2.emit("finish", n3, r2);
                  }
                  o2.close();
                }), e4.once("close", function() {
                  o2.emit("finish"), o2.close();
                });
              }
              u.document.body ? e3() : i.attachEvent("load", e3);
            }
            e2(t, r), t.enabled = function() {
              return s.enabled();
            }, t.prototype.close = function() {
              this.ifr && this.ifr.close(), this.removeAllListeners(), this.ifr = null;
            }, o.exports = t;
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, { "./info-iframe-receiver": 10, "./transport/iframe": 22, "./utils/event": 46, "debug": void 0, "events": 3, "inherits": 54 }], 12: [function(e, t, n) {
        "use strict";
        var r = e("events").EventEmitter, o = e("inherits"), i = e("./utils/url"), s = e("./transport/sender/xdr"), a = e("./transport/sender/xhr-cors"), l = e("./transport/sender/xhr-local"), u = e("./transport/sender/xhr-fake"), c = e("./info-iframe"), f = e("./info-ajax"), h = function() {
        };
        function d(e2, t2) {
          h(e2);
          var n2 = this;
          r.call(this), setTimeout(function() {
            n2.doXhr(e2, t2);
          }, 0);
        }
        o(d, r), d._getReceiver = function(e2, t2, n2) {
          return n2.sameOrigin ? new f(t2, l) : a.enabled ? new f(t2, a) : s.enabled && n2.sameScheme ? new f(t2, s) : c.enabled() ? new c(e2, t2) : new f(t2, u);
        }, d.prototype.doXhr = function(e2, t2) {
          var n2 = this, r2 = i.addPath(e2, "/info");
          h("doXhr", r2), this.xo = d._getReceiver(e2, r2, t2), this.timeoutRef = setTimeout(function() {
            h("timeout"), n2._cleanup(false), n2.emit("finish");
          }, d.timeout), this.xo.once("finish", function(e3, t3) {
            h("finish", e3, t3), n2._cleanup(true), n2.emit("finish", e3, t3);
          });
        }, d.prototype._cleanup = function(e2) {
          h("_cleanup"), clearTimeout(this.timeoutRef), this.timeoutRef = null, !e2 && this.xo && this.xo.close(), this.xo = null;
        }, d.prototype.close = function() {
          h("close"), this.removeAllListeners(), this._cleanup(false);
        }, d.timeout = 8e3, t.exports = d;
      }, { "./info-ajax": 9, "./info-iframe": 11, "./transport/sender/xdr": 34, "./transport/sender/xhr-cors": 35, "./transport/sender/xhr-fake": 36, "./transport/sender/xhr-local": 37, "./utils/url": 52, "debug": void 0, "events": 3, "inherits": 54 }], 13: [function(e, t, n) {
        (function(e2) {
          (function() {
            "use strict";
            t.exports = e2.location || { origin: "http://localhost:80", protocol: "http:", host: "localhost", port: 80, href: "http://localhost/", hash: "" };
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, {}], 14: [function(x, _, e) {
        (function(w) {
          (function() {
            "use strict";
            x("./shims");
            var r, l = x("url-parse"), e2 = x("inherits"), u = x("./utils/random"), t = x("./utils/escape"), c = x("./utils/url"), i = x("./utils/event"), n = x("./utils/transport"), o = x("./utils/object"), f = x("./utils/browser"), h = x("./utils/log"), s = x("./event/event"), d = x("./event/eventtarget"), p = x("./location"), a = x("./event/close"), m = x("./event/trans-message"), v = x("./info-receiver"), b = function() {
            };
            function y(e3, t2, n2) {
              if (!(this instanceof y))
                return new y(e3, t2, n2);
              if (arguments.length < 1)
                throw new TypeError("Failed to construct 'SockJS: 1 argument required, but only 0 present");
              d.call(this), this.readyState = y.CONNECTING, this.extensions = "", this.protocol = "", (n2 = n2 || {}).protocols_whitelist && h.warn("'protocols_whitelist' is DEPRECATED. Use 'transports' instead."), this._transportsWhitelist = n2.transports, this._transportOptions = n2.transportOptions || {}, this._timeout = n2.timeout || 0;
              var r2 = n2.sessionId || 8;
              if ("function" == typeof r2)
                this._generateSessionId = r2;
              else {
                if ("number" != typeof r2)
                  throw new TypeError("If sessionId is used in the options, it needs to be a number or a function.");
                this._generateSessionId = function() {
                  return u.string(r2);
                };
              }
              this._server = n2.server || u.numberString(1e3);
              var o2 = new l(e3);
              if (!o2.host || !o2.protocol)
                throw new SyntaxError("The URL '" + e3 + "' is invalid");
              if (o2.hash)
                throw new SyntaxError("The URL must not contain a fragment");
              if ("http:" !== o2.protocol && "https:" !== o2.protocol)
                throw new SyntaxError("The URL's scheme must be either 'http:' or 'https:'. '" + o2.protocol + "' is not allowed.");
              var i2 = "https:" === o2.protocol;
              if ("https:" === p.protocol && !i2 && !c.isLoopbackAddr(o2.hostname))
                throw new Error("SecurityError: An insecure SockJS connection may not be initiated from a page loaded over HTTPS");
              t2 ? Array.isArray(t2) || (t2 = [t2]) : t2 = [];
              var s2 = t2.sort();
              s2.forEach(function(e4, t3) {
                if (!e4)
                  throw new SyntaxError("The protocols entry '" + e4 + "' is invalid.");
                if (t3 < s2.length - 1 && e4 === s2[t3 + 1])
                  throw new SyntaxError("The protocols entry '" + e4 + "' is duplicated.");
              });
              var a2 = c.getOrigin(p.href);
              this._origin = a2 ? a2.toLowerCase() : null, o2.set("pathname", o2.pathname.replace(/\/+$/, "")), this.url = o2.href, b("using url", this.url), this._urlInfo = { nullOrigin: !f.hasDomain(), sameOrigin: c.isOriginEqual(this.url, p.href), sameScheme: c.isSchemeEqual(this.url, p.href) }, this._ir = new v(this.url, this._urlInfo), this._ir.once("finish", this._receiveInfo.bind(this));
            }
            function g(e3) {
              return 1e3 === e3 || 3e3 <= e3 && e3 <= 4999;
            }
            e2(y, d), y.prototype.close = function(e3, t2) {
              if (e3 && !g(e3))
                throw new Error("InvalidAccessError: Invalid code");
              if (t2 && 123 < t2.length)
                throw new SyntaxError("reason argument has an invalid length");
              if (this.readyState !== y.CLOSING && this.readyState !== y.CLOSED) {
                this._close(e3 || 1e3, t2 || "Normal closure", true);
              }
            }, y.prototype.send = function(e3) {
              if ("string" != typeof e3 && (e3 = "" + e3), this.readyState === y.CONNECTING)
                throw new Error("InvalidStateError: The connection has not been established yet");
              this.readyState === y.OPEN && this._transport.send(t.quote(e3));
            }, y.version = x("./version"), y.CONNECTING = 0, y.OPEN = 1, y.CLOSING = 2, y.CLOSED = 3, y.prototype._receiveInfo = function(e3, t2) {
              if (b("_receiveInfo", t2), this._ir = null, e3) {
                this._rto = this.countRTO(t2), this._transUrl = e3.base_url ? e3.base_url : this.url, e3 = o.extend(e3, this._urlInfo), b("info", e3);
                var n2 = r.filterToEnabled(this._transportsWhitelist, e3);
                this._transports = n2.main, b(this._transports.length + " enabled transports"), this._connect();
              } else
                this._close(1002, "Cannot connect to server");
            }, y.prototype._connect = function() {
              for (var e3 = this._transports.shift(); e3; e3 = this._transports.shift()) {
                if (b("attempt", e3.transportName), e3.needBody && (!w.document.body || void 0 !== w.document.readyState && "complete" !== w.document.readyState && "interactive" !== w.document.readyState))
                  return b("waiting for body"), this._transports.unshift(e3), void i.attachEvent("load", this._connect.bind(this));
                var t2 = Math.max(this._timeout, this._rto * e3.roundTrips || 5e3);
                this._transportTimeoutId = setTimeout(this._transportTimeout.bind(this), t2), b("using timeout", t2);
                var n2 = c.addPath(this._transUrl, "/" + this._server + "/" + this._generateSessionId()), r2 = this._transportOptions[e3.transportName];
                b("transport url", n2);
                var o2 = new e3(n2, this._transUrl, r2);
                return o2.on("message", this._transportMessage.bind(this)), o2.once("close", this._transportClose.bind(this)), o2.transportName = e3.transportName, void (this._transport = o2);
              }
              this._close(2e3, "All transports failed", false);
            }, y.prototype._transportTimeout = function() {
              b("_transportTimeout"), this.readyState === y.CONNECTING && (this._transport && this._transport.close(), this._transportClose(2007, "Transport timed out"));
            }, y.prototype._transportMessage = function(e3) {
              b("_transportMessage", e3);
              var t2, n2 = this, r2 = e3.slice(0, 1), o2 = e3.slice(1);
              switch (r2) {
                case "o":
                  return void this._open();
                case "h":
                  return this.dispatchEvent(new s("heartbeat")), void b("heartbeat", this.transport);
              }
              if (o2)
                try {
                  t2 = JSON.parse(o2);
                } catch (e4) {
                  b("bad json", o2);
                }
              if (void 0 !== t2)
                switch (r2) {
                  case "a":
                    Array.isArray(t2) && t2.forEach(function(e4) {
                      b("message", n2.transport, e4), n2.dispatchEvent(new m(e4));
                    });
                    break;
                  case "m":
                    b("message", this.transport, t2), this.dispatchEvent(new m(t2));
                    break;
                  case "c":
                    Array.isArray(t2) && 2 === t2.length && this._close(t2[0], t2[1], true);
                }
              else
                b("empty payload", o2);
            }, y.prototype._transportClose = function(e3, t2) {
              b("_transportClose", this.transport, e3, t2), this._transport && (this._transport.removeAllListeners(), this._transport = null, this.transport = null), g(e3) || 2e3 === e3 || this.readyState !== y.CONNECTING ? this._close(e3, t2) : this._connect();
            }, y.prototype._open = function() {
              b("_open", this._transport && this._transport.transportName, this.readyState), this.readyState === y.CONNECTING ? (this._transportTimeoutId && (clearTimeout(this._transportTimeoutId), this._transportTimeoutId = null), this.readyState = y.OPEN, this.transport = this._transport.transportName, this.dispatchEvent(new s("open")), b("connected", this.transport)) : this._close(1006, "Server lost session");
            }, y.prototype._close = function(t2, n2, r2) {
              b("_close", this.transport, t2, n2, r2, this.readyState);
              var o2 = false;
              if (this._ir && (o2 = true, this._ir.close(), this._ir = null), this._transport && (this._transport.close(), this._transport = null, this.transport = null), this.readyState === y.CLOSED)
                throw new Error("InvalidStateError: SockJS has already been closed");
              this.readyState = y.CLOSING, setTimeout((function() {
                this.readyState = y.CLOSED, o2 && this.dispatchEvent(new s("error"));
                var e3 = new a("close");
                e3.wasClean = r2 || false, e3.code = t2 || 1e3, e3.reason = n2, this.dispatchEvent(e3), this.onmessage = this.onclose = this.onerror = null, b("disconnected");
              }).bind(this), 0);
            }, y.prototype.countRTO = function(e3) {
              return 100 < e3 ? 4 * e3 : 300 + e3;
            }, _.exports = function(e3) {
              return r = n(e3), x("./iframe-bootstrap")(y, e3), y;
            };
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, { "./event/close": 2, "./event/event": 4, "./event/eventtarget": 5, "./event/trans-message": 6, "./iframe-bootstrap": 8, "./info-receiver": 12, "./location": 13, "./shims": 15, "./utils/browser": 44, "./utils/escape": 45, "./utils/event": 46, "./utils/log": 48, "./utils/object": 49, "./utils/random": 50, "./utils/transport": 51, "./utils/url": 52, "./version": 53, "debug": void 0, "inherits": 54, "url-parse": 57 }], 15: [function(e, t, n) {
        "use strict";
        function a(e2) {
          return "[object Function]" === i.toString.call(e2);
        }
        function l(e2) {
          return "[object String]" === f.call(e2);
        }
        var o, c = Array.prototype, i = Object.prototype, r = Function.prototype, s = String.prototype, u = c.slice, f = i.toString, h = Object.defineProperty && function() {
          try {
            return Object.defineProperty({}, "x", {}), true;
          } catch (e2) {
            return false;
          }
        }();
        o = h ? function(e2, t2, n2, r2) {
          !r2 && t2 in e2 || Object.defineProperty(e2, t2, { configurable: true, enumerable: false, writable: true, value: n2 });
        } : function(e2, t2, n2, r2) {
          !r2 && t2 in e2 || (e2[t2] = n2);
        };
        function d(e2, t2, n2) {
          for (var r2 in t2)
            i.hasOwnProperty.call(t2, r2) && o(e2, r2, t2[r2], n2);
        }
        function p(e2) {
          if (null == e2)
            throw new TypeError("can't convert " + e2 + " to object");
          return Object(e2);
        }
        function m() {
        }
        d(r, { bind: function(t2) {
          var n2 = this;
          if (!a(n2))
            throw new TypeError("Function.prototype.bind called on incompatible " + n2);
          for (var r2 = u.call(arguments, 1), e2 = Math.max(0, n2.length - r2.length), o2 = [], i2 = 0; i2 < e2; i2++)
            o2.push("$" + i2);
          var s2 = Function("binder", "return function (" + o2.join(",") + "){ return binder.apply(this, arguments); }")(function() {
            if (this instanceof s2) {
              var e3 = n2.apply(this, r2.concat(u.call(arguments)));
              return Object(e3) === e3 ? e3 : this;
            }
            return n2.apply(t2, r2.concat(u.call(arguments)));
          });
          return n2.prototype && (m.prototype = n2.prototype, s2.prototype = new m(), m.prototype = null), s2;
        } }), d(Array, { isArray: function(e2) {
          return "[object Array]" === f.call(e2);
        } });
        var v, b, y, g = Object("a"), w = "a" !== g[0] || !(0 in g);
        d(c, { forEach: function(e2, t2) {
          var n2 = p(this), r2 = w && l(this) ? this.split("") : n2, o2 = t2, i2 = -1, s2 = r2.length >>> 0;
          if (!a(e2))
            throw new TypeError();
          for (; ++i2 < s2; )
            i2 in r2 && e2.call(o2, r2[i2], i2, n2);
        } }, (v = c.forEach, y = b = true, v && (v.call("foo", function(e2, t2, n2) {
          "object" != typeof n2 && (b = false);
        }), v.call([1], function() {
          y = "string" == typeof this;
        }, "x")), !(v && b && y)));
        var x = Array.prototype.indexOf && -1 !== [0, 1].indexOf(1, 2);
        d(c, { indexOf: function(e2, t2) {
          var n2 = w && l(this) ? this.split("") : p(this), r2 = n2.length >>> 0;
          if (!r2)
            return -1;
          var o2 = 0;
          for (1 < arguments.length && (o2 = function(e3) {
            var t3 = +e3;
            return t3 != t3 ? t3 = 0 : 0 !== t3 && t3 !== 1 / 0 && t3 !== -1 / 0 && (t3 = (0 < t3 || -1) * Math.floor(Math.abs(t3))), t3;
          }(t2)), o2 = 0 <= o2 ? o2 : Math.max(0, r2 + o2); o2 < r2; o2++)
            if (o2 in n2 && n2[o2] === e2)
              return o2;
          return -1;
        } }, x);
        var _, E = s.split;
        2 !== "ab".split(/(?:ab)*/).length || 4 !== ".".split(/(.?)(.?)/).length || "t" === "tesst".split(/(s)*/)[1] || 4 !== "test".split(/(?:)/, -1).length || "".split(/.?/).length || 1 < ".".split(/()()/).length ? (_ = void 0 === /()??/.exec("")[1], s.split = function(e2, t2) {
          var n2 = this;
          if (void 0 === e2 && 0 === t2)
            return [];
          if ("[object RegExp]" !== f.call(e2))
            return E.call(this, e2, t2);
          var r2, o2, i2, s2, a2 = [], l2 = (e2.ignoreCase ? "i" : "") + (e2.multiline ? "m" : "") + (e2.extended ? "x" : "") + (e2.sticky ? "y" : ""), u2 = 0;
          for (e2 = new RegExp(e2.source, l2 + "g"), n2 += "", _ || (r2 = new RegExp("^" + e2.source + "$(?!\\s)", l2)), t2 = void 0 === t2 ? -1 >>> 0 : function(e3) {
            return e3 >>> 0;
          }(t2); (o2 = e2.exec(n2)) && !(u2 < (i2 = o2.index + o2[0].length) && (a2.push(n2.slice(u2, o2.index)), !_ && 1 < o2.length && o2[0].replace(r2, function() {
            for (var e3 = 1; e3 < arguments.length - 2; e3++)
              void 0 === arguments[e3] && (o2[e3] = void 0);
          }), 1 < o2.length && o2.index < n2.length && c.push.apply(a2, o2.slice(1)), s2 = o2[0].length, u2 = i2, a2.length >= t2)); )
            e2.lastIndex === o2.index && e2.lastIndex++;
          return u2 === n2.length ? !s2 && e2.test("") || a2.push("") : a2.push(n2.slice(u2)), a2.length > t2 ? a2.slice(0, t2) : a2;
        }) : "0".split(void 0, 0).length && (s.split = function(e2, t2) {
          return void 0 === e2 && 0 === t2 ? [] : E.call(this, e2, t2);
        });
        var S = s.substr, O = "".substr && "b" !== "0b".substr(-1);
        d(s, { substr: function(e2, t2) {
          return S.call(this, e2 < 0 && (e2 = this.length + e2) < 0 ? 0 : e2, t2);
        } }, O);
      }, {}], 16: [function(e, t, n) {
        "use strict";
        t.exports = [e("./transport/websocket"), e("./transport/xhr-streaming"), e("./transport/xdr-streaming"), e("./transport/eventsource"), e("./transport/lib/iframe-wrap")(e("./transport/eventsource")), e("./transport/htmlfile"), e("./transport/lib/iframe-wrap")(e("./transport/htmlfile")), e("./transport/xhr-polling"), e("./transport/xdr-polling"), e("./transport/lib/iframe-wrap")(e("./transport/xhr-polling")), e("./transport/jsonp-polling")];
      }, { "./transport/eventsource": 20, "./transport/htmlfile": 21, "./transport/jsonp-polling": 23, "./transport/lib/iframe-wrap": 26, "./transport/websocket": 38, "./transport/xdr-polling": 39, "./transport/xdr-streaming": 40, "./transport/xhr-polling": 41, "./transport/xhr-streaming": 42 }], 17: [function(o, f, e) {
        (function(r) {
          (function() {
            "use strict";
            var i = o("events").EventEmitter, e2 = o("inherits"), s = o("../../utils/event"), a = o("../../utils/url"), l = r.XMLHttpRequest, u = function() {
            };
            function c(e3, t2, n2, r2) {
              u(e3, t2);
              var o2 = this;
              i.call(this), setTimeout(function() {
                o2._start(e3, t2, n2, r2);
              }, 0);
            }
            e2(c, i), c.prototype._start = function(e3, t2, n2, r2) {
              var o2 = this;
              try {
                this.xhr = new l();
              } catch (e4) {
              }
              if (!this.xhr)
                return u("no xhr"), this.emit("finish", 0, "no xhr support"), void this._cleanup();
              t2 = a.addQuery(t2, "t=" + +/* @__PURE__ */ new Date()), this.unloadRef = s.unloadAdd(function() {
                u("unload cleanup"), o2._cleanup(true);
              });
              try {
                this.xhr.open(e3, t2, true), this.timeout && "timeout" in this.xhr && (this.xhr.timeout = this.timeout, this.xhr.ontimeout = function() {
                  u("xhr timeout"), o2.emit("finish", 0, ""), o2._cleanup(false);
                });
              } catch (e4) {
                return u("exception", e4), this.emit("finish", 0, ""), void this._cleanup(false);
              }
              if (r2 && r2.noCredentials || !c.supportsCORS || (u("withCredentials"), this.xhr.withCredentials = true), r2 && r2.headers)
                for (var i2 in r2.headers)
                  this.xhr.setRequestHeader(i2, r2.headers[i2]);
              this.xhr.onreadystatechange = function() {
                if (o2.xhr) {
                  var e4, t3, n3 = o2.xhr;
                  switch (u("readyState", n3.readyState), n3.readyState) {
                    case 3:
                      try {
                        t3 = n3.status, e4 = n3.responseText;
                      } catch (e5) {
                      }
                      u("status", t3), 1223 === t3 && (t3 = 204), 200 === t3 && e4 && 0 < e4.length && (u("chunk"), o2.emit("chunk", t3, e4));
                      break;
                    case 4:
                      t3 = n3.status, u("status", t3), 1223 === t3 && (t3 = 204), 12005 !== t3 && 12029 !== t3 || (t3 = 0), u("finish", t3, n3.responseText), o2.emit("finish", t3, n3.responseText), o2._cleanup(false);
                  }
                }
              };
              try {
                o2.xhr.send(n2);
              } catch (e4) {
                o2.emit("finish", 0, ""), o2._cleanup(false);
              }
            }, c.prototype._cleanup = function(e3) {
              if (u("cleanup"), this.xhr) {
                if (this.removeAllListeners(), s.unloadDel(this.unloadRef), this.xhr.onreadystatechange = function() {
                }, this.xhr.ontimeout && (this.xhr.ontimeout = null), e3)
                  try {
                    this.xhr.abort();
                  } catch (e4) {
                  }
                this.unloadRef = this.xhr = null;
              }
            }, c.prototype.close = function() {
              u("close"), this._cleanup(true);
            }, c.enabled = !!l;
            var t = ["Active"].concat("Object").join("X");
            !c.enabled && t in r && (u("overriding xmlhttprequest"), c.enabled = !!new (l = function() {
              try {
                return new r[t]("Microsoft.XMLHTTP");
              } catch (e3) {
                return null;
              }
            })());
            var n = false;
            try {
              n = "withCredentials" in new l();
            } catch (e3) {
            }
            c.supportsCORS = n, f.exports = c;
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, { "../../utils/event": 46, "../../utils/url": 52, "debug": void 0, "events": 3, "inherits": 54 }], 18: [function(e, t, n) {
        (function(e2) {
          (function() {
            t.exports = e2.EventSource;
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, {}], 19: [function(e, n, t) {
        (function(e2) {
          (function() {
            "use strict";
            var t2 = e2.WebSocket || e2.MozWebSocket;
            n.exports = t2 ? function(e3) {
              return new t2(e3);
            } : void 0;
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, {}], 20: [function(e, t, n) {
        "use strict";
        var r = e("inherits"), o = e("./lib/ajax-based"), i = e("./receiver/eventsource"), s = e("./sender/xhr-cors"), a = e("eventsource");
        function l(e2) {
          if (!l.enabled())
            throw new Error("Transport created when disabled");
          o.call(this, e2, "/eventsource", i, s);
        }
        r(l, o), l.enabled = function() {
          return !!a;
        }, l.transportName = "eventsource", l.roundTrips = 2, t.exports = l;
      }, { "./lib/ajax-based": 24, "./receiver/eventsource": 29, "./sender/xhr-cors": 35, "eventsource": 18, "inherits": 54 }], 21: [function(e, t, n) {
        "use strict";
        var r = e("inherits"), o = e("./receiver/htmlfile"), i = e("./sender/xhr-local"), s = e("./lib/ajax-based");
        function a(e2) {
          if (!o.enabled)
            throw new Error("Transport created when disabled");
          s.call(this, e2, "/htmlfile", o, i);
        }
        r(a, s), a.enabled = function(e2) {
          return o.enabled && e2.sameOrigin;
        }, a.transportName = "htmlfile", a.roundTrips = 2, t.exports = a;
      }, { "./lib/ajax-based": 24, "./receiver/htmlfile": 30, "./sender/xhr-local": 37, "inherits": 54 }], 22: [function(e, t, n) {
        "use strict";
        var r = e("inherits"), i = e("events").EventEmitter, o = e("../version"), s = e("../utils/url"), a = e("../utils/iframe"), l = e("../utils/event"), u = e("../utils/random"), c = function() {
        };
        function f(e2, t2, n2) {
          if (!f.enabled())
            throw new Error("Transport created when disabled");
          i.call(this);
          var r2 = this;
          this.origin = s.getOrigin(n2), this.baseUrl = n2, this.transUrl = t2, this.transport = e2, this.windowId = u.string(8);
          var o2 = s.addPath(n2, "/iframe.html") + "#" + this.windowId;
          c(e2, t2, o2), this.iframeObj = a.createIframe(o2, function(e3) {
            c("err callback"), r2.emit("close", 1006, "Unable to load an iframe (" + e3 + ")"), r2.close();
          }), this.onmessageCallback = this._message.bind(this), l.attachEvent("message", this.onmessageCallback);
        }
        r(f, i), f.prototype.close = function() {
          if (c("close"), this.removeAllListeners(), this.iframeObj) {
            l.detachEvent("message", this.onmessageCallback);
            try {
              this.postMessage("c");
            } catch (e2) {
            }
            this.iframeObj.cleanup(), this.iframeObj = null, this.onmessageCallback = this.iframeObj = null;
          }
        }, f.prototype._message = function(t2) {
          if (c("message", t2.data), s.isOriginEqual(t2.origin, this.origin)) {
            var n2;
            try {
              n2 = JSON.parse(t2.data);
            } catch (e3) {
              return void c("bad json", t2.data);
            }
            if (n2.windowId === this.windowId)
              switch (n2.type) {
                case "s":
                  this.iframeObj.loaded(), this.postMessage("s", JSON.stringify([o, this.transport, this.transUrl, this.baseUrl]));
                  break;
                case "t":
                  this.emit("message", n2.data);
                  break;
                case "c":
                  var e2;
                  try {
                    e2 = JSON.parse(n2.data);
                  } catch (e3) {
                    return void c("bad json", n2.data);
                  }
                  this.emit("close", e2[0], e2[1]), this.close();
              }
            else
              c("mismatched window id", n2.windowId, this.windowId);
          } else
            c("not same origin", t2.origin, this.origin);
        }, f.prototype.postMessage = function(e2, t2) {
          c("postMessage", e2, t2), this.iframeObj.post(JSON.stringify({ windowId: this.windowId, type: e2, data: t2 || "" }), this.origin);
        }, f.prototype.send = function(e2) {
          c("send", e2), this.postMessage("m", e2);
        }, f.enabled = function() {
          return a.iframeEnabled;
        }, f.transportName = "iframe", f.roundTrips = 2, t.exports = f;
      }, { "../utils/event": 46, "../utils/iframe": 47, "../utils/random": 50, "../utils/url": 52, "../version": 53, "debug": void 0, "events": 3, "inherits": 54 }], 23: [function(s, a, e) {
        (function(i) {
          (function() {
            "use strict";
            var e2 = s("inherits"), t = s("./lib/sender-receiver"), n = s("./receiver/jsonp"), r = s("./sender/jsonp");
            function o(e3) {
              if (!o.enabled())
                throw new Error("Transport created when disabled");
              t.call(this, e3, "/jsonp", r, n);
            }
            e2(o, t), o.enabled = function() {
              return !!i.document;
            }, o.transportName = "jsonp-polling", o.roundTrips = 1, o.needBody = true, a.exports = o;
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, { "./lib/sender-receiver": 28, "./receiver/jsonp": 31, "./sender/jsonp": 33, "inherits": 54 }], 24: [function(e, t, n) {
        "use strict";
        var r = e("inherits"), a = e("../../utils/url"), o = e("./sender-receiver"), l = function() {
        };
        function i(e2, t2, n2, r2) {
          o.call(this, e2, t2, function(s) {
            return function(e3, t3, n3) {
              l("create ajax sender", e3, t3);
              var r3 = {};
              "string" == typeof t3 && (r3.headers = { "Content-type": "text/plain" });
              var o2 = a.addPath(e3, "/xhr_send"), i2 = new s("POST", o2, t3, r3);
              return i2.once("finish", function(e4) {
                if (l("finish", e4), i2 = null, 200 !== e4 && 204 !== e4)
                  return n3(new Error("http status " + e4));
                n3();
              }), function() {
                l("abort"), i2.close(), i2 = null;
                var e4 = new Error("Aborted");
                e4.code = 1e3, n3(e4);
              };
            };
          }(r2), n2, r2);
        }
        r(i, o), t.exports = i;
      }, { "../../utils/url": 52, "./sender-receiver": 28, "debug": void 0, "inherits": 54 }], 25: [function(e, t, n) {
        "use strict";
        var r = e("inherits"), o = e("events").EventEmitter, i = function() {
        };
        function s(e2, t2) {
          i(e2), o.call(this), this.sendBuffer = [], this.sender = t2, this.url = e2;
        }
        r(s, o), s.prototype.send = function(e2) {
          i("send", e2), this.sendBuffer.push(e2), this.sendStop || this.sendSchedule();
        }, s.prototype.sendScheduleWait = function() {
          i("sendScheduleWait");
          var e2, t2 = this;
          this.sendStop = function() {
            i("sendStop"), t2.sendStop = null, clearTimeout(e2);
          }, e2 = setTimeout(function() {
            i("timeout"), t2.sendStop = null, t2.sendSchedule();
          }, 25);
        }, s.prototype.sendSchedule = function() {
          i("sendSchedule", this.sendBuffer.length);
          var t2 = this;
          if (0 < this.sendBuffer.length) {
            var e2 = "[" + this.sendBuffer.join(",") + "]";
            this.sendStop = this.sender(this.url, e2, function(e3) {
              t2.sendStop = null, e3 ? (i("error", e3), t2.emit("close", e3.code || 1006, "Sending error: " + e3), t2.close()) : t2.sendScheduleWait();
            }), this.sendBuffer = [];
          }
        }, s.prototype._cleanup = function() {
          i("_cleanup"), this.removeAllListeners();
        }, s.prototype.close = function() {
          i("close"), this._cleanup(), this.sendStop && (this.sendStop(), this.sendStop = null);
        }, t.exports = s;
      }, { "debug": void 0, "events": 3, "inherits": 54 }], 26: [function(e, n, t) {
        (function(s) {
          (function() {
            "use strict";
            var t2 = e("inherits"), o = e("../iframe"), i = e("../../utils/object");
            n.exports = function(r) {
              function e2(e3, t3) {
                o.call(this, r.transportName, e3, t3);
              }
              return t2(e2, o), e2.enabled = function(e3, t3) {
                if (!s.document)
                  return false;
                var n2 = i.extend({}, t3);
                return n2.sameOrigin = true, r.enabled(n2) && o.enabled();
              }, e2.transportName = "iframe-" + r.transportName, e2.needBody = true, e2.roundTrips = o.roundTrips + r.roundTrips - 1, e2.facadeTransport = r, e2;
            };
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, { "../../utils/object": 49, "../iframe": 22, "inherits": 54 }], 27: [function(e, t, n) {
        "use strict";
        var r = e("inherits"), o = e("events").EventEmitter, i = function() {
        };
        function s(e2, t2, n2) {
          i(t2), o.call(this), this.Receiver = e2, this.receiveUrl = t2, this.AjaxObject = n2, this._scheduleReceiver();
        }
        r(s, o), s.prototype._scheduleReceiver = function() {
          i("_scheduleReceiver");
          var n2 = this, r2 = this.poll = new this.Receiver(this.receiveUrl, this.AjaxObject);
          r2.on("message", function(e2) {
            i("message", e2), n2.emit("message", e2);
          }), r2.once("close", function(e2, t2) {
            i("close", e2, t2, n2.pollIsClosing), n2.poll = r2 = null, n2.pollIsClosing || ("network" === t2 ? n2._scheduleReceiver() : (n2.emit("close", e2 || 1006, t2), n2.removeAllListeners()));
          });
        }, s.prototype.abort = function() {
          i("abort"), this.removeAllListeners(), this.pollIsClosing = true, this.poll && this.poll.abort();
        }, t.exports = s;
      }, { "debug": void 0, "events": 3, "inherits": 54 }], 28: [function(e, t, n) {
        "use strict";
        var r = e("inherits"), a = e("../../utils/url"), l = e("./buffered-sender"), u = e("./polling"), c = function() {
        };
        function o(e2, t2, n2, r2, o2) {
          var i = a.addPath(e2, t2);
          c(i);
          var s = this;
          l.call(this, e2, n2), this.poll = new u(r2, i, o2), this.poll.on("message", function(e3) {
            c("poll message", e3), s.emit("message", e3);
          }), this.poll.once("close", function(e3, t3) {
            c("poll close", e3, t3), s.poll = null, s.emit("close", e3, t3), s.close();
          });
        }
        r(o, l), o.prototype.close = function() {
          l.prototype.close.call(this), c("close"), this.removeAllListeners(), this.poll && (this.poll.abort(), this.poll = null);
        }, t.exports = o;
      }, { "../../utils/url": 52, "./buffered-sender": 25, "./polling": 27, "debug": void 0, "inherits": 54 }], 29: [function(e, t, n) {
        "use strict";
        var r = e("inherits"), o = e("events").EventEmitter, i = e("eventsource"), s = function() {
        };
        function a(e2) {
          s(e2), o.call(this);
          var n2 = this, r2 = this.es = new i(e2);
          r2.onmessage = function(e3) {
            s("message", e3.data), n2.emit("message", decodeURI(e3.data));
          }, r2.onerror = function(e3) {
            s("error", r2.readyState, e3);
            var t2 = 2 !== r2.readyState ? "network" : "permanent";
            n2._cleanup(), n2._close(t2);
          };
        }
        r(a, o), a.prototype.abort = function() {
          s("abort"), this._cleanup(), this._close("user");
        }, a.prototype._cleanup = function() {
          s("cleanup");
          var e2 = this.es;
          e2 && (e2.onmessage = e2.onerror = null, e2.close(), this.es = null);
        }, a.prototype._close = function(e2) {
          s("close", e2);
          var t2 = this;
          setTimeout(function() {
            t2.emit("close", null, e2), t2.removeAllListeners();
          }, 200);
        }, t.exports = a;
      }, { "debug": void 0, "events": 3, "eventsource": 18, "inherits": 54 }], 30: [function(n, c, e) {
        (function(u) {
          (function() {
            "use strict";
            var e2 = n("inherits"), r = n("../../utils/iframe"), o = n("../../utils/url"), i = n("events").EventEmitter, s = n("../../utils/random"), a = function() {
            };
            function l(e3) {
              a(e3), i.call(this);
              var t2 = this;
              r.polluteGlobalNamespace(), this.id = "a" + s.string(6), e3 = o.addQuery(e3, "c=" + decodeURIComponent(r.WPrefix + "." + this.id)), a("using htmlfile", l.htmlfileEnabled);
              var n2 = l.htmlfileEnabled ? r.createHtmlfile : r.createIframe;
              u[r.WPrefix][this.id] = { start: function() {
                a("start"), t2.iframeObj.loaded();
              }, message: function(e4) {
                a("message", e4), t2.emit("message", e4);
              }, stop: function() {
                a("stop"), t2._cleanup(), t2._close("network");
              } }, this.iframeObj = n2(e3, function() {
                a("callback"), t2._cleanup(), t2._close("permanent");
              });
            }
            e2(l, i), l.prototype.abort = function() {
              a("abort"), this._cleanup(), this._close("user");
            }, l.prototype._cleanup = function() {
              a("_cleanup"), this.iframeObj && (this.iframeObj.cleanup(), this.iframeObj = null), delete u[r.WPrefix][this.id];
            }, l.prototype._close = function(e3) {
              a("_close", e3), this.emit("close", null, e3), this.removeAllListeners();
            }, l.htmlfileEnabled = false;
            var t = ["Active"].concat("Object").join("X");
            if (t in u)
              try {
                l.htmlfileEnabled = !!new u[t]("htmlfile");
              } catch (e3) {
              }
            l.enabled = l.htmlfileEnabled || r.iframeEnabled, c.exports = l;
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, { "../../utils/iframe": 47, "../../utils/random": 50, "../../utils/url": 52, "debug": void 0, "events": 3, "inherits": 54 }], 31: [function(t, n, e) {
        (function(c) {
          (function() {
            "use strict";
            var r = t("../../utils/iframe"), i = t("../../utils/random"), s = t("../../utils/browser"), o = t("../../utils/url"), e2 = t("inherits"), a = t("events").EventEmitter, l = function() {
            };
            function u(e3) {
              l(e3);
              var t2 = this;
              a.call(this), r.polluteGlobalNamespace(), this.id = "a" + i.string(6);
              var n2 = o.addQuery(e3, "c=" + encodeURIComponent(r.WPrefix + "." + this.id));
              c[r.WPrefix][this.id] = this._callback.bind(this), this._createScript(n2), this.timeoutId = setTimeout(function() {
                l("timeout"), t2._abort(new Error("JSONP script loaded abnormally (timeout)"));
              }, u.timeout);
            }
            e2(u, a), u.prototype.abort = function() {
              if (l("abort"), c[r.WPrefix][this.id]) {
                var e3 = new Error("JSONP user aborted read");
                e3.code = 1e3, this._abort(e3);
              }
            }, u.timeout = 35e3, u.scriptErrorTimeout = 1e3, u.prototype._callback = function(e3) {
              l("_callback", e3), this._cleanup(), this.aborting || (e3 && (l("message", e3), this.emit("message", e3)), this.emit("close", null, "network"), this.removeAllListeners());
            }, u.prototype._abort = function(e3) {
              l("_abort", e3), this._cleanup(), this.aborting = true, this.emit("close", e3.code, e3.message), this.removeAllListeners();
            }, u.prototype._cleanup = function() {
              if (l("_cleanup"), clearTimeout(this.timeoutId), this.script2 && (this.script2.parentNode.removeChild(this.script2), this.script2 = null), this.script) {
                var e3 = this.script;
                e3.parentNode.removeChild(e3), e3.onreadystatechange = e3.onerror = e3.onload = e3.onclick = null, this.script = null;
              }
              delete c[r.WPrefix][this.id];
            }, u.prototype._scriptError = function() {
              l("_scriptError");
              var e3 = this;
              this.errorTimer || (this.errorTimer = setTimeout(function() {
                e3.loadedOkay || e3._abort(new Error("JSONP script loaded abnormally (onerror)"));
              }, u.scriptErrorTimeout));
            }, u.prototype._createScript = function(e3) {
              l("_createScript", e3);
              var t2, n2 = this, r2 = this.script = c.document.createElement("script");
              if (r2.id = "a" + i.string(8), r2.src = e3, r2.type = "text/javascript", r2.charset = "UTF-8", r2.onerror = this._scriptError.bind(this), r2.onload = function() {
                l("onload"), n2._abort(new Error("JSONP script loaded abnormally (onload)"));
              }, r2.onreadystatechange = function() {
                if (l("onreadystatechange", r2.readyState), /loaded|closed/.test(r2.readyState)) {
                  if (r2 && r2.htmlFor && r2.onclick) {
                    n2.loadedOkay = true;
                    try {
                      r2.onclick();
                    } catch (e4) {
                    }
                  }
                  r2 && n2._abort(new Error("JSONP script loaded abnormally (onreadystatechange)"));
                }
              }, void 0 === r2.async && c.document.attachEvent)
                if (s.isOpera())
                  (t2 = this.script2 = c.document.createElement("script")).text = "try{var a = document.getElementById('" + r2.id + "'); if(a)a.onerror();}catch(x){};", r2.async = t2.async = false;
                else {
                  try {
                    r2.htmlFor = r2.id, r2.event = "onclick";
                  } catch (e4) {
                  }
                  r2.async = true;
                }
              void 0 !== r2.async && (r2.async = true);
              var o2 = c.document.getElementsByTagName("head")[0];
              o2.insertBefore(r2, o2.firstChild), t2 && o2.insertBefore(t2, o2.firstChild);
            }, n.exports = u;
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, { "../../utils/browser": 44, "../../utils/iframe": 47, "../../utils/random": 50, "../../utils/url": 52, "debug": void 0, "events": 3, "inherits": 54 }], 32: [function(e, t, n) {
        "use strict";
        var r = e("inherits"), o = e("events").EventEmitter, i = function() {
        };
        function s(e2, t2) {
          i(e2), o.call(this);
          var r2 = this;
          this.bufferPosition = 0, this.xo = new t2("POST", e2, null), this.xo.on("chunk", this._chunkHandler.bind(this)), this.xo.once("finish", function(e3, t3) {
            i("finish", e3, t3), r2._chunkHandler(e3, t3), r2.xo = null;
            var n2 = 200 === e3 ? "network" : "permanent";
            i("close", n2), r2.emit("close", null, n2), r2._cleanup();
          });
        }
        r(s, o), s.prototype._chunkHandler = function(e2, t2) {
          if (i("_chunkHandler", e2), 200 === e2 && t2)
            for (var n2 = -1; ; this.bufferPosition += n2 + 1) {
              var r2 = t2.slice(this.bufferPosition);
              if (-1 === (n2 = r2.indexOf("\n")))
                break;
              var o2 = r2.slice(0, n2);
              o2 && (i("message", o2), this.emit("message", o2));
            }
        }, s.prototype._cleanup = function() {
          i("_cleanup"), this.removeAllListeners();
        }, s.prototype.abort = function() {
          i("abort"), this.xo && (this.xo.close(), i("close"), this.emit("close", null, "user"), this.xo = null), this._cleanup();
        }, t.exports = s;
      }, { "debug": void 0, "events": 3, "inherits": 54 }], 33: [function(e, t, n) {
        (function(f) {
          (function() {
            "use strict";
            var s, a, l = e("../../utils/random"), u = e("../../utils/url"), c = function() {
            };
            t.exports = function(e2, t2, n2) {
              c(e2, t2), s || (c("createForm"), (s = f.document.createElement("form")).style.display = "none", s.style.position = "absolute", s.method = "POST", s.enctype = "application/x-www-form-urlencoded", s.acceptCharset = "UTF-8", (a = f.document.createElement("textarea")).name = "d", s.appendChild(a), f.document.body.appendChild(s));
              var r = "a" + l.string(8);
              s.target = r, s.action = u.addQuery(u.addPath(e2, "/jsonp_send"), "i=" + r);
              var o = function(t3) {
                c("createIframe", t3);
                try {
                  return f.document.createElement('<iframe name="' + t3 + '">');
                } catch (e3) {
                  var n3 = f.document.createElement("iframe");
                  return n3.name = t3, n3;
                }
              }(r);
              o.id = r, o.style.display = "none", s.appendChild(o);
              try {
                a.value = t2;
              } catch (e3) {
              }
              s.submit();
              function i(e3) {
                c("completed", r, e3), o.onerror && (o.onreadystatechange = o.onerror = o.onload = null, setTimeout(function() {
                  c("cleaning up", r), o.parentNode.removeChild(o), o = null;
                }, 500), a.value = "", n2(e3));
              }
              return o.onerror = function() {
                c("onerror", r), i();
              }, o.onload = function() {
                c("onload", r), i();
              }, o.onreadystatechange = function(e3) {
                c("onreadystatechange", r, o.readyState, e3), "complete" === o.readyState && i();
              }, function() {
                c("aborted", r), i(new Error("Aborted"));
              };
            };
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, { "../../utils/random": 50, "../../utils/url": 52, "debug": void 0 }], 34: [function(r, u, e) {
        (function(l) {
          (function() {
            "use strict";
            var o = r("events").EventEmitter, e2 = r("inherits"), i = r("../../utils/event"), t = r("../../utils/browser"), s = r("../../utils/url"), a = function() {
            };
            function n(e3, t2, n2) {
              a(e3, t2);
              var r2 = this;
              o.call(this), setTimeout(function() {
                r2._start(e3, t2, n2);
              }, 0);
            }
            e2(n, o), n.prototype._start = function(e3, t2, n2) {
              a("_start");
              var r2 = this, o2 = new l.XDomainRequest();
              t2 = s.addQuery(t2, "t=" + +/* @__PURE__ */ new Date()), o2.onerror = function() {
                a("onerror"), r2._error();
              }, o2.ontimeout = function() {
                a("ontimeout"), r2._error();
              }, o2.onprogress = function() {
                a("progress", o2.responseText), r2.emit("chunk", 200, o2.responseText);
              }, o2.onload = function() {
                a("load"), r2.emit("finish", 200, o2.responseText), r2._cleanup(false);
              }, this.xdr = o2, this.unloadRef = i.unloadAdd(function() {
                r2._cleanup(true);
              });
              try {
                this.xdr.open(e3, t2), this.timeout && (this.xdr.timeout = this.timeout), this.xdr.send(n2);
              } catch (e4) {
                this._error();
              }
            }, n.prototype._error = function() {
              this.emit("finish", 0, ""), this._cleanup(false);
            }, n.prototype._cleanup = function(e3) {
              if (a("cleanup", e3), this.xdr) {
                if (this.removeAllListeners(), i.unloadDel(this.unloadRef), this.xdr.ontimeout = this.xdr.onerror = this.xdr.onprogress = this.xdr.onload = null, e3)
                  try {
                    this.xdr.abort();
                  } catch (e4) {
                  }
                this.unloadRef = this.xdr = null;
              }
            }, n.prototype.close = function() {
              a("close"), this._cleanup(true);
            }, n.enabled = !(!l.XDomainRequest || !t.hasDomain()), u.exports = n;
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, { "../../utils/browser": 44, "../../utils/event": 46, "../../utils/url": 52, "debug": void 0, "events": 3, "inherits": 54 }], 35: [function(e, t, n) {
        "use strict";
        var r = e("inherits"), o = e("../driver/xhr");
        function i(e2, t2, n2, r2) {
          o.call(this, e2, t2, n2, r2);
        }
        r(i, o), i.enabled = o.enabled && o.supportsCORS, t.exports = i;
      }, { "../driver/xhr": 17, "inherits": 54 }], 36: [function(e, t, n) {
        "use strict";
        var r = e("events").EventEmitter;
        function o() {
          var e2 = this;
          r.call(this), this.to = setTimeout(function() {
            e2.emit("finish", 200, "{}");
          }, o.timeout);
        }
        e("inherits")(o, r), o.prototype.close = function() {
          clearTimeout(this.to);
        }, o.timeout = 2e3, t.exports = o;
      }, { "events": 3, "inherits": 54 }], 37: [function(e, t, n) {
        "use strict";
        var r = e("inherits"), o = e("../driver/xhr");
        function i(e2, t2, n2) {
          o.call(this, e2, t2, n2, { noCredentials: true });
        }
        r(i, o), i.enabled = o.enabled, t.exports = i;
      }, { "../driver/xhr": 17, "inherits": 54 }], 38: [function(e, t, n) {
        "use strict";
        var i = e("../utils/event"), s = e("../utils/url"), r = e("inherits"), a = e("events").EventEmitter, l = e("./driver/websocket"), u = function() {
        };
        function c(e2, t2, n2) {
          if (!c.enabled())
            throw new Error("Transport created when disabled");
          a.call(this), u("constructor", e2);
          var r2 = this, o = s.addPath(e2, "/websocket");
          o = "https" === o.slice(0, 5) ? "wss" + o.slice(5) : "ws" + o.slice(4), this.url = o, this.ws = new l(this.url, [], n2), this.ws.onmessage = function(e3) {
            u("message event", e3.data), r2.emit("message", e3.data);
          }, this.unloadRef = i.unloadAdd(function() {
            u("unload"), r2.ws.close();
          }), this.ws.onclose = function(e3) {
            u("close event", e3.code, e3.reason), r2.emit("close", e3.code, e3.reason), r2._cleanup();
          }, this.ws.onerror = function(e3) {
            u("error event", e3), r2.emit("close", 1006, "WebSocket connection broken"), r2._cleanup();
          };
        }
        r(c, a), c.prototype.send = function(e2) {
          var t2 = "[" + e2 + "]";
          u("send", t2), this.ws.send(t2);
        }, c.prototype.close = function() {
          u("close");
          var e2 = this.ws;
          this._cleanup(), e2 && e2.close();
        }, c.prototype._cleanup = function() {
          u("_cleanup");
          var e2 = this.ws;
          e2 && (e2.onmessage = e2.onclose = e2.onerror = null), i.unloadDel(this.unloadRef), this.unloadRef = this.ws = null, this.removeAllListeners();
        }, c.enabled = function() {
          return u("enabled"), !!l;
        }, c.transportName = "websocket", c.roundTrips = 2, t.exports = c;
      }, { "../utils/event": 46, "../utils/url": 52, "./driver/websocket": 19, "debug": void 0, "events": 3, "inherits": 54 }], 39: [function(e, t, n) {
        "use strict";
        var r = e("inherits"), o = e("./lib/ajax-based"), i = e("./xdr-streaming"), s = e("./receiver/xhr"), a = e("./sender/xdr");
        function l(e2) {
          if (!a.enabled)
            throw new Error("Transport created when disabled");
          o.call(this, e2, "/xhr", s, a);
        }
        r(l, o), l.enabled = i.enabled, l.transportName = "xdr-polling", l.roundTrips = 2, t.exports = l;
      }, { "./lib/ajax-based": 24, "./receiver/xhr": 32, "./sender/xdr": 34, "./xdr-streaming": 40, "inherits": 54 }], 40: [function(e, t, n) {
        "use strict";
        var r = e("inherits"), o = e("./lib/ajax-based"), i = e("./receiver/xhr"), s = e("./sender/xdr");
        function a(e2) {
          if (!s.enabled)
            throw new Error("Transport created when disabled");
          o.call(this, e2, "/xhr_streaming", i, s);
        }
        r(a, o), a.enabled = function(e2) {
          return !e2.cookie_needed && !e2.nullOrigin && (s.enabled && e2.sameScheme);
        }, a.transportName = "xdr-streaming", a.roundTrips = 2, t.exports = a;
      }, { "./lib/ajax-based": 24, "./receiver/xhr": 32, "./sender/xdr": 34, "inherits": 54 }], 41: [function(e, t, n) {
        "use strict";
        var r = e("inherits"), o = e("./lib/ajax-based"), i = e("./receiver/xhr"), s = e("./sender/xhr-cors"), a = e("./sender/xhr-local");
        function l(e2) {
          if (!a.enabled && !s.enabled)
            throw new Error("Transport created when disabled");
          o.call(this, e2, "/xhr", i, s);
        }
        r(l, o), l.enabled = function(e2) {
          return !e2.nullOrigin && (!(!a.enabled || !e2.sameOrigin) || s.enabled);
        }, l.transportName = "xhr-polling", l.roundTrips = 2, t.exports = l;
      }, { "./lib/ajax-based": 24, "./receiver/xhr": 32, "./sender/xhr-cors": 35, "./sender/xhr-local": 37, "inherits": 54 }], 42: [function(l, u, e) {
        (function(a) {
          (function() {
            "use strict";
            var e2 = l("inherits"), t = l("./lib/ajax-based"), n = l("./receiver/xhr"), r = l("./sender/xhr-cors"), o = l("./sender/xhr-local"), i = l("../utils/browser");
            function s(e3) {
              if (!o.enabled && !r.enabled)
                throw new Error("Transport created when disabled");
              t.call(this, e3, "/xhr_streaming", n, r);
            }
            e2(s, t), s.enabled = function(e3) {
              return !e3.nullOrigin && (!i.isOpera() && r.enabled);
            }, s.transportName = "xhr-streaming", s.roundTrips = 2, s.needBody = !!a.document, u.exports = s;
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, { "../utils/browser": 44, "./lib/ajax-based": 24, "./receiver/xhr": 32, "./sender/xhr-cors": 35, "./sender/xhr-local": 37, "inherits": 54 }], 43: [function(e, t, n) {
        (function(n2) {
          (function() {
            "use strict";
            n2.crypto && n2.crypto.getRandomValues ? t.exports.randomBytes = function(e2) {
              var t2 = new Uint8Array(e2);
              return n2.crypto.getRandomValues(t2), t2;
            } : t.exports.randomBytes = function(e2) {
              for (var t2 = new Array(e2), n3 = 0; n3 < e2; n3++)
                t2[n3] = Math.floor(256 * Math.random());
              return t2;
            };
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, {}], 44: [function(e, t, n) {
        (function(e2) {
          (function() {
            "use strict";
            t.exports = { isOpera: function() {
              return e2.navigator && /opera/i.test(e2.navigator.userAgent);
            }, isKonqueror: function() {
              return e2.navigator && /konqueror/i.test(e2.navigator.userAgent);
            }, hasDomain: function() {
              if (!e2.document)
                return true;
              try {
                return !!e2.document.domain;
              } catch (e3) {
                return false;
              }
            } };
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, {}], 45: [function(e, t, n) {
        "use strict";
        var r, o = /[\x00-\x1f\ud800-\udfff\ufffe\uffff\u0300-\u0333\u033d-\u0346\u034a-\u034c\u0350-\u0352\u0357-\u0358\u035c-\u0362\u0374\u037e\u0387\u0591-\u05af\u05c4\u0610-\u0617\u0653-\u0654\u0657-\u065b\u065d-\u065e\u06df-\u06e2\u06eb-\u06ec\u0730\u0732-\u0733\u0735-\u0736\u073a\u073d\u073f-\u0741\u0743\u0745\u0747\u07eb-\u07f1\u0951\u0958-\u095f\u09dc-\u09dd\u09df\u0a33\u0a36\u0a59-\u0a5b\u0a5e\u0b5c-\u0b5d\u0e38-\u0e39\u0f43\u0f4d\u0f52\u0f57\u0f5c\u0f69\u0f72-\u0f76\u0f78\u0f80-\u0f83\u0f93\u0f9d\u0fa2\u0fa7\u0fac\u0fb9\u1939-\u193a\u1a17\u1b6b\u1cda-\u1cdb\u1dc0-\u1dcf\u1dfc\u1dfe\u1f71\u1f73\u1f75\u1f77\u1f79\u1f7b\u1f7d\u1fbb\u1fbe\u1fc9\u1fcb\u1fd3\u1fdb\u1fe3\u1feb\u1fee-\u1fef\u1ff9\u1ffb\u1ffd\u2000-\u2001\u20d0-\u20d1\u20d4-\u20d7\u20e7-\u20e9\u2126\u212a-\u212b\u2329-\u232a\u2adc\u302b-\u302c\uaab2-\uaab3\uf900-\ufa0d\ufa10\ufa12\ufa15-\ufa1e\ufa20\ufa22\ufa25-\ufa26\ufa2a-\ufa2d\ufa30-\ufa6d\ufa70-\ufad9\ufb1d\ufb1f\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufb4e\ufff0-\uffff]/g;
        t.exports = { quote: function(e2) {
          var t2 = JSON.stringify(e2);
          return o.lastIndex = 0, o.test(t2) ? (r = r || function(e3) {
            var t3, n2 = {}, r2 = [];
            for (t3 = 0; t3 < 65536; t3++)
              r2.push(String.fromCharCode(t3));
            return e3.lastIndex = 0, r2.join("").replace(e3, function(e4) {
              return n2[e4] = "\\u" + ("0000" + e4.charCodeAt(0).toString(16)).slice(-4), "";
            }), e3.lastIndex = 0, n2;
          }(o), t2.replace(o, function(e3) {
            return r[e3];
          })) : t2;
        } };
      }, {}], 46: [function(e, t, n) {
        (function(s) {
          (function() {
            "use strict";
            var n2 = e("./random"), r = {}, o = false, i = s.chrome && s.chrome.app && s.chrome.app.runtime;
            t.exports = { attachEvent: function(e2, t2) {
              void 0 !== s.addEventListener ? s.addEventListener(e2, t2, false) : s.document && s.attachEvent && (s.document.attachEvent("on" + e2, t2), s.attachEvent("on" + e2, t2));
            }, detachEvent: function(e2, t2) {
              void 0 !== s.addEventListener ? s.removeEventListener(e2, t2, false) : s.document && s.detachEvent && (s.document.detachEvent("on" + e2, t2), s.detachEvent("on" + e2, t2));
            }, unloadAdd: function(e2) {
              if (i)
                return null;
              var t2 = n2.string(8);
              return r[t2] = e2, o && setTimeout(this.triggerUnloadCallbacks, 0), t2;
            }, unloadDel: function(e2) {
              e2 in r && delete r[e2];
            }, triggerUnloadCallbacks: function() {
              for (var e2 in r)
                r[e2](), delete r[e2];
            } };
            i || t.exports.attachEvent("unload", function() {
              o || (o = true, t.exports.triggerUnloadCallbacks());
            });
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, { "./random": 50 }], 47: [function(t, p, e) {
        (function(d) {
          (function() {
            "use strict";
            var f = t("./event"), e2 = t("./browser"), h = function() {
            };
            p.exports = { WPrefix: "_jp", currentWindowId: null, polluteGlobalNamespace: function() {
              p.exports.WPrefix in d || (d[p.exports.WPrefix] = {});
            }, postMessage: function(e3, t2) {
              d.parent !== d ? d.parent.postMessage(JSON.stringify({ windowId: p.exports.currentWindowId, type: e3, data: t2 || "" }), "*") : h("Cannot postMessage, no parent window.", e3, t2);
            }, createIframe: function(e3, t2) {
              function n() {
                h("unattach"), clearTimeout(i);
                try {
                  a.onload = null;
                } catch (e4) {
                }
                a.onerror = null;
              }
              function r() {
                h("cleanup"), a && (n(), setTimeout(function() {
                  a && a.parentNode.removeChild(a), a = null;
                }, 0), f.unloadDel(s));
              }
              function o(e4) {
                h("onerror", e4), a && (r(), t2(e4));
              }
              var i, s, a = d.document.createElement("iframe");
              return a.src = e3, a.style.display = "none", a.style.position = "absolute", a.onerror = function() {
                o("onerror");
              }, a.onload = function() {
                h("onload"), clearTimeout(i), i = setTimeout(function() {
                  o("onload timeout");
                }, 2e3);
              }, d.document.body.appendChild(a), i = setTimeout(function() {
                o("timeout");
              }, 15e3), s = f.unloadAdd(r), { post: function(e4, t3) {
                h("post", e4, t3), setTimeout(function() {
                  try {
                    a && a.contentWindow && a.contentWindow.postMessage(e4, t3);
                  } catch (e5) {
                  }
                }, 0);
              }, cleanup: r, loaded: n };
            }, createHtmlfile: function(e3, t2) {
              function n() {
                clearTimeout(i), a.onerror = null;
              }
              function r() {
                u && (n(), f.unloadDel(s), a.parentNode.removeChild(a), a = u = null, CollectGarbage());
              }
              function o(e4) {
                h("onerror", e4), u && (r(), t2(e4));
              }
              var i, s, a, l = ["Active"].concat("Object").join("X"), u = new d[l]("htmlfile");
              u.open(), u.write('<html><script>document.domain="' + d.document.domain + '";<\/script></html>'), u.close(), u.parentWindow[p.exports.WPrefix] = d[p.exports.WPrefix];
              var c = u.createElement("div");
              return u.body.appendChild(c), a = u.createElement("iframe"), c.appendChild(a), a.src = e3, a.onerror = function() {
                o("onerror");
              }, i = setTimeout(function() {
                o("timeout");
              }, 15e3), s = f.unloadAdd(r), { post: function(e4, t3) {
                try {
                  setTimeout(function() {
                    a && a.contentWindow && a.contentWindow.postMessage(e4, t3);
                  }, 0);
                } catch (e5) {
                }
              }, cleanup: r, loaded: n };
            } }, p.exports.iframeEnabled = false, d.document && (p.exports.iframeEnabled = ("function" == typeof d.postMessage || "object" == typeof d.postMessage) && !e2.isKonqueror());
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, { "./browser": 44, "./event": 46, "debug": void 0 }], 48: [function(e, t, n) {
        (function(r) {
          (function() {
            "use strict";
            var n2 = {};
            ["log", "debug", "warn"].forEach(function(e2) {
              var t2;
              try {
                t2 = r.console && r.console[e2] && r.console[e2].apply;
              } catch (e3) {
              }
              n2[e2] = t2 ? function() {
                return r.console[e2].apply(r.console, arguments);
              } : "log" === e2 ? function() {
              } : n2.log;
            }), t.exports = n2;
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, {}], 49: [function(e, t, n) {
        "use strict";
        t.exports = { isObject: function(e2) {
          var t2 = typeof e2;
          return "function" == t2 || "object" == t2 && !!e2;
        }, extend: function(e2) {
          if (!this.isObject(e2))
            return e2;
          for (var t2, n2, r = 1, o = arguments.length; r < o; r++)
            for (n2 in t2 = arguments[r])
              Object.prototype.hasOwnProperty.call(t2, n2) && (e2[n2] = t2[n2]);
          return e2;
        } };
      }, {}], 50: [function(e, t, n) {
        "use strict";
        var i = e("crypto"), s = "abcdefghijklmnopqrstuvwxyz012345";
        t.exports = { string: function(e2) {
          for (var t2 = s.length, n2 = i.randomBytes(e2), r = [], o = 0; o < e2; o++)
            r.push(s.substr(n2[o] % t2, 1));
          return r.join("");
        }, number: function(e2) {
          return Math.floor(Math.random() * e2);
        }, numberString: function(e2) {
          var t2 = ("" + (e2 - 1)).length;
          return (new Array(t2 + 1).join("0") + this.number(e2)).slice(-t2);
        } };
      }, { "crypto": 43 }], 51: [function(e, t, n) {
        "use strict";
        var o = function() {
        };
        t.exports = function(e2) {
          return { filterToEnabled: function(t2, n2) {
            var r = { main: [], facade: [] };
            return t2 ? "string" == typeof t2 && (t2 = [t2]) : t2 = [], e2.forEach(function(e3) {
              e3 && ("websocket" !== e3.transportName || false !== n2.websocket ? t2.length && -1 === t2.indexOf(e3.transportName) ? o("not in whitelist", e3.transportName) : e3.enabled(n2) ? (o("enabled", e3.transportName), r.main.push(e3), e3.facadeTransport && r.facade.push(e3.facadeTransport)) : o("disabled", e3.transportName) : o("disabled from server", "websocket"));
            }), r;
          } };
        };
      }, { "debug": void 0 }], 52: [function(e, t, n) {
        "use strict";
        var r = e("url-parse"), o = function() {
        };
        t.exports = { getOrigin: function(e2) {
          if (!e2)
            return null;
          var t2 = new r(e2);
          if ("file:" === t2.protocol)
            return null;
          var n2 = t2.port;
          return n2 = n2 || ("https:" === t2.protocol ? "443" : "80"), t2.protocol + "//" + t2.hostname + ":" + n2;
        }, isOriginEqual: function(e2, t2) {
          var n2 = this.getOrigin(e2) === this.getOrigin(t2);
          return o("same", e2, t2, n2), n2;
        }, isSchemeEqual: function(e2, t2) {
          return e2.split(":")[0] === t2.split(":")[0];
        }, addPath: function(e2, t2) {
          var n2 = e2.split("?");
          return n2[0] + t2 + (n2[1] ? "?" + n2[1] : "");
        }, addQuery: function(e2, t2) {
          return e2 + (-1 === e2.indexOf("?") ? "?" + t2 : "&" + t2);
        }, isLoopbackAddr: function(e2) {
          return /^127\.([0-9]{1,3})\.([0-9]{1,3})\.([0-9]{1,3})$/i.test(e2) || /^\[::1\]$/.test(e2);
        } };
      }, { "debug": void 0, "url-parse": 57 }], 53: [function(e, t, n) {
        t.exports = "1.6.1";
      }, {}], 54: [function(e, t, n) {
        "function" == typeof Object.create ? t.exports = function(e2, t2) {
          t2 && (e2.super_ = t2, e2.prototype = Object.create(t2.prototype, { constructor: { value: e2, enumerable: false, writable: true, configurable: true } }));
        } : t.exports = function(e2, t2) {
          if (t2) {
            let n3 = function() {
            };
            var n2 = n3;
            e2.super_ = t2;
            n3.prototype = t2.prototype, e2.prototype = new n3(), e2.prototype.constructor = e2;
          }
        };
      }, {}], 55: [function(e, t, n) {
        "use strict";
        var i = Object.prototype.hasOwnProperty;
        function s(e2) {
          try {
            return decodeURIComponent(e2.replace(/\+/g, " "));
          } catch (e3) {
            return null;
          }
        }
        n.stringify = function(e2, t2) {
          t2 = t2 || "";
          var n2, r, o = [];
          for (r in "string" != typeof t2 && (t2 = "?"), e2)
            if (i.call(e2, r)) {
              if ((n2 = e2[r]) || null != n2 && !isNaN(n2) || (n2 = ""), r = encodeURIComponent(r), n2 = encodeURIComponent(n2), null === r || null === n2)
                continue;
              o.push(r + "=" + n2);
            }
          return o.length ? t2 + o.join("&") : "";
        }, n.parse = function(e2) {
          for (var t2, n2 = /([^=?&]+)=?([^&]*)/g, r = {}; t2 = n2.exec(e2); ) {
            var o = s(t2[1]), i2 = s(t2[2]);
            null === o || null === i2 || o in r || (r[o] = i2);
          }
          return r;
        };
      }, {}], 56: [function(e, t, n) {
        "use strict";
        t.exports = function(e2, t2) {
          if (t2 = t2.split(":")[0], !(e2 = +e2))
            return false;
          switch (t2) {
            case "http":
            case "ws":
              return 80 !== e2;
            case "https":
            case "wss":
              return 443 !== e2;
            case "ftp":
              return 21 !== e2;
            case "gopher":
              return 70 !== e2;
            case "file":
              return false;
          }
          return 0 !== e2;
        };
      }, {}], 57: [function(e, n, t) {
        (function(a) {
          (function() {
            "use strict";
            var d = e("requires-port"), p = e("querystringify"), t2 = /^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/, m = /[\n\r\t]/g, i = /^[A-Za-z][A-Za-z0-9+-.]*:\/\//, l = /:\d+$/, u = /^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i, v = /^[a-zA-Z]:/;
            function b(e2) {
              return (e2 || "").toString().replace(t2, "");
            }
            var y = [["#", "hash"], ["?", "query"], function(e2, t3) {
              return w(t3.protocol) ? e2.replace(/\\/g, "/") : e2;
            }, ["/", "pathname"], ["@", "auth", 1], [NaN, "host", void 0, 1, 1], [/:(\d*)$/, "port", void 0, 1], [NaN, "hostname", void 0, 1, 1]], s = { hash: 1, query: 1 };
            function g(e2) {
              var t3, n2 = ("undefined" != typeof window ? window : void 0 !== a ? a : "undefined" != typeof self ? self : {}).location || {}, r = {}, o = typeof (e2 = e2 || n2);
              if ("blob:" === e2.protocol)
                r = new _(unescape(e2.pathname), {});
              else if ("string" == o)
                for (t3 in r = new _(e2, {}), s)
                  delete r[t3];
              else if ("object" == o) {
                for (t3 in e2)
                  t3 in s || (r[t3] = e2[t3]);
                void 0 === r.slashes && (r.slashes = i.test(e2.href));
              }
              return r;
            }
            function w(e2) {
              return "file:" === e2 || "ftp:" === e2 || "http:" === e2 || "https:" === e2 || "ws:" === e2 || "wss:" === e2;
            }
            function x(e2, t3) {
              e2 = (e2 = b(e2)).replace(m, ""), t3 = t3 || {};
              var n2, r = u.exec(e2), o = r[1] ? r[1].toLowerCase() : "", i2 = !!r[2], s2 = !!r[3], a2 = 0;
              return i2 ? a2 = s2 ? (n2 = r[2] + r[3] + r[4], r[2].length + r[3].length) : (n2 = r[2] + r[4], r[2].length) : s2 ? (n2 = r[3] + r[4], a2 = r[3].length) : n2 = r[4], "file:" === o ? 2 <= a2 && (n2 = n2.slice(2)) : w(o) ? n2 = r[4] : o ? i2 && (n2 = n2.slice(2)) : 2 <= a2 && w(t3.protocol) && (n2 = r[4]), { protocol: o, slashes: i2 || w(o), slashesCount: a2, rest: n2 };
            }
            function _(e2, t3, n2) {
              if (e2 = (e2 = b(e2)).replace(m, ""), !(this instanceof _))
                return new _(e2, t3, n2);
              var r, o, i2, s2, a2, l2, u2 = y.slice(), c = typeof t3, f = this, h = 0;
              for ("object" != c && "string" != c && (n2 = t3, t3 = null), n2 && "function" != typeof n2 && (n2 = p.parse), r = !(o = x(e2 || "", t3 = g(t3))).protocol && !o.slashes, f.slashes = o.slashes || r && t3.slashes, f.protocol = o.protocol || t3.protocol || "", e2 = o.rest, ("file:" === o.protocol && (2 !== o.slashesCount || v.test(e2)) || !o.slashes && (o.protocol || o.slashesCount < 2 || !w(f.protocol))) && (u2[3] = [/(.*)/, "pathname"]); h < u2.length; h++)
                "function" != typeof (s2 = u2[h]) ? (i2 = s2[0], l2 = s2[1], i2 != i2 ? f[l2] = e2 : "string" == typeof i2 ? ~(a2 = "@" === i2 ? e2.lastIndexOf(i2) : e2.indexOf(i2)) && (e2 = "number" == typeof s2[2] ? (f[l2] = e2.slice(0, a2), e2.slice(a2 + s2[2])) : (f[l2] = e2.slice(a2), e2.slice(0, a2))) : (a2 = i2.exec(e2)) && (f[l2] = a2[1], e2 = e2.slice(0, a2.index)), f[l2] = f[l2] || r && s2[3] && t3[l2] || "", s2[4] && (f[l2] = f[l2].toLowerCase())) : e2 = s2(e2, f);
              n2 && (f.query = n2(f.query)), r && t3.slashes && "/" !== f.pathname.charAt(0) && ("" !== f.pathname || "" !== t3.pathname) && (f.pathname = function(e3, t4) {
                if ("" === e3)
                  return t4;
                for (var n3 = (t4 || "/").split("/").slice(0, -1).concat(e3.split("/")), r2 = n3.length, o2 = n3[r2 - 1], i3 = false, s3 = 0; r2--; )
                  "." === n3[r2] ? n3.splice(r2, 1) : ".." === n3[r2] ? (n3.splice(r2, 1), s3++) : s3 && (0 === r2 && (i3 = true), n3.splice(r2, 1), s3--);
                return i3 && n3.unshift(""), "." !== o2 && ".." !== o2 || n3.push(""), n3.join("/");
              }(f.pathname, t3.pathname)), "/" !== f.pathname.charAt(0) && w(f.protocol) && (f.pathname = "/" + f.pathname), d(f.port, f.protocol) || (f.host = f.hostname, f.port = ""), f.username = f.password = "", f.auth && (~(a2 = f.auth.indexOf(":")) ? (f.username = f.auth.slice(0, a2), f.username = encodeURIComponent(decodeURIComponent(f.username)), f.password = f.auth.slice(a2 + 1), f.password = encodeURIComponent(decodeURIComponent(f.password))) : f.username = encodeURIComponent(decodeURIComponent(f.auth)), f.auth = f.password ? f.username + ":" + f.password : f.username), f.origin = "file:" !== f.protocol && w(f.protocol) && f.host ? f.protocol + "//" + f.host : "null", f.href = f.toString();
            }
            _.prototype = { set: function(e2, t3, n2) {
              var r = this;
              switch (e2) {
                case "query":
                  "string" == typeof t3 && t3.length && (t3 = (n2 || p.parse)(t3)), r[e2] = t3;
                  break;
                case "port":
                  r[e2] = t3, d(t3, r.protocol) ? t3 && (r.host = r.hostname + ":" + t3) : (r.host = r.hostname, r[e2] = "");
                  break;
                case "hostname":
                  r[e2] = t3, r.port && (t3 += ":" + r.port), r.host = t3;
                  break;
                case "host":
                  r[e2] = t3, l.test(t3) ? (t3 = t3.split(":"), r.port = t3.pop(), r.hostname = t3.join(":")) : (r.hostname = t3, r.port = "");
                  break;
                case "protocol":
                  r.protocol = t3.toLowerCase(), r.slashes = !n2;
                  break;
                case "pathname":
                case "hash":
                  if (t3) {
                    var o = "pathname" === e2 ? "/" : "#";
                    r[e2] = t3.charAt(0) !== o ? o + t3 : t3;
                  } else
                    r[e2] = t3;
                  break;
                case "username":
                case "password":
                  r[e2] = encodeURIComponent(t3);
                  break;
                case "auth":
                  var i2 = t3.indexOf(":");
                  ~i2 ? (r.username = t3.slice(0, i2), r.username = encodeURIComponent(decodeURIComponent(r.username)), r.password = t3.slice(i2 + 1), r.password = encodeURIComponent(decodeURIComponent(r.password))) : r.username = encodeURIComponent(decodeURIComponent(t3));
              }
              for (var s2 = 0; s2 < y.length; s2++) {
                var a2 = y[s2];
                a2[4] && (r[a2[1]] = r[a2[1]].toLowerCase());
              }
              return r.auth = r.password ? r.username + ":" + r.password : r.username, r.origin = "file:" !== r.protocol && w(r.protocol) && r.host ? r.protocol + "//" + r.host : "null", r.href = r.toString(), r;
            }, toString: function(e2) {
              e2 && "function" == typeof e2 || (e2 = p.stringify);
              var t3, n2 = this, r = n2.host, o = n2.protocol;
              o && ":" !== o.charAt(o.length - 1) && (o += ":");
              var i2 = o + (n2.protocol && n2.slashes || w(n2.protocol) ? "//" : "");
              return n2.username ? (i2 += n2.username, n2.password && (i2 += ":" + n2.password), i2 += "@") : n2.password ? (i2 += ":" + n2.password, i2 += "@") : "file:" !== n2.protocol && w(n2.protocol) && !r && "/" !== n2.pathname && (i2 += "@"), (":" === r[r.length - 1] || l.test(n2.hostname) && !n2.port) && (r += ":"), i2 += r + n2.pathname, (t3 = "object" == typeof n2.query ? e2(n2.query) : n2.query) && (i2 += "?" !== t3.charAt(0) ? "?" + t3 : t3), n2.hash && (i2 += n2.hash), i2;
            } }, _.extractProtocol = x, _.location = g, _.trimLeft = b, _.qs = p, n.exports = _;
          }).call(this);
        }).call(this, "undefined" != typeof global ? global : "undefined" != typeof self ? self : "undefined" != typeof window ? window : {});
      }, { "querystringify": 55, "requires-port": 56 }] }, {}, [1])(1);
    });
  }
});
export default require_sockjs_min();
//# sourceMappingURL=sockjs-client_dist_sockjs__min__js.js.map
