{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/themes/dracula-soft.mjs"], "sourcesContent": ["var draculaSoft = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBackground\": \"#BD93F910\",\n    \"activityBar.activeBorder\": \"#FF79C680\",\n    \"activityBar.background\": \"#343746\",\n    \"activityBar.foreground\": \"#f6f6f4\",\n    \"activityBar.inactiveForeground\": \"#7b7f8b\",\n    \"activityBarBadge.background\": \"#f286c4\",\n    \"activityBarBadge.foreground\": \"#f6f6f4\",\n    \"badge.background\": \"#44475A\",\n    \"badge.foreground\": \"#f6f6f4\",\n    \"breadcrumb.activeSelectionForeground\": \"#f6f6f4\",\n    \"breadcrumb.background\": \"#282A36\",\n    \"breadcrumb.focusForeground\": \"#f6f6f4\",\n    \"breadcrumb.foreground\": \"#7b7f8b\",\n    \"breadcrumbPicker.background\": \"#191A21\",\n    \"button.background\": \"#44475A\",\n    \"button.foreground\": \"#f6f6f4\",\n    \"button.secondaryBackground\": \"#282A36\",\n    \"button.secondaryForeground\": \"#f6f6f4\",\n    \"button.secondaryHoverBackground\": \"#343746\",\n    \"debugToolBar.background\": \"#262626\",\n    \"diffEditor.insertedTextBackground\": \"#50FA7B20\",\n    \"diffEditor.removedTextBackground\": \"#FF555550\",\n    \"dropdown.background\": \"#343746\",\n    \"dropdown.border\": \"#191A21\",\n    \"dropdown.foreground\": \"#f6f6f4\",\n    \"editor.background\": \"#282A36\",\n    \"editor.findMatchBackground\": \"#FFB86C80\",\n    \"editor.findMatchHighlightBackground\": \"#FFFFFF40\",\n    \"editor.findRangeHighlightBackground\": \"#44475A75\",\n    \"editor.foldBackground\": \"#21222C80\",\n    \"editor.foreground\": \"#f6f6f4\",\n    \"editor.hoverHighlightBackground\": \"#8BE9FD50\",\n    \"editor.lineHighlightBorder\": \"#44475A\",\n    \"editor.rangeHighlightBackground\": \"#BD93F915\",\n    \"editor.selectionBackground\": \"#44475A\",\n    \"editor.selectionHighlightBackground\": \"#424450\",\n    \"editor.snippetFinalTabstopHighlightBackground\": \"#282A36\",\n    \"editor.snippetFinalTabstopHighlightBorder\": \"#62e884\",\n    \"editor.snippetTabstopHighlightBackground\": \"#282A36\",\n    \"editor.snippetTabstopHighlightBorder\": \"#7b7f8b\",\n    \"editor.wordHighlightBackground\": \"#8BE9FD50\",\n    \"editor.wordHighlightStrongBackground\": \"#50FA7B50\",\n    \"editorBracketHighlight.foreground1\": \"#f6f6f4\",\n    \"editorBracketHighlight.foreground2\": \"#f286c4\",\n    \"editorBracketHighlight.foreground3\": \"#97e1f1\",\n    \"editorBracketHighlight.foreground4\": \"#62e884\",\n    \"editorBracketHighlight.foreground5\": \"#bf9eee\",\n    \"editorBracketHighlight.foreground6\": \"#FFB86C\",\n    \"editorBracketHighlight.unexpectedBracket.foreground\": \"#ee6666\",\n    \"editorCodeLens.foreground\": \"#7b7f8b\",\n    \"editorError.foreground\": \"#ee6666\",\n    \"editorGroup.border\": \"#bf9eee\",\n    \"editorGroup.dropBackground\": \"#44475A70\",\n    \"editorGroupHeader.tabsBackground\": \"#191A21\",\n    \"editorGutter.addedBackground\": \"#50FA7B80\",\n    \"editorGutter.deletedBackground\": \"#FF555580\",\n    \"editorGutter.modifiedBackground\": \"#8BE9FD80\",\n    \"editorHoverWidget.background\": \"#282A36\",\n    \"editorHoverWidget.border\": \"#7b7f8b\",\n    \"editorIndentGuide.activeBackground\": \"#FFFFFF45\",\n    \"editorIndentGuide.background\": \"#FFFFFF1A\",\n    \"editorLineNumber.foreground\": \"#7b7f8b\",\n    \"editorLink.activeForeground\": \"#97e1f1\",\n    \"editorMarkerNavigation.background\": \"#262626\",\n    \"editorOverviewRuler.addedForeground\": \"#50FA7B80\",\n    \"editorOverviewRuler.border\": \"#191A21\",\n    \"editorOverviewRuler.currentContentForeground\": \"#62e884\",\n    \"editorOverviewRuler.deletedForeground\": \"#FF555580\",\n    \"editorOverviewRuler.errorForeground\": \"#FF555580\",\n    \"editorOverviewRuler.incomingContentForeground\": \"#bf9eee\",\n    \"editorOverviewRuler.infoForeground\": \"#8BE9FD80\",\n    \"editorOverviewRuler.modifiedForeground\": \"#8BE9FD80\",\n    \"editorOverviewRuler.selectionHighlightForeground\": \"#FFB86C\",\n    \"editorOverviewRuler.warningForeground\": \"#FFB86C80\",\n    \"editorOverviewRuler.wordHighlightForeground\": \"#97e1f1\",\n    \"editorOverviewRuler.wordHighlightStrongForeground\": \"#62e884\",\n    \"editorRuler.foreground\": \"#FFFFFF1A\",\n    \"editorSuggestWidget.background\": \"#262626\",\n    \"editorSuggestWidget.foreground\": \"#f6f6f4\",\n    \"editorSuggestWidget.selectedBackground\": \"#44475A\",\n    \"editorWarning.foreground\": \"#97e1f1\",\n    \"editorWhitespace.foreground\": \"#FFFFFF1A\",\n    \"editorWidget.background\": \"#262626\",\n    \"errorForeground\": \"#ee6666\",\n    \"extensionButton.prominentBackground\": \"#50FA7B90\",\n    \"extensionButton.prominentForeground\": \"#f6f6f4\",\n    \"extensionButton.prominentHoverBackground\": \"#50FA7B60\",\n    \"focusBorder\": \"#7b7f8b\",\n    \"foreground\": \"#f6f6f4\",\n    \"gitDecoration.conflictingResourceForeground\": \"#FFB86C\",\n    \"gitDecoration.deletedResourceForeground\": \"#ee6666\",\n    \"gitDecoration.ignoredResourceForeground\": \"#7b7f8b\",\n    \"gitDecoration.modifiedResourceForeground\": \"#97e1f1\",\n    \"gitDecoration.untrackedResourceForeground\": \"#62e884\",\n    \"inlineChat.regionHighlight\": \"#343746\",\n    \"input.background\": \"#282A36\",\n    \"input.border\": \"#191A21\",\n    \"input.foreground\": \"#f6f6f4\",\n    \"input.placeholderForeground\": \"#7b7f8b\",\n    \"inputOption.activeBorder\": \"#bf9eee\",\n    \"inputValidation.errorBorder\": \"#ee6666\",\n    \"inputValidation.infoBorder\": \"#f286c4\",\n    \"inputValidation.warningBorder\": \"#FFB86C\",\n    \"list.activeSelectionBackground\": \"#44475A\",\n    \"list.activeSelectionForeground\": \"#f6f6f4\",\n    \"list.dropBackground\": \"#44475A\",\n    \"list.errorForeground\": \"#ee6666\",\n    \"list.focusBackground\": \"#44475A75\",\n    \"list.highlightForeground\": \"#97e1f1\",\n    \"list.hoverBackground\": \"#44475A75\",\n    \"list.inactiveSelectionBackground\": \"#44475A75\",\n    \"list.warningForeground\": \"#FFB86C\",\n    \"listFilterWidget.background\": \"#343746\",\n    \"listFilterWidget.noMatchesOutline\": \"#ee6666\",\n    \"listFilterWidget.outline\": \"#424450\",\n    \"merge.currentHeaderBackground\": \"#50FA7B90\",\n    \"merge.incomingHeaderBackground\": \"#BD93F990\",\n    \"panel.background\": \"#282A36\",\n    \"panel.border\": \"#bf9eee\",\n    \"panelTitle.activeBorder\": \"#f286c4\",\n    \"panelTitle.activeForeground\": \"#f6f6f4\",\n    \"panelTitle.inactiveForeground\": \"#7b7f8b\",\n    \"peekView.border\": \"#44475A\",\n    \"peekViewEditor.background\": \"#282A36\",\n    \"peekViewEditor.matchHighlightBackground\": \"#F1FA8C80\",\n    \"peekViewResult.background\": \"#262626\",\n    \"peekViewResult.fileForeground\": \"#f6f6f4\",\n    \"peekViewResult.lineForeground\": \"#f6f6f4\",\n    \"peekViewResult.matchHighlightBackground\": \"#F1FA8C80\",\n    \"peekViewResult.selectionBackground\": \"#44475A\",\n    \"peekViewResult.selectionForeground\": \"#f6f6f4\",\n    \"peekViewTitle.background\": \"#191A21\",\n    \"peekViewTitleDescription.foreground\": \"#7b7f8b\",\n    \"peekViewTitleLabel.foreground\": \"#f6f6f4\",\n    \"pickerGroup.border\": \"#bf9eee\",\n    \"pickerGroup.foreground\": \"#97e1f1\",\n    \"progressBar.background\": \"#f286c4\",\n    \"selection.background\": \"#bf9eee\",\n    \"settings.checkboxBackground\": \"#262626\",\n    \"settings.checkboxBorder\": \"#191A21\",\n    \"settings.checkboxForeground\": \"#f6f6f4\",\n    \"settings.dropdownBackground\": \"#262626\",\n    \"settings.dropdownBorder\": \"#191A21\",\n    \"settings.dropdownForeground\": \"#f6f6f4\",\n    \"settings.headerForeground\": \"#f6f6f4\",\n    \"settings.modifiedItemIndicator\": \"#FFB86C\",\n    \"settings.numberInputBackground\": \"#262626\",\n    \"settings.numberInputBorder\": \"#191A21\",\n    \"settings.numberInputForeground\": \"#f6f6f4\",\n    \"settings.textInputBackground\": \"#262626\",\n    \"settings.textInputBorder\": \"#191A21\",\n    \"settings.textInputForeground\": \"#f6f6f4\",\n    \"sideBar.background\": \"#262626\",\n    \"sideBarSectionHeader.background\": \"#282A36\",\n    \"sideBarSectionHeader.border\": \"#191A21\",\n    \"sideBarTitle.foreground\": \"#f6f6f4\",\n    \"statusBar.background\": \"#191A21\",\n    \"statusBar.debuggingBackground\": \"#ee6666\",\n    \"statusBar.debuggingForeground\": \"#191A21\",\n    \"statusBar.foreground\": \"#f6f6f4\",\n    \"statusBar.noFolderBackground\": \"#191A21\",\n    \"statusBar.noFolderForeground\": \"#f6f6f4\",\n    \"statusBarItem.prominentBackground\": \"#ee6666\",\n    \"statusBarItem.prominentHoverBackground\": \"#FFB86C\",\n    \"statusBarItem.remoteBackground\": \"#bf9eee\",\n    \"statusBarItem.remoteForeground\": \"#282A36\",\n    \"tab.activeBackground\": \"#282A36\",\n    \"tab.activeBorderTop\": \"#FF79C680\",\n    \"tab.activeForeground\": \"#f6f6f4\",\n    \"tab.border\": \"#191A21\",\n    \"tab.inactiveBackground\": \"#262626\",\n    \"tab.inactiveForeground\": \"#7b7f8b\",\n    \"terminal.ansiBlack\": \"#262626\",\n    \"terminal.ansiBlue\": \"#bf9eee\",\n    \"terminal.ansiBrightBlack\": \"#7b7f8b\",\n    \"terminal.ansiBrightBlue\": \"#d6b4f7\",\n    \"terminal.ansiBrightCyan\": \"#adf6f6\",\n    \"terminal.ansiBrightGreen\": \"#78f09a\",\n    \"terminal.ansiBrightMagenta\": \"#f49dda\",\n    \"terminal.ansiBrightRed\": \"#f07c7c\",\n    \"terminal.ansiBrightWhite\": \"#ffffff\",\n    \"terminal.ansiBrightYellow\": \"#f6f6ae\",\n    \"terminal.ansiCyan\": \"#97e1f1\",\n    \"terminal.ansiGreen\": \"#62e884\",\n    \"terminal.ansiMagenta\": \"#f286c4\",\n    \"terminal.ansiRed\": \"#ee6666\",\n    \"terminal.ansiWhite\": \"#f6f6f4\",\n    \"terminal.ansiYellow\": \"#e7ee98\",\n    \"terminal.background\": \"#282A36\",\n    \"terminal.foreground\": \"#f6f6f4\",\n    \"titleBar.activeBackground\": \"#262626\",\n    \"titleBar.activeForeground\": \"#f6f6f4\",\n    \"titleBar.inactiveBackground\": \"#191A21\",\n    \"titleBar.inactiveForeground\": \"#7b7f8b\",\n    \"walkThrough.embeddedEditorBackground\": \"#262626\"\n  },\n  \"displayName\": \"Dracula Soft\",\n  \"name\": \"dracula-soft\",\n  \"semanticHighlighting\": true,\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"emphasis\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": [\n        \"strong\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": [\n        \"header\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bf9eee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.diff\",\n        \"meta.diff.header\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7b7f8b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inserted\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#62e884\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.deleted\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ee6666\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.changed\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FFB86C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"invalid\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline italic\",\n        \"foreground\": \"#ee6666\"\n      }\n    },\n    {\n      \"scope\": [\n        \"invalid.deprecated\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline italic\",\n        \"foreground\": \"#f6f6f4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.filename\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e7ee98\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.error\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ee6666\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"underline\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.bold\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#FFB86C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.heading\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#bf9eee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.italic\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#e7ee98\"\n      }\n    },\n    {\n      \"scope\": [\n        \"beginning.punctuation.definition.list.markdown\",\n        \"beginning.punctuation.definition.quote.markdown\",\n        \"punctuation.definition.link.restructuredtext\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#97e1f1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inline.raw\",\n        \"markup.raw.restructuredtext\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#62e884\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline.link\",\n        \"markup.underline.link.image\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#97e1f1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.link.reference.def.restructuredtext\",\n        \"punctuation.definition.directive.restructuredtext\",\n        \"string.other.link.description\",\n        \"string.other.link.title\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f286c4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.directive.restructuredtext\",\n        \"markup.quote\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#e7ee98\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.separator.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7b7f8b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"fenced_code.block.language\",\n        \"markup.raw.inner.restructuredtext\",\n        \"markup.fenced_code.block.markdown punctuation.definition.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#62e884\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.constant.restructuredtext\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bf9eee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.heading.markdown punctuation.definition.string.begin\",\n        \"markup.heading.markdown punctuation.definition.string.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bf9eee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.paragraph.markdown punctuation.definition.string.begin\",\n        \"meta.paragraph.markdown punctuation.definition.string.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f6f6f4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.quote.markdown meta.paragraph.markdown punctuation.definition.string.begin\",\n        \"markup.quote.markdown meta.paragraph.markdown punctuation.definition.string.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e7ee98\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type.class\",\n        \"entity.name.class\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"normal\",\n        \"foreground\": \"#97e1f1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.expressions-and-types.swift\",\n        \"keyword.other.this\",\n        \"variable.language\",\n        \"variable.language punctuation.definition.variable.php\",\n        \"variable.other.readwrite.instance.ruby\",\n        \"variable.parameter.function.language.special\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#bf9eee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.inherited-class\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#97e1f1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment\",\n        \"punctuation.definition.comment\",\n        \"unused.comment\",\n        \"wildcard.comment\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7b7f8b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment keyword.codetag.notation\",\n        \"comment.block.documentation keyword\",\n        \"comment.block.documentation storage.type.class\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f286c4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment.block.documentation entity.name.type\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#97e1f1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment.block.documentation entity.name.type punctuation.definition.bracket\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#97e1f1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"comment.block.documentation variable\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#FFB86C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant\",\n        \"variable.other.constant\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bf9eee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.character.escape\",\n        \"constant.character.string.escape\",\n        \"constant.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f286c4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f286c4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name.parent-selector\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f286c4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#62e884\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function\",\n        \"meta.function-call.object\",\n        \"meta.function-call.php\",\n        \"meta.function-call.static\",\n        \"meta.method-call.java meta.method\",\n        \"meta.method.groovy\",\n        \"support.function.any-method.lua\",\n        \"keyword.operator.function.infix\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#62e884\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.variable.parameter\",\n        \"meta.at-rule.function variable\",\n        \"meta.at-rule.mixin variable\",\n        \"meta.function.arguments variable.other.php\",\n        \"meta.selectionset.graphql meta.arguments.graphql variable.arguments.graphql\",\n        \"variable.parameter\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#FFB86C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.decorator variable.other.readwrite\",\n        \"meta.decorator variable.other.property\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#62e884\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.decorator variable.other.object\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#62e884\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword\",\n        \"punctuation.definition.keyword\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f286c4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.new\",\n        \"keyword.operator.new\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.selector\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f286c4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#97e1f1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.function.magic\",\n        \"support.variable\",\n        \"variable.other.predefined\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"regular\",\n        \"foreground\": \"#bf9eee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.function\",\n        \"support.type.property-name\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"regular\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.symbol.hashkey punctuation.definition.constant.ruby\",\n        \"entity.other.attribute-name.placeholder punctuation\",\n        \"entity.other.attribute-name.pseudo-class punctuation\",\n        \"entity.other.attribute-name.pseudo-element punctuation\",\n        \"meta.group.double.toml\",\n        \"meta.group.toml\",\n        \"meta.object-binding-pattern-variable punctuation.destructuring\",\n        \"punctuation.colon.graphql\",\n        \"punctuation.definition.block.scalar.folded.yaml\",\n        \"punctuation.definition.block.scalar.literal.yaml\",\n        \"punctuation.definition.block.sequence.item.yaml\",\n        \"punctuation.definition.entity.other.inherited-class\",\n        \"punctuation.function.swift\",\n        \"punctuation.separator.dictionary.key-value\",\n        \"punctuation.separator.hash\",\n        \"punctuation.separator.inheritance\",\n        \"punctuation.separator.key-value\",\n        \"punctuation.separator.key-value.mapping.yaml\",\n        \"punctuation.separator.namespace\",\n        \"punctuation.separator.pointer-access\",\n        \"punctuation.separator.slice\",\n        \"string.unquoted.heredoc punctuation.definition.string\",\n        \"support.other.chomping-indicator.yaml\",\n        \"punctuation.separator.annotation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f286c4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.other.powershell\",\n        \"keyword.other.statement-separator.powershell\",\n        \"meta.brace.round\",\n        \"meta.function-call punctuation\",\n        \"punctuation.definition.arguments.begin\",\n        \"punctuation.definition.arguments.end\",\n        \"punctuation.definition.entity.begin\",\n        \"punctuation.definition.entity.end\",\n        \"punctuation.definition.tag.cs\",\n        \"punctuation.definition.type.begin\",\n        \"punctuation.definition.type.end\",\n        \"punctuation.section.scope.begin\",\n        \"punctuation.section.scope.end\",\n        \"punctuation.terminator.expression.php\",\n        \"storage.type.generic.java\",\n        \"string.template meta.brace\",\n        \"string.template punctuation.accessor\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f6f6f4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.string-contents.quoted.double punctuation.definition.variable\",\n        \"punctuation.definition.interpolation.begin\",\n        \"punctuation.definition.interpolation.end\",\n        \"punctuation.definition.template-expression.begin\",\n        \"punctuation.definition.template-expression.end\",\n        \"punctuation.section.embedded.begin\",\n        \"punctuation.section.embedded.coffee\",\n        \"punctuation.section.embedded.end\",\n        \"punctuation.section.embedded.end source.php\",\n        \"punctuation.section.embedded.end source.ruby\",\n        \"punctuation.definition.variable.makefile\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f286c4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function.target.makefile\",\n        \"entity.name.section.toml\",\n        \"entity.name.tag.yaml\",\n        \"variable.other.key.toml\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#97e1f1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.date\",\n        \"constant.other.timestamp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FFB86C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.alias.yaml\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic underline\",\n        \"foreground\": \"#62e884\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage\",\n        \"meta.implementation storage.type.objc\",\n        \"meta.interface-or-protocol storage.type.objc\",\n        \"source.groovy storage.type.def\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"regular\",\n        \"foreground\": \"#f286c4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type\",\n        \"keyword.primitive-datatypes.swift\",\n        \"keyword.type.cs\",\n        \"meta.protocol-list.objc\",\n        \"meta.return-type.objc\",\n        \"source.go storage.type\",\n        \"source.groovy storage.type\",\n        \"source.java storage.type\",\n        \"source.powershell entity.other.attribute-name\",\n        \"storage.class.std.rust\",\n        \"storage.type.attribute.swift\",\n        \"storage.type.c\",\n        \"storage.type.core.rust\",\n        \"storage.type.cs\",\n        \"storage.type.groovy\",\n        \"storage.type.objc\",\n        \"storage.type.php\",\n        \"storage.type.haskell\",\n        \"storage.type.ocaml\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#97e1f1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type.type-parameter\",\n        \"meta.indexer.mappedtype.declaration entity.name.type\",\n        \"meta.type.parameters entity.name.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FFB86C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.modifier\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f286c4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp\",\n        \"constant.other.character-class.set.regexp\",\n        \"constant.character.escape.backslash.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e7ee98\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.group.capture.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f286c4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp punctuation.definition.string.begin\",\n        \"string.regexp punctuation.definition.string.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ee6666\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.character-class.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#97e1f1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.group.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FFB86C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.group.assertion.regexp\",\n        \"keyword.operator.negation.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ee6666\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.assertion.look-ahead.regexp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#62e884\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e7ee98\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.string.begin\",\n        \"punctuation.definition.string.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#dee492\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.support.type.property-name.begin\",\n        \"punctuation.support.type.property-name.end\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#97e2f2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.quoted.docstring.multi\",\n        \"string.quoted.docstring.multi.python punctuation.definition.string.begin\",\n        \"string.quoted.docstring.multi.python punctuation.definition.string.end\",\n        \"string.quoted.docstring.multi.python constant.character.escape\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7b7f8b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable\",\n        \"constant.other.key.perl\",\n        \"support.variable.property\",\n        \"variable.other.constant.js\",\n        \"variable.other.constant.ts\",\n        \"variable.other.constant.tsx\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f6f6f4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.import variable.other.readwrite\",\n        \"meta.variable.assignment.destructured.object.coffee variable\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#FFB86C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.import variable.other.readwrite.alias\",\n        \"meta.export variable.other.readwrite.alias\",\n        \"meta.variable.assignment.destructured.object.coffee variable variable\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"normal\",\n        \"foreground\": \"#f6f6f4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.selectionset.graphql variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e7ee98\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.selectionset.graphql meta.arguments variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f6f6f4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.fragment.graphql\",\n        \"variable.fragment.graphql\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#97e1f1\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.other.symbol.hashkey.ruby\",\n        \"keyword.operator.dereference.java\",\n        \"keyword.operator.navigation.groovy\",\n        \"meta.scope.for-loop.shell punctuation.definition.string.begin\",\n        \"meta.scope.for-loop.shell punctuation.definition.string.end\",\n        \"meta.scope.for-loop.shell string\",\n        \"storage.modifier.import\",\n        \"punctuation.section.embedded.begin.tsx\",\n        \"punctuation.section.embedded.end.tsx\",\n        \"punctuation.section.embedded.begin.jsx\",\n        \"punctuation.section.embedded.end.jsx\",\n        \"punctuation.separator.list.comma.css\",\n        \"constant.language.empty-list.haskell\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f6f6f4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.shell variable.other\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bf9eee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"normal\",\n        \"foreground\": \"#bf9eee\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.scope.prerequisites.makefile\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e7ee98\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.attribute-selector.scss\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e7ee98\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.attribute-selector.end.bracket.square.scss\",\n        \"punctuation.definition.attribute-selector.begin.bracket.square.scss\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f6f6f4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.preprocessor.haskell\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7b7f8b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"log.error\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#ee6666\"\n      }\n    },\n    {\n      \"scope\": [\n        \"log.warning\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#e7ee98\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { draculaSoft as default };\n"], "mappings": ";;;AAAA,IAAI,cAAc,OAAO,OAAO;AAAA,EAC9B,UAAU;AAAA,IACR,gCAAgC;AAAA,IAChC,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,0BAA0B;AAAA,IAC1B,kCAAkC;AAAA,IAClC,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,wCAAwC;AAAA,IACxC,yBAAyB;AAAA,IACzB,8BAA8B;AAAA,IAC9B,yBAAyB;AAAA,IACzB,+BAA+B;AAAA,IAC/B,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,8BAA8B;AAAA,IAC9B,mCAAmC;AAAA,IACnC,2BAA2B;AAAA,IAC3B,qCAAqC;AAAA,IACrC,oCAAoC;AAAA,IACpC,uBAAuB;AAAA,IACvB,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,IACvB,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,uCAAuC;AAAA,IACvC,uCAAuC;AAAA,IACvC,yBAAyB;AAAA,IACzB,qBAAqB;AAAA,IACrB,mCAAmC;AAAA,IACnC,8BAA8B;AAAA,IAC9B,mCAAmC;AAAA,IACnC,8BAA8B;AAAA,IAC9B,uCAAuC;AAAA,IACvC,iDAAiD;AAAA,IACjD,6CAA6C;AAAA,IAC7C,4CAA4C;AAAA,IAC5C,wCAAwC;AAAA,IACxC,kCAAkC;AAAA,IAClC,wCAAwC;AAAA,IACxC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,uDAAuD;AAAA,IACvD,6BAA6B;AAAA,IAC7B,0BAA0B;AAAA,IAC1B,sBAAsB;AAAA,IACtB,8BAA8B;AAAA,IAC9B,oCAAoC;AAAA,IACpC,gCAAgC;AAAA,IAChC,kCAAkC;AAAA,IAClC,mCAAmC;AAAA,IACnC,gCAAgC;AAAA,IAChC,4BAA4B;AAAA,IAC5B,sCAAsC;AAAA,IACtC,gCAAgC;AAAA,IAChC,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,qCAAqC;AAAA,IACrC,uCAAuC;AAAA,IACvC,8BAA8B;AAAA,IAC9B,gDAAgD;AAAA,IAChD,yCAAyC;AAAA,IACzC,uCAAuC;AAAA,IACvC,iDAAiD;AAAA,IACjD,sCAAsC;AAAA,IACtC,0CAA0C;AAAA,IAC1C,oDAAoD;AAAA,IACpD,yCAAyC;AAAA,IACzC,+CAA+C;AAAA,IAC/C,qDAAqD;AAAA,IACrD,0BAA0B;AAAA,IAC1B,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,0CAA0C;AAAA,IAC1C,4BAA4B;AAAA,IAC5B,+BAA+B;AAAA,IAC/B,2BAA2B;AAAA,IAC3B,mBAAmB;AAAA,IACnB,uCAAuC;AAAA,IACvC,uCAAuC;AAAA,IACvC,4CAA4C;AAAA,IAC5C,eAAe;AAAA,IACf,cAAc;AAAA,IACd,+CAA+C;AAAA,IAC/C,2CAA2C;AAAA,IAC3C,2CAA2C;AAAA,IAC3C,4CAA4C;AAAA,IAC5C,6CAA6C;AAAA,IAC7C,8BAA8B;AAAA,IAC9B,oBAAoB;AAAA,IACpB,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,+BAA+B;AAAA,IAC/B,8BAA8B;AAAA,IAC9B,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,uBAAuB;AAAA,IACvB,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,4BAA4B;AAAA,IAC5B,wBAAwB;AAAA,IACxB,oCAAoC;AAAA,IACpC,0BAA0B;AAAA,IAC1B,+BAA+B;AAAA,IAC/B,qCAAqC;AAAA,IACrC,4BAA4B;AAAA,IAC5B,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,oBAAoB;AAAA,IACpB,gBAAgB;AAAA,IAChB,2BAA2B;AAAA,IAC3B,+BAA+B;AAAA,IAC/B,iCAAiC;AAAA,IACjC,mBAAmB;AAAA,IACnB,6BAA6B;AAAA,IAC7B,2CAA2C;AAAA,IAC3C,6BAA6B;AAAA,IAC7B,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,4BAA4B;AAAA,IAC5B,uCAAuC;AAAA,IACvC,iCAAiC;AAAA,IACjC,sBAAsB;AAAA,IACtB,0BAA0B;AAAA,IAC1B,0BAA0B;AAAA,IAC1B,wBAAwB;AAAA,IACxB,+BAA+B;AAAA,IAC/B,2BAA2B;AAAA,IAC3B,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,2BAA2B;AAAA,IAC3B,+BAA+B;AAAA,IAC/B,6BAA6B;AAAA,IAC7B,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,kCAAkC;AAAA,IAClC,gCAAgC;AAAA,IAChC,4BAA4B;AAAA,IAC5B,gCAAgC;AAAA,IAChC,sBAAsB;AAAA,IACtB,mCAAmC;AAAA,IACnC,+BAA+B;AAAA,IAC/B,2BAA2B;AAAA,IAC3B,wBAAwB;AAAA,IACxB,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,wBAAwB;AAAA,IACxB,gCAAgC;AAAA,IAChC,gCAAgC;AAAA,IAChC,qCAAqC;AAAA,IACrC,0CAA0C;AAAA,IAC1C,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,wBAAwB;AAAA,IACxB,cAAc;AAAA,IACd,0BAA0B;AAAA,IAC1B,0BAA0B;AAAA,IAC1B,sBAAsB;AAAA,IACtB,qBAAqB;AAAA,IACrB,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,4BAA4B;AAAA,IAC5B,8BAA8B;AAAA,IAC9B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,6BAA6B;AAAA,IAC7B,qBAAqB;AAAA,IACrB,sBAAsB;AAAA,IACtB,wBAAwB;AAAA,IACxB,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,IACvB,6BAA6B;AAAA,IAC7B,6BAA6B;AAAA,IAC7B,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,wCAAwC;AAAA,EAC1C;AAAA,EACA,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,wBAAwB;AAAA,EACxB,eAAe;AAAA,IACb;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AACV,CAAC;", "names": []}