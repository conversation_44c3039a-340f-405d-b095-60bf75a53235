{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/prisma.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Prisma\", \"fileTypes\": [\"prisma\"], \"name\": \"prisma\", \"patterns\": [{ \"include\": \"#triple_comment\" }, { \"include\": \"#double_comment\" }, { \"include\": \"#model_block_definition\" }, { \"include\": \"#config_block_definition\" }, { \"include\": \"#enum_block_definition\" }, { \"include\": \"#type_definition\" }], \"repository\": { \"array\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"name\": \"source.prisma.array\", \"patterns\": [{ \"include\": \"#value\" }] }, \"assignment\": { \"patterns\": [{ \"begin\": \"^\\\\s*(\\\\w+)\\\\s*(=)\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"variable.other.assignment.prisma\" }, \"2\": { \"name\": \"keyword.operator.terraform\" } }, \"end\": \"\\\\n\", \"patterns\": [{ \"include\": \"#value\" }, { \"include\": \"#double_comment_inline\" }] }] }, \"attribute\": { \"captures\": { \"1\": { \"name\": \"entity.name.function.attribute.prisma\" } }, \"match\": \"(@@?[\\\\w\\\\.]+)\", \"name\": \"source.prisma.attribute\" }, \"attribute_with_arguments\": { \"begin\": \"(@@?[\\\\w\\\\.]+)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.attribute.prisma\" }, \"2\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"name\": \"source.prisma.attribute.with_arguments\", \"patterns\": [{ \"include\": \"#named_argument\" }, { \"include\": \"#value\" }] }, \"boolean\": { \"match\": \"\\\\b(true|false)\\\\b\", \"name\": \"constant.language.boolean.prisma\" }, \"config_block_definition\": { \"begin\": \"^\\\\s*(generator|datasource)\\\\s+([A-Za-z][\\\\w]*)\\\\s+({)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.config.prisma\" }, \"2\": { \"name\": \"entity.name.type.config.prisma\" }, \"3\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"end\": \"\\\\s*\\\\}\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"name\": \"source.prisma.embedded.source\", \"patterns\": [{ \"include\": \"#triple_comment\" }, { \"include\": \"#double_comment\" }, { \"include\": \"#assignment\" }] }, \"double_comment\": { \"begin\": \"//\", \"end\": \"$\\\\n?\", \"name\": \"comment.prisma\" }, \"double_comment_inline\": { \"match\": \"//[^\\\\n]*\", \"name\": \"comment.prisma\" }, \"double_quoted_string\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"string.quoted.double.start.prisma\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"string.quoted.double.end.prisma\" } }, \"name\": \"unnamed\", \"patterns\": [{ \"include\": \"#string_interpolation\" }, { \"match\": \"([\\\\w\\\\-\\\\/\\\\._\\\\\\\\%@:\\\\?=]+)\", \"name\": \"string.quoted.double.prisma\" }] }, \"enum_block_definition\": { \"begin\": \"^\\\\s*(enum)\\\\s+([A-Za-z][\\\\w]*)\\\\s+({)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.enum.prisma\" }, \"2\": { \"name\": \"entity.name.type.enum.prisma\" }, \"3\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"end\": \"\\\\s*\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"name\": \"source.prisma.embedded.source\", \"patterns\": [{ \"include\": \"#triple_comment\" }, { \"include\": \"#double_comment\" }, { \"include\": \"#enum_value_definition\" }] }, \"enum_value_definition\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.other.assignment.prisma\" } }, \"match\": \"^\\\\s*(\\\\w+)\\\\s*\" }, { \"include\": \"#attribute_with_arguments\" }, { \"include\": \"#attribute\" }] }, \"field_definition\": { \"name\": \"scalar.field\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.other.assignment.prisma\" }, \"2\": { \"name\": \"invalid.illegal.colon.prisma\" }, \"3\": { \"name\": \"variable.language.relations.prisma\" }, \"4\": { \"name\": \"support.type.primitive.prisma\" }, \"5\": { \"name\": \"keyword.operator.list_type.prisma\" }, \"6\": { \"name\": \"keyword.operator.optional_type.prisma\" }, \"7\": { \"name\": \"invalid.illegal.required_type.prisma\" } }, \"match\": \"^\\\\s*(\\\\w+)(\\\\s*:)?\\\\s+((?!(?:Int|BigInt|String|DateTime|Bytes|Decimal|Float|Json|Boolean)\\\\b)\\\\b\\\\w+)?(Int|BigInt|String|DateTime|Bytes|Decimal|Float|Json|Boolean)?(\\\\[\\\\])?(\\\\?)?(\\\\!)?\" }, { \"include\": \"#attribute_with_arguments\" }, { \"include\": \"#attribute\" }] }, \"functional\": { \"begin\": \"(\\\\w+)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.functional.prisma\" }, \"2\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"name\": \"source.prisma.functional\", \"patterns\": [{ \"include\": \"#value\" }] }, \"identifier\": { \"patterns\": [{ \"match\": \"\\\\b(\\\\w)+\\\\b\", \"name\": \"support.constant.constant.prisma\" }] }, \"literal\": { \"name\": \"source.prisma.literal\", \"patterns\": [{ \"include\": \"#boolean\" }, { \"include\": \"#number\" }, { \"include\": \"#double_quoted_string\" }, { \"include\": \"#identifier\" }] }, \"map_key\": { \"name\": \"source.prisma.key\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.parameter.key.prisma\" }, \"2\": { \"name\": \"punctuation.definition.separator.key-value.prisma\" } }, \"match\": \"(\\\\w+)\\\\s*(:)\\\\s*\" }] }, \"model_block_definition\": { \"begin\": \"^\\\\s*(model|type|view)\\\\s+([A-Za-z][\\\\w]*)\\\\s*({)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.model.prisma\" }, \"2\": { \"name\": \"entity.name.type.model.prisma\" }, \"3\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"end\": \"\\\\s*\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.prisma\" } }, \"name\": \"source.prisma.embedded.source\", \"patterns\": [{ \"include\": \"#triple_comment\" }, { \"include\": \"#double_comment\" }, { \"include\": \"#field_definition\" }] }, \"named_argument\": { \"name\": \"source.prisma.named_argument\", \"patterns\": [{ \"include\": \"#map_key\" }, { \"include\": \"#value\" }] }, \"number\": { \"match\": \"((0(x|X)[0-9a-fA-F]*)|(\\\\+|-)?\\\\b(([0-9]+\\\\.?[0-9]*)|(\\\\.[0-9]+))((e|E)(\\\\+|-)?[0-9]+)?)([LlFfUuDdg]|UL|ul)?\\\\b\", \"name\": \"constant.numeric.prisma\" }, \"string_interpolation\": { \"patterns\": [{ \"begin\": \"\\\\$\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.interpolation.start.prisma\" } }, \"end\": \"\\\\s*\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control.interpolation.end.prisma\" } }, \"name\": \"source.tag.embedded.source.prisma\", \"patterns\": [{ \"include\": \"#value\" }] }] }, \"triple_comment\": { \"begin\": \"///\", \"end\": \"$\\\\n?\", \"name\": \"comment.prisma\" }, \"type_definition\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"storage.type.type.prisma\" }, \"2\": { \"name\": \"entity.name.type.type.prisma\" }, \"3\": { \"name\": \"support.type.primitive.prisma\" } }, \"match\": \"^\\\\s*(type)\\\\s+(\\\\w+)\\\\s*=\\\\s*(\\\\w+)\" }, { \"include\": \"#attribute_with_arguments\" }, { \"include\": \"#attribute\" }] }, \"value\": { \"name\": \"source.prisma.value\", \"patterns\": [{ \"include\": \"#array\" }, { \"include\": \"#functional\" }, { \"include\": \"#literal\" }] } }, \"scopeName\": \"source.prisma\" });\nvar prisma = [\n  lang\n];\n\nexport { prisma as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,UAAU,aAAa,CAAC,QAAQ,GAAG,QAAQ,UAAU,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,mBAAmB,CAAC,GAAG,cAAc,EAAE,SAAS,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,SAAS,CAAC,EAAE,GAAG,cAAc,EAAE,YAAY,CAAC,EAAE,SAAS,0BAA0B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,yBAAyB,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,SAAS,kBAAkB,QAAQ,0BAA0B,GAAG,4BAA4B,EAAE,SAAS,uBAAuB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,QAAQ,0CAA0C,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,SAAS,CAAC,EAAE,GAAG,WAAW,EAAE,SAAS,sBAAsB,QAAQ,mCAAmC,GAAG,2BAA2B,EAAE,SAAS,0DAA0D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,iCAAiC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,QAAQ,iCAAiC,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,kBAAkB,EAAE,SAAS,MAAM,OAAO,SAAS,QAAQ,iBAAiB,GAAG,yBAAyB,EAAE,SAAS,aAAa,QAAQ,iBAAiB,GAAG,wBAAwB,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,QAAQ,WAAW,YAAY,CAAC,EAAE,WAAW,wBAAwB,GAAG,EAAE,SAAS,iCAAiC,QAAQ,8BAA8B,CAAC,EAAE,GAAG,yBAAyB,EAAE,SAAS,0CAA0C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,QAAQ,iCAAiC,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,yBAAyB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,kBAAkB,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,oBAAoB,EAAE,QAAQ,gBAAgB,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,6LAA6L,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,eAAe,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,SAAS,CAAC,EAAE,GAAG,cAAc,EAAE,YAAY,CAAC,EAAE,SAAS,gBAAgB,QAAQ,mCAAmC,CAAC,EAAE,GAAG,WAAW,EAAE,QAAQ,yBAAyB,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,WAAW,EAAE,QAAQ,qBAAqB,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,oDAAoD,EAAE,GAAG,SAAS,oBAAoB,CAAC,EAAE,GAAG,0BAA0B,EAAE,SAAS,qDAAqD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,QAAQ,iCAAiC,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,kBAAkB,EAAE,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,SAAS,CAAC,EAAE,GAAG,UAAU,EAAE,SAAS,mHAAmH,QAAQ,0BAA0B,GAAG,wBAAwB,EAAE,YAAY,CAAC,EAAE,SAAS,UAAU,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,qCAAqC,YAAY,CAAC,EAAE,WAAW,SAAS,CAAC,EAAE,CAAC,EAAE,GAAG,kBAAkB,EAAE,SAAS,OAAO,OAAO,SAAS,QAAQ,iBAAiB,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,SAAS,uCAAuC,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,SAAS,EAAE,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,EAAE,GAAG,aAAa,gBAAgB,CAAC;AAC78M,IAAI,SAAS;AAAA,EACX;AACF;", "names": []}