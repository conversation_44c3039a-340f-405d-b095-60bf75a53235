{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/make.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Makefile\", \"name\": \"make\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#variables\" }, { \"include\": \"#variable-assignment\" }, { \"include\": \"#directives\" }, { \"include\": \"#recipe\" }, { \"include\": \"#target\" }], \"repository\": { \"another-variable-braces\": { \"patterns\": [{ \"begin\": \"(?<={)(?!})\", \"end\": \"(?=}|((?<!\\\\\\\\)\\\\n))\", \"name\": \"variable.other.makefile\", \"patterns\": [{ \"include\": \"#variables\" }, { \"match\": \"\\\\\\\\\\\\n\", \"name\": \"constant.character.escape.continuation.makefile\" }] }] }, \"another-variable-parentheses\": { \"patterns\": [{ \"begin\": \"(?<=\\\\()(?!\\\\))\", \"end\": \"(?=\\\\)|((?<!\\\\\\\\)\\\\n))\", \"name\": \"variable.other.makefile\", \"patterns\": [{ \"include\": \"#variables\" }, { \"match\": \"\\\\\\\\\\\\n\", \"name\": \"constant.character.escape.continuation.makefile\" }] }] }, \"braces-interpolation\": { \"begin\": \"{\", \"end\": \"}\", \"patterns\": [{ \"include\": \"#variables\" }, { \"include\": \"#interpolation\" }] }, \"builtin-variable-braces\": { \"patterns\": [{ \"match\": \"(?<={)(MAKEFILES|VPATH|SHELL|MAKESHELL|MAKE|MAKELEVEL|MAKEFLAGS|MAKECMDGOALS|CURDIR|SUFFIXES|\\\\.LIBPATTERNS)(?=\\\\s*})\", \"name\": \"variable.language.makefile\" }] }, \"builtin-variable-parentheses\": { \"patterns\": [{ \"match\": \"(?<=\\\\()(MAKEFILES|VPATH|SHELL|MAKESHELL|MAKE|MAKELEVEL|MAKEFLAGS|MAKECMDGOALS|CURDIR|SUFFIXES|\\\\.LIBPATTERNS)(?=\\\\s*\\\\))\", \"name\": \"variable.language.makefile\" }] }, \"comma\": { \"match\": \",\", \"name\": \"punctuation.separator.delimeter.comma.makefile\" }, \"comment\": { \"begin\": \"(^[ ]+)?((?<!\\\\\\\\)(\\\\\\\\\\\\\\\\)*)(?=#)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.makefile\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \"#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.makefile\" } }, \"end\": \"(?=[^\\\\\\\\])$\", \"name\": \"comment.line.number-sign.makefile\", \"patterns\": [{ \"match\": \"\\\\\\\\\\\\n\", \"name\": \"constant.character.escape.continuation.makefile\" }] }] }, \"directives\": { \"patterns\": [{ \"begin\": \"^[ ]*([s\\\\-]?include)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.include.makefile\" } }, \"end\": \"^\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#variables\" }, { \"match\": \"%\", \"name\": \"constant.other.placeholder.makefile\" }] }, { \"begin\": \"^[ ]*(vpath)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.vpath.makefile\" } }, \"end\": \"^\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#variables\" }, { \"match\": \"%\", \"name\": \"constant.other.placeholder.makefile\" }] }, { \"begin\": \"^\\\\s*(?:(override)\\\\s*)?(define)\\\\s*([^\\\\s]+)\\\\s*(=|\\\\?=|:=|\\\\+=)?(?=\\\\s)\", \"captures\": { \"1\": { \"name\": \"keyword.control.override.makefile\" }, \"2\": { \"name\": \"keyword.control.define.makefile\" }, \"3\": { \"name\": \"variable.other.makefile\" }, \"4\": { \"name\": \"punctuation.separator.key-value.makefile\" } }, \"end\": \"^\\\\s*(endef)\\\\b\", \"name\": \"meta.scope.conditional.makefile\", \"patterns\": [{ \"begin\": \"\\\\G(?!\\\\n)\", \"end\": \"^\", \"patterns\": [{ \"include\": \"#comment\" }] }, { \"include\": \"#variables\" }, { \"include\": \"#directives\" }] }, { \"begin\": \"^[ ]*(export)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.$1.makefile\" } }, \"end\": \"^\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#variable-assignment\" }, { \"match\": \"[^\\\\s]+\", \"name\": \"variable.other.makefile\" }] }, { \"begin\": \"^[ ]*(override|private)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.$1.makefile\" } }, \"end\": \"^\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#variable-assignment\" }] }, { \"begin\": \"^[ ]*(unexport|undefine)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.$1.makefile\" } }, \"end\": \"^\", \"patterns\": [{ \"include\": \"#comment\" }, { \"match\": \"[^\\\\s]+\", \"name\": \"variable.other.makefile\" }] }, { \"begin\": \"^\\\\s*(ifeq|ifneq|ifdef|ifndef)(?=\\\\s)\", \"captures\": { \"1\": { \"name\": \"keyword.control.$1.makefile\" } }, \"end\": \"^\\\\s*(endif)\\\\b\", \"name\": \"meta.scope.conditional.makefile\", \"patterns\": [{ \"begin\": \"\\\\G\", \"end\": \"^\", \"name\": \"meta.scope.condition.makefile\", \"patterns\": [{ \"include\": \"#comma\" }, { \"include\": \"#variables\" }, { \"include\": \"#comment\" }] }, { \"begin\": \"^\\\\s*else(?=\\\\s)\\\\s*(ifeq|ifneq|ifdef|ifndef)*(?=\\\\s)\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.else.makefile\" } }, \"end\": \"^\", \"patterns\": [{ \"include\": \"#comma\" }, { \"include\": \"#variables\" }, { \"include\": \"#comment\" }] }, { \"include\": \"$self\" }] }] }, \"flavor-variable-braces\": { \"patterns\": [{ \"begin\": \"(?<={)(origin|flavor)\\\\s(?=[^\\\\s}]+\\\\s*})\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.$1.makefile\" } }, \"contentName\": \"variable.other.makefile\", \"end\": \"(?=})\", \"name\": \"meta.scope.function-call.makefile\", \"patterns\": [{ \"include\": \"#variables\" }] }] }, \"flavor-variable-parentheses\": { \"patterns\": [{ \"begin\": \"(?<=\\\\()(origin|flavor)\\\\s(?=[^\\\\s)]+\\\\s*\\\\))\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.$1.makefile\" } }, \"contentName\": \"variable.other.makefile\", \"end\": \"(?=\\\\))\", \"name\": \"meta.scope.function-call.makefile\", \"patterns\": [{ \"include\": \"#variables\" }] }] }, \"function-variable-braces\": { \"patterns\": [{ \"begin\": \"(?<={)(subst|patsubst|strip|findstring|filter(-out)?|sort|word(list)?|firstword|lastword|dir|notdir|suffix|basename|addsuffix|addprefix|join|wildcard|realpath|abspath|info|error|warning|shell|foreach|if|or|and|call|eval|value|file|guile)\\\\s\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.$1.makefile\" } }, \"end\": \"(?=}|((?<!\\\\\\\\)\\\\n))\", \"name\": \"meta.scope.function-call.makefile\", \"patterns\": [{ \"include\": \"#comma\" }, { \"include\": \"#variables\" }, { \"include\": \"#interpolation\" }, { \"match\": \"%|\\\\*\", \"name\": \"constant.other.placeholder.makefile\" }, { \"match\": \"\\\\\\\\\\\\n\", \"name\": \"constant.character.escape.continuation.makefile\" }] }] }, \"function-variable-parentheses\": { \"patterns\": [{ \"begin\": \"(?<=\\\\()(subst|patsubst|strip|findstring|filter(-out)?|sort|word(list)?|firstword|lastword|dir|notdir|suffix|basename|addsuffix|addprefix|join|wildcard|realpath|abspath|info|error|warning|shell|foreach|if|or|and|call|eval|value|file|guile)\\\\s\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.$1.makefile\" } }, \"end\": \"(?=\\\\)|((?<!\\\\\\\\)\\\\n))\", \"name\": \"meta.scope.function-call.makefile\", \"patterns\": [{ \"include\": \"#comma\" }, { \"include\": \"#variables\" }, { \"include\": \"#interpolation\" }, { \"match\": \"%|\\\\*\", \"name\": \"constant.other.placeholder.makefile\" }, { \"match\": \"\\\\\\\\\\\\n\", \"name\": \"constant.character.escape.continuation.makefile\" }] }] }, \"interpolation\": { \"patterns\": [{ \"include\": \"#parentheses-interpolation\" }, { \"include\": \"#braces-interpolation\" }] }, \"parentheses-interpolation\": { \"begin\": \"\\\\(\", \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#variables\" }, { \"include\": \"#interpolation\" }] }, \"recipe\": { \"begin\": \"^\\\\t([+\\\\-@]*)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.$1.makefile\" } }, \"end\": \"[^\\\\\\\\]$\", \"name\": \"meta.scope.recipe.makefile\", \"patterns\": [{ \"match\": \"\\\\\\\\\\\\n\", \"name\": \"constant.character.escape.continuation.makefile\" }, { \"include\": \"#variables\" }] }, \"simple-variable\": { \"patterns\": [{ \"match\": \"\\\\$[^(){}]\", \"name\": \"variable.language.makefile\" }] }, \"target\": { \"begin\": \"^(?!\\\\t)([^:]*)(:)(?!\\\\=)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"support.function.target.$1.makefile\" } }, \"match\": \"^\\\\s*(\\\\.(PHONY|SUFFIXES|DEFAULT|PRECIOUS|INTERMEDIATE|SECONDARY|SECONDEXPANSION|DELETE_ON_ERROR|IGNORE|LOW_RESOLUTION_TIME|SILENT|EXPORT_ALL_VARIABLES|NOTPARALLEL|ONESHELL|POSIX))\\\\s*$\" }, { \"begin\": \"(?=\\\\S)\", \"end\": \"(?=\\\\s|$)\", \"name\": \"entity.name.function.target.makefile\", \"patterns\": [{ \"include\": \"#variables\" }, { \"match\": \"%\", \"name\": \"constant.other.placeholder.makefile\" }] }] }, \"2\": { \"name\": \"punctuation.separator.key-value.makefile\" } }, \"end\": \"[^\\\\\\\\]$\", \"name\": \"meta.scope.target.makefile\", \"patterns\": [{ \"begin\": \"\\\\G\", \"end\": \"(?=[^\\\\\\\\])$\", \"name\": \"meta.scope.prerequisites.makefile\", \"patterns\": [{ \"match\": \"\\\\\\\\\\\\n\", \"name\": \"constant.character.escape.continuation.makefile\" }, { \"match\": \"%|\\\\*\", \"name\": \"constant.other.placeholder.makefile\" }, { \"include\": \"#comment\" }, { \"include\": \"#variables\" }] }] }, \"variable-assignment\": { \"begin\": \"(^[ ]*|\\\\G\\\\s*)([^\\\\s:#=]+)\\\\s*((?<![?:+!])=|\\\\?=|:=|\\\\+=|!=)\", \"beginCaptures\": { \"2\": { \"name\": \"variable.other.makefile\", \"patterns\": [{ \"include\": \"#variables\" }] }, \"3\": { \"name\": \"punctuation.separator.key-value.makefile\" } }, \"end\": \"\\\\n\", \"patterns\": [{ \"match\": \"\\\\\\\\\\\\n\", \"name\": \"constant.character.escape.continuation.makefile\" }, { \"include\": \"#comment\" }, { \"include\": \"#variables\" }] }, \"variable-braces\": { \"patterns\": [{ \"begin\": \"\\\\${\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.variable.makefile\" } }, \"end\": \"}|((?<!\\\\\\\\)\\\\n)\", \"name\": \"string.interpolated.makefile\", \"patterns\": [{ \"include\": \"#variables\" }, { \"include\": \"#builtin-variable-braces\" }, { \"include\": \"#function-variable-braces\" }, { \"include\": \"#flavor-variable-braces\" }, { \"include\": \"#another-variable-braces\" }] }] }, \"variable-parentheses\": { \"patterns\": [{ \"begin\": \"\\\\$\\\\(\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.variable.makefile\" } }, \"end\": \"\\\\)|((?<!\\\\\\\\)\\\\n)\", \"name\": \"string.interpolated.makefile\", \"patterns\": [{ \"include\": \"#variables\" }, { \"include\": \"#builtin-variable-parentheses\" }, { \"include\": \"#function-variable-parentheses\" }, { \"include\": \"#flavor-variable-parentheses\" }, { \"include\": \"#another-variable-parentheses\" }] }] }, \"variables\": { \"patterns\": [{ \"include\": \"#simple-variable\" }, { \"include\": \"#variable-parentheses\" }, { \"include\": \"#variable-braces\" }] } }, \"scopeName\": \"source.makefile\", \"aliases\": [\"makefile\"] });\nvar make = [\n  lang\n];\n\nexport { make as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,YAAY,QAAQ,QAAQ,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,GAAG,cAAc,EAAE,2BAA2B,EAAE,YAAY,CAAC,EAAE,SAAS,eAAe,OAAO,wBAAwB,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,WAAW,QAAQ,kDAAkD,CAAC,EAAE,CAAC,EAAE,GAAG,gCAAgC,EAAE,YAAY,CAAC,EAAE,SAAS,mBAAmB,OAAO,0BAA0B,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,WAAW,QAAQ,kDAAkD,CAAC,EAAE,CAAC,EAAE,GAAG,wBAAwB,EAAE,SAAS,KAAK,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,2BAA2B,EAAE,YAAY,CAAC,EAAE,SAAS,yHAAyH,QAAQ,6BAA6B,CAAC,EAAE,GAAG,gCAAgC,EAAE,YAAY,CAAC,EAAE,SAAS,6HAA6H,QAAQ,6BAA6B,CAAC,EAAE,GAAG,SAAS,EAAE,SAAS,KAAK,QAAQ,iDAAiD,GAAG,WAAW,EAAE,SAAS,uCAAuC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,gBAAgB,QAAQ,qCAAqC,YAAY,CAAC,EAAE,SAAS,WAAW,QAAQ,kDAAkD,CAAC,EAAE,CAAC,EAAE,GAAG,cAAc,EAAE,YAAY,CAAC,EAAE,SAAS,4BAA4B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,KAAK,QAAQ,sCAAsC,CAAC,EAAE,GAAG,EAAE,SAAS,mBAAmB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,KAAK,QAAQ,sCAAsC,CAAC,EAAE,GAAG,EAAE,SAAS,6EAA6E,YAAY,EAAE,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,0BAA0B,GAAG,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,mBAAmB,QAAQ,mCAAmC,YAAY,CAAC,EAAE,SAAS,cAAc,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,oBAAoB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,SAAS,WAAW,QAAQ,0BAA0B,CAAC,EAAE,GAAG,EAAE,SAAS,8BAA8B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,SAAS,+BAA+B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,WAAW,QAAQ,0BAA0B,CAAC,EAAE,GAAG,EAAE,SAAS,yCAAyC,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,mBAAmB,QAAQ,mCAAmC,YAAY,CAAC,EAAE,SAAS,OAAO,OAAO,KAAK,QAAQ,iCAAiC,YAAY,CAAC,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,EAAE,SAAS,yDAAyD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,0BAA0B,EAAE,YAAY,CAAC,EAAE,SAAS,6CAA6C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,eAAe,2BAA2B,OAAO,SAAS,QAAQ,qCAAqC,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,+BAA+B,EAAE,YAAY,CAAC,EAAE,SAAS,iDAAiD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,eAAe,2BAA2B,OAAO,WAAW,QAAQ,qCAAqC,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,4BAA4B,EAAE,YAAY,CAAC,EAAE,SAAS,oPAAoP,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,OAAO,wBAAwB,QAAQ,qCAAqC,YAAY,CAAC,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,SAAS,SAAS,QAAQ,sCAAsC,GAAG,EAAE,SAAS,WAAW,QAAQ,kDAAkD,CAAC,EAAE,CAAC,EAAE,GAAG,iCAAiC,EAAE,YAAY,CAAC,EAAE,SAAS,sPAAsP,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,OAAO,0BAA0B,QAAQ,qCAAqC,YAAY,CAAC,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,SAAS,SAAS,QAAQ,sCAAsC,GAAG,EAAE,SAAS,WAAW,QAAQ,kDAAkD,CAAC,EAAE,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,WAAW,6BAA6B,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,6BAA6B,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,UAAU,EAAE,SAAS,kBAAkB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,YAAY,QAAQ,8BAA8B,YAAY,CAAC,EAAE,SAAS,WAAW,QAAQ,kDAAkD,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,SAAS,cAAc,QAAQ,6BAA6B,CAAC,EAAE,GAAG,UAAU,EAAE,SAAS,6BAA6B,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,SAAS,4LAA4L,GAAG,EAAE,SAAS,WAAW,OAAO,aAAa,QAAQ,wCAAwC,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,KAAK,QAAQ,sCAAsC,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,YAAY,QAAQ,8BAA8B,YAAY,CAAC,EAAE,SAAS,OAAO,OAAO,gBAAgB,QAAQ,qCAAqC,YAAY,CAAC,EAAE,SAAS,WAAW,QAAQ,kDAAkD,GAAG,EAAE,SAAS,SAAS,QAAQ,sCAAsC,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,iEAAiE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,SAAS,WAAW,QAAQ,kDAAkD,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,SAAS,QAAQ,YAAY,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,oBAAoB,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,2BAA2B,CAAC,EAAE,CAAC,EAAE,GAAG,wBAAwB,EAAE,YAAY,CAAC,EAAE,SAAS,UAAU,YAAY,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,sBAAsB,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,+BAA+B,GAAG,EAAE,WAAW,gCAAgC,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,mBAAmB,CAAC,EAAE,EAAE,GAAG,aAAa,mBAAmB,WAAW,CAAC,UAAU,EAAE,CAAC;AACv1S,IAAI,OAAO;AAAA,EACT;AACF;", "names": []}