{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/jsonl.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"JSON Lines\", \"name\": \"jsonl\", \"patterns\": [{ \"include\": \"#value\" }], \"repository\": { \"array\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.begin.json.lines\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.end.json.lines\" } }, \"name\": \"meta.structure.array.json.lines\", \"patterns\": [{ \"include\": \"#value\" }, { \"match\": \",\", \"name\": \"punctuation.separator.array.json.lines\" }, { \"match\": \"[^\\\\s\\\\]]\", \"name\": \"invalid.illegal.expected-array-separator.json.lines\" }] }, \"comments\": { \"patterns\": [{ \"begin\": \"/\\\\*\\\\*(?!/)\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.json.lines\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block.documentation.json.lines\" }, { \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.json.lines\" } }, \"end\": \"\\\\*/\", \"name\": \"comment.block.json.lines\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.json.lines\" } }, \"match\": \"(//).*$\\\\n?\", \"name\": \"comment.line.double-slash.js\" }] }, \"constant\": { \"match\": \"\\\\b(?:true|false|null)\\\\b\", \"name\": \"constant.language.json.lines\" }, \"number\": { \"match\": \"(?x)\\n-?\\n(?:\\n0\\n|\\n[1-9]\\n\\\\d*\\n)\\n(?:\\n(?:\\n\\\\.\\n\\\\d+\\n)?\\n(?:\\n[eE]\\n[+-]?\\n\\\\d+\\n)?\\n)?\", \"name\": \"constant.numeric.json.lines\" }, \"object\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.dictionary.begin.json.lines\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.dictionary.end.json.lines\" } }, \"name\": \"meta.structure.dictionary.json.lines\", \"patterns\": [{ \"comment\": \"the JSON object key\", \"include\": \"#objectkey\" }, { \"include\": \"#comments\" }, { \"begin\": \":\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.separator.dictionary.key-value.json.lines\" } }, \"end\": \"(,)|(?=\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.separator.dictionary.pair.json.lines\" } }, \"name\": \"meta.structure.dictionary.value.json.lines\", \"patterns\": [{ \"comment\": \"the JSON object value\", \"include\": \"#value\" }, { \"match\": \"[^\\\\s,]\", \"name\": \"invalid.illegal.expected-dictionary-separator.json.lines\" }] }, { \"match\": \"[^\\\\s\\\\}]\", \"name\": \"invalid.illegal.expected-dictionary-separator.json.lines\" }] }, \"objectkey\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.support.type.property-name.begin.json.lines\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.support.type.property-name.end.json.lines\" } }, \"name\": \"string.json.lines support.type.property-name.json.lines\", \"patterns\": [{ \"include\": \"#stringcontent\" }] }, \"string\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.json.lines\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.json.lines\" } }, \"name\": \"string.quoted.double.json.lines\", \"patterns\": [{ \"include\": \"#stringcontent\" }] }, \"stringcontent\": { \"patterns\": [{ \"match\": '(?x)\\n\\\\\\\\\\n(?:\\n[\"\\\\\\\\/bfnrt]\\n|\\nu\\n[0-9a-fA-F]{4})', \"name\": \"constant.character.escape.json.lines\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"invalid.illegal.unrecognized-string-escape.json.lines\" }] }, \"value\": { \"patterns\": [{ \"include\": \"#constant\" }, { \"include\": \"#number\" }, { \"include\": \"#string\" }, { \"include\": \"#array\" }, { \"include\": \"#object\" }, { \"include\": \"#comments\" }] } }, \"scopeName\": \"source.json.lines\" });\nvar jsonl = [\n  lang\n];\n\nexport { jsonl as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,cAAc,QAAQ,SAAS,YAAY,CAAC,EAAE,WAAW,SAAS,CAAC,GAAG,cAAc,EAAE,SAAS,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,QAAQ,mCAAmC,YAAY,CAAC,EAAE,WAAW,SAAS,GAAG,EAAE,SAAS,KAAK,QAAQ,yCAAyC,GAAG,EAAE,SAAS,aAAa,QAAQ,sDAAsD,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,gBAAgB,YAAY,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,QAAQ,QAAQ,yCAAyC,GAAG,EAAE,SAAS,QAAQ,YAAY,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,QAAQ,QAAQ,2BAA2B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,SAAS,eAAe,QAAQ,+BAA+B,CAAC,EAAE,GAAG,YAAY,EAAE,SAAS,6BAA6B,QAAQ,+BAA+B,GAAG,UAAU,EAAE,SAAS,gGAAgG,QAAQ,8BAA8B,GAAG,UAAU,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,mDAAmD,EAAE,GAAG,QAAQ,wCAAwC,YAAY,CAAC,EAAE,WAAW,uBAAuB,WAAW,aAAa,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wDAAwD,EAAE,GAAG,OAAO,eAAe,eAAe,EAAE,KAAK,EAAE,QAAQ,mDAAmD,EAAE,GAAG,QAAQ,8CAA8C,YAAY,CAAC,EAAE,WAAW,yBAAyB,WAAW,SAAS,GAAG,EAAE,SAAS,WAAW,QAAQ,2DAA2D,CAAC,EAAE,GAAG,EAAE,SAAS,aAAa,QAAQ,2DAA2D,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0DAA0D,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,wDAAwD,EAAE,GAAG,QAAQ,2DAA2D,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,UAAU,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,QAAQ,mCAAmC,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,SAAS,yDAAyD,QAAQ,uCAAuC,GAAG,EAAE,SAAS,SAAS,QAAQ,wDAAwD,CAAC,EAAE,GAAG,SAAS,EAAE,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,EAAE,GAAG,aAAa,oBAAoB,CAAC;AAC9xG,IAAI,QAAQ;AAAA,EACV;AACF;", "names": []}