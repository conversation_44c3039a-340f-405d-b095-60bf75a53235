{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/tcl.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Tcl\", \"fileTypes\": [\"tcl\"], \"foldingStartMarker\": \"\\\\{\\\\s*$\", \"foldingStopMarker\": \"^\\\\s*\\\\}\", \"name\": \"tcl\", \"patterns\": [{ \"begin\": \"(?<=^|;)\\\\s*((#))\", \"beginCaptures\": { \"1\": { \"name\": \"comment.line.number-sign.tcl\" }, \"2\": { \"name\": \"punctuation.definition.comment.tcl\" } }, \"contentName\": \"comment.line.number-sign.tcl\", \"end\": \"\\\\n\", \"patterns\": [{ \"match\": \"(\\\\\\\\\\\\\\\\|\\\\\\\\\\\\n)\" }] }, { \"captures\": { \"1\": { \"name\": \"keyword.control.tcl\" } }, \"match\": \"(?<=^|[\\\\[{;])\\\\s*(if|while|for|catch|default|return|break|continue|switch|exit|foreach|try|throw)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.tcl\" } }, \"match\": \"(?<=^|})\\\\s*(then|elseif|else)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.tcl\" }, \"2\": { \"name\": \"entity.name.function.tcl\" } }, \"match\": \"(?<=^|{)\\\\s*(proc)\\\\s+([^\\\\s]+)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.tcl\" } }, \"match\": \"(?<=^|[\\\\[{;])\\\\s*(after|append|array|auto_execok|auto_import|auto_load|auto_mkindex|auto_mkindex_old|auto_qualify|auto_reset|bgerror|binary|cd|clock|close|concat|dde|encoding|eof|error|eval|exec|expr|fblocked|fconfigure|fcopy|file|fileevent|filename|flush|format|gets|glob|global|history|http|incr|info|interp|join|lappend|library|lindex|linsert|list|llength|load|lrange|lreplace|lsearch|lset|lsort|memory|msgcat|namespace|open|package|parray|pid|pkg::create|pkg_mkIndex|proc|puts|pwd|re_syntax|read|registry|rename|resource|scan|seek|set|socket|SafeBase|source|split|string|subst|Tcl|tcl_endOfWord|tcl_findLibrary|tcl_startOfNextWord|tcl_startOfPreviousWord|tcl_wordBreakAfter|tcl_wordBreakBefore|tcltest|tclvars|tell|time|trace|unknown|unset|update|uplevel|upvar|variable|vwait)\\\\b\" }, { \"begin\": \"(?<=^|[\\\\[{;])\\\\s*(regexp|regsub)\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.tcl\" } }, \"comment\": \"special-case regexp/regsub keyword in order to handle the expression\", \"end\": \"[\\\\n;\\\\]]\", \"patterns\": [{ \"match\": \"\\\\\\\\(?:.|\\\\n)\", \"name\": \"constant.character.escape.tcl\" }, { \"comment\": \"switch for regexp\", \"match\": \"-\\\\w+\\\\s*\" }, { \"applyEndPatternLast\": 1, \"begin\": \"--\\\\s*\", \"comment\": \"end of switches\", \"end\": \"\", \"patterns\": [{ \"include\": \"#regexp\" }] }, { \"include\": \"#regexp\" }] }, { \"include\": \"#escape\" }, { \"include\": \"#variable\" }, { \"include\": \"#operator\" }, { \"include\": \"#numeric\" }, { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.tcl\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.tcl\" } }, \"name\": \"string.quoted.double.tcl\", \"patterns\": [{ \"include\": \"#escape\" }, { \"include\": \"#variable\" }, { \"include\": \"#embedded\" }] }], \"repository\": { \"bare-string\": { \"begin\": '(?:^|(?<=\\\\s))\"', \"comment\": \"matches a single quote-enclosed word without scoping\", \"end\": '\"([^\\\\s\\\\]]*)', \"endCaptures\": { \"1\": { \"name\": \"invalid.illegal.tcl\" } }, \"patterns\": [{ \"include\": \"#escape\" }, { \"include\": \"#variable\" }] }, \"braces\": { \"begin\": \"(?:^|(?<=\\\\s))\\\\{\", \"comment\": \"matches a single brace-enclosed word\", \"end\": \"\\\\}([^\\\\s\\\\]]*)\", \"endCaptures\": { \"1\": { \"name\": \"invalid.illegal.tcl\" } }, \"patterns\": [{ \"match\": \"\\\\\\\\[{}\\\\n]\", \"name\": \"constant.character.escape.tcl\" }, { \"include\": \"#inner-braces\" }] }, \"embedded\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.begin.tcl\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.tcl\" } }, \"name\": \"source.tcl.embedded\", \"patterns\": [{ \"include\": \"source.tcl\" }] }, \"escape\": { \"match\": \"\\\\\\\\(\\\\d{1,3}|x[a-fA-F0-9]+|u[a-fA-F0-9]{1,4}|.|\\\\n)\", \"name\": \"constant.character.escape.tcl\" }, \"inner-braces\": { \"begin\": \"\\\\{\", \"comment\": \"matches a nested brace in a brace-enclosed word\", \"end\": \"\\\\}\", \"patterns\": [{ \"match\": \"\\\\\\\\[{}\\\\n]\", \"name\": \"constant.character.escape.tcl\" }, { \"include\": \"#inner-braces\" }] }, \"numeric\": { \"match\": \"(?<![a-zA-Z])([+-]?([0-9]*[.])?[0-9]+f?)(?![\\\\.a-zA-Z])\", \"name\": \"constant.numeric.tcl\" }, \"operator\": { \"match\": \"(?<= |\\\\d)(-|\\\\+|~|&{1,2}|\\\\|{1,2}|<{1,2}|>{1,2}|\\\\*{1,2}|!|%|\\\\/|<=|>=|={1,2}|!=|\\\\^)(?= |\\\\d)\", \"name\": \"keyword.operator.tcl\" }, \"regexp\": { \"begin\": \"(?=\\\\S)(?![\\\\n;\\\\]])\", \"comment\": \"matches a single word, named as a regexp, then swallows the rest of the command\", \"end\": \"(?=[\\\\n;\\\\]])\", \"patterns\": [{ \"begin\": \"(?=[^ \\\\t\\\\n;])\", \"end\": \"(?=[ \\\\t\\\\n;])\", \"name\": \"string.regexp.tcl\", \"patterns\": [{ \"include\": \"#braces\" }, { \"include\": \"#bare-string\" }, { \"include\": \"#escape\" }, { \"include\": \"#variable\" }] }, { \"begin\": \"[ \\\\t]\", \"comment\": \"swallow the rest of the command\", \"end\": \"(?=[\\\\n;\\\\]])\", \"patterns\": [{ \"include\": \"#variable\" }, { \"include\": \"#embedded\" }, { \"include\": \"#escape\" }, { \"include\": \"#braces\" }, { \"include\": \"#string\" }] }] }, \"string\": { \"applyEndPatternLast\": 1, \"begin\": '(?:^|(?<=\\\\s))(?=\")', \"comment\": \"matches a single quote-enclosed word with scoping\", \"end\": \"\", \"name\": \"string.quoted.double.tcl\", \"patterns\": [{ \"include\": \"#bare-string\" }] }, \"variable\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.variable.tcl\" } }, \"match\": \"(\\\\$)((?:[a-zA-Z0-9_]|::)+(\\\\([^\\\\)]+\\\\))?|\\\\{[^\\\\}]*\\\\})\", \"name\": \"support.function.tcl\" } }, \"scopeName\": \"source.tcl\" });\nvar tcl = [\n  lang\n];\n\nexport { tcl as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,OAAO,aAAa,CAAC,KAAK,GAAG,sBAAsB,YAAY,qBAAqB,YAAY,QAAQ,OAAO,YAAY,CAAC,EAAE,SAAS,qBAAqB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,eAAe,gCAAgC,OAAO,OAAO,YAAY,CAAC,EAAE,SAAS,qBAAqB,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,SAAS,wGAAwG,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,SAAS,oCAAoC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oBAAoB,GAAG,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,SAAS,kCAAkC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,SAAS,mxBAAmxB,GAAG,EAAE,SAAS,4CAA4C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,WAAW,wEAAwE,OAAO,aAAa,YAAY,CAAC,EAAE,SAAS,iBAAiB,QAAQ,gCAAgC,GAAG,EAAE,WAAW,qBAAqB,SAAS,YAAY,GAAG,EAAE,uBAAuB,GAAG,SAAS,UAAU,WAAW,mBAAmB,OAAO,IAAI,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,eAAe,EAAE,SAAS,mBAAmB,WAAW,wDAAwD,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,UAAU,EAAE,SAAS,qBAAqB,WAAW,wCAAwC,OAAO,mBAAmB,eAAe,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,eAAe,QAAQ,gCAAgC,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,YAAY,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,UAAU,EAAE,SAAS,wDAAwD,QAAQ,gCAAgC,GAAG,gBAAgB,EAAE,SAAS,OAAO,WAAW,mDAAmD,OAAO,OAAO,YAAY,CAAC,EAAE,SAAS,eAAe,QAAQ,gCAAgC,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,WAAW,EAAE,SAAS,2DAA2D,QAAQ,uBAAuB,GAAG,YAAY,EAAE,SAAS,mGAAmG,QAAQ,uBAAuB,GAAG,UAAU,EAAE,SAAS,wBAAwB,WAAW,mFAAmF,OAAO,iBAAiB,YAAY,CAAC,EAAE,SAAS,mBAAmB,OAAO,kBAAkB,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,UAAU,WAAW,mCAAmC,OAAO,iBAAiB,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,uBAAuB,GAAG,SAAS,uBAAuB,WAAW,qDAAqD,OAAO,IAAI,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,SAAS,6DAA6D,QAAQ,uBAAuB,EAAE,GAAG,aAAa,aAAa,CAAC;AAC/lK,IAAI,MAAM;AAAA,EACR;AACF;", "names": []}