{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/v.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"V\", \"fileTypes\": [\".v\", \".vh\", \".vsh\", \".vv\", \"v.mod\"], \"name\": \"v\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#function-decl\" }, { \"include\": \"#as-is\" }, { \"include\": \"#attributes\" }, { \"include\": \"#assignment\" }, { \"include\": \"#module-decl\" }, { \"include\": \"#import-decl\" }, { \"include\": \"#hash-decl\" }, { \"include\": \"#brackets\" }, { \"include\": \"#builtin-fix\" }, { \"include\": \"#escaped-fix\" }, { \"include\": \"#operators\" }, { \"include\": \"#function-limited-overload-decl\" }, { \"include\": \"#function-extend-decl\" }, { \"include\": \"#function-exist\" }, { \"include\": \"#generic\" }, { \"include\": \"#constants\" }, { \"include\": \"#type\" }, { \"include\": \"#enum\" }, { \"include\": \"#interface\" }, { \"include\": \"#struct\" }, { \"include\": \"#keywords\" }, { \"include\": \"#storage\" }, { \"include\": \"#numbers\" }, { \"include\": \"#strings\" }, { \"include\": \"#types\" }, { \"include\": \"#punctuations\" }, { \"include\": \"#variable-assign\" }, { \"include\": \"#function-decl\" }], \"repository\": { \"as-is\": { \"begin\": \"\\\\s+(as|is)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.$1.v\" } }, \"end\": \"([\\\\w.]*)\", \"endCaptures\": { \"1\": { \"name\": \"entity.name.alias.v\" } } }, \"assignment\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#operators\" }] } }, \"match\": \"\\\\s+((?:\\\\:|\\\\+|\\\\-|\\\\*|/|\\\\%|\\\\&|\\\\||\\\\^)?=)\\\\s+\", \"name\": \"meta.definition.variable.v\" }, \"attributes\": { \"captures\": { \"1\": { \"name\": \"meta.function.attribute.v\" }, \"2\": { \"name\": \"punctuation.definition.begin.bracket.square.v\" }, \"3\": { \"name\": \"storage.modifier.attribute.v\" }, \"4\": { \"name\": \"punctuation.definition.end.bracket.square.v\" } }, \"match\": \"^\\\\s*((\\\\[)(deprecated|unsafe|console|heap|manualfree|typedef|live|inline|flag|ref_only|direct_array_access|callconv)(\\\\]))\", \"name\": \"meta.definition.attribute.v\" }, \"brackets\": { \"patterns\": [{ \"begin\": \"{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.bracket.curly.begin.v\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.bracket.curly.end.v\" } }, \"patterns\": [{ \"include\": \"$self\" }] }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.bracket.round.begin.v\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.bracket.round.end.v\" } }, \"patterns\": [{ \"include\": \"$self\" }] }, { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.bracket.square.begin.v\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.bracket.square.end.v\" } }, \"patterns\": [{ \"include\": \"$self\" }] }] }, \"builtin-fix\": { \"patterns\": [{ \"patterns\": [{ \"match\": \"(const)(?=\\\\s*\\\\()\", \"name\": \"storage.modifier.v\" }, { \"match\": \"\\\\b(fn|type|enum|struct|union|interface|map|assert|sizeof|typeof|__offsetof)\\\\b(?=\\\\s*\\\\()\", \"name\": \"keyword.$1.v\" }] }, { \"patterns\": [{ \"match\": \"(\\\\$if|\\\\$else)(?=\\\\s*\\\\()\", \"name\": \"keyword.control.v\" }, { \"match\": \"\\\\b(as|in|is|or|break|continue|default|unsafe|match|if|else|for|go|spawn|goto|defer|return|shared|select|rlock|lock|atomic|asm)\\\\b(?=\\\\s*\\\\()\", \"name\": \"keyword.control.v\" }] }, { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"storage.type.numeric.v\" } }, \"match\": \"(?<!.)(i?(?:8|16|nt|64|128)|u?(?:16|32|64|128)|f?(?:32|64))(?=\\\\s*\\\\()\", \"name\": \"meta.expr.numeric.cast.v\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.$1.v\" } }, \"match\": \"(bool|byte|byteptr|charptr|voidptr|string|rune|size_t|[ui]size)(?=\\\\s*\\\\()\", \"name\": \"meta.expr.bool.cast.v\" }] }] }, \"comments\": { \"patterns\": [{ \"begin\": \"/\\\\*\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.begin.v\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.end.v\" } }, \"name\": \"comment.block.documentation.v\", \"patterns\": [{ \"include\": \"#comments\" }] }, { \"begin\": \"//\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.begin.v\" } }, \"end\": \"$\", \"name\": \"comment.line.double-slash.v\" }] }, \"constants\": { \"match\": \"\\\\b(true|false|none)\\\\b\", \"name\": \"constant.language.v\" }, \"enum\": { \"captures\": { \"1\": { \"name\": \"storage.modifier.$1.v\" }, \"2\": { \"name\": \"storage.type.enum.v\" }, \"3\": { \"name\": \"entity.name.enum.v\" } }, \"match\": \"^\\\\s*(?:(pub)?\\\\s+)?(enum)\\\\s+(?:\\\\w+\\\\.)?(\\\\w*)\", \"name\": \"meta.definition.enum.v\" }, \"function-decl\": { \"captures\": { \"1\": { \"name\": \"storage.modifier.v\" }, \"2\": { \"name\": \"keyword.fn.v\" }, \"3\": { \"name\": \"entity.name.function.v\" }, \"4\": { \"patterns\": [{ \"include\": \"#generic\" }] } }, \"match\": \"^(\\\\bpub\\\\b\\\\s+)?(\\\\bfn\\\\b)\\\\s+(?:\\\\([^\\\\)]+\\\\)\\\\s+)?(?:(?:C\\\\.)?)(\\\\w+)\\\\s*((?<=[\\\\w\\\\s+])(\\\\<)(\\\\w+)(\\\\>))?\", \"name\": \"meta.definition.function.v\" }, \"function-exist\": { \"captures\": { \"0\": { \"name\": \"meta.function.call.v\" }, \"1\": { \"patterns\": [{ \"include\": \"#illegal-name\" }, { \"match\": \"\\\\w+\", \"name\": \"entity.name.function.v\" }] }, \"2\": { \"patterns\": [{ \"include\": \"#generic\" }] } }, \"match\": \"(\\\\w+)((?<=[\\\\w\\\\s+])(\\\\<)(\\\\w+)(\\\\>))?(?=\\\\s*\\\\()\", \"name\": \"meta.support.function.v\" }, \"function-extend-decl\": { \"captures\": { \"1\": { \"name\": \"storage.modifier.v\" }, \"2\": { \"name\": \"keyword.fn.v\" }, \"3\": { \"name\": \"punctuation.definition.bracket.round.begin.v\" }, \"4\": { \"patterns\": [{ \"include\": \"#brackets\" }, { \"include\": \"#storage\" }, { \"include\": \"#generic\" }, { \"include\": \"#types\" }, { \"include\": \"#punctuation\" }] }, \"5\": { \"name\": \"punctuation.definition.bracket.round.end.v\" }, \"6\": { \"patterns\": [{ \"include\": \"#illegal-name\" }, { \"match\": \"\\\\w+\", \"name\": \"entity.name.function.v\" }] }, \"7\": { \"patterns\": [{ \"include\": \"#generic\" }] } }, \"match\": \"^\\\\s*(pub)?\\\\s*(fn)\\\\s*(\\\\()([^\\\\)]*)(\\\\))\\\\s*(?:(?:C\\\\.)?)(\\\\w+)\\\\s*((?<=[\\\\w\\\\s+])(\\\\<)(\\\\w+)(\\\\>))?\", \"name\": \"meta.definition.function.v\" }, \"function-limited-overload-decl\": { \"captures\": { \"1\": { \"name\": \"storage.modifier.v\" }, \"2\": { \"name\": \"keyword.fn.v\" }, \"3\": { \"name\": \"punctuation.definition.bracket.round.begin.v\" }, \"4\": { \"patterns\": [{ \"include\": \"#brackets\" }, { \"include\": \"#storage\" }, { \"include\": \"#generic\" }, { \"include\": \"#types\" }, { \"include\": \"#punctuation\" }] }, \"5\": { \"name\": \"punctuation.definition.bracket.round.end.v\" }, \"6\": { \"patterns\": [{ \"include\": \"#operators\" }] }, \"7\": { \"name\": \"punctuation.definition.bracket.round.begin.v\" }, \"8\": { \"patterns\": [{ \"include\": \"#brackets\" }, { \"include\": \"#storage\" }, { \"include\": \"#generic\" }, { \"include\": \"#types\" }, { \"include\": \"#punctuation\" }] }, \"9\": { \"name\": \"punctuation.definition.bracket.round.end.v\" }, \"10\": { \"patterns\": [{ \"include\": \"#illegal-name\" }, { \"match\": \"\\\\w+\", \"name\": \"entity.name.function.v\" }] } }, \"match\": \"^\\\\s*(pub)?\\\\s*(fn)\\\\s*(\\\\()([^\\\\)]*)(\\\\))\\\\s*([\\\\+\\\\-\\\\*\\\\/])?\\\\s*(\\\\()([^\\\\)]*)(\\\\))\\\\s*(?:(?:C\\\\.)?)(\\\\w+)\", \"name\": \"meta.definition.function.v\" }, \"generic\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.bracket.angle.begin.v\" }, \"2\": { \"patterns\": [{ \"include\": \"#illegal-name\" }, { \"match\": \"\\\\w+\", \"name\": \"entity.name.generic.v\" }] }, \"3\": { \"name\": \"punctuation.definition.bracket.angle.end.v\" } }, \"match\": \"(?<=[\\\\w\\\\s+])(\\\\<)(\\\\w+)(\\\\>)\", \"name\": \"meta.definition.generic.v\" }] }, \"hash-decl\": { \"begin\": \"^\\\\s*(#)\", \"end\": \"$\", \"name\": \"markup.bold.v\" }, \"illegal-name\": { \"match\": \"\\\\d\\\\w+\", \"name\": \"invalid.illegal.v\" }, \"import-decl\": { \"begin\": \"^\\\\s*(import)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.import.v\" } }, \"end\": \"([\\\\w.]+)\", \"endCaptures\": { \"1\": { \"name\": \"entity.name.import.v\" } }, \"name\": \"meta.import.v\" }, \"interface\": { \"captures\": { \"1\": { \"name\": \"storage.modifier.$1.v\" }, \"2\": { \"name\": \"keyword.interface.v\" }, \"3\": { \"patterns\": [{ \"include\": \"#illegal-name\" }, { \"match\": \"\\\\w+\", \"name\": \"entity.name.interface.v\" }] } }, \"match\": \"^\\\\s*(?:(pub)?\\\\s+)?(interface)\\\\s+(\\\\w*)\", \"name\": \"meta.definition.interface.v\" }, \"keywords\": { \"patterns\": [{ \"match\": \"(\\\\$if|\\\\$else)\", \"name\": \"keyword.control.v\" }, { \"match\": \"(?<!@)\\\\b(as|it|is|in|or|break|continue|default|unsafe|match|if|else|for|go|spawn|goto|defer|return|shared|select|rlock|lock|atomic|asm)\\\\b\", \"name\": \"keyword.control.v\" }, { \"match\": \"(?<!@)\\\\b(fn|type|typeof|enum|struct|interface|map|assert|sizeof|__offsetof)\\\\b\", \"name\": \"keyword.$1.v\" }] }, \"module-decl\": { \"begin\": \"^\\\\s*(module)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.module.v\" } }, \"end\": \"([\\\\w.]+)\", \"endCaptures\": { \"1\": { \"name\": \"entity.name.module.v\" } }, \"name\": \"meta.module.v\" }, \"numbers\": { \"patterns\": [{ \"match\": \"([0-9]+(_?))+(\\\\.)([0-9]+[eE][-+]?[0-9]+)\", \"name\": \"constant.numeric.exponential.v\" }, { \"match\": \"([0-9]+(_?))+(\\\\.)([0-9]+)\", \"name\": \"constant.numeric.float.v\" }, { \"match\": \"(?:0b)(?:(?:[0-1]+)(?:_?))+\", \"name\": \"constant.numeric.binary.v\" }, { \"match\": \"(?:0o)(?:(?:[0-7]+)(?:_?))+\", \"name\": \"constant.numeric.octal.v\" }, { \"match\": \"(?:0x)(?:(?:[0-9a-fA-F]+)(?:_?))+\", \"name\": \"constant.numeric.hex.v\" }, { \"match\": \"(?:(?:[0-9]+)(?:[_]?))+\", \"name\": \"constant.numeric.integer.v\" }] }, \"operators\": { \"patterns\": [{ \"match\": \"(\\\\+|\\\\-|\\\\*|\\\\/|\\\\%|\\\\+\\\\+|\\\\-\\\\-|\\\\>\\\\>|\\\\<\\\\<)\", \"name\": \"keyword.operator.arithmetic.v\" }, { \"match\": \"(\\\\=\\\\=|\\\\!\\\\=|\\\\>|\\\\<|\\\\>\\\\=|\\\\<\\\\=)\", \"name\": \"keyword.operator.relation.v\" }, { \"match\": \"(\\\\:\\\\=|\\\\=|\\\\+\\\\=|\\\\-\\\\=|\\\\*\\\\=|\\\\/\\\\=|\\\\%\\\\=|\\\\&\\\\=|\\\\|\\\\=|\\\\^\\\\=|\\\\~\\\\=|\\\\&\\\\&\\\\=|\\\\|\\\\|\\\\=|\\\\>\\\\>\\\\=|\\\\<\\\\<\\\\=)\", \"name\": \"keyword.operator.assignment.v\" }, { \"match\": \"(\\\\&|\\\\||\\\\^|\\\\~|<(?!<)|>(?!>))\", \"name\": \"keyword.operator.bitwise.v\" }, { \"match\": \"(\\\\&\\\\&|\\\\|\\\\||\\\\!)\", \"name\": \"keyword.operator.logical.v\" }, { \"match\": \"\\\\?\", \"name\": \"keyword.operator.optional.v\" }] }, \"punctuation\": { \"patterns\": [{ \"match\": \"\\\\.\", \"name\": \"punctuation.delimiter.period.dot.v\" }, { \"match\": \",\", \"name\": \"punctuation.delimiter.comma.v\" }, { \"match\": \":\", \"name\": \"punctuation.separator.key-value.colon.v\" }, { \"match\": \";\", \"name\": \"punctuation.definition.other.semicolon.v\" }, { \"match\": \"\\\\?\", \"name\": \"punctuation.definition.other.questionmark.v\" }, { \"match\": \"#\", \"name\": \"punctuation.hash.v\" }] }, \"punctuations\": { \"patterns\": [{ \"match\": \"(?:\\\\.)\", \"name\": \"punctuation.accessor.v\" }, { \"match\": \"(?:,)\", \"name\": \"punctuation.separator.comma.v\" }] }, \"storage\": { \"match\": \"\\\\b(const|mut|pub)\\\\b\", \"name\": \"storage.modifier.v\" }, \"string-escaped-char\": { \"patterns\": [{ \"match\": `\\\\\\\\([0-7]{3}|[\\\\$abfnrtv\\\\\\\\'\"]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4}|U[0-9a-fA-F]{8})`, \"name\": \"constant.character.escape.v\" }, { \"match\": `\\\\\\\\[^0-7\\\\$xuUabfnrtv\\\\'\"]`, \"name\": \"invalid.illegal.unknown-escape.v\" }] }, \"string-interpolation\": { \"captures\": { \"1\": { \"patterns\": [{ \"match\": \"\\\\$\\\\d[\\\\.\\\\w]+\", \"name\": \"invalid.illegal.v\" }, { \"match\": \"\\\\$([\\\\.\\\\w]+|\\\\{.*?\\\\})\", \"name\": \"variable.other.interpolated.v\" }] } }, \"match\": \"(\\\\$([\\\\w.]+|\\\\{.*?\\\\}))\", \"name\": \"meta.string.interpolation.v\" }, \"string-placeholder\": { \"match\": \"%(\\\\[\\\\d+\\\\])?([\\\\+#\\\\-0\\\\x20]{,2}((\\\\d+|\\\\*)?(\\\\.?(\\\\d+|\\\\*|(\\\\[\\\\d+\\\\])\\\\*?)?(\\\\[\\\\d+\\\\])?)?))?[vT%tbcdoqxXUbeEfFgGsp]\", \"name\": \"constant.other.placeholder.v\" }, \"strings\": { \"patterns\": [{ \"begin\": \"`\", \"end\": \"`\", \"name\": \"string.quoted.rune.v\", \"patterns\": [{ \"include\": \"#string-escaped-char\" }, { \"include\": \"#string-interpolation\" }, { \"include\": \"#string-placeholder\" }] }, { \"begin\": \"(r)'\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.string.v\" } }, \"end\": \"'\", \"name\": \"string.quoted.raw.v\", \"patterns\": [{ \"include\": \"#string-interpolation\" }, { \"include\": \"#string-placeholder\" }] }, { \"begin\": '(r)\"', \"beginCaptures\": { \"1\": { \"name\": \"storage.type.string.v\" } }, \"end\": '\"', \"name\": \"string.quoted.raw.v\", \"patterns\": [{ \"include\": \"#string-interpolation\" }, { \"include\": \"#string-placeholder\" }] }, { \"begin\": \"(c?)'\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.string.v\" } }, \"end\": \"'\", \"name\": \"string.quoted.v\", \"patterns\": [{ \"include\": \"#string-escaped-char\" }, { \"include\": \"#string-interpolation\" }, { \"include\": \"#string-placeholder\" }] }, { \"begin\": '(c?)\"', \"beginCaptures\": { \"1\": { \"name\": \"storage.type.string.v\" } }, \"end\": '\"', \"name\": \"string.quoted.v\", \"patterns\": [{ \"include\": \"#string-escaped-char\" }, { \"include\": \"#string-interpolation\" }, { \"include\": \"#string-placeholder\" }] }] }, \"struct\": { \"patterns\": [{ \"begin\": \"^\\\\s*(?:(mut|pub(?:\\\\s+mut)?|__global)\\\\s+)?(struct|union)\\\\s+([\\\\w.]+)\\\\s*|({)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.modifier.$1.v\" }, \"2\": { \"name\": \"storage.type.struct.v\" }, \"3\": { \"name\": \"entity.name.type.v\" }, \"4\": { \"name\": \"punctuation.definition.bracket.curly.begin.v\" } }, \"end\": \"\\\\s*|(})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.bracket.curly.end.v\" } }, \"name\": \"meta.definition.struct.v\", \"patterns\": [{ \"include\": \"#struct-access-modifier\" }, { \"captures\": { \"1\": { \"name\": \"variable.other.property.v\" }, \"2\": { \"patterns\": [{ \"include\": \"#numbers\" }, { \"include\": \"#brackets\" }, { \"include\": \"#types\" }, { \"match\": \"\\\\w+\", \"name\": \"storage.type.other.v\" }] }, \"3\": { \"name\": \"keyword.operator.assignment.v\" }, \"4\": { \"patterns\": [{ \"include\": \"$self\" }] } }, \"match\": \"\\\\b(\\\\w+)\\\\s+([\\\\w\\\\[\\\\]\\\\*&.]+)(?:\\\\s*(=)\\\\s*((?:.(?=$|//|/\\\\*))*+))?\" }, { \"include\": \"#types\" }, { \"include\": \"$self\" }] }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.$1.v\" }, \"2\": { \"name\": \"storage.type.struct.v\" }, \"3\": { \"name\": \"entity.name.struct.v\" } }, \"match\": \"^\\\\s*(?:(mut|pub(?:\\\\s+mut)?|__global))\\\\s+?(struct)\\\\s+(?:\\\\s+([\\\\w.]+))?\", \"name\": \"meta.definition.struct.v\" }] }, \"struct-access-modifier\": { \"captures\": { \"1\": { \"name\": \"storage.modifier.$1.v\" }, \"2\": { \"name\": \"punctuation.separator.struct.key-value.v\" } }, \"match\": \"(?<=\\\\s|^)(mut|pub(?:\\\\s+mut)?|__global)(:|\\\\b)\" }, \"type\": { \"captures\": { \"1\": { \"name\": \"storage.modifier.$1.v\" }, \"2\": { \"name\": \"storage.type.type.v\" }, \"3\": { \"patterns\": [{ \"include\": \"#illegal-name\" }, { \"include\": \"#types\" }, { \"match\": \"\\\\w+\", \"name\": \"entity.name.type.v\" }] }, \"4\": { \"patterns\": [{ \"include\": \"#illegal-name\" }, { \"include\": \"#types\" }, { \"match\": \"\\\\w+\", \"name\": \"entity.name.type.v\" }] } }, \"match\": \"^\\\\s*(?:(pub)?\\\\s+)?(type)\\\\s+(\\\\w*)\\\\s+(?:\\\\w+\\\\.+)?(\\\\w*)\", \"name\": \"meta.definition.type.v\" }, \"types\": { \"patterns\": [{ \"match\": \"(?<!\\\\.)\\\\b(i(8|16|nt|64|128)|u(8|16|32|64|128)|f(32|64))\\\\b\", \"name\": \"storage.type.numeric.v\" }, { \"match\": \"(?<!\\\\.)\\\\b(bool|byte|byteptr|charptr|voidptr|string|ustring|rune)\\\\b\", \"name\": \"storage.type.$1.v\" }] }, \"variable-assign\": { \"captures\": { \"0\": { \"patterns\": [{ \"match\": \"[a-zA-Z_]\\\\w*\", \"name\": \"variable.other.assignment.v\" }, { \"include\": \"#punctuation\" }] } }, \"match\": \"[a-zA-Z_]\\\\w*(?:,\\\\s*[a-zA-Z_]\\\\w*)*(?=\\\\s*(?:=|:=))\" } }, \"scopeName\": \"source.v\" });\nvar v = [\n  lang\n];\n\nexport { v as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,KAAK,aAAa,CAAC,MAAM,OAAO,QAAQ,OAAO,OAAO,GAAG,QAAQ,KAAK,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,iBAAiB,CAAC,GAAG,cAAc,EAAE,SAAS,EAAE,SAAS,mBAAmB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,eAAe,EAAE,GAAG,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,EAAE,GAAG,cAAc,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,EAAE,GAAG,SAAS,qDAAqD,QAAQ,6BAA6B,GAAG,cAAc,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,gDAAgD,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,SAAS,+HAA+H,QAAQ,8BAA8B,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,YAAY,CAAC,EAAE,SAAS,sBAAsB,QAAQ,qBAAqB,GAAG,EAAE,SAAS,8FAA8F,QAAQ,eAAe,CAAC,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,SAAS,8BAA8B,QAAQ,oBAAoB,GAAG,EAAE,SAAS,iJAAiJ,QAAQ,oBAAoB,CAAC,EAAE,GAAG,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,SAAS,0EAA0E,QAAQ,2BAA2B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,SAAS,8EAA8E,QAAQ,wBAAwB,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,QAAQ,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,QAAQ,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,QAAQ,iCAAiC,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,MAAM,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,KAAK,QAAQ,8BAA8B,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,2BAA2B,QAAQ,sBAAsB,GAAG,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,QAAQ,qBAAqB,EAAE,GAAG,SAAS,oDAAoD,QAAQ,yBAAyB,GAAG,iBAAiB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qBAAqB,GAAG,KAAK,EAAE,QAAQ,eAAe,GAAG,KAAK,EAAE,QAAQ,yBAAyB,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,EAAE,GAAG,SAAS,iHAAiH,QAAQ,6BAA6B,GAAG,kBAAkB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,SAAS,QAAQ,QAAQ,yBAAyB,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,EAAE,GAAG,SAAS,sDAAsD,QAAQ,0BAA0B,GAAG,wBAAwB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qBAAqB,GAAG,KAAK,EAAE,QAAQ,eAAe,GAAG,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,SAAS,QAAQ,QAAQ,yBAAyB,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,EAAE,GAAG,SAAS,0GAA0G,QAAQ,6BAA6B,GAAG,kCAAkC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qBAAqB,GAAG,KAAK,EAAE,QAAQ,eAAe,GAAG,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,MAAM,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,SAAS,QAAQ,QAAQ,yBAAyB,CAAC,EAAE,EAAE,GAAG,SAAS,iHAAiH,QAAQ,6BAA6B,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,SAAS,QAAQ,QAAQ,wBAAwB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,SAAS,kCAAkC,QAAQ,4BAA4B,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,YAAY,OAAO,KAAK,QAAQ,gBAAgB,GAAG,gBAAgB,EAAE,SAAS,WAAW,QAAQ,oBAAoB,GAAG,eAAe,EAAE,SAAS,qBAAqB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mBAAmB,EAAE,GAAG,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,QAAQ,gBAAgB,GAAG,aAAa,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,SAAS,QAAQ,QAAQ,0BAA0B,CAAC,EAAE,EAAE,GAAG,SAAS,6CAA6C,QAAQ,8BAA8B,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,mBAAmB,QAAQ,oBAAoB,GAAG,EAAE,SAAS,+IAA+I,QAAQ,oBAAoB,GAAG,EAAE,SAAS,mFAAmF,QAAQ,eAAe,CAAC,EAAE,GAAG,eAAe,EAAE,SAAS,qBAAqB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mBAAmB,EAAE,GAAG,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,QAAQ,gBAAgB,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,SAAS,6CAA6C,QAAQ,iCAAiC,GAAG,EAAE,SAAS,8BAA8B,QAAQ,2BAA2B,GAAG,EAAE,SAAS,+BAA+B,QAAQ,4BAA4B,GAAG,EAAE,SAAS,+BAA+B,QAAQ,2BAA2B,GAAG,EAAE,SAAS,qCAAqC,QAAQ,yBAAyB,GAAG,EAAE,SAAS,2BAA2B,QAAQ,6BAA6B,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,SAAS,qDAAqD,QAAQ,gCAAgC,GAAG,EAAE,SAAS,yCAAyC,QAAQ,8BAA8B,GAAG,EAAE,SAAS,uHAAuH,QAAQ,gCAAgC,GAAG,EAAE,SAAS,mCAAmC,QAAQ,6BAA6B,GAAG,EAAE,SAAS,uBAAuB,QAAQ,6BAA6B,GAAG,EAAE,SAAS,OAAO,QAAQ,8BAA8B,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,QAAQ,qCAAqC,GAAG,EAAE,SAAS,KAAK,QAAQ,gCAAgC,GAAG,EAAE,SAAS,KAAK,QAAQ,0CAA0C,GAAG,EAAE,SAAS,KAAK,QAAQ,2CAA2C,GAAG,EAAE,SAAS,OAAO,QAAQ,8CAA8C,GAAG,EAAE,SAAS,KAAK,QAAQ,qBAAqB,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,CAAC,EAAE,SAAS,WAAW,QAAQ,yBAAyB,GAAG,EAAE,SAAS,SAAS,QAAQ,gCAAgC,CAAC,EAAE,GAAG,WAAW,EAAE,SAAS,yBAAyB,QAAQ,qBAAqB,GAAG,uBAAuB,EAAE,YAAY,CAAC,EAAE,SAAS,qFAAqF,QAAQ,8BAA8B,GAAG,EAAE,SAAS,+BAA+B,QAAQ,mCAAmC,CAAC,EAAE,GAAG,wBAAwB,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,mBAAmB,QAAQ,oBAAoB,GAAG,EAAE,SAAS,4BAA4B,QAAQ,gCAAgC,CAAC,EAAE,EAAE,GAAG,SAAS,4BAA4B,QAAQ,8BAA8B,GAAG,sBAAsB,EAAE,SAAS,4HAA4H,QAAQ,+BAA+B,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,SAAS,KAAK,OAAO,KAAK,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,EAAE,SAAS,QAAQ,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,OAAO,KAAK,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,EAAE,SAAS,QAAQ,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,OAAO,KAAK,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,OAAO,KAAK,QAAQ,mBAAmB,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,OAAO,KAAK,QAAQ,mBAAmB,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,SAAS,mFAAmF,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,qBAAqB,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,OAAO,YAAY,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,0BAA0B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,SAAS,QAAQ,QAAQ,uBAAuB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,EAAE,GAAG,SAAS,yEAAyE,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,SAAS,8EAA8E,QAAQ,2BAA2B,CAAC,EAAE,GAAG,0BAA0B,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,SAAS,kDAAkD,GAAG,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,SAAS,QAAQ,QAAQ,qBAAqB,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,SAAS,QAAQ,QAAQ,qBAAqB,CAAC,EAAE,EAAE,GAAG,SAAS,+DAA+D,QAAQ,yBAAyB,GAAG,SAAS,EAAE,YAAY,CAAC,EAAE,SAAS,gEAAgE,QAAQ,yBAAyB,GAAG,EAAE,SAAS,yEAAyE,QAAQ,oBAAoB,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,iBAAiB,QAAQ,8BAA8B,GAAG,EAAE,WAAW,eAAe,CAAC,EAAE,EAAE,GAAG,SAAS,uDAAuD,EAAE,GAAG,aAAa,WAAW,CAAC;AACzqc,IAAI,IAAI;AAAA,EACN;AACF;", "names": []}