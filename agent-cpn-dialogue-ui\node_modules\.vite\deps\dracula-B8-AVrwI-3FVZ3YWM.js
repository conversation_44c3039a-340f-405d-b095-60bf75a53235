import "./chunk-2LSFTFF7.js";

// node_modules/.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/vue-element-plus-x/dist/dracula-B8-AVrwI.js
var dracula = Object.freeze(JSON.parse('{"colors":{"activityBar.activeBackground":"#BD93F910","activityBar.activeBorder":"#FF79C680","activityBar.background":"#343746","activityBar.foreground":"#F8F8F2","activityBar.inactiveForeground":"#6272A4","activityBarBadge.background":"#FF79C6","activityBarBadge.foreground":"#F8F8F2","badge.background":"#44475A","badge.foreground":"#F8F8F2","breadcrumb.activeSelectionForeground":"#F8F8F2","breadcrumb.background":"#282A36","breadcrumb.focusForeground":"#F8F8F2","breadcrumb.foreground":"#6272A4","breadcrumbPicker.background":"#191A21","button.background":"#44475A","button.foreground":"#F8F8F2","button.secondaryBackground":"#282A36","button.secondaryForeground":"#F8F8F2","button.secondaryHoverBackground":"#343746","debugToolBar.background":"#21222C","diffEditor.insertedTextBackground":"#50FA7B20","diffEditor.removedTextBackground":"#FF555550","dropdown.background":"#343746","dropdown.border":"#191A21","dropdown.foreground":"#F8F8F2","editor.background":"#282A36","editor.findMatchBackground":"#FFB86C80","editor.findMatchHighlightBackground":"#FFFFFF40","editor.findRangeHighlightBackground":"#44475A75","editor.foldBackground":"#21222C80","editor.foreground":"#F8F8F2","editor.hoverHighlightBackground":"#8BE9FD50","editor.lineHighlightBorder":"#44475A","editor.rangeHighlightBackground":"#BD93F915","editor.selectionBackground":"#44475A","editor.selectionHighlightBackground":"#424450","editor.snippetFinalTabstopHighlightBackground":"#282A36","editor.snippetFinalTabstopHighlightBorder":"#50FA7B","editor.snippetTabstopHighlightBackground":"#282A36","editor.snippetTabstopHighlightBorder":"#6272A4","editor.wordHighlightBackground":"#8BE9FD50","editor.wordHighlightStrongBackground":"#50FA7B50","editorBracketHighlight.foreground1":"#F8F8F2","editorBracketHighlight.foreground2":"#FF79C6","editorBracketHighlight.foreground3":"#8BE9FD","editorBracketHighlight.foreground4":"#50FA7B","editorBracketHighlight.foreground5":"#BD93F9","editorBracketHighlight.foreground6":"#FFB86C","editorBracketHighlight.unexpectedBracket.foreground":"#FF5555","editorCodeLens.foreground":"#6272A4","editorError.foreground":"#FF5555","editorGroup.border":"#BD93F9","editorGroup.dropBackground":"#44475A70","editorGroupHeader.tabsBackground":"#191A21","editorGutter.addedBackground":"#50FA7B80","editorGutter.deletedBackground":"#FF555580","editorGutter.modifiedBackground":"#8BE9FD80","editorHoverWidget.background":"#282A36","editorHoverWidget.border":"#6272A4","editorIndentGuide.activeBackground":"#FFFFFF45","editorIndentGuide.background":"#FFFFFF1A","editorLineNumber.foreground":"#6272A4","editorLink.activeForeground":"#8BE9FD","editorMarkerNavigation.background":"#21222C","editorOverviewRuler.addedForeground":"#50FA7B80","editorOverviewRuler.border":"#191A21","editorOverviewRuler.currentContentForeground":"#50FA7B","editorOverviewRuler.deletedForeground":"#FF555580","editorOverviewRuler.errorForeground":"#FF555580","editorOverviewRuler.incomingContentForeground":"#BD93F9","editorOverviewRuler.infoForeground":"#8BE9FD80","editorOverviewRuler.modifiedForeground":"#8BE9FD80","editorOverviewRuler.selectionHighlightForeground":"#FFB86C","editorOverviewRuler.warningForeground":"#FFB86C80","editorOverviewRuler.wordHighlightForeground":"#8BE9FD","editorOverviewRuler.wordHighlightStrongForeground":"#50FA7B","editorRuler.foreground":"#FFFFFF1A","editorSuggestWidget.background":"#21222C","editorSuggestWidget.foreground":"#F8F8F2","editorSuggestWidget.selectedBackground":"#44475A","editorWarning.foreground":"#8BE9FD","editorWhitespace.foreground":"#FFFFFF1A","editorWidget.background":"#21222C","errorForeground":"#FF5555","extensionButton.prominentBackground":"#50FA7B90","extensionButton.prominentForeground":"#F8F8F2","extensionButton.prominentHoverBackground":"#50FA7B60","focusBorder":"#6272A4","foreground":"#F8F8F2","gitDecoration.conflictingResourceForeground":"#FFB86C","gitDecoration.deletedResourceForeground":"#FF5555","gitDecoration.ignoredResourceForeground":"#6272A4","gitDecoration.modifiedResourceForeground":"#8BE9FD","gitDecoration.untrackedResourceForeground":"#50FA7B","inlineChat.regionHighlight":"#343746","input.background":"#282A36","input.border":"#191A21","input.foreground":"#F8F8F2","input.placeholderForeground":"#6272A4","inputOption.activeBorder":"#BD93F9","inputValidation.errorBorder":"#FF5555","inputValidation.infoBorder":"#FF79C6","inputValidation.warningBorder":"#FFB86C","list.activeSelectionBackground":"#44475A","list.activeSelectionForeground":"#F8F8F2","list.dropBackground":"#44475A","list.errorForeground":"#FF5555","list.focusBackground":"#44475A75","list.highlightForeground":"#8BE9FD","list.hoverBackground":"#44475A75","list.inactiveSelectionBackground":"#44475A75","list.warningForeground":"#FFB86C","listFilterWidget.background":"#343746","listFilterWidget.noMatchesOutline":"#FF5555","listFilterWidget.outline":"#424450","merge.currentHeaderBackground":"#50FA7B90","merge.incomingHeaderBackground":"#BD93F990","panel.background":"#282A36","panel.border":"#BD93F9","panelTitle.activeBorder":"#FF79C6","panelTitle.activeForeground":"#F8F8F2","panelTitle.inactiveForeground":"#6272A4","peekView.border":"#44475A","peekViewEditor.background":"#282A36","peekViewEditor.matchHighlightBackground":"#F1FA8C80","peekViewResult.background":"#21222C","peekViewResult.fileForeground":"#F8F8F2","peekViewResult.lineForeground":"#F8F8F2","peekViewResult.matchHighlightBackground":"#F1FA8C80","peekViewResult.selectionBackground":"#44475A","peekViewResult.selectionForeground":"#F8F8F2","peekViewTitle.background":"#191A21","peekViewTitleDescription.foreground":"#6272A4","peekViewTitleLabel.foreground":"#F8F8F2","pickerGroup.border":"#BD93F9","pickerGroup.foreground":"#8BE9FD","progressBar.background":"#FF79C6","selection.background":"#BD93F9","settings.checkboxBackground":"#21222C","settings.checkboxBorder":"#191A21","settings.checkboxForeground":"#F8F8F2","settings.dropdownBackground":"#21222C","settings.dropdownBorder":"#191A21","settings.dropdownForeground":"#F8F8F2","settings.headerForeground":"#F8F8F2","settings.modifiedItemIndicator":"#FFB86C","settings.numberInputBackground":"#21222C","settings.numberInputBorder":"#191A21","settings.numberInputForeground":"#F8F8F2","settings.textInputBackground":"#21222C","settings.textInputBorder":"#191A21","settings.textInputForeground":"#F8F8F2","sideBar.background":"#21222C","sideBarSectionHeader.background":"#282A36","sideBarSectionHeader.border":"#191A21","sideBarTitle.foreground":"#F8F8F2","statusBar.background":"#191A21","statusBar.debuggingBackground":"#FF5555","statusBar.debuggingForeground":"#191A21","statusBar.foreground":"#F8F8F2","statusBar.noFolderBackground":"#191A21","statusBar.noFolderForeground":"#F8F8F2","statusBarItem.prominentBackground":"#FF5555","statusBarItem.prominentHoverBackground":"#FFB86C","statusBarItem.remoteBackground":"#BD93F9","statusBarItem.remoteForeground":"#282A36","tab.activeBackground":"#282A36","tab.activeBorderTop":"#FF79C680","tab.activeForeground":"#F8F8F2","tab.border":"#191A21","tab.inactiveBackground":"#21222C","tab.inactiveForeground":"#6272A4","terminal.ansiBlack":"#21222C","terminal.ansiBlue":"#BD93F9","terminal.ansiBrightBlack":"#6272A4","terminal.ansiBrightBlue":"#D6ACFF","terminal.ansiBrightCyan":"#A4FFFF","terminal.ansiBrightGreen":"#69FF94","terminal.ansiBrightMagenta":"#FF92DF","terminal.ansiBrightRed":"#FF6E6E","terminal.ansiBrightWhite":"#FFFFFF","terminal.ansiBrightYellow":"#FFFFA5","terminal.ansiCyan":"#8BE9FD","terminal.ansiGreen":"#50FA7B","terminal.ansiMagenta":"#FF79C6","terminal.ansiRed":"#FF5555","terminal.ansiWhite":"#F8F8F2","terminal.ansiYellow":"#F1FA8C","terminal.background":"#282A36","terminal.foreground":"#F8F8F2","titleBar.activeBackground":"#21222C","titleBar.activeForeground":"#F8F8F2","titleBar.inactiveBackground":"#191A21","titleBar.inactiveForeground":"#6272A4","walkThrough.embeddedEditorBackground":"#21222C"},"displayName":"Dracula Theme","name":"dracula","semanticHighlighting":true,"tokenColors":[{"scope":["emphasis"],"settings":{"fontStyle":"italic"}},{"scope":["strong"],"settings":{"fontStyle":"bold"}},{"scope":["header"],"settings":{"foreground":"#BD93F9"}},{"scope":["meta.diff","meta.diff.header"],"settings":{"foreground":"#6272A4"}},{"scope":["markup.inserted"],"settings":{"foreground":"#50FA7B"}},{"scope":["markup.deleted"],"settings":{"foreground":"#FF5555"}},{"scope":["markup.changed"],"settings":{"foreground":"#FFB86C"}},{"scope":["invalid"],"settings":{"fontStyle":"underline italic","foreground":"#FF5555"}},{"scope":["invalid.deprecated"],"settings":{"fontStyle":"underline italic","foreground":"#F8F8F2"}},{"scope":["entity.name.filename"],"settings":{"foreground":"#F1FA8C"}},{"scope":["markup.error"],"settings":{"foreground":"#FF5555"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.bold"],"settings":{"fontStyle":"bold","foreground":"#FFB86C"}},{"scope":["markup.heading"],"settings":{"fontStyle":"bold","foreground":"#BD93F9"}},{"scope":["markup.italic"],"settings":{"fontStyle":"italic","foreground":"#F1FA8C"}},{"scope":["beginning.punctuation.definition.list.markdown","beginning.punctuation.definition.quote.markdown","punctuation.definition.link.restructuredtext"],"settings":{"foreground":"#8BE9FD"}},{"scope":["markup.inline.raw","markup.raw.restructuredtext"],"settings":{"foreground":"#50FA7B"}},{"scope":["markup.underline.link","markup.underline.link.image"],"settings":{"foreground":"#8BE9FD"}},{"scope":["meta.link.reference.def.restructuredtext","punctuation.definition.directive.restructuredtext","string.other.link.description","string.other.link.title"],"settings":{"foreground":"#FF79C6"}},{"scope":["entity.name.directive.restructuredtext","markup.quote"],"settings":{"fontStyle":"italic","foreground":"#F1FA8C"}},{"scope":["meta.separator.markdown"],"settings":{"foreground":"#6272A4"}},{"scope":["fenced_code.block.language","markup.raw.inner.restructuredtext","markup.fenced_code.block.markdown punctuation.definition.markdown"],"settings":{"foreground":"#50FA7B"}},{"scope":["punctuation.definition.constant.restructuredtext"],"settings":{"foreground":"#BD93F9"}},{"scope":["markup.heading.markdown punctuation.definition.string.begin","markup.heading.markdown punctuation.definition.string.end"],"settings":{"foreground":"#BD93F9"}},{"scope":["meta.paragraph.markdown punctuation.definition.string.begin","meta.paragraph.markdown punctuation.definition.string.end"],"settings":{"foreground":"#F8F8F2"}},{"scope":["markup.quote.markdown meta.paragraph.markdown punctuation.definition.string.begin","markup.quote.markdown meta.paragraph.markdown punctuation.definition.string.end"],"settings":{"foreground":"#F1FA8C"}},{"scope":["entity.name.type.class","entity.name.class"],"settings":{"fontStyle":"normal","foreground":"#8BE9FD"}},{"scope":["keyword.expressions-and-types.swift","keyword.other.this","variable.language","variable.language punctuation.definition.variable.php","variable.other.readwrite.instance.ruby","variable.parameter.function.language.special"],"settings":{"fontStyle":"italic","foreground":"#BD93F9"}},{"scope":["entity.other.inherited-class"],"settings":{"fontStyle":"italic","foreground":"#8BE9FD"}},{"scope":["comment","punctuation.definition.comment","unused.comment","wildcard.comment"],"settings":{"foreground":"#6272A4"}},{"scope":["comment keyword.codetag.notation","comment.block.documentation keyword","comment.block.documentation storage.type.class"],"settings":{"foreground":"#FF79C6"}},{"scope":["comment.block.documentation entity.name.type"],"settings":{"fontStyle":"italic","foreground":"#8BE9FD"}},{"scope":["comment.block.documentation entity.name.type punctuation.definition.bracket"],"settings":{"foreground":"#8BE9FD"}},{"scope":["comment.block.documentation variable"],"settings":{"fontStyle":"italic","foreground":"#FFB86C"}},{"scope":["constant","variable.other.constant"],"settings":{"foreground":"#BD93F9"}},{"scope":["constant.character.escape","constant.character.string.escape","constant.regexp"],"settings":{"foreground":"#FF79C6"}},{"scope":["entity.name.tag"],"settings":{"foreground":"#FF79C6"}},{"scope":["entity.other.attribute-name.parent-selector"],"settings":{"foreground":"#FF79C6"}},{"scope":["entity.other.attribute-name"],"settings":{"fontStyle":"italic","foreground":"#50FA7B"}},{"scope":["entity.name.function","meta.function-call.object","meta.function-call.php","meta.function-call.static","meta.method-call.java meta.method","meta.method.groovy","support.function.any-method.lua","keyword.operator.function.infix"],"settings":{"foreground":"#50FA7B"}},{"scope":["entity.name.variable.parameter","meta.at-rule.function variable","meta.at-rule.mixin variable","meta.function.arguments variable.other.php","meta.selectionset.graphql meta.arguments.graphql variable.arguments.graphql","variable.parameter"],"settings":{"fontStyle":"italic","foreground":"#FFB86C"}},{"scope":["meta.decorator variable.other.readwrite","meta.decorator variable.other.property"],"settings":{"fontStyle":"italic","foreground":"#50FA7B"}},{"scope":["meta.decorator variable.other.object"],"settings":{"foreground":"#50FA7B"}},{"scope":["keyword","punctuation.definition.keyword"],"settings":{"foreground":"#FF79C6"}},{"scope":["keyword.control.new","keyword.operator.new"],"settings":{"fontStyle":"bold"}},{"scope":["meta.selector"],"settings":{"foreground":"#FF79C6"}},{"scope":["support"],"settings":{"fontStyle":"italic","foreground":"#8BE9FD"}},{"scope":["support.function.magic","support.variable","variable.other.predefined"],"settings":{"fontStyle":"regular","foreground":"#BD93F9"}},{"scope":["support.function","support.type.property-name"],"settings":{"fontStyle":"regular"}},{"scope":["constant.other.symbol.hashkey punctuation.definition.constant.ruby","entity.other.attribute-name.placeholder punctuation","entity.other.attribute-name.pseudo-class punctuation","entity.other.attribute-name.pseudo-element punctuation","meta.group.double.toml","meta.group.toml","meta.object-binding-pattern-variable punctuation.destructuring","punctuation.colon.graphql","punctuation.definition.block.scalar.folded.yaml","punctuation.definition.block.scalar.literal.yaml","punctuation.definition.block.sequence.item.yaml","punctuation.definition.entity.other.inherited-class","punctuation.function.swift","punctuation.separator.dictionary.key-value","punctuation.separator.hash","punctuation.separator.inheritance","punctuation.separator.key-value","punctuation.separator.key-value.mapping.yaml","punctuation.separator.namespace","punctuation.separator.pointer-access","punctuation.separator.slice","string.unquoted.heredoc punctuation.definition.string","support.other.chomping-indicator.yaml","punctuation.separator.annotation"],"settings":{"foreground":"#FF79C6"}},{"scope":["keyword.operator.other.powershell","keyword.other.statement-separator.powershell","meta.brace.round","meta.function-call punctuation","punctuation.definition.arguments.begin","punctuation.definition.arguments.end","punctuation.definition.entity.begin","punctuation.definition.entity.end","punctuation.definition.tag.cs","punctuation.definition.type.begin","punctuation.definition.type.end","punctuation.section.scope.begin","punctuation.section.scope.end","punctuation.terminator.expression.php","storage.type.generic.java","string.template meta.brace","string.template punctuation.accessor"],"settings":{"foreground":"#F8F8F2"}},{"scope":["meta.string-contents.quoted.double punctuation.definition.variable","punctuation.definition.interpolation.begin","punctuation.definition.interpolation.end","punctuation.definition.template-expression.begin","punctuation.definition.template-expression.end","punctuation.section.embedded.begin","punctuation.section.embedded.coffee","punctuation.section.embedded.end","punctuation.section.embedded.end source.php","punctuation.section.embedded.end source.ruby","punctuation.definition.variable.makefile"],"settings":{"foreground":"#FF79C6"}},{"scope":["entity.name.function.target.makefile","entity.name.section.toml","entity.name.tag.yaml","variable.other.key.toml"],"settings":{"foreground":"#8BE9FD"}},{"scope":["constant.other.date","constant.other.timestamp"],"settings":{"foreground":"#FFB86C"}},{"scope":["variable.other.alias.yaml"],"settings":{"fontStyle":"italic underline","foreground":"#50FA7B"}},{"scope":["storage","meta.implementation storage.type.objc","meta.interface-or-protocol storage.type.objc","source.groovy storage.type.def"],"settings":{"fontStyle":"regular","foreground":"#FF79C6"}},{"scope":["entity.name.type","keyword.primitive-datatypes.swift","keyword.type.cs","meta.protocol-list.objc","meta.return-type.objc","source.go storage.type","source.groovy storage.type","source.java storage.type","source.powershell entity.other.attribute-name","storage.class.std.rust","storage.type.attribute.swift","storage.type.c","storage.type.core.rust","storage.type.cs","storage.type.groovy","storage.type.objc","storage.type.php","storage.type.haskell","storage.type.ocaml"],"settings":{"fontStyle":"italic","foreground":"#8BE9FD"}},{"scope":["entity.name.type.type-parameter","meta.indexer.mappedtype.declaration entity.name.type","meta.type.parameters entity.name.type"],"settings":{"foreground":"#FFB86C"}},{"scope":["storage.modifier"],"settings":{"foreground":"#FF79C6"}},{"scope":["string.regexp","constant.other.character-class.set.regexp","constant.character.escape.backslash.regexp"],"settings":{"foreground":"#F1FA8C"}},{"scope":["punctuation.definition.group.capture.regexp"],"settings":{"foreground":"#FF79C6"}},{"scope":["string.regexp punctuation.definition.string.begin","string.regexp punctuation.definition.string.end"],"settings":{"foreground":"#FF5555"}},{"scope":["punctuation.definition.character-class.regexp"],"settings":{"foreground":"#8BE9FD"}},{"scope":["punctuation.definition.group.regexp"],"settings":{"foreground":"#FFB86C"}},{"scope":["punctuation.definition.group.assertion.regexp","keyword.operator.negation.regexp"],"settings":{"foreground":"#FF5555"}},{"scope":["meta.assertion.look-ahead.regexp"],"settings":{"foreground":"#50FA7B"}},{"scope":["string"],"settings":{"foreground":"#F1FA8C"}},{"scope":["punctuation.definition.string.begin","punctuation.definition.string.end"],"settings":{"foreground":"#E9F284"}},{"scope":["punctuation.support.type.property-name.begin","punctuation.support.type.property-name.end"],"settings":{"foreground":"#8BE9FE"}},{"scope":["string.quoted.docstring.multi","string.quoted.docstring.multi.python punctuation.definition.string.begin","string.quoted.docstring.multi.python punctuation.definition.string.end","string.quoted.docstring.multi.python constant.character.escape"],"settings":{"foreground":"#6272A4"}},{"scope":["variable","constant.other.key.perl","support.variable.property","variable.other.constant.js","variable.other.constant.ts","variable.other.constant.tsx"],"settings":{"foreground":"#F8F8F2"}},{"scope":["meta.import variable.other.readwrite","meta.variable.assignment.destructured.object.coffee variable"],"settings":{"fontStyle":"italic","foreground":"#FFB86C"}},{"scope":["meta.import variable.other.readwrite.alias","meta.export variable.other.readwrite.alias","meta.variable.assignment.destructured.object.coffee variable variable"],"settings":{"fontStyle":"normal","foreground":"#F8F8F2"}},{"scope":["meta.selectionset.graphql variable"],"settings":{"foreground":"#F1FA8C"}},{"scope":["meta.selectionset.graphql meta.arguments variable"],"settings":{"foreground":"#F8F8F2"}},{"scope":["entity.name.fragment.graphql","variable.fragment.graphql"],"settings":{"foreground":"#8BE9FD"}},{"scope":["constant.other.symbol.hashkey.ruby","keyword.operator.dereference.java","keyword.operator.navigation.groovy","meta.scope.for-loop.shell punctuation.definition.string.begin","meta.scope.for-loop.shell punctuation.definition.string.end","meta.scope.for-loop.shell string","storage.modifier.import","punctuation.section.embedded.begin.tsx","punctuation.section.embedded.end.tsx","punctuation.section.embedded.begin.jsx","punctuation.section.embedded.end.jsx","punctuation.separator.list.comma.css","constant.language.empty-list.haskell"],"settings":{"foreground":"#F8F8F2"}},{"scope":["source.shell variable.other"],"settings":{"foreground":"#BD93F9"}},{"scope":["support.constant"],"settings":{"fontStyle":"normal","foreground":"#BD93F9"}},{"scope":["meta.scope.prerequisites.makefile"],"settings":{"foreground":"#F1FA8C"}},{"scope":["meta.attribute-selector.scss"],"settings":{"foreground":"#F1FA8C"}},{"scope":["punctuation.definition.attribute-selector.end.bracket.square.scss","punctuation.definition.attribute-selector.begin.bracket.square.scss"],"settings":{"foreground":"#F8F8F2"}},{"scope":["meta.preprocessor.haskell"],"settings":{"foreground":"#6272A4"}},{"scope":["log.error"],"settings":{"fontStyle":"bold","foreground":"#FF5555"}},{"scope":["log.warning"],"settings":{"fontStyle":"bold","foreground":"#F1FA8C"}}],"type":"dark"}'));
export {
  dracula as default
};
//# sourceMappingURL=dracula-B8-AVrwI-3FVZ3YWM.js.map
