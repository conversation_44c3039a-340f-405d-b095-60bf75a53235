{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/powerquery.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"PowerQuery\", \"fileTypes\": [\"pq\", \"pqm\"], \"name\": \"powerquery\", \"patterns\": [{ \"include\": \"#Noise\" }, { \"include\": \"#LiteralExpression\" }, { \"include\": \"#Keywords\" }, { \"include\": \"#ImplicitVariable\" }, { \"include\": \"#IntrinsicVariable\" }, { \"include\": \"#Operators\" }, { \"include\": \"#DotOperators\" }, { \"include\": \"#TypeName\" }, { \"include\": \"#RecordExpression\" }, { \"include\": \"#Punctuation\" }, { \"include\": \"#QuotedIdentifier\" }, { \"include\": \"#Identifier\" }], \"repository\": { \"BlockComment\": { \"begin\": \"/\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.block.powerquery\" }, \"DecimalNumber\": { \"match\": \"(?<![\\\\d\\\\w])(\\\\d*\\\\.\\\\d+)\\\\b\", \"name\": \"constant.numeric.decimal.powerquery\" }, \"DotOperators\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.ellipsis.powerquery\" }, \"2\": { \"name\": \"keyword.operator.list.powerquery\" } }, \"match\": \"(?<!\\\\.)(?:(\\\\.\\\\.\\\\.)|(\\\\.\\\\.))(?!\\\\.)\" }, \"EscapeSequence\": { \"begin\": \"#\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.escapesequence.begin.powerquery\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.escapesequence.end.powerquery\" } }, \"name\": \"constant.character.escapesequence.powerquery\", \"patterns\": [{ \"match\": \"(#|\\\\h{4}|\\\\h{8}|cr|lf|tab)(?:,(#|\\\\h{4}|\\\\h{8}|cr|lf|tab))*\" }, { \"match\": \"[^\\\\)]\", \"name\": \"invalid.illegal.escapesequence.powerquery\" }] }, \"FloatNumber\": { \"match\": \"(\\\\d*\\\\.)?\\\\d+(e|E)(\\\\+|-)?\\\\d+\", \"name\": \"constant.numeric.float.powerquery\" }, \"HexNumber\": { \"match\": \"0(x|X)\\\\h+\", \"name\": \"constant.numeric.integer.hexadecimal.powerquery\" }, \"Identifier\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.inclusiveidentifier.powerquery\" }, \"2\": { \"name\": \"entity.name.powerquery\" } }, \"match\": \"(?x:(?<![\\\\._\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Nd}\\\\p{Pc}\\\\p{Mn}\\\\p{Mc}\\\\p{Cf}])(@?)([_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}][_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Nd}\\\\p{Pc}\\\\p{Mn}\\\\p{Mc}\\\\p{Cf}]*(?:\\\\.[_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}][_\\\\p{Lu}\\\\p{Ll}\\\\p{Lt}\\\\p{Lm}\\\\p{Lo}\\\\p{Nl}\\\\p{Nd}\\\\p{Pc}\\\\p{Mn}\\\\p{Mc}\\\\p{Cf}])*)\\\\b)\" }, \"ImplicitVariable\": { \"match\": \"\\\\b_\\\\b\", \"name\": \"keyword.operator.implicitvariable.powerquery\" }, \"InclusiveIdentifier\": { \"captures\": { \"0\": { \"name\": \"inclusiveidentifier.powerquery\" } }, \"match\": \"@\" }, \"IntNumber\": { \"captures\": { \"1\": { \"name\": \"constant.numeric.integer.powerquery\" } }, \"match\": \"\\\\b(\\\\d+)\\\\b\" }, \"IntrinsicVariable\": { \"captures\": { \"1\": { \"name\": \"constant.language.intrinsicvariable.powerquery\" } }, \"match\": \"(?<![\\\\d\\\\w])(#sections|#shared)\\\\b\" }, \"Keywords\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.word.logical.powerquery\" }, \"2\": { \"name\": \"keyword.control.conditional.powerquery\" }, \"3\": { \"name\": \"keyword.control.exception.powerquery\" }, \"4\": { \"name\": \"keyword.other.powerquery\" }, \"5\": { \"name\": \"keyword.powerquery\" } }, \"match\": \"\\\\b(?:(and|or|not)|(if|then|else)|(try|otherwise)|(as|each|in|is|let|meta|type|error)|(section|shared))\\\\b\" }, \"LineComment\": { \"match\": \"//.*\", \"name\": \"comment.line.double-slash.powerquery\" }, \"LiteralExpression\": { \"patterns\": [{ \"include\": \"#String\" }, { \"include\": \"#NumericConstant\" }, { \"include\": \"#LogicalConstant\" }, { \"include\": \"#NullConstant\" }, { \"include\": \"#FloatNumber\" }, { \"include\": \"#DecimalNumber\" }, { \"include\": \"#HexNumber\" }, { \"include\": \"#IntNumber\" }] }, \"LogicalConstant\": { \"match\": \"\\\\b(true|false)\\\\b\", \"name\": \"constant.language.logical.powerquery\" }, \"Noise\": { \"patterns\": [{ \"include\": \"#BlockComment\" }, { \"include\": \"#LineComment\" }, { \"include\": \"#Whitespace\" }] }, \"NullConstant\": { \"match\": \"\\\\b(null)\\\\b\", \"name\": \"constant.language.null.powerquery\" }, \"NumericConstant\": { \"captures\": { \"1\": { \"name\": \"constant.language.numeric.float.powerquery\" } }, \"match\": \"(?<![\\\\d\\\\w])(#infinity|#nan)\\\\b\" }, \"Operators\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.function.powerquery\" }, \"2\": { \"name\": \"keyword.operator.assignment-or-comparison.powerquery\" }, \"3\": { \"name\": \"keyword.operator.comparison.powerquery\" }, \"4\": { \"name\": \"keyword.operator.combination.powerquery\" }, \"5\": { \"name\": \"keyword.operator.arithmetic.powerquery\" }, \"6\": { \"name\": \"keyword.operator.sectionaccess.powerquery\" }, \"7\": { \"name\": \"keyword.operator.optional.powerquery\" } }, \"match\": \"(=>)|(=)|(<>|<|>|<=|>=)|(&)|(\\\\+|-|\\\\*|\\\\/)|(!)|(\\\\?)\" }, \"Punctuation\": { \"captures\": { \"1\": { \"name\": \"punctuation.separator.powerquery\" }, \"2\": { \"name\": \"punctuation.section.parens.begin.powerquery\" }, \"3\": { \"name\": \"punctuation.section.parens.end.powerquery\" }, \"4\": { \"name\": \"punctuation.section.braces.begin.powerquery\" }, \"5\": { \"name\": \"punctuation.section.braces.end.powerquery\" } }, \"match\": \"(,)|(\\\\()|(\\\\))|({)|(})\" }, \"QuotedIdentifier\": { \"begin\": '#\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.quotedidentifier.begin.powerquery\" } }, \"end\": '\"(?!\")', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.quotedidentifier.end.powerquery\" } }, \"name\": \"entity.name.powerquery\", \"patterns\": [{ \"match\": '\"\"', \"name\": \"constant.character.escape.quote.powerquery\" }, { \"include\": \"#EscapeSequence\" }] }, \"RecordExpression\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.brackets.begin.powerquery\" } }, \"contentName\": \"meta.recordexpression.powerquery\", \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.brackets.end.powerquery\" } }, \"patterns\": [{ \"include\": \"$self\" }] }, \"String\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.powerquery\" } }, \"end\": '\"(?!\")', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.powerquery\" } }, \"name\": \"string.quoted.double.powerquery\", \"patterns\": [{ \"match\": '\"\"', \"name\": \"constant.character.escape.quote.powerquery\" }, { \"include\": \"#EscapeSequence\" }] }, \"TypeName\": { \"captures\": { \"1\": { \"name\": \"storage.modifier.powerquery\" }, \"2\": { \"name\": \"storage.type.powerquery\" } }, \"match\": \"\\\\b(?:(optional|nullable)|(action|any|anynonnull|binary|date|datetime|datetimezone|duration|function|list|logical|none|null|number|record|table|text|type))\\\\b\" }, \"Whitespace\": { \"match\": \"\\\\s+\" } }, \"scopeName\": \"source.powerquery\" });\nvar powerquery = [\n  lang\n];\n\nexport { powerquery as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,cAAc,aAAa,CAAC,MAAM,KAAK,GAAG,QAAQ,cAAc,YAAY,CAAC,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,cAAc,CAAC,GAAG,cAAc,EAAE,gBAAgB,EAAE,SAAS,QAAQ,OAAO,QAAQ,QAAQ,2BAA2B,GAAG,iBAAiB,EAAE,SAAS,iCAAiC,QAAQ,sCAAsC,GAAG,gBAAgB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,0CAA0C,GAAG,kBAAkB,EAAE,SAAS,QAAQ,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yDAAyD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,uDAAuD,EAAE,GAAG,QAAQ,gDAAgD,YAAY,CAAC,EAAE,SAAS,+DAA+D,GAAG,EAAE,SAAS,UAAU,QAAQ,4CAA4C,CAAC,EAAE,GAAG,eAAe,EAAE,SAAS,mCAAmC,QAAQ,oCAAoC,GAAG,aAAa,EAAE,SAAS,cAAc,QAAQ,kDAAkD,GAAG,cAAc,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,kDAAkD,GAAG,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,SAAS,4WAA4W,GAAG,oBAAoB,EAAE,SAAS,WAAW,QAAQ,+CAA+C,GAAG,uBAAuB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,SAAS,IAAI,GAAG,aAAa,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,SAAS,eAAe,GAAG,qBAAqB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,SAAS,sCAAsC,GAAG,YAAY,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,qBAAqB,EAAE,GAAG,SAAS,6GAA6G,GAAG,eAAe,EAAE,SAAS,QAAQ,QAAQ,uCAAuC,GAAG,qBAAqB,EAAE,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,sBAAsB,QAAQ,uCAAuC,GAAG,SAAS,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,gBAAgB,QAAQ,oCAAoC,GAAG,mBAAmB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,SAAS,mCAAmC,GAAG,aAAa,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,uDAAuD,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,wDAAwD,GAAG,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,SAAS,0BAA0B,GAAG,oBAAoB,EAAE,SAAS,MAAM,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2DAA2D,EAAE,GAAG,OAAO,UAAU,eAAe,EAAE,KAAK,EAAE,QAAQ,yDAAyD,EAAE,GAAG,QAAQ,0BAA0B,YAAY,CAAC,EAAE,SAAS,MAAM,QAAQ,6CAA6C,GAAG,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,oBAAoB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,eAAe,oCAAoC,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,UAAU,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,UAAU,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,QAAQ,mCAAmC,YAAY,CAAC,EAAE,SAAS,MAAM,QAAQ,6CAA6C,GAAG,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,SAAS,iKAAiK,GAAG,cAAc,EAAE,SAAS,OAAO,EAAE,GAAG,aAAa,oBAAoB,CAAC;AACpjM,IAAI,aAAa;AAAA,EACf;AACF;", "names": []}