{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/powershell.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"PowerShell\", \"name\": \"powershell\", \"patterns\": [{ \"begin\": \"<#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.block.begin.powershell\" } }, \"end\": \"#>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.block.end.powershell\" } }, \"name\": \"comment.block.powershell\", \"patterns\": [{ \"include\": \"#commentEmbeddedDocs\" }] }, { \"match\": \"[2-6]>&1|>>|>|<<|<|>|>\\\\||[1-6]>|[1-6]>>\", \"name\": \"keyword.operator.redirection.powershell\" }, { \"include\": \"#commands\" }, { \"include\": \"#commentLine\" }, { \"include\": \"#variable\" }, { \"include\": \"#subexpression\" }, { \"include\": \"#function\" }, { \"include\": \"#attribute\" }, { \"include\": \"#UsingDirective\" }, { \"include\": \"#type\" }, { \"include\": \"#hashtable\" }, { \"include\": \"#doubleQuotedString\" }, { \"include\": \"#scriptblock\" }, { \"comment\": \"Needed to parse stuff correctly in 'argument mode'. (See about_parsing.)\", \"include\": \"#doubleQuotedStringEscapes\" }, { \"applyEndPatternLast\": true, \"begin\": \"['\\\\x{2018}-\\\\x{201B}]\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.powershell\" } }, \"end\": \"['\\\\x{2018}-\\\\x{201B}]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.powershell\" } }, \"name\": \"string.quoted.single.powershell\", \"patterns\": [{ \"match\": \"['\\\\x{2018}-\\\\x{201B}]{2}\", \"name\": \"constant.character.escape.powershell\" }] }, { \"begin\": '(@[\"\\\\x{201C}-\\\\x{201E}])\\\\s*$', \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.powershell\" } }, \"end\": '^[\"\\\\x{201C}-\\\\x{201E}]@', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.powershell\" } }, \"name\": \"string.quoted.double.heredoc.powershell\", \"patterns\": [{ \"include\": \"#variableNoProperty\" }, { \"include\": \"#doubleQuotedStringEscapes\" }, { \"include\": \"#interpolation\" }] }, { \"begin\": \"(@['\\\\x{2018}-\\\\x{201B}])\\\\s*$\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.powershell\" } }, \"end\": \"^['\\\\x{2018}-\\\\x{201B}]@\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.powershell\" } }, \"name\": \"string.quoted.single.heredoc.powershell\" }, { \"include\": \"#numericConstant\" }, { \"begin\": \"(@)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.array.begin.powershell\" }, \"2\": { \"name\": \"punctuation.section.group.begin.powershell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.group.end.powershell\" } }, \"name\": \"meta.group.array-expression.powershell\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"begin\": \"((\\\\$))(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.substatement.powershell\" }, \"2\": { \"name\": \"punctuation.definition.subexpression.powershell\" }, \"3\": { \"name\": \"punctuation.section.group.begin.powershell\" } }, \"comment\": \"TODO: move to repo; make recursive.\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.group.end.powershell\" } }, \"name\": \"meta.group.complex.subexpression.powershell\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"match\": \"(\\\\b(([A-Za-z0-9\\\\-_\\\\.]+)\\\\.(?i:exe|com|cmd|bat))\\\\b)\", \"name\": \"support.function.powershell\" }, { \"match\": \"(?<!\\\\w|-|\\\\.)((?i:begin|break|catch|clean|continue|data|default|define|do|dynamicparam|else|elseif|end|exit|finally|for|from|if|in|inlinescript|parallel|param|process|return|sequence|switch|throw|trap|try|until|var|while)|%|\\\\?)(?!\\\\w)\", \"name\": \"keyword.control.powershell\" }, { \"match\": \"(?<!\\\\w|-|[^\\\\)]\\\\.)((?i:(foreach|where)(?!-object))|%|\\\\?)(?!\\\\w)\", \"name\": \"keyword.control.powershell\" }, { \"begin\": \"(?<!\\\\w)(--%)(?!\\\\w)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.powershell\" } }, \"comment\": \"This should be moved to the repository at some point.\", \"end\": \"$\", \"patterns\": [{ \"match\": \".+\", \"name\": \"string.unquoted.powershell\" }] }, { \"comment\": \"This should only be relevant inside a class but will require a rework of how classes are matched. This is a temp fix.\", \"match\": \"(?<!\\\\w)((?i:hidden|static))(?!\\\\w)\", \"name\": \"storage.modifier.powershell\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.powershell\" }, \"2\": { \"name\": \"entity.name.function\" } }, \"comment\": \"capture should be entity.name.type, but it doesn't provide a good color in the default schema.\", \"match\": \"(?<!\\\\w|-)((?i:class)|%|\\\\?)(?:\\\\s)+((?:\\\\p{L}|\\\\d|_|-|)+)\\\\b\" }, { \"match\": \"(?<!\\\\w)-(?i:is(?:not)?|as)\\\\b\", \"name\": \"keyword.operator.comparison.powershell\" }, { \"match\": \"(?<!\\\\w)-(?i:[ic]?(?:eq|ne|[gl][te]|(?:not)?(?:like|match|contains|in)|replace))(?!\\\\p{L})\", \"name\": \"keyword.operator.comparison.powershell\" }, { \"match\": \"(?<!\\\\w)-(?i:join|split)(?!\\\\p{L})|!\", \"name\": \"keyword.operator.unary.powershell\" }, { \"match\": \"(?<!\\\\w)-(?i:and|or|not|xor)(?!\\\\p{L})|!\", \"name\": \"keyword.operator.logical.powershell\" }, { \"match\": \"(?<!\\\\w)-(?i:band|bor|bnot|bxor|shl|shr)(?!\\\\p{L})\", \"name\": \"keyword.operator.bitwise.powershell\" }, { \"match\": \"(?<!\\\\w)-(?i:f)(?!\\\\p{L})\", \"name\": \"keyword.operator.string-format.powershell\" }, { \"match\": \"[+%*/-]?=|[+/*%-]\", \"name\": \"keyword.operator.assignment.powershell\" }, { \"match\": \"\\\\|{2}|&{2}|;\", \"name\": \"punctuation.terminator.statement.powershell\" }, { \"match\": \"&|(?<!\\\\w)\\\\.(?= )|`|,|\\\\|\", \"name\": \"keyword.operator.other.powershell\" }, { \"comment\": \"This is very imprecise, is there a syntax for 'must come after...' \", \"match\": \"(?<!\\\\s|^)\\\\.\\\\.(?=\\\\-?\\\\d|\\\\(|\\\\$)\", \"name\": \"keyword.operator.range.powershell\" }], \"repository\": { \"RequiresDirective\": { \"begin\": \"(?<=#)(?i:(requires))\\\\s\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.requires.powershell\" } }, \"end\": \"$\", \"name\": \"meta.requires.powershell\", \"patterns\": [{ \"match\": \"\\\\-(?i:Modules|PSSnapin|RunAsAdministrator|ShellId|Version|Assembly|PSEdition)\", \"name\": \"keyword.other.powershell\" }, { \"match\": \"(?<!-)\\\\b\\\\p{L}+|\\\\d+(?:\\\\.\\\\d+)*\", \"name\": \"variable.parameter.powershell\" }, { \"include\": \"#hashtable\" }] }, \"UsingDirective\": { \"captures\": { \"1\": { \"name\": \"keyword.control.using.powershell\" }, \"2\": { \"name\": \"keyword.other.powershell\" }, \"3\": { \"name\": \"variable.parameter.powershell\" } }, \"match\": \"(?<!\\\\w)(?i:(using))\\\\s+(?i:(namespace|module))\\\\s+(?i:((?:\\\\w+(?:\\\\.)?)+))\" }, \"attribute\": { \"begin\": \"(\\\\[)\\\\s*\\\\b(?i)(cmdletbinding|alias|outputtype|parameter|validatenotnull|validatenotnullorempty|validatecount|validateset|allownull|allowemptycollection|allowemptystring|validatescript|validaterange|validatepattern|validatelength|supportswildcards)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.bracket.begin.powershell\" }, \"2\": { \"name\": \"support.function.attribute.powershell\" } }, \"end\": \"(\\\\])\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.bracket.end.powershell\" } }, \"name\": \"meta.attribute.powershell\", \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.group.begin.powershell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.group.end.powershell\" } }, \"patterns\": [{ \"include\": \"$self\" }, { \"captures\": { \"1\": { \"name\": \"variable.parameter.attribute.powershell\" }, \"2\": { \"name\": \"keyword.operator.assignment.powershell\" } }, \"match\": \"(?i)\\\\b(mandatory|valuefrompipeline|valuefrompipelinebypropertyname|valuefromremainingarguments|position|parametersetname|defaultparametersetname|supportsshouldprocess|supportspaging|positionalbinding|helpuri|confirmimpact|helpmessage)\\\\b(?:\\\\s+)?(=)?\" }] }] }, \"commands\": { \"patterns\": [{ \"comment\": \"Verb-Noun pattern:\", \"match\": \"(?:(\\\\p{L}|\\\\d|_|-|\\\\\\\\|\\\\:)*\\\\\\\\)?\\\\b(?i:Add|Approve|Assert|Backup|Block|Build|Checkpoint|Clear|Close|Compare|Complete|Compress|Confirm|Connect|Convert|ConvertFrom|ConvertTo|Copy|Debug|Deny|Deploy|Disable|Disconnect|Dismount|Edit|Enable|Enter|Exit|Expand|Export|Find|Format|Get|Grant|Group|Hide|Import|Initialize|Install|Invoke|Join|Limit|Lock|Measure|Merge|Mount|Move|New|Open|Optimize|Out|Ping|Pop|Protect|Publish|Push|Read|Receive|Redo|Register|Remove|Rename|Repair|Request|Reset|Resize|Resolve|Restart|Restore|Resume|Revoke|Save|Search|Select|Send|Set|Show|Skip|Split|Start|Step|Stop|Submit|Suspend|Switch|Sync|Test|Trace|Unblock|Undo|Uninstall|Unlock|Unprotect|Unpublish|Unregister|Update|Use|Wait|Watch|Write)\\\\-.+?(?:\\\\.(?i:exe|cmd|bat|ps1))?\\\\b\", \"name\": \"support.function.powershell\" }, { \"comment\": \"Builtin cmdlets with reserved verbs\", \"match\": \"(?<!\\\\w)(?i:foreach-object)(?!\\\\w)\", \"name\": \"support.function.powershell\" }, { \"comment\": \"Builtin cmdlets with reserved verbs\", \"match\": \"(?<!\\\\w)(?i:where-object)(?!\\\\w)\", \"name\": \"support.function.powershell\" }, { \"comment\": \"Builtin cmdlets with reserved verbs\", \"match\": \"(?<!\\\\w)(?i:sort-object)(?!\\\\w)\", \"name\": \"support.function.powershell\" }, { \"comment\": \"Builtin cmdlets with reserved verbs\", \"match\": \"(?<!\\\\w)(?i:tee-object)(?!\\\\w)\", \"name\": \"support.function.powershell\" }] }, \"commentEmbeddedDocs\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.string.documentation.powershell\" }, \"2\": { \"name\": \"keyword.operator.documentation.powershell\" } }, \"comment\": \"these embedded doc keywords do not support arguments, must be the only thing on the line\", \"match\": \"(?:^|\\\\G)(?i:\\\\s*(\\\\.)(COMPONENT|DESCRIPTION|EXAMPLE|FUNCTIONALITY|INPUTS|LINK|NOTES|OUTPUTS|ROLE|SYNOPSIS))\\\\s*$\", \"name\": \"comment.documentation.embedded.powershell\" }, { \"captures\": { \"1\": { \"name\": \"constant.string.documentation.powershell\" }, \"2\": { \"name\": \"keyword.operator.documentation.powershell\" }, \"3\": { \"name\": \"keyword.operator.documentation.powershell\" } }, \"comment\": \"these embedded doc keywords require arguments though the type required may be inconsistent, they may not all be able to use the same argument match\", \"match\": \"(?:^|\\\\G)(?i:\\\\s*(\\\\.)(EXTERNALHELP|FORWARDHELP(?:CATEGORY|TARGETNAME)|PARAMETER|REMOTEHELPRUNSPACE))\\\\s+(.+?)\\\\s*$\", \"name\": \"comment.documentation.embedded.powershell\" }] }, \"commentLine\": { \"begin\": \"(?<![`\\\\\\\\-])(#)#*\", \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.powershell\" } }, \"end\": \"$\\\\n?\", \"name\": \"comment.line.powershell\", \"patterns\": [{ \"include\": \"#commentEmbeddedDocs\" }, { \"include\": \"#RequiresDirective\" }] }, \"doubleQuotedString\": { \"applyEndPatternLast\": true, \"begin\": '[\"\\\\x{201C}-\\\\x{201E}]', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.powershell\" } }, \"end\": '[\"\\\\x{201C}-\\\\x{201E}]', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.powershell\" } }, \"name\": \"string.quoted.double.powershell\", \"patterns\": [{ \"match\": \"(?i)\\\\b[A-Z0-9._%+-]+@[A-Z0-9.-]+\\\\.[A-Z]{2,64}\\\\b\" }, { \"include\": \"#variableNoProperty\" }, { \"include\": \"#doubleQuotedStringEscapes\" }, { \"match\": '[\"\\\\x{201C}-\\\\x{201E}]{2}', \"name\": \"constant.character.escape.powershell\" }, { \"include\": \"#interpolation\" }, { \"match\": \"`\\\\s*$\", \"name\": \"keyword.other.powershell\" }] }, \"doubleQuotedStringEscapes\": { \"patterns\": [{ \"match\": \"`[`0abefnrtv'\\\"\\\\x{2018}-\\\\x{201E}$]\", \"name\": \"constant.character.escape.powershell\" }, { \"include\": \"#unicodeEscape\" }] }, \"function\": { \"begin\": \"^(?:\\\\s*+)(?i)(function|filter|configuration|workflow)\\\\s+(?:(global|local|script|private):)?((?:\\\\p{L}|\\\\d|_|-|\\\\.)+)\", \"beginCaptures\": { \"0\": { \"name\": \"meta.function.powershell\" }, \"1\": { \"name\": \"storage.type.powershell\" }, \"2\": { \"name\": \"storage.modifier.scope.powershell\" }, \"3\": { \"name\": \"entity.name.function.powershell\" } }, \"end\": \"(?=\\\\{|\\\\()\", \"patterns\": [{ \"include\": \"#commentLine\" }] }, \"hashtable\": { \"begin\": \"(@)(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.hashtable.begin.powershell\" }, \"2\": { \"name\": \"punctuation.section.braces.begin.powershell\" } }, \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.braces.end.powershell\" } }, \"name\": \"meta.hashtable.powershell\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.powershell\" }, \"2\": { \"name\": \"variable.other.readwrite.powershell\" }, \"3\": { \"name\": \"punctuation.definition.string.end.powershell\" }, \"4\": { \"name\": \"keyword.operator.assignment.powershell\" } }, \"match\": `\\\\b((?:\\\\'|\\\\\")?)(\\\\w+)((?:\\\\'|\\\\\")?)(?:\\\\s+)?(=)(?:\\\\s+)?`, \"name\": \"meta.hashtable.assignment.powershell\" }, { \"include\": \"#scriptblock\" }, { \"include\": \"$self\" }] }, \"interpolation\": { \"begin\": \"(((\\\\$)))((\\\\())\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.substatement.powershell\" }, \"2\": { \"name\": \"punctuation.definition.substatement.powershell\" }, \"3\": { \"name\": \"punctuation.section.embedded.substatement.begin.powershell\" }, \"4\": { \"name\": \"punctuation.section.group.begin.powershell\" }, \"5\": { \"name\": \"punctuation.section.embedded.substatement.begin.powershell\" } }, \"contentName\": \"interpolated.complex.source.powershell\", \"end\": \"(\\\\))\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.group.end.powershell\" }, \"1\": { \"name\": \"punctuation.section.embedded.substatement.end.powershell\" } }, \"name\": \"meta.embedded.substatement.powershell\", \"patterns\": [{ \"include\": \"$self\" }] }, \"numericConstant\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.numeric.hex.powershell\" }, \"2\": { \"name\": \"keyword.other.powershell\" } }, \"match\": \"(?<!\\\\w)([-+]?0(?:x|X)[0-9a-fA-F_]+(?:U|u|L|l|UL|Ul|uL|ul|LU|Lu|lU|lu)?)((?i:[kmgtp]b)?)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"constant.numeric.integer.powershell\" }, \"2\": { \"name\": \"keyword.other.powershell\" } }, \"match\": \"(?<!\\\\w)([-+]?(?:[0-9_]+)?\\\\.[0-9_]+(?:(?:e|E)[0-9]+)?(?:F|f|D|d|M|m)?)((?i:[kmgtp]b)?)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"constant.numeric.octal.powershell\" }, \"2\": { \"name\": \"keyword.other.powershell\" } }, \"match\": \"(?<!\\\\w)([-+]?0(?:b|B)[01_]+(?:U|u|L|l|UL|Ul|uL|ul|LU|Lu|lU|lu)?)((?i:[kmgtp]b)?)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"constant.numeric.integer.powershell\" }, \"2\": { \"name\": \"keyword.other.powershell\" } }, \"match\": \"(?<!\\\\w)([-+]?[0-9_]+(?:e|E)(?:[0-9_])?+(?:F|f|D|d|M|m)?)((?i:[kmgtp]b)?)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"constant.numeric.integer.powershell\" }, \"2\": { \"name\": \"keyword.other.powershell\" } }, \"match\": \"(?<!\\\\w)([-+]?[0-9_]+\\\\.(?:e|E)(?:[0-9_])?+(?:F|f|D|d|M|m)?)((?i:[kmgtp]b)?)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"constant.numeric.integer.powershell\" }, \"2\": { \"name\": \"keyword.other.powershell\" } }, \"match\": \"(?<!\\\\w)([-+]?[0-9_]+[\\\\.]?(?:F|f|D|d|M|m))((?i:[kmgtp]b)?)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"constant.numeric.integer.powershell\" }, \"2\": { \"name\": \"keyword.other.powershell\" } }, \"match\": \"(?<!\\\\w)([-+]?[0-9_]+[\\\\.]?(?:U|u|L|l|UL|Ul|uL|ul|LU|Lu|lU|lu)?)((?i:[kmgtp]b)?)\\\\b\" }] }, \"scriptblock\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.braces.begin.powershell\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.braces.end.powershell\" } }, \"name\": \"meta.scriptblock.powershell\", \"patterns\": [{ \"include\": \"$self\" }] }, \"subexpression\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.group.begin.powershell\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.group.end.powershell\" } }, \"name\": \"meta.group.simple.subexpression.powershell\", \"patterns\": [{ \"include\": \"$self\" }] }, \"type\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.bracket.begin.powershell\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.bracket.end.powershell\" } }, \"patterns\": [{ \"match\": \"(?!\\\\d+|\\\\.)(?:\\\\p{L}|\\\\p{N}|\\\\.)+\", \"name\": \"storage.type.powershell\" }, { \"include\": \"$self\" }] }, \"unicodeEscape\": { \"comment\": \"`u{xxxx} added in PowerShell 6.0\", \"patterns\": [{ \"match\": \"`u\\\\{(?:(?:10)?([0-9a-fA-F]){1,4}|0?\\\\g<1>{1,5})}\", \"name\": \"constant.character.escape.powershell\" }, { \"match\": \"`u(?:\\\\{[0-9a-fA-F]{,6}.)?\", \"name\": \"invalid.character.escape.powershell\" }] }, \"variable\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"constant.language.powershell\" }, \"1\": { \"name\": \"punctuation.definition.variable.powershell\" } }, \"comment\": \"These are special constants.\", \"match\": \"(\\\\$)(?i:(False|Null|True))\\\\b\" }, { \"captures\": { \"0\": { \"name\": \"support.constant.variable.powershell\" }, \"1\": { \"name\": \"punctuation.definition.variable.powershell\" }, \"3\": { \"name\": \"variable.other.member.powershell\" } }, \"comment\": \"These are the other built-in constants.\", \"match\": \"(\\\\$)(?i:(Error|ExecutionContext|Host|Home|PID|PsHome|PsVersionTable|ShellID))((?:\\\\.(?:\\\\p{L}|\\\\d|_)+)*\\\\b)?\\\\b\" }, { \"captures\": { \"0\": { \"name\": \"support.variable.automatic.powershell\" }, \"1\": { \"name\": \"punctuation.definition.variable.powershell\" }, \"3\": { \"name\": \"variable.other.member.powershell\" } }, \"comment\": \"Automatic variables are not constants, but they are read-only. In monokai (default) color schema support.variable doesn't have color, so we use constant.\", \"match\": \"(\\\\$)((?:[$^?])|(?i:_|Args|ConsoleFileName|Event|EventArgs|EventSubscriber|ForEach|Input|LastExitCode|Matches|MyInvocation|NestedPromptLevel|Profile|PSBoundParameters|PsCmdlet|PsCulture|PSDebugContext|PSItem|PSCommandPath|PSScriptRoot|PsUICulture|Pwd|Sender|SourceArgs|SourceEventArgs|StackTrace|Switch|This)\\\\b)((?:\\\\.(?:\\\\p{L}|\\\\d|_)+)*\\\\b)?\" }, { \"captures\": { \"0\": { \"name\": \"variable.language.powershell\" }, \"1\": { \"name\": \"punctuation.definition.variable.powershell\" }, \"3\": { \"name\": \"variable.other.member.powershell\" } }, \"comment\": \"Style preference variables as language variables so that they stand out.\", \"match\": \"(\\\\$)(?i:(ConfirmPreference|DebugPreference|ErrorActionPreference|ErrorView|FormatEnumerationLimit|InformationPreference|LogCommandHealthEvent|LogCommandLifecycleEvent|LogEngineHealthEvent|LogEngineLifecycleEvent|LogProviderHealthEvent|LogProviderLifecycleEvent|MaximumAliasCount|MaximumDriveCount|MaximumErrorCount|MaximumFunctionCount|MaximumHistoryCount|MaximumVariableCount|OFS|OutputEncoding|PSCulture|PSDebugContext|PSDefaultParameterValues|PSEmailServer|PSItem|PSModuleAutoLoadingPreference|PSModuleAutoloadingPreference|PSSenderInfo|PSSessionApplicationName|PSSessionConfigurationName|PSSessionOption|ProgressPreference|VerbosePreference|WarningPreference|WhatIfPreference))((?:\\\\.(?:\\\\p{L}|\\\\d|_)+)*\\\\b)?\\\\b\" }, { \"captures\": { \"0\": { \"name\": \"variable.other.readwrite.powershell\" }, \"1\": { \"name\": \"punctuation.definition.variable.powershell\" }, \"2\": { \"name\": \"storage.modifier.scope.powershell\" }, \"4\": { \"name\": \"variable.other.member.powershell\" } }, \"match\": \"(?i:(\\\\$|@)(global|local|private|script|using|workflow):((?:\\\\p{L}|\\\\d|_)+))((?:\\\\.(?:\\\\p{L}|\\\\d|_)+)*\\\\b)?\" }, { \"captures\": { \"0\": { \"name\": \"variable.other.readwrite.powershell\" }, \"1\": { \"name\": \"punctuation.definition.variable.powershell\" }, \"2\": { \"name\": \"punctuation.section.braces.begin.powershell\" }, \"3\": { \"name\": \"storage.modifier.scope.powershell\" }, \"5\": { \"name\": \"punctuation.section.braces.end.powershell\" }, \"6\": { \"name\": \"variable.other.member.powershell\" } }, \"match\": \"(?i:(\\\\$)(\\\\{)(global|local|private|script|using|workflow):([^}]*[^}`])(\\\\}))((?:\\\\.(?:\\\\p{L}|\\\\d|_)+)*\\\\b)?\" }, { \"captures\": { \"0\": { \"name\": \"variable.other.readwrite.powershell\" }, \"1\": { \"name\": \"punctuation.definition.variable.powershell\" }, \"2\": { \"name\": \"support.variable.drive.powershell\" }, \"4\": { \"name\": \"variable.other.member.powershell\" } }, \"match\": \"(?i:(\\\\$|@)((?:\\\\p{L}|\\\\d|_)+:)?((?:\\\\p{L}|\\\\d|_)+))((?:\\\\.(?:\\\\p{L}|\\\\d|_)+)*\\\\b)?\" }, { \"captures\": { \"0\": { \"name\": \"variable.other.readwrite.powershell\" }, \"1\": { \"name\": \"punctuation.definition.variable.powershell\" }, \"2\": { \"name\": \"punctuation.section.braces.begin.powershell\" }, \"3\": { \"name\": \"support.variable.drive.powershell\" }, \"5\": { \"name\": \"punctuation.section.braces.end.powershell\" }, \"6\": { \"name\": \"variable.other.member.powershell\" } }, \"match\": \"(?i:(\\\\$)(\\\\{)((?:\\\\p{L}|\\\\d|_)+:)?([^}]*[^}`])(\\\\}))((?:\\\\.(?:\\\\p{L}|\\\\d|_)+)*\\\\b)?\" }] }, \"variableNoProperty\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"constant.language.powershell\" }, \"1\": { \"name\": \"punctuation.definition.variable.powershell\" } }, \"comment\": \"These are special constants.\", \"match\": \"(\\\\$)(?i:(False|Null|True))\\\\b\" }, { \"captures\": { \"0\": { \"name\": \"support.constant.variable.powershell\" }, \"1\": { \"name\": \"punctuation.definition.variable.powershell\" }, \"3\": { \"name\": \"variable.other.member.powershell\" } }, \"comment\": \"These are the other built-in constants.\", \"match\": \"(\\\\$)(?i:(Error|ExecutionContext|Host|Home|PID|PsHome|PsVersionTable|ShellID))\\\\b\" }, { \"captures\": { \"0\": { \"name\": \"support.variable.automatic.powershell\" }, \"1\": { \"name\": \"punctuation.definition.variable.powershell\" }, \"3\": { \"name\": \"variable.other.member.powershell\" } }, \"comment\": \"Automatic variables are not constants, but they are read-only...\", \"match\": \"(\\\\$)((?:[$^?])|(?i:_|Args|ConsoleFileName|Event|EventArgs|EventSubscriber|ForEach|Input|LastExitCode|Matches|MyInvocation|NestedPromptLevel|Profile|PSBoundParameters|PsCmdlet|PsCulture|PSDebugContext|PSItem|PSCommandPath|PSScriptRoot|PsUICulture|Pwd|Sender|SourceArgs|SourceEventArgs|StackTrace|Switch|This)\\\\b)\" }, { \"captures\": { \"0\": { \"name\": \"variable.language.powershell\" }, \"1\": { \"name\": \"punctuation.definition.variable.powershell\" }, \"3\": { \"name\": \"variable.other.member.powershell\" } }, \"comment\": \"Style preference variables as language variables so that they stand out.\", \"match\": \"(\\\\$)(?i:(ConfirmPreference|DebugPreference|ErrorActionPreference|ErrorView|FormatEnumerationLimit|InformationPreference|LogCommandHealthEvent|LogCommandLifecycleEvent|LogEngineHealthEvent|LogEngineLifecycleEvent|LogProviderHealthEvent|LogProviderLifecycleEvent|MaximumAliasCount|MaximumDriveCount|MaximumErrorCount|MaximumFunctionCount|MaximumHistoryCount|MaximumVariableCount|OFS|OutputEncoding|PSCulture|PSDebugContext|PSDefaultParameterValues|PSEmailServer|PSItem|PSModuleAutoLoadingPreference|PSModuleAutoloadingPreference|PSSenderInfo|PSSessionApplicationName|PSSessionConfigurationName|PSSessionOption|ProgressPreference|VerbosePreference|WarningPreference|WhatIfPreference))\\\\b\" }, { \"captures\": { \"0\": { \"name\": \"variable.other.readwrite.powershell\" }, \"1\": { \"name\": \"punctuation.definition.variable.powershell\" }, \"2\": { \"name\": \"storage.modifier.scope.powershell\" }, \"4\": { \"name\": \"variable.other.member.powershell\" } }, \"match\": \"(?i:(\\\\$)(global|local|private|script|using|workflow):((?:\\\\p{L}|\\\\d|_)+))\" }, { \"captures\": { \"0\": { \"name\": \"variable.other.readwrite.powershell\" }, \"1\": { \"name\": \"punctuation.definition.variable.powershell\" }, \"2\": { \"name\": \"storage.modifier.scope.powershell\" }, \"4\": { \"name\": \"keyword.other.powershell\" }, \"5\": { \"name\": \"variable.other.member.powershell\" } }, \"match\": \"(?i:(\\\\$)(\\\\{)(global|local|private|script|using|workflow):([^}]*[^}`])(\\\\}))\" }, { \"captures\": { \"0\": { \"name\": \"variable.other.readwrite.powershell\" }, \"1\": { \"name\": \"punctuation.definition.variable.powershell\" }, \"2\": { \"name\": \"support.variable.drive.powershell\" }, \"4\": { \"name\": \"variable.other.member.powershell\" } }, \"match\": \"(?i:(\\\\$)((?:\\\\p{L}|\\\\d|_)+:)?((?:\\\\p{L}|\\\\d|_)+))\" }, { \"captures\": { \"0\": { \"name\": \"variable.other.readwrite.powershell\" }, \"1\": { \"name\": \"punctuation.definition.variable.powershell\" }, \"2\": { \"name\": \"punctuation.section.braces.begin\" }, \"3\": { \"name\": \"support.variable.drive.powershell\" }, \"5\": { \"name\": \"punctuation.section.braces.end\" } }, \"match\": \"(?i:(\\\\$)(\\\\{)((?:\\\\p{L}|\\\\d|_)+:)?([^}]*[^}`])(\\\\}))\" }] } }, \"scopeName\": \"source.powershell\", \"aliases\": [\"ps\", \"ps1\"] });\nvar powershell = [\n  lang\n];\n\nexport { powershell as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,cAAc,QAAQ,cAAc,YAAY,CAAC,EAAE,SAAS,MAAM,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wDAAwD,EAAE,GAAG,OAAO,MAAM,eAAe,EAAE,KAAK,EAAE,QAAQ,sDAAsD,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,SAAS,4CAA4C,QAAQ,0CAA0C,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,4EAA4E,WAAW,6BAA6B,GAAG,EAAE,uBAAuB,MAAM,SAAS,0BAA0B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,0BAA0B,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,QAAQ,mCAAmC,YAAY,CAAC,EAAE,SAAS,6BAA6B,QAAQ,uCAAuC,CAAC,EAAE,GAAG,EAAE,SAAS,kCAAkC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,4BAA4B,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,QAAQ,2CAA2C,YAAY,CAAC,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,6BAA6B,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,SAAS,kCAAkC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,4BAA4B,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,QAAQ,0CAA0C,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,SAAS,YAAY,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,0CAA0C,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,gBAAgB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,kDAAkD,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,uCAAuC,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,+CAA+C,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,0DAA0D,QAAQ,8BAA8B,GAAG,EAAE,SAAS,gPAAgP,QAAQ,6BAA6B,GAAG,EAAE,SAAS,sEAAsE,QAAQ,6BAA6B,GAAG,EAAE,SAAS,wBAAwB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,WAAW,yDAAyD,OAAO,KAAK,YAAY,CAAC,EAAE,SAAS,MAAM,QAAQ,6BAA6B,CAAC,EAAE,GAAG,EAAE,WAAW,yHAAyH,SAAS,uCAAuC,QAAQ,8BAA8B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0BAA0B,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,WAAW,kGAAkG,SAAS,gEAAgE,GAAG,EAAE,SAAS,kCAAkC,QAAQ,yCAAyC,GAAG,EAAE,SAAS,8FAA8F,QAAQ,yCAAyC,GAAG,EAAE,SAAS,wCAAwC,QAAQ,oCAAoC,GAAG,EAAE,SAAS,4CAA4C,QAAQ,sCAAsC,GAAG,EAAE,SAAS,sDAAsD,QAAQ,sCAAsC,GAAG,EAAE,SAAS,6BAA6B,QAAQ,4CAA4C,GAAG,EAAE,SAAS,qBAAqB,QAAQ,yCAAyC,GAAG,EAAE,SAAS,iBAAiB,QAAQ,8CAA8C,GAAG,EAAE,SAAS,8BAA8B,QAAQ,oCAAoC,GAAG,EAAE,WAAW,uEAAuE,SAAS,uCAAuC,QAAQ,oCAAoC,CAAC,GAAG,cAAc,EAAE,qBAAqB,EAAE,SAAS,4BAA4B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,KAAK,QAAQ,4BAA4B,YAAY,CAAC,EAAE,SAAS,kFAAkF,QAAQ,2BAA2B,GAAG,EAAE,SAAS,qCAAqC,QAAQ,gCAAgC,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,SAAS,8EAA8E,GAAG,aAAa,EAAE,SAAS,gQAAgQ,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,SAAS,8PAA8P,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,WAAW,sBAAsB,SAAS,qvBAAqvB,QAAQ,8BAA8B,GAAG,EAAE,WAAW,uCAAuC,SAAS,sCAAsC,QAAQ,8BAA8B,GAAG,EAAE,WAAW,uCAAuC,SAAS,oCAAoC,QAAQ,8BAA8B,GAAG,EAAE,WAAW,uCAAuC,SAAS,mCAAmC,QAAQ,8BAA8B,GAAG,EAAE,WAAW,uCAAuC,SAAS,kCAAkC,QAAQ,8BAA8B,CAAC,EAAE,GAAG,uBAAuB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,WAAW,4FAA4F,SAAS,qHAAqH,QAAQ,4CAA4C,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,WAAW,uJAAuJ,SAAS,uHAAuH,QAAQ,4CAA4C,CAAC,EAAE,GAAG,eAAe,EAAE,SAAS,sBAAsB,YAAY,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,SAAS,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,sBAAsB,EAAE,uBAAuB,MAAM,SAAS,0BAA0B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,0BAA0B,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,QAAQ,mCAAmC,YAAY,CAAC,EAAE,SAAS,qDAAqD,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,6BAA6B,GAAG,EAAE,SAAS,6BAA6B,QAAQ,uCAAuC,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,SAAS,UAAU,QAAQ,2BAA2B,CAAC,EAAE,GAAG,6BAA6B,EAAE,YAAY,CAAC,EAAE,SAAS,wCAAwC,QAAQ,uCAAuC,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,YAAY,EAAE,SAAS,0HAA0H,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,0BAA0B,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,eAAe,YAAY,CAAC,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,YAAY,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iDAAiD,GAAG,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,SAAS,8DAA8D,QAAQ,uCAAuC,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,oBAAoB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,iDAAiD,GAAG,KAAK,EAAE,QAAQ,6DAA6D,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,6DAA6D,EAAE,GAAG,eAAe,0CAA0C,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,2DAA2D,EAAE,GAAG,QAAQ,yCAAyC,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,SAAS,8FAA8F,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,SAAS,6FAA6F,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,SAAS,uFAAuF,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,SAAS,+EAA+E,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,SAAS,kFAAkF,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,SAAS,iEAAiE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,SAAS,sFAAsF,CAAC,EAAE,GAAG,eAAe,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,QAAQ,+BAA+B,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,8CAA8C,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,QAAQ,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,sCAAsC,QAAQ,0BAA0B,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,iBAAiB,EAAE,WAAW,oCAAoC,YAAY,CAAC,EAAE,SAAS,qDAAqD,QAAQ,uCAAuC,GAAG,EAAE,SAAS,8BAA8B,QAAQ,sCAAsC,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,gCAAgC,SAAS,iCAAiC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,WAAW,2CAA2C,SAAS,mHAAmH,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,WAAW,6JAA6J,SAAS,0VAA0V,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,WAAW,4EAA4E,SAAS,+sBAA+sB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,8GAA8G,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,+GAA+G,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,sFAAsF,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,uFAAuF,CAAC,EAAE,GAAG,sBAAsB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,WAAW,gCAAgC,SAAS,iCAAiC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,WAAW,2CAA2C,SAAS,oFAAoF,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,WAAW,oEAAoE,SAAS,2TAA2T,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,WAAW,4EAA4E,SAAS,grBAAgrB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,6EAA6E,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,gFAAgF,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,qDAAqD,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,SAAS,wDAAwD,CAAC,EAAE,EAAE,GAAG,aAAa,qBAAqB,WAAW,CAAC,MAAM,KAAK,EAAE,CAAC;AACxrtB,IAAI,aAAa;AAAA,EACf;AACF;", "names": []}