{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/shaderlab.mjs"], "sourcesContent": ["import hlsl from './hlsl.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"ShaderLab\", \"name\": \"shaderlab\", \"patterns\": [{ \"begin\": \"//\", \"end\": \"$\", \"name\": \"comment.line.double-slash.shaderlab\" }, { \"match\": \"\\\\b(?i:Range|Float|Int|Color|Vector|2D|3D|Cube|Any)\\\\b\", \"name\": \"support.type.basic.shaderlab\" }, { \"include\": \"#numbers\" }, { \"match\": \"\\\\b(?i:Shader|Properties|SubShader|Pass|Category)\\\\b\", \"name\": \"storage.type.structure.shaderlab\" }, { \"match\": \"\\\\b(?i:Name|Tags|Fallback|CustomEditor|Cull|ZWrite|ZTest|Offset|Blend|BlendOp|ColorMask|AlphaToMask|LOD|Lighting|Stencil|Ref|ReadMask|WriteMask|Comp|CompBack|CompFront|Fail|ZFail|UsePass|GrabPass|Dependency|Material|Diffuse|Ambient|Shininess|Specular|Emission|Fog|Mode|Density|SeparateSpecular|SetTexture|Combine|ConstantColor|Matrix|AlphaTest|ColorMaterial|BindChannels|Bind)\\\\b\", \"name\": \"support.type.propertyname.shaderlab\" }, { \"match\": \"\\\\b(?i:Back|Front|On|Off|[RGBA]{1,3}|AmbientAndDiffuse|Emission)\\\\b\", \"name\": \"support.constant.property-value.shaderlab\" }, { \"match\": \"\\\\b(?i:Less|Greater|LEqual|GEqual|Equal|NotEqual|Always|Never)\\\\b\", \"name\": \"support.constant.property-value.comparisonfunction.shaderlab\" }, { \"match\": \"\\\\b(?i:Keep|Zero|Replace|IncrSat|DecrSat|Invert|IncrWrap|DecrWrap)\\\\b\", \"name\": \"support.constant.property-value.stenciloperation.shaderlab\" }, { \"match\": \"\\\\b(?i:Previous|Primary|Texture|Constant|Lerp|Double|Quad|Alpha)\\\\b\", \"name\": \"support.constant.property-value.texturecombiners.shaderlab\" }, { \"match\": \"\\\\b(?i:Global|Linear|Exp2|Exp)\\\\b\", \"name\": \"support.constant.property-value.fog.shaderlab\" }, { \"match\": \"\\\\b(?i:Vertex|Normal|Tangent|TexCoord0|TexCoord1)\\\\b\", \"name\": \"support.constant.property-value.bindchannels.shaderlab\" }, { \"match\": \"\\\\b(?i:Add|Sub|RevSub|Min|Max|LogicalClear|LogicalSet|LogicalCopyInverted|LogicalCopy|LogicalNoop|LogicalInvert|LogicalAnd|LogicalNand|LogicalOr|LogicalNor|LogicalXor|LogicalEquiv|LogicalAndReverse|LogicalAndInverted|LogicalOrReverse|LogicalOrInverted)\\\\b\", \"name\": \"support.constant.property-value.blendoperations.shaderlab\" }, { \"match\": \"\\\\b(?i:One|Zero|SrcColor|SrcAlpha|DstColor|DstAlpha|OneMinusSrcColor|OneMinusSrcAlpha|OneMinusDstColor|OneMinusDstAlpha)\\\\b\", \"name\": \"support.constant.property-value.blendfactors.shaderlab\" }, { \"match\": '\\\\[([a-zA-Z_][a-zA-Z0-9_]*)\\\\](?!\\\\s*[a-zA-Z_][a-zA-Z0-9_]*\\\\s*\\\\(\")', \"name\": \"support.variable.reference.shaderlab\" }, { \"begin\": \"(\\\\[)\", \"end\": \"(\\\\])\", \"name\": \"meta.attribute.shaderlab\", \"patterns\": [{ \"match\": \"\\\\G([a-zA-Z]+)\\\\b\", \"name\": \"support.type.attributename.shaderlab\" }, { \"include\": \"#numbers\" }] }, { \"match\": \"\\\\b([a-zA-Z_][a-zA-Z0-9_]*)\\\\s*\\\\(\", \"name\": \"support.variable.declaration.shaderlab\" }, { \"begin\": \"\\\\b(CGPROGRAM|CGINCLUDE)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other\" } }, \"end\": \"\\\\b(ENDCG)\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"keyword.other\" } }, \"name\": \"meta.cgblock\", \"patterns\": [{ \"include\": \"#hlsl-embedded\" }] }, { \"begin\": \"\\\\b(HLSLPROGRAM|HLSLINCLUDE)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other\" } }, \"end\": \"\\\\b(ENDHLSL)\\\\b\", \"endCaptures\": { \"1\": { \"name\": \"keyword.other\" } }, \"name\": \"meta.hlslblock\", \"patterns\": [{ \"include\": \"#hlsl-embedded\" }] }, { \"begin\": '\"', \"end\": '\"', \"name\": \"string.quoted.double.shaderlab\" }], \"repository\": { \"hlsl-embedded\": { \"patterns\": [{ \"include\": \"source.hlsl\" }, { \"match\": \"\\\\b(fixed([1-4](x[1-4])?)?)\\\\b\", \"name\": \"storage.type.basic.shaderlab\" }, { \"match\": \"\\\\b(UNITY_MATRIX_MVP|UNITY_MATRIX_MV|UNITY_MATRIX_M|UNITY_MATRIX_V|UNITY_MATRIX_P|UNITY_MATRIX_VP|UNITY_MATRIX_T_MV|UNITY_MATRIX_I_V|UNITY_MATRIX_IT_MV|_Object2World|_World2Object|unity_ObjectToWorld|unity_WorldToObject)\\\\b\", \"name\": \"support.variable.transformations.shaderlab\" }, { \"match\": \"\\\\b(_WorldSpaceCameraPos|_ProjectionParams|_ScreenParams|_ZBufferParams|unity_OrthoParams|unity_CameraProjection|unity_CameraInvProjection|unity_CameraWorldClipPlanes)\\\\b\", \"name\": \"support.variable.camera.shaderlab\" }, { \"match\": \"\\\\b(_Time|_SinTime|_CosTime|unity_DeltaTime)\\\\b\", \"name\": \"support.variable.time.shaderlab\" }, { \"match\": \"\\\\b(_LightColor0|_WorldSpaceLightPos0|_LightMatrix0|unity_4LightPosX0|unity_4LightPosY0|unity_4LightPosZ0|unity_4LightAtten0|unity_LightColor|_LightColor|unity_LightPosition|unity_LightAtten|unity_SpotDirection)\\\\b\", \"name\": \"support.variable.lighting.shaderlab\" }, { \"match\": \"\\\\b(unity_AmbientSky|unity_AmbientEquator|unity_AmbientGround|UNITY_LIGHTMODEL_AMBIENT|unity_FogColor|unity_FogParams)\\\\b\", \"name\": \"support.variable.fog.shaderlab\" }, { \"match\": \"\\\\b(unity_LODFade)\\\\b\", \"name\": \"support.variable.various.shaderlab\" }, { \"match\": \"\\\\b(SHADER_API_D3D9|SHADER_API_D3D11|SHADER_API_GLCORE|SHADER_API_OPENGL|SHADER_API_GLES|SHADER_API_GLES3|SHADER_API_METAL|SHADER_API_D3D11_9X|SHADER_API_PSSL|SHADER_API_XBOXONE|SHADER_API_PSP2|SHADER_API_WIIU|SHADER_API_MOBILE|SHADER_API_GLSL)\\\\b\", \"name\": \"support.variable.preprocessor.targetplatform.shaderlab\" }, { \"match\": \"\\\\b(SHADER_TARGET)\\\\b\", \"name\": \"support.variable.preprocessor.targetmodel.shaderlab\" }, { \"match\": \"\\\\b(UNITY_VERSION)\\\\b\", \"name\": \"support.variable.preprocessor.unityversion.shaderlab\" }, { \"match\": \"\\\\b(UNITY_BRANCH|UNITY_FLATTEN|UNITY_NO_SCREENSPACE_SHADOWS|UNITY_NO_LINEAR_COLORSPACE|UNITY_NO_RGBM|UNITY_NO_DXT5nm|UNITY_FRAMEBUFFER_FETCH_AVAILABLE|UNITY_USE_RGBA_FOR_POINT_SHADOWS|UNITY_ATTEN_CHANNEL|UNITY_HALF_TEXEL_OFFSET|UNITY_UV_STARTS_AT_TOP|UNITY_MIGHT_NOT_HAVE_DEPTH_Texture|UNITY_NEAR_CLIP_VALUE|UNITY_VPOS_TYPE|UNITY_CAN_COMPILE_TESSELLATION|UNITY_COMPILER_HLSL|UNITY_COMPILER_HLSL2GLSL|UNITY_COMPILER_CG|UNITY_REVERSED_Z)\\\\b\", \"name\": \"support.variable.preprocessor.platformdifference.shaderlab\" }, { \"match\": \"\\\\b(UNITY_PASS_FORWARDBASE|UNITY_PASS_FORWARDADD|UNITY_PASS_DEFERRED|UNITY_PASS_SHADOWCASTER|UNITY_PASS_PREPASSBASE|UNITY_PASS_PREPASSFINAL)\\\\b\", \"name\": \"support.variable.preprocessor.texture2D.shaderlab\" }, { \"match\": \"\\\\b(appdata_base|appdata_tan|appdata_full|appdata_img)\\\\b\", \"name\": \"support.class.structures.shaderlab\" }, { \"match\": \"\\\\b(SurfaceOutputStandardSpecular|SurfaceOutputStandard|SurfaceOutput|Input)\\\\b\", \"name\": \"support.class.surface.shaderlab\" }] }, \"numbers\": { \"patterns\": [{ \"match\": \"\\\\b([0-9]+\\\\.?[0-9]*)\\\\b\", \"name\": \"constant.numeric.shaderlab\" }] } }, \"scopeName\": \"source.shaderlab\", \"embeddedLangs\": [\"hlsl\"], \"aliases\": [\"shader\"] });\nvar shaderlab = [\n  ...hlsl,\n  lang\n];\n\nexport { shaderlab as default };\n"], "mappings": ";;;;;;AAEA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,aAAa,QAAQ,aAAa,YAAY,CAAC,EAAE,SAAS,MAAM,OAAO,KAAK,QAAQ,sCAAsC,GAAG,EAAE,SAAS,0DAA0D,QAAQ,+BAA+B,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,wDAAwD,QAAQ,mCAAmC,GAAG,EAAE,SAAS,+XAA+X,QAAQ,sCAAsC,GAAG,EAAE,SAAS,uEAAuE,QAAQ,4CAA4C,GAAG,EAAE,SAAS,qEAAqE,QAAQ,+DAA+D,GAAG,EAAE,SAAS,yEAAyE,QAAQ,6DAA6D,GAAG,EAAE,SAAS,uEAAuE,QAAQ,6DAA6D,GAAG,EAAE,SAAS,qCAAqC,QAAQ,gDAAgD,GAAG,EAAE,SAAS,wDAAwD,QAAQ,yDAAyD,GAAG,EAAE,SAAS,mQAAmQ,QAAQ,4DAA4D,GAAG,EAAE,SAAS,+HAA+H,QAAQ,yDAAyD,GAAG,EAAE,SAAS,wEAAwE,QAAQ,uCAAuC,GAAG,EAAE,SAAS,SAAS,OAAO,SAAS,QAAQ,4BAA4B,YAAY,CAAC,EAAE,SAAS,qBAAqB,QAAQ,uCAAuC,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,EAAE,SAAS,sCAAsC,QAAQ,yCAAyC,GAAG,EAAE,SAAS,+BAA+B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gBAAgB,EAAE,GAAG,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,gBAAgB,EAAE,GAAG,QAAQ,gBAAgB,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,SAAS,mCAAmC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gBAAgB,EAAE,GAAG,OAAO,mBAAmB,eAAe,EAAE,KAAK,EAAE,QAAQ,gBAAgB,EAAE,GAAG,QAAQ,kBAAkB,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,OAAO,KAAK,QAAQ,iCAAiC,CAAC,GAAG,cAAc,EAAE,iBAAiB,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,SAAS,kCAAkC,QAAQ,+BAA+B,GAAG,EAAE,SAAS,mOAAmO,QAAQ,6CAA6C,GAAG,EAAE,SAAS,8KAA8K,QAAQ,oCAAoC,GAAG,EAAE,SAAS,mDAAmD,QAAQ,kCAAkC,GAAG,EAAE,SAAS,0NAA0N,QAAQ,sCAAsC,GAAG,EAAE,SAAS,6HAA6H,QAAQ,iCAAiC,GAAG,EAAE,SAAS,yBAAyB,QAAQ,qCAAqC,GAAG,EAAE,SAAS,2PAA2P,QAAQ,yDAAyD,GAAG,EAAE,SAAS,yBAAyB,QAAQ,sDAAsD,GAAG,EAAE,SAAS,yBAAyB,QAAQ,uDAAuD,GAAG,EAAE,SAAS,0bAA0b,QAAQ,6DAA6D,GAAG,EAAE,SAAS,mJAAmJ,QAAQ,oDAAoD,GAAG,EAAE,SAAS,6DAA6D,QAAQ,qCAAqC,GAAG,EAAE,SAAS,mFAAmF,QAAQ,kCAAkC,CAAC,EAAE,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,SAAS,4BAA4B,QAAQ,6BAA6B,CAAC,EAAE,EAAE,GAAG,aAAa,oBAAoB,iBAAiB,CAAC,MAAM,GAAG,WAAW,CAAC,QAAQ,EAAE,CAAC;AAC7rM,IAAI,YAAY;AAAA,EACd,GAAG;AAAA,EACH;AACF;", "names": []}