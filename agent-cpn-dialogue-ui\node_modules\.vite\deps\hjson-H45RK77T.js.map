{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/hjson.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Hjson\", \"fileTypes\": [\"hjson\"], \"foldingStartMarker\": \"(?x:     # turn on extended mode\\n    ^    # a line beginning with\\n    \\\\s*    # some optional space\\n    [{\\\\[]  # the start of an object or array\\n    (?!    # but not followed by\\n    .*   # whatever\\n    [}\\\\]]  # and the close of an object or array\\n    ,?   # an optional comma\\n    \\\\s*  # some optional space\\n    $    # at the end of the line\\n    )\\n    |    # ...or...\\n    [{\\\\[]  # the start of an object or array\\n    \\\\s*    # some optional space\\n    $    # at the end of the line\\n  )\", \"foldingStopMarker\": \"(?x:   # turn on extended mode\\n    ^    # a line beginning with\\n    \\\\s*  # some optional space\\n    [}\\\\]]  # and the close of an object or array\\n  )\", \"name\": \"hjson\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#value\" }, { \"match\": \"[^\\\\s]\", \"name\": \"invalid.illegal.excess-characters.hjson\" }], \"repository\": { \"array\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.begin.hjson\" } }, \"end\": \"(\\\\])(?:\\\\s*([^,\\\\s]+))?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.array.end.hjson\" }, \"2\": { \"name\": \"invalid.illegal.value.hjson\" } }, \"name\": \"meta.structure.array.hjson\", \"patterns\": [{ \"include\": \"#arrayContent\" }] }, \"arrayArray\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.begin.hjson\" } }, \"end\": \"(\\\\])(?:\\\\s*([^,\\\\s\\\\]]+))?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.array.end.hjson\" }, \"2\": { \"name\": \"invalid.illegal.value.hjson\" } }, \"name\": \"meta.structure.array.hjson\", \"patterns\": [{ \"include\": \"#arrayContent\" }] }, \"arrayConstant\": { \"captures\": { \"1\": { \"name\": \"constant.language.hjson\" }, \"2\": { \"name\": \"punctuation.separator.array.after-const.hjson\" } }, \"match\": \"\\\\b(true|false|null)(?:[\\\\t ]*(?=,)|[\\\\t ]*(?:(,)[\\\\t ]*)?(?=$|#|/\\\\*|//|\\\\]))\" }, \"arrayContent\": { \"name\": \"meta.structure.array.hjson\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#arrayValue\" }, { \"begin\": \"(?<=\\\\[)|,\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.dictionary.pair.hjson\" } }, \"end\": \"(?=[^\\\\s,/#])|(?=/[^/*])\", \"patterns\": [{ \"include\": \"#comments\" }, { \"match\": \",\", \"name\": \"invalid.illegal.extra-comma.hjson\" }] }, { \"match\": \",\", \"name\": \"punctuation.separator.array.hjson\" }, { \"match\": \"[^\\\\s\\\\]]\", \"name\": \"invalid.illegal.expected-array-separator.hjson\" }] }, \"arrayJstring\": { \"patterns\": [{ \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.hjson\" } }, \"end\": '(\")(?:\\\\s*((?:[^,\\\\s\\\\]#/]|/[^/*])+))?', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.hjson\" }, \"2\": { \"name\": \"invalid.illegal.value.hjson\" } }, \"name\": \"string.quoted.double.hjson\", \"patterns\": [{ \"include\": \"#jstringDoubleContent\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.hjson\" } }, \"end\": \"(')(?:\\\\s*((?:[^,\\\\s\\\\]#/]|/[^/*])+))?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.hjson\" }, \"2\": { \"name\": \"invalid.illegal.value.hjson\" } }, \"name\": \"string.quoted.single.hjson\", \"patterns\": [{ \"include\": \"#jstringSingleContent\" }] }] }, \"arrayMstring\": { \"begin\": \"'''\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.hjson\" } }, \"end\": \"(''')(?:\\\\s*((?:[^,\\\\s\\\\]#/]|/[^/*])+))?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.hjson\" }, \"2\": { \"name\": \"invalid.illegal.value.hjson\" } }, \"name\": \"string.quoted.multiline.hjson\" }, \"arrayNumber\": { \"captures\": { \"1\": { \"name\": \"constant.numeric.hjson\" }, \"2\": { \"name\": \"punctuation.separator.array.after-num.hjson\" } }, \"match\": \"(-?(?:0|(?:[1-9]\\\\d*))(?:\\\\.\\\\d+)?(?:[eE][+-]?\\\\d+)?)(?:[\\\\t ]*(?=,)|[\\\\t ]*(?:(,)[\\\\t ]*)?(?=$|#|/\\\\*|//|\\\\]))\" }, \"arrayObject\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.dictionary.begin.hjson\" } }, \"end\": \"(\\\\}|(?<=\\\\}))(?:\\\\s*([^,\\\\s\\\\]]+))?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.dictionary.end.hjson\" }, \"2\": { \"name\": \"invalid.illegal.value.hjson\" } }, \"name\": \"meta.structure.dictionary.hjson\", \"patterns\": [{ \"include\": \"#objectContent\" }] }, \"arrayString\": { \"patterns\": [{ \"include\": \"#arrayMstring\" }, { \"include\": \"#arrayJstring\" }, { \"include\": \"#ustring\" }] }, \"arrayValue\": { \"patterns\": [{ \"include\": \"#arrayNumber\" }, { \"include\": \"#arrayConstant\" }, { \"include\": \"#arrayString\" }, { \"include\": \"#arrayObject\" }, { \"include\": \"#arrayArray\" }] }, \"comments\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.hjson\" } }, \"match\": \"^\\\\s*(#).*(?:\\\\n)?\", \"name\": \"comment.line.hash\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.hjson\" } }, \"match\": \"^\\\\s*(//).*(?:\\\\n)?\", \"name\": \"comment.line.double-slash\" }, { \"begin\": \"^\\\\s*/\\\\*\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.hjson\" } }, \"end\": \"\\\\*/(?:\\\\s*\\\\n)?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.hjson\" } }, \"name\": \"comment.block.double-slash\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.hjson\" } }, \"match\": \"(#)[^\\\\n]*\", \"name\": \"comment.line.hash\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.hjson\" } }, \"match\": \"(//)[^\\\\n]*\", \"name\": \"comment.line.double-slash\" }, { \"begin\": \"/\\\\*\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.hjson\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.hjson\" } }, \"name\": \"comment.block.double-slash\" }] }, \"commentsNewline\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.hjson\" } }, \"match\": \"(#).*\\\\n\", \"name\": \"comment.line.hash\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.hjson\" } }, \"match\": \"(//).*\\\\n\", \"name\": \"comment.line.double-slash\" }, { \"begin\": \"/\\\\*\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.hjson\" } }, \"end\": \"\\\\*/(\\\\s*\\\\n)?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.hjson\" } }, \"name\": \"comment.block.double-slash\" }] }, \"constant\": { \"captures\": { \"1\": { \"name\": \"constant.language.hjson\" } }, \"match\": \"\\\\b(true|false|null)[\\\\t ]*(?=$|#|/\\\\*|//|\\\\])\" }, \"jstring\": { \"patterns\": [{ \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.hjson\" } }, \"end\": '(\")(?:\\\\s*((?:[^\\\\s#/]|/[^/*]).*)$)?', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.hjson\" }, \"2\": { \"name\": \"invalid.illegal.value.hjson\" } }, \"name\": \"string.quoted.double.hjson\", \"patterns\": [{ \"include\": \"#jstringDoubleContent\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.hjson\" } }, \"end\": \"(')(?:\\\\s*((?:[^\\\\s#/]|/[^/*]).*)$)?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.hjson\" }, \"2\": { \"name\": \"invalid.illegal.value.hjson\" } }, \"name\": \"string.quoted.single.hjson\", \"patterns\": [{ \"include\": \"#jstringSingleContent\" }] }] }, \"jstringDoubleContent\": { \"patterns\": [{ \"match\": `\\\\\\\\(?:[\"'\\\\\\\\\\\\/bfnrt]|u[0-9a-fA-F]{4})`, \"name\": \"constant.character.escape.hjson\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"invalid.illegal.unrecognized-string-escape.hjson\" }, { \"match\": '[^\"]*[^\\\\n\\\\r\"\\\\\\\\]$', \"name\": \"invalid.illegal.string.hjson\" }] }, \"jstringSingleContent\": { \"patterns\": [{ \"match\": `\\\\\\\\(?:[\"'\\\\\\\\\\\\/bfnrt]|u[0-9a-fA-F]{4})`, \"name\": \"constant.character.escape.hjson\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"invalid.illegal.unrecognized-string-escape.hjson\" }, { \"match\": \"[^']*[^\\\\n\\\\r'\\\\\\\\]$\", \"name\": \"invalid.illegal.string.hjson\" }] }, \"key\": { \"begin\": `(?x:\n(\n(?:[^:,\\\\{\\\\}\\\\[\\\\]\\\\s\"'][^:,\\\\{\\\\}\\\\[\\\\]\\\\s]*) |\n(?:\n'\n(?:\n[^\\\\\\\\'] |\n(\\\\\\\\(?:[\"'\\\\\\\\\\\\/bfnrt]|u[0-9a-fA-F]{4})) |\n(\\\\\\\\.)\n)*\n'\n) |\n(?:\n\"\n(?:\n[^\\\\\\\\\"] |\n(\\\\\\\\(?:[\"'\\\\\\\\\\\\/bfnrt]|u[0-9a-fA-F]{4})) |\n(\\\\\\\\.)\n)*\n\"\n)\n)\n\\\\s*\n(?!\\\\n)\n([,\\\\{\\\\}\\\\[\\\\]]*)\n)`, \"beginCaptures\": { \"0\": { \"name\": \"meta.structure.key-value.begin.hjson\" }, \"1\": { \"name\": \"support.type.property-name.hjson\" }, \"2\": { \"name\": \"constant.character.escape.hjson\" }, \"3\": { \"name\": \"invalid.illegal.unrecognized-string-escape.hjson\" }, \"4\": { \"name\": \"constant.character.escape.hjson\" }, \"5\": { \"name\": \"invalid.illegal.unrecognized-string-escape.hjson\" }, \"6\": { \"name\": \"invalid.illegal.separator.hjson\" }, \"7\": { \"name\": \"invalid.illegal.property-name.hjson\" } }, \"end\": \"(?<!^|:)\\\\s*\\\\n|(?=})|(,)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.separator.dictionary.pair.hjson\" } }, \"patterns\": [{ \"include\": \"#commentsNewline\" }, { \"include\": \"#keyValue\" }, { \"match\": \"[^\\\\s]\", \"name\": \"invalid.illegal.object-property.hjson\" }] }, \"keyValue\": { \"begin\": \"(?x:\\n\\\\s*\\n(:)\\n\\\\s*\\n([,\\\\}\\\\]]*)\\n)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.dictionary.key-value.hjson\" }, \"2\": { \"name\": \"invalid.illegal.object-property.hjson\" } }, \"end\": \"(?<!^)\\\\s*(?=\\\\n)|(?=[},])\", \"name\": \"meta.structure.key-value.hjson\", \"patterns\": [{ \"include\": \"#comments\" }, { \"match\": \"^\\\\s+\" }, { \"include\": \"#objectValue\" }, { \"captures\": { \"1\": { \"name\": \"invalid.illegal.object-property.closing-bracket.hjson\" } }, \"match\": \"^\\\\s*(\\\\})\" }, { \"match\": \"[^\\\\s]\", \"name\": \"invalid.illegal.object-property.hjson\" }] }, \"mstring\": { \"begin\": \"'''\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.hjson\" } }, \"end\": \"(''')(?:\\\\s*((?:[^\\\\s#/]|/[^/*]).*)$)?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.hjson\" }, \"2\": { \"name\": \"invalid.illegal.value.hjson\" } }, \"name\": \"string.quoted.multiline.hjson\" }, \"number\": { \"captures\": { \"1\": { \"name\": \"constant.numeric.hjson\" } }, \"match\": \"(-?(?:0|(?:[1-9]\\\\d*))(?:\\\\.\\\\d+)?(?:[eE][+-]?\\\\d+)?)[\\\\t ]*(?=$|#|/\\\\*|//|\\\\])\" }, \"object\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.dictionary.begin.hjson\" } }, \"end\": \"(\\\\}|(?<=\\\\}))(?:\\\\s*([^,\\\\s]+))?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.dictionary.end.hjson\" }, \"2\": { \"name\": \"invalid.illegal.value.hjson\" } }, \"name\": \"meta.structure.dictionary.hjson\", \"patterns\": [{ \"include\": \"#objectContent\" }] }, \"objectArray\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.array.begin.hjson\" } }, \"end\": \"(\\\\])(?:\\\\s*([^,\\\\s\\\\}]+))?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.array.end.hjson\" }, \"2\": { \"name\": \"invalid.illegal.value.hjson\" } }, \"name\": \"meta.structure.array.hjson\", \"patterns\": [{ \"include\": \"#arrayContent\" }] }, \"objectConstant\": { \"captures\": { \"1\": { \"name\": \"constant.language.hjson\" }, \"2\": { \"name\": \"punctuation.separator.dictionary.pair.after-const.hjson\" } }, \"match\": \"\\\\b(true|false|null)(?:[\\\\t ]*(?=,)|[\\\\t ]*(?:(,)[\\\\t ]*)?(?=$|#|/\\\\*|//|\\\\}))\" }, \"objectContent\": { \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#key\" }, { \"match\": \":[.|\\\\s]\", \"name\": \"invalid.illegal.object-property.hjson\" }, { \"begin\": \"(?<=\\\\{|,)|,\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.separator.dictionary.pair.hjson\" } }, \"end\": \"(?=[^\\\\s,/#])|(?=/[^/*])\", \"patterns\": [{ \"include\": \"#comments\" }, { \"match\": \",\", \"name\": \"invalid.illegal.extra-comma.hjson\" }] }, { \"match\": \"[^\\\\s]\", \"name\": \"invalid.illegal.object-property.hjson\" }] }, \"objectJstring\": { \"patterns\": [{ \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.hjson\" } }, \"end\": '(\")(?:\\\\s*((?:[^,\\\\s\\\\}#/]|/[^/*])+))?', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.hjson\" }, \"2\": { \"name\": \"invalid.illegal.value.hjson\" } }, \"name\": \"string.quoted.double.hjson\", \"patterns\": [{ \"include\": \"#jstringDoubleContent\" }] }, { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.hjson\" } }, \"end\": \"(')(?:\\\\s*((?:[^,\\\\s\\\\}#/]|/[^/*])+))?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.hjson\" }, \"2\": { \"name\": \"invalid.illegal.value.hjson\" } }, \"name\": \"string.quoted.single.hjson\", \"patterns\": [{ \"include\": \"#jstringSingleContent\" }] }] }, \"objectMstring\": { \"begin\": \"'''\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.hjson\" } }, \"end\": \"(''')(?:\\\\s*((?:[^,\\\\s\\\\}#/]|/[^/*])+))?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.hjson\" }, \"2\": { \"name\": \"invalid.illegal.value.hjson\" } }, \"name\": \"string.quoted.multiline.hjson\" }, \"objectNumber\": { \"captures\": { \"1\": { \"name\": \"constant.numeric.hjson\" }, \"2\": { \"name\": \"punctuation.separator.dictionary.pair.after-num.hjson\" } }, \"match\": \"(-?(?:0|(?:[1-9]\\\\d*))(?:\\\\.\\\\d+)?(?:[eE][+-]?\\\\d+)?)(?:[\\\\t ]*(?=,)|[\\\\t ]*(?:(,)[\\\\t ]*)?(?=$|#|/\\\\*|//|\\\\}))\" }, \"objectObject\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.dictionary.begin.hjson\" } }, \"end\": \"(\\\\}|(?<=\\\\})\\\\}?)(?:\\\\s*([^,\\\\s}]+))?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.dictionary.end.hjson\" }, \"2\": { \"name\": \"invalid.illegal.value.hjson\" } }, \"name\": \"meta.structure.dictionary.hjson\", \"patterns\": [{ \"include\": \"#objectContent\" }] }, \"objectString\": { \"patterns\": [{ \"include\": \"#objectMstring\" }, { \"include\": \"#objectJstring\" }, { \"include\": \"#ustring\" }] }, \"objectValue\": { \"patterns\": [{ \"include\": \"#objectNumber\" }, { \"include\": \"#objectConstant\" }, { \"include\": \"#objectString\" }, { \"include\": \"#objectObject\" }, { \"include\": \"#objectArray\" }] }, \"string\": { \"patterns\": [{ \"include\": \"#mstring\" }, { \"include\": \"#jstring\" }, { \"include\": \"#ustring\" }] }, \"ustring\": { \"match\": \"([^:,\\\\{\\\\[\\\\}\\\\]\\\\s].*)$\", \"name\": \"string.quoted.none.hjson\" }, \"value\": { \"patterns\": [{ \"include\": \"#number\" }, { \"include\": \"#constant\" }, { \"include\": \"#string\" }, { \"include\": \"#object\" }, { \"include\": \"#array\" }] } }, \"scopeName\": \"source.hjson\" });\nvar hjson = [\n  lang\n];\n\nexport { hjson as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,SAAS,aAAa,CAAC,OAAO,GAAG,sBAAsB,0fAA0f,qBAAqB,6JAA6J,QAAQ,SAAS,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,SAAS,UAAU,QAAQ,0CAA0C,CAAC,GAAG,cAAc,EAAE,SAAS,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,4BAA4B,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,+BAA+B,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0BAA0B,GAAG,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,SAAS,iFAAiF,GAAG,gBAAgB,EAAE,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,OAAO,4BAA4B,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,SAAS,KAAK,QAAQ,oCAAoC,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,QAAQ,oCAAoC,GAAG,EAAE,SAAS,aAAa,QAAQ,iDAAiD,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,0CAA0C,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,0CAA0C,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,wBAAwB,CAAC,EAAE,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,4CAA4C,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,gCAAgC,GAAG,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yBAAyB,GAAG,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,SAAS,kHAAkH,GAAG,eAAe,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,wCAAwC,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,mCAAmC,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,cAAc,EAAE,YAAY,CAAC,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,sBAAsB,QAAQ,oBAAoB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,uBAAuB,QAAQ,4BAA4B,GAAG,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,oBAAoB,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,QAAQ,6BAA6B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,cAAc,QAAQ,oBAAoB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,eAAe,QAAQ,4BAA4B,GAAG,EAAE,SAAS,QAAQ,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,QAAQ,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,QAAQ,6BAA6B,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,YAAY,QAAQ,oBAAoB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,aAAa,QAAQ,4BAA4B,GAAG,EAAE,SAAS,QAAQ,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,kBAAkB,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,QAAQ,6BAA6B,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,SAAS,iDAAiD,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,wCAAwC,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,wCAAwC,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,wBAAwB,CAAC,EAAE,CAAC,EAAE,GAAG,wBAAwB,EAAE,YAAY,CAAC,EAAE,SAAS,4CAA4C,QAAQ,kCAAkC,GAAG,EAAE,SAAS,SAAS,QAAQ,mDAAmD,GAAG,EAAE,SAAS,wBAAwB,QAAQ,+BAA+B,CAAC,EAAE,GAAG,wBAAwB,EAAE,YAAY,CAAC,EAAE,SAAS,4CAA4C,QAAQ,kCAAkC,GAAG,EAAE,SAAS,SAAS,QAAQ,mDAAmD,GAAG,EAAE,SAAS,wBAAwB,QAAQ,+BAA+B,CAAC,EAAE,GAAG,OAAO,EAAE,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAyB38O,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,mDAAmD,GAAG,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,mDAAmD,GAAG,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,6BAA6B,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,SAAS,UAAU,QAAQ,wCAAwC,CAAC,EAAE,GAAG,YAAY,EAAE,SAAS,0CAA0C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mDAAmD,GAAG,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,OAAO,8BAA8B,QAAQ,kCAAkC,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,SAAS,QAAQ,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wDAAwD,EAAE,GAAG,SAAS,aAAa,GAAG,EAAE,SAAS,UAAU,QAAQ,wCAAwC,CAAC,EAAE,GAAG,WAAW,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,0CAA0C,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,gCAAgC,GAAG,UAAU,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,SAAS,kFAAkF,GAAG,UAAU,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,qCAAqC,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,mCAAmC,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,eAAe,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,+BAA+B,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0BAA0B,GAAG,KAAK,EAAE,QAAQ,0DAA0D,EAAE,GAAG,SAAS,iFAAiF,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,OAAO,GAAG,EAAE,SAAS,YAAY,QAAQ,wCAAwC,GAAG,EAAE,SAAS,gBAAgB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,OAAO,4BAA4B,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,SAAS,KAAK,QAAQ,oCAAoC,CAAC,EAAE,GAAG,EAAE,SAAS,UAAU,QAAQ,wCAAwC,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,0CAA0C,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,0CAA0C,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,wBAAwB,CAAC,EAAE,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,4CAA4C,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,gCAAgC,GAAG,gBAAgB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yBAAyB,GAAG,KAAK,EAAE,QAAQ,wDAAwD,EAAE,GAAG,SAAS,kHAAkH,GAAG,gBAAgB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,0CAA0C,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,mCAAmC,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,WAAW,EAAE,SAAS,6BAA6B,QAAQ,2BAA2B,GAAG,SAAS,EAAE,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,SAAS,CAAC,EAAE,EAAE,GAAG,aAAa,eAAe,CAAC;AACzqL,IAAI,QAAQ;AAAA,EACV;AACF;", "names": []}