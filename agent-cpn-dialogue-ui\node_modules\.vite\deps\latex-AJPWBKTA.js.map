{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/latex.mjs"], "sourcesContent": ["import tex from './tex.mjs';\nimport css from './css.mjs';\nimport haskell from './haskell.mjs';\nimport html from './html.mjs';\nimport xml from './xml.mjs';\nimport java from './java.mjs';\nimport lua from './lua.mjs';\nimport julia from './julia.mjs';\nimport ruby from './ruby.mjs';\nimport javascript from './javascript.mjs';\nimport typescript from './typescript.mjs';\nimport python from './python.mjs';\nimport yaml from './yaml.mjs';\nimport rust from './rust.mjs';\nimport scala from './scala.mjs';\nimport gnuplot from './gnuplot.mjs';\nimport './r.mjs';\nimport './c.mjs';\nimport './cpp.mjs';\nimport './glsl.mjs';\nimport './sql.mjs';\nimport './shellscript.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"LaTeX\", \"name\": \"latex\", \"patterns\": [{ \"comment\": \"This scope identifies partially typed commands such as `\\\\tab`. We use this to trigger \\u201CCommand Completion\\u201D only when it makes sense.\", \"match\": \"(?<=\\\\\\\\[\\\\w@]|\\\\\\\\[\\\\w@]{2}|\\\\\\\\[\\\\w@]{3}|\\\\\\\\[\\\\w@]{4}|\\\\\\\\[\\\\w@]{5}|\\\\\\\\[\\\\w@]{6})\\\\s\", \"name\": \"meta.space-after-command.latex\" }, { \"begin\": \"((\\\\\\\\)(?:usepackage|documentclass))\\\\b(?=\\\\[|\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.preamble.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" } }, \"end\": \"(?<=\\\\})\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"name\": \"meta.preamble.latex\", \"patterns\": [{ \"include\": \"#multiline-optional-arg\" }, { \"begin\": \"((?:\\\\G|(?<=\\\\]))\\\\{)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"contentName\": \"support.class.latex\", \"end\": \"(\\\\})\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"patterns\": [{ \"include\": \"$self\" }] }] }, { \"begin\": \"((\\\\\\\\)(?:include|input))(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.include.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"name\": \"meta.include.latex\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"begin\": \"((\\\\\\\\)((?:sub){0,2}section|(?:sub)?paragraph|chapter|part|addpart|addchap|addsec|minisec|frametitle)(?:\\\\*)?)((?:\\\\[[^\\\\[]*?\\\\]){0,2})(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.section.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" }, \"4\": { \"patterns\": [{ \"include\": \"#optional-arg\" }] }, \"5\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"comment\": \"this works OK with all kinds of crazy stuff as long as section is one line\", \"contentName\": \"entity.name.section.latex\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"name\": \"meta.function.section.$3.latex\", \"patterns\": [{ \"include\": \"text.tex#braces\" }, { \"include\": \"$base\" }] }, { \"begin\": \"((?:\\\\s*)\\\\\\\\begin\\\\{songs\\\\}\\\\{.*\\\\})\", \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"contentName\": \"meta.data.environment.songs.latex\", \"end\": \"(\\\\\\\\end\\\\{songs\\\\}(?:\\\\s*\\\\n)?)\", \"name\": \"meta.function.environment.songs.latex\", \"patterns\": [{ \"begin\": \"\\\\\\\\\\\\[\", \"end\": \"\\\\]\", \"name\": \"meta.chord.block.latex support.class.chord.block.environment.latex\", \"patterns\": [{ \"include\": \"$base\" }] }, { \"match\": \"\\\\^\", \"name\": \"meta.chord.block.latex support.class.chord.block.environment.latex\" }, { \"include\": \"$self\" }] }, { \"begin\": \"(^\\\\s*)?\\\\\\\\begin\\\\{(lstlisting|minted|pyglist)\\\\}(?=\\\\[|\\\\{)\", \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"end\": \"\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)\\\\}\", \"patterns\": [{ \"include\": \"#multiline-optional-arg-no-highlight\" }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)((?:c|cpp))(\\\\})\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"2\": { \"name\": \"variable.parameter.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"contentName\": \"source.cpp.embedded.latex\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)\\\\})\", \"patterns\": [{ \"include\": \"source.cpp.embedded.latex\" }] }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)((?:asy|asymptote))(\\\\})\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"2\": { \"name\": \"variable.parameter.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"contentName\": \"source.asy\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)\\\\})\", \"patterns\": [{ \"include\": \"source.asy\" }] }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)((?:css))(\\\\})\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"2\": { \"name\": \"variable.parameter.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"contentName\": \"source.css\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)\\\\})\", \"patterns\": [{ \"include\": \"source.css\" }] }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)((?:hs|haskell))(\\\\})\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"2\": { \"name\": \"variable.parameter.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"contentName\": \"source.haskell\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)\\\\})\", \"patterns\": [{ \"include\": \"source.haskell\" }] }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)((?:html))(\\\\})\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"2\": { \"name\": \"variable.parameter.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"contentName\": \"text.html\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)\\\\})\", \"patterns\": [{ \"include\": \"text.html.basic\" }] }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)((?:xml))(\\\\})\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"2\": { \"name\": \"variable.parameter.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"contentName\": \"text.xml\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)\\\\})\", \"patterns\": [{ \"include\": \"text.xml\" }] }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)((?:java))(\\\\})\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"2\": { \"name\": \"variable.parameter.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"contentName\": \"source.java\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)\\\\})\", \"patterns\": [{ \"include\": \"source.java\" }] }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)((?:lua))(\\\\})\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"2\": { \"name\": \"variable.parameter.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"contentName\": \"source.lua\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)\\\\})\", \"patterns\": [{ \"include\": \"source.lua\" }] }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)((?:jl|julia))(\\\\})\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"2\": { \"name\": \"variable.parameter.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"contentName\": \"source.julia\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)\\\\})\", \"patterns\": [{ \"include\": \"source.julia\" }] }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)((?:rb|ruby))(\\\\})\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"2\": { \"name\": \"variable.parameter.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"contentName\": \"source.ruby\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)\\\\})\", \"patterns\": [{ \"include\": \"source.ruby\" }] }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)((?:js|javascript))(\\\\})\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"2\": { \"name\": \"variable.parameter.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"contentName\": \"source.js\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)\\\\})\", \"patterns\": [{ \"include\": \"source.js\" }] }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)((?:ts|typescript))(\\\\})\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"2\": { \"name\": \"variable.parameter.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"contentName\": \"source.ts\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)\\\\})\", \"patterns\": [{ \"include\": \"source.ts\" }] }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)((?:py|python))(\\\\})\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"2\": { \"name\": \"variable.parameter.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"contentName\": \"source.python\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)\\\\})\", \"patterns\": [{ \"include\": \"source.python\" }] }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)((?:yaml))(\\\\})\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"2\": { \"name\": \"variable.parameter.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"contentName\": \"source.yaml\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)\\\\})\", \"patterns\": [{ \"include\": \"source.yaml\" }] }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)((?:rust))(\\\\})\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"2\": { \"name\": \"variable.parameter.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"contentName\": \"source.rust\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:minted|lstlisting|pyglist)\\\\})\", \"patterns\": [{ \"include\": \"source.rust\" }] }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)([a-zA-Z]*)(\\\\})\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"2\": { \"name\": \"variable.parameter.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"contentName\": \"meta.function.embedded.latex\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:lstlisting|minted|pyglist)\\\\})\", \"name\": \"meta.embedded.block.generic.latex\" }] }, { \"begin\": \"\\\\s*\\\\\\\\begin\\\\{(?:cppcode)\\\\*?\\\\}(?:\\\\[[a-zA-Z0-9_-]*\\\\])?(?=\\\\[|\\\\{|\\\\s*$)\", \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"end\": \"\\\\s*\\\\\\\\end\\\\{(?:cppcode)\\\\*?\\\\}\", \"patterns\": [{ \"include\": \"#multiline-optional-arg-no-highlight\" }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"contentName\": \"variable.parameter.function.latex\", \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.latex\" } } }, { \"begin\": \"^(?=\\\\s*)\", \"contentName\": \"source.cpp.embedded.latex\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:cppcode)\\\\*?\\\\})\", \"patterns\": [{ \"include\": \"source.cpp.embedded.latex\" }] }] }, { \"begin\": \"\\\\s*\\\\\\\\begin\\\\{(?:hscode)\\\\*?\\\\}(?:\\\\[[a-zA-Z0-9_-]*\\\\])?(?=\\\\[|\\\\{|\\\\s*$)\", \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"end\": \"\\\\s*\\\\\\\\end\\\\{(?:hscode)\\\\*?\\\\}\", \"patterns\": [{ \"include\": \"#multiline-optional-arg-no-highlight\" }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"contentName\": \"variable.parameter.function.latex\", \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.latex\" } } }, { \"begin\": \"^(?=\\\\s*)\", \"contentName\": \"source.haskell\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:hscode)\\\\*?\\\\})\", \"patterns\": [{ \"include\": \"source.haskell\" }] }] }, { \"begin\": \"\\\\s*\\\\\\\\begin\\\\{(?:luacode)\\\\*?\\\\}(?:\\\\[[a-zA-Z0-9_-]*\\\\])?(?=\\\\[|\\\\{|\\\\s*$)\", \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"end\": \"\\\\s*\\\\\\\\end\\\\{(?:luacode)\\\\*?\\\\}\", \"patterns\": [{ \"include\": \"#multiline-optional-arg-no-highlight\" }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"contentName\": \"variable.parameter.function.latex\", \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.latex\" } } }, { \"begin\": \"^(?=\\\\s*)\", \"contentName\": \"source.lua\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:luacode)\\\\*?\\\\})\", \"patterns\": [{ \"include\": \"source.lua\" }] }] }, { \"begin\": \"\\\\s*\\\\\\\\begin\\\\{(?:jlcode|jlverbatim|jlblock|jlconcode|jlconsole|jlconverbatim)\\\\*?\\\\}(?:\\\\[[a-zA-Z0-9_-]*\\\\])?(?=\\\\[|\\\\{|\\\\s*$)\", \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"end\": \"\\\\s*\\\\\\\\end\\\\{(?:jlcode|jlverbatim|jlblock|jlconcode|jlconsole|jlconverbatim)\\\\*?\\\\}\", \"patterns\": [{ \"include\": \"#multiline-optional-arg-no-highlight\" }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"contentName\": \"variable.parameter.function.latex\", \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.latex\" } } }, { \"begin\": \"^(?=\\\\s*)\", \"contentName\": \"source.julia\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:jlcode|jlverbatim|jlblock|jlconcode|jlconsole|jlconverbatim)\\\\*?\\\\})\", \"patterns\": [{ \"include\": \"source.julia\" }] }] }, { \"begin\": \"\\\\s*\\\\\\\\begin\\\\{(?:juliacode|juliaverbatim|juliablock|juliaconcode|juliaconsole|juliaconverbatim)\\\\*?\\\\}(?:\\\\[[a-zA-Z0-9_-]*\\\\])?(?=\\\\[|\\\\{|\\\\s*$)\", \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"end\": \"\\\\s*\\\\\\\\end\\\\{(?:juliacode|juliaverbatim|juliablock|juliaconcode|juliaconsole|juliaconverbatim)\\\\*?\\\\}\", \"patterns\": [{ \"include\": \"#multiline-optional-arg-no-highlight\" }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"contentName\": \"variable.parameter.function.latex\", \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.latex\" } } }, { \"begin\": \"^(?=\\\\s*)\", \"contentName\": \"source.julia\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:juliacode|juliaverbatim|juliablock|juliaconcode|juliaconsole|juliaconverbatim)\\\\*?\\\\})\", \"patterns\": [{ \"include\": \"source.julia\" }] }] }, { \"begin\": \"\\\\s*\\\\\\\\begin\\\\{(?:sageblock|sagesilent|sageverbatim|sageexample|sagecommandline|python|pythonq|pythonrepl)\\\\*?\\\\}(?:\\\\[[a-zA-Z0-9_-]*\\\\])?(?=\\\\[|\\\\{|\\\\s*$)\", \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"end\": \"\\\\s*\\\\\\\\end\\\\{(?:sageblock|sagesilent|sageverbatim|sageexample|sagecommandline|python|pythonq|pythonrepl)\\\\*?\\\\}\", \"patterns\": [{ \"include\": \"#multiline-optional-arg-no-highlight\" }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"contentName\": \"variable.parameter.function.latex\", \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.latex\" } } }, { \"begin\": \"^(?=\\\\s*)\", \"contentName\": \"source.python\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:sageblock|sagesilent|sageverbatim|sageexample|sagecommandline|python|pythonq|pythonrepl)\\\\*?\\\\})\", \"patterns\": [{ \"include\": \"source.python\" }] }] }, { \"begin\": \"\\\\s*\\\\\\\\begin\\\\{(?:pycode|pyverbatim|pyblock|pyconcode|pyconsole|pyconverbatim)\\\\*?\\\\}(?:\\\\[[a-zA-Z0-9_-]*\\\\])?(?=\\\\[|\\\\{|\\\\s*$)\", \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"end\": \"\\\\s*\\\\\\\\end\\\\{(?:pycode|pyverbatim|pyblock|pyconcode|pyconsole|pyconverbatim)\\\\*?\\\\}\", \"patterns\": [{ \"include\": \"#multiline-optional-arg-no-highlight\" }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"contentName\": \"variable.parameter.function.latex\", \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.latex\" } } }, { \"begin\": \"^(?=\\\\s*)\", \"contentName\": \"source.python\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:pycode|pyverbatim|pyblock|pyconcode|pyconsole|pyconverbatim)\\\\*?\\\\})\", \"patterns\": [{ \"include\": \"source.python\" }] }] }, { \"begin\": \"\\\\s*\\\\\\\\begin\\\\{(?:pylabcode|pylabverbatim|pylabblock|pylabconcode|pylabconsole|pylabconverbatim)\\\\*?\\\\}(?:\\\\[[a-zA-Z0-9_-]*\\\\])?(?=\\\\[|\\\\{|\\\\s*$)\", \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"end\": \"\\\\s*\\\\\\\\end\\\\{(?:pylabcode|pylabverbatim|pylabblock|pylabconcode|pylabconsole|pylabconverbatim)\\\\*?\\\\}\", \"patterns\": [{ \"include\": \"#multiline-optional-arg-no-highlight\" }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"contentName\": \"variable.parameter.function.latex\", \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.latex\" } } }, { \"begin\": \"^(?=\\\\s*)\", \"contentName\": \"source.python\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:pylabcode|pylabverbatim|pylabblock|pylabconcode|pylabconsole|pylabconverbatim)\\\\*?\\\\})\", \"patterns\": [{ \"include\": \"source.python\" }] }] }, { \"begin\": \"\\\\s*\\\\\\\\begin\\\\{(?:sympycode|sympyverbatim|sympyblock|sympyconcode|sympyconsole|sympyconverbatim)\\\\*?\\\\}(?:\\\\[[a-zA-Z0-9_-]*\\\\])?(?=\\\\[|\\\\{|\\\\s*$)\", \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"end\": \"\\\\s*\\\\\\\\end\\\\{(?:sympycode|sympyverbatim|sympyblock|sympyconcode|sympyconsole|sympyconverbatim)\\\\*?\\\\}\", \"patterns\": [{ \"include\": \"#multiline-optional-arg-no-highlight\" }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"contentName\": \"variable.parameter.function.latex\", \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.latex\" } } }, { \"begin\": \"^(?=\\\\s*)\", \"contentName\": \"source.python\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:sympycode|sympyverbatim|sympyblock|sympyconcode|sympyconsole|sympyconverbatim)\\\\*?\\\\})\", \"patterns\": [{ \"include\": \"source.python\" }] }] }, { \"begin\": \"\\\\s*\\\\\\\\begin\\\\{(?:scalacode)\\\\*?\\\\}(?:\\\\[[a-zA-Z0-9_-]*\\\\])?(?=\\\\[|\\\\{|\\\\s*$)\", \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"end\": \"\\\\s*\\\\\\\\end\\\\{(?:scalacode)\\\\*?\\\\}\", \"patterns\": [{ \"include\": \"#multiline-optional-arg-no-highlight\" }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"contentName\": \"variable.parameter.function.latex\", \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.latex\" } } }, { \"begin\": \"^(?=\\\\s*)\", \"contentName\": \"source.scala\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:scalacode)\\\\*?\\\\})\", \"patterns\": [{ \"include\": \"source.scala\" }] }] }, { \"begin\": \"\\\\s*\\\\\\\\begin\\\\{(?:asy|asycode)\\\\*?\\\\}(?:\\\\[[a-zA-Z0-9_-]*\\\\])?(?=\\\\[|\\\\{|\\\\s*$)\", \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"end\": \"\\\\s*\\\\\\\\end\\\\{(?:asy|asycode)\\\\*?\\\\}\", \"patterns\": [{ \"include\": \"#multiline-optional-arg-no-highlight\" }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"contentName\": \"variable.parameter.function.latex\", \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.latex\" } } }, { \"begin\": \"^(?=\\\\s*)\", \"contentName\": \"source.asymptote\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:asy|asycode)\\\\*?\\\\})\", \"patterns\": [{ \"include\": \"source.asymptote\" }] }] }, { \"begin\": \"\\\\s*\\\\\\\\begin\\\\{(?:dot2tex|dotcode)\\\\*?\\\\}(?:\\\\[[a-zA-Z0-9_-]*\\\\])?(?=\\\\[|\\\\{|\\\\s*$)\", \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"end\": \"\\\\s*\\\\\\\\end\\\\{(?:dot2tex|dotcode)\\\\*?\\\\}\", \"patterns\": [{ \"include\": \"#multiline-optional-arg-no-highlight\" }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"contentName\": \"variable.parameter.function.latex\", \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.latex\" } } }, { \"begin\": \"^(?=\\\\s*)\", \"contentName\": \"source.dot\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:dot2tex|dotcode)\\\\*?\\\\})\", \"patterns\": [{ \"include\": \"source.dot\" }] }] }, { \"begin\": \"\\\\s*\\\\\\\\begin\\\\{(?:gnuplot)\\\\*?\\\\}(?:\\\\[[a-zA-Z0-9_-]*\\\\])?(?=\\\\[|\\\\{|\\\\s*$)\", \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"end\": \"\\\\s*\\\\\\\\end\\\\{(?:gnuplot)\\\\*?\\\\}\", \"patterns\": [{ \"include\": \"#multiline-optional-arg-no-highlight\" }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"contentName\": \"variable.parameter.function.latex\", \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.latex\" } } }, { \"begin\": \"^(?=\\\\s*)\", \"contentName\": \"source.gnuplot\", \"end\": \"^\\\\s*(?=\\\\\\\\end\\\\{(?:gnuplot)\\\\*?\\\\})\", \"patterns\": [{ \"include\": \"source.gnuplot\" }] }] }, { \"begin\": \"((?:\\\\s*)\\\\\\\\begin\\\\{([a-zA-Z]*code|lstlisting|minted|pyglist)(?:\\\\*)?\\\\}(?:\\\\[.*\\\\])?(?:\\\\{.*\\\\})?)\", \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"contentName\": \"meta.function.embedded.latex\", \"end\": \"(\\\\\\\\end\\\\{\\\\2\\\\}(?:\\\\s*\\\\n)?)\", \"name\": \"meta.embedded.block.generic.latex\" }, { \"begin\": \"((\\\\\\\\)addplot)(?:\\\\+?)((?:\\\\[[^\\\\[]*\\\\]))*\\\\s*(gnuplot)\\\\s*((?:\\\\[[^\\\\[]*\\\\]))*\\\\s*(\\\\{)\", \"captures\": { \"1\": { \"name\": \"support.function.be.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" }, \"3\": { \"patterns\": [{ \"include\": \"#optional-arg\" }] }, \"4\": { \"name\": \"variable.parameter.function.latex\" }, \"5\": { \"patterns\": [{ \"include\": \"#optional-arg\" }] }, \"6\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"end\": \"\\\\s*(\\\\};)\", \"patterns\": [{ \"begin\": \"%\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.latex\" } }, \"end\": \"$\\\\n?\", \"name\": \"comment.line.percentage.latex\" }, { \"include\": \"source.gnuplot\" }] }, { \"begin\": \"(\\\\s*\\\\\\\\begin\\\\{((?:fboxv|boxedv|V|v|spv)erbatim\\\\*?)\\\\})\", \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"contentName\": \"markup.raw.verbatim.latex\", \"end\": \"(\\\\\\\\end\\\\{\\\\2\\\\})\", \"name\": \"meta.function.verbatim.latex\" }, { \"begin\": \"(\\\\s*\\\\\\\\begin\\\\{VerbatimOut\\\\}\\\\{[^\\\\}]*\\\\})\", \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"contentName\": \"markup.raw.verbatim.latex\", \"end\": \"(\\\\\\\\end\\\\{\\\\VerbatimOut\\\\})\", \"name\": \"meta.function.verbatim.latex\" }, { \"begin\": \"(\\\\s*\\\\\\\\begin\\\\{alltt\\\\})\", \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"contentName\": \"markup.raw.verbatim.latex\", \"end\": \"(\\\\\\\\end\\\\{alltt\\\\})\", \"name\": \"meta.function.alltt.latex\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.function.latex\" } }, \"match\": \"(\\\\\\\\)[A-Za-z]+\", \"name\": \"support.function.general.latex\" }] }, { \"begin\": \"(\\\\s*\\\\\\\\begin\\\\{([Cc]omment)\\\\})\", \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"contentName\": \"punctuation.definition.comment.latex\", \"end\": \"(\\\\\\\\end\\\\{\\\\2\\\\})\", \"name\": \"meta.function.verbatim.latex\" }, { \"begin\": \"(?:\\\\s*)((\\\\\\\\)(?:href|hyperref|hyperimage))(?=\\\\[|\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.url.latex\" } }, \"comment\": \"Captures \\\\command[option]{url}{optional category}{optional name}{text}\", \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"name\": \"meta.function.hyperlink.latex\", \"patterns\": [{ \"include\": \"#multiline-optional-arg-no-highlight\" }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(\\\\{)([^}]*)(\\\\})(?:\\\\{[^}]*\\\\}){2}?(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"2\": { \"name\": \"markup.underline.link.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.end.latex\" }, \"4\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"contentName\": \"meta.variable.parameter.function.latex\", \"end\": \"(?=\\\\})\", \"patterns\": [{ \"include\": \"$base\" }] }, { \"begin\": \"(?:\\\\G|(?<=\\\\]))(?:(\\\\{)[^}]*(\\\\}))?(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"2\": { \"name\": \"punctuation.definition.arguments.end.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"contentName\": \"meta.variable.parameter.function.latex\", \"end\": \"(?=\\\\})\", \"patterns\": [{ \"include\": \"$base\" }] }] }, { \"captures\": { \"1\": { \"name\": \"support.function.url.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"5\": { \"name\": \"punctuation.definition.arguments.end.latex\" }, \"'\": { \"name\": \"markup.underline.link.latex\" } }, \"match\": \"(?:\\\\s*)((\\\\\\\\)url)(\\\\{)([^}]*)(\\\\})\", \"name\": \"meta.function.link.url.latex\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"comment\": \"These two patterns match the \\\\begin{document} and \\\\end{document} commands, so that the environment matching pattern following them will ignore those commands.\", \"match\": \"(\\\\s*\\\\\\\\begin\\\\{document\\\\})\", \"name\": \"meta.function.begin-document.latex\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"match\": \"(\\\\s*\\\\\\\\end\\\\{document\\\\})\", \"name\": \"meta.function.end-document.latex\" }, { \"begin\": \"(?:\\\\s*)((\\\\\\\\)begin)(\\\\{)((?:\\\\+?array|equation|(?:IEEE)?eqnarray|multline|align|aligned|alignat|alignedat|flalign|flaligned|flalignat|split|gather|gathered|\\\\+?cases|(?:display)?math|\\\\+?[a-zA-Z]*matrix|[pbBvV]?NiceMatrix|[pbBvV]?NiceArray|(?:(?:arg)?(?:mini|maxi)))(?:\\\\*|!)?)(\\\\})(\\\\s*\\\\n)?\", \"captures\": { \"1\": { \"name\": \"support.function.be.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"4\": { \"name\": \"variable.parameter.function.latex\" }, \"5\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"contentName\": \"meta.math.block.latex support.class.math.block.environment.latex\", \"end\": \"(?:\\\\s*)((\\\\\\\\)end)(\\\\{)(\\\\4)(\\\\})(?:\\\\s*\\\\n)?\", \"name\": \"meta.function.environment.math.latex\", \"patterns\": [{ \"match\": \"(?<!\\\\\\\\)&\", \"name\": \"keyword.control.equation.align.latex\" }, { \"match\": \"\\\\\\\\\\\\\\\\\", \"name\": \"keyword.control.equation.newline.latex\" }, { \"include\": \"#definition-label\" }, { \"include\": \"text.tex#math\" }, { \"include\": \"$base\" }] }, { \"begin\": \"(?:\\\\s*)(\\\\\\\\begin\\\\{empheq\\\\}(?:\\\\[.*\\\\])?)\", \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"contentName\": \"meta.math.block.latex support.class.math.block.environment.latex\", \"end\": \"(?:\\\\s*)(\\\\\\\\end\\\\{empheq\\\\})\", \"name\": \"meta.function.environment.math.latex\", \"patterns\": [{ \"match\": \"(?<!\\\\\\\\)&\", \"name\": \"keyword.control.equation.align.latex\" }, { \"match\": \"\\\\\\\\\\\\\\\\\", \"name\": \"keyword.control.equation.newline.latex\" }, { \"include\": \"#definition-label\" }, { \"include\": \"text.tex#math\" }, { \"include\": \"$base\" }] }, { \"begin\": \"(\\\\s*\\\\\\\\begin\\\\{(tabular[xy*]?|xltabular|longtable|(?:long)?tabu|(?:long|tall)?tblr|NiceTabular[X*]?|booktabs)\\\\}(\\\\s*\\\\n)?)\", \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"contentName\": \"meta.data.environment.tabular.latex\", \"end\": \"(\\\\s*\\\\\\\\end\\\\{(\\\\2)\\\\}(?:\\\\s*\\\\n)?)\", \"name\": \"meta.function.environment.tabular.latex\", \"patterns\": [{ \"match\": \"(?<!\\\\\\\\)&\", \"name\": \"keyword.control.table.cell.latex\" }, { \"match\": \"\\\\\\\\\\\\\\\\\", \"name\": \"keyword.control.table.newline.latex\" }, { \"include\": \"$base\" }] }, { \"begin\": \"(\\\\s*\\\\\\\\begin\\\\{(itemize|enumerate|description|list)\\\\})\", \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"end\": \"(\\\\\\\\end\\\\{\\\\2\\\\}(?:\\\\s*\\\\n)?)\", \"name\": \"meta.function.environment.list.latex\", \"patterns\": [{ \"include\": \"$base\" }] }, { \"begin\": \"(\\\\s*\\\\\\\\begin\\\\{tikzpicture\\\\})\", \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"end\": \"(\\\\\\\\end\\\\{tikzpicture\\\\}(?:\\\\s*\\\\n)?)\", \"name\": \"meta.function.environment.latex.tikz\", \"patterns\": [{ \"include\": \"$base\" }] }, { \"begin\": \"(\\\\s*\\\\\\\\begin\\\\{frame\\\\})\", \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"end\": \"(\\\\\\\\end\\\\{frame\\\\})\", \"name\": \"meta.function.environment.frame.latex\", \"patterns\": [{ \"include\": \"$base\" }] }, { \"begin\": \"(\\\\s*\\\\\\\\begin\\\\{(mpost\\\\*?)\\\\})\", \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"end\": \"(\\\\\\\\end\\\\{\\\\2\\\\}(?:\\\\s*\\\\n)?)\", \"name\": \"meta.function.environment.latex.mpost\" }, { \"begin\": \"(\\\\s*\\\\\\\\begin\\\\{markdown\\\\})\", \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"contentName\": \"meta.embedded.markdown_latex_combined\", \"end\": \"(\\\\\\\\end\\\\{markdown\\\\})\", \"patterns\": [{ \"include\": \"text.tex.markdown_latex_combined\" }] }, { \"begin\": \"(\\\\s*\\\\\\\\begin\\\\{(\\\\w+\\\\*?)\\\\})\", \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#begin-env-tokenizer\" }] } }, \"end\": \"(\\\\\\\\end\\\\{\\\\2\\\\}(?:\\\\s*\\\\n)?)\", \"name\": \"meta.function.environment.general.latex\", \"patterns\": [{ \"include\": \"$base\" }] }, { \"captures\": { \"1\": { \"name\": \"storage.type.function.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.begin.latex\" }, \"4\": { \"name\": \"support.function.general.latex\" }, \"5\": { \"name\": \"punctuation.definition.function.latex\" }, \"6\": { \"name\": \"punctuation.definition.end.latex\" } }, \"match\": \"((\\\\\\\\)(?:newcommand|renewcommand|(?:re)?newrobustcmd|DeclareRobustCommand))\\\\*?({)((\\\\\\\\)[^}]*)(})\" }, { \"begin\": \"((\\\\\\\\)marginpar)((?:\\\\[[^\\\\[]*?\\\\])*)(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.marginpar.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" }, \"3\": { \"patterns\": [{ \"include\": \"#optional-arg\" }] }, \"4\": { \"name\": \"punctuation.definition.marginpar.begin.latex\" } }, \"contentName\": \"meta.paragraph.margin.latex\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.marginpar.end.latex\" } }, \"patterns\": [{ \"include\": \"text.tex#braces\" }, { \"include\": \"$base\" }] }, { \"begin\": \"((\\\\\\\\)footnote)((?:\\\\[[^\\\\[]*?\\\\])*)(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.footnote.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" }, \"3\": { \"patterns\": [{ \"include\": \"#optional-arg\" }] }, \"4\": { \"name\": \"punctuation.definition.footnote.begin.latex\" } }, \"contentName\": \"entity.name.footnote.latex\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.footnote.end.latex\" } }, \"patterns\": [{ \"include\": \"text.tex#braces\" }, { \"include\": \"$base\" }] }, { \"begin\": \"((\\\\\\\\)emph)(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.emph.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.emph.begin.latex\" } }, \"contentName\": \"markup.italic.emph.latex\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.emph.end.latex\" } }, \"name\": \"meta.function.emph.latex\", \"patterns\": [{ \"include\": \"text.tex#braces\" }, { \"include\": \"$base\" }] }, { \"begin\": \"((\\\\\\\\)textit)(\\\\{)\", \"captures\": { \"1\": { \"name\": \"support.function.textit.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.textit.begin.latex\" } }, \"comment\": \"We put the keyword in a capture and name this capture, so that disabling spell checking for \\u201Ckeyword\\u201D won't be inherited by the argument to \\\\textit{...}.\\n\\nPut specific matches for particular LaTeX keyword.functions before the last two more general functions\", \"contentName\": \"markup.italic.textit.latex\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.textit.end.latex\" } }, \"name\": \"meta.function.textit.latex\", \"patterns\": [{ \"include\": \"text.tex#braces\" }, { \"include\": \"$base\" }] }, { \"begin\": \"((\\\\\\\\)textbf)(\\\\{)\", \"captures\": { \"1\": { \"name\": \"support.function.textbf.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.textbf.begin.latex\" } }, \"contentName\": \"markup.bold.textbf.latex\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.textbf.end.latex\" } }, \"name\": \"meta.function.textbf.latex\", \"patterns\": [{ \"include\": \"text.tex#braces\" }, { \"include\": \"$base\" }] }, { \"begin\": \"((\\\\\\\\)texttt)(\\\\{)\", \"captures\": { \"1\": { \"name\": \"support.function.texttt.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.texttt.begin.latex\" } }, \"contentName\": \"markup.raw.texttt.latex\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.texttt.end.latex\" } }, \"name\": \"meta.function.texttt.latex\", \"patterns\": [{ \"include\": \"text.tex#braces\" }, { \"include\": \"$base\" }] }, { \"captures\": { \"0\": { \"name\": \"keyword.other.item.latex\" }, \"1\": { \"name\": \"punctuation.definition.keyword.latex\" } }, \"match\": \"(\\\\\\\\)item\\\\b\", \"name\": \"meta.scope.item.latex\" }, { \"begin\": \"((\\\\\\\\)(?:[aA]uto|foot|full|no|ref|short|[tT]ext|[pP]aren|[sS]mart)?[cC]ite(?:al)?(?:p|s|t|author|year(?:par)?|title)?[ANP]*\\\\*?)((?:(?:\\\\([^\\\\)]*\\\\)){0,2}(?:\\\\[[^\\\\]]*\\\\]){0,2}\\\\{[\\\\p{Alphabetic}:.]*\\\\})*)(?:([<\\\\[])[^\\\\]<>]*([>\\\\]]))?(?:(\\\\[)[^\\\\]]*(\\\\]))?(\\\\{)\", \"captures\": { \"1\": { \"name\": \"keyword.control.cite.latex\" }, \"2\": { \"name\": \"punctuation.definition.keyword.latex\" }, \"3\": { \"patterns\": [{ \"include\": \"#autocites-arg\" }] }, \"4\": { \"name\": \"punctuation.definition.arguments.optional.begin.latex\" }, \"5\": { \"name\": \"punctuation.definition.arguments.optional.end.latex\" }, \"6\": { \"name\": \"punctuation.definition.arguments.optional.begin.latex\" }, \"7\": { \"name\": \"punctuation.definition.arguments.optional.end.latex\" }, \"8\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"name\": \"meta.citation.latex\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"comment.line.percentage.tex\" }, \"2\": { \"name\": \"punctuation.definition.comment.tex\" } }, \"match\": \"((%).*)$\" }, { \"match\": \"[\\\\p{Alphabetic}\\\\p{Number}:.-]+\", \"name\": \"constant.other.reference.citation.latex\" }] }, { \"begin\": \"((\\\\\\\\)bibentry)(\\\\{)\", \"captures\": { \"1\": { \"name\": \"keyword.control.cite.latex\" }, \"2\": { \"name\": \"punctuation.definition.keyword.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"name\": \"meta.citation.latex\", \"patterns\": [{ \"match\": \"[\\\\p{Alphabetic}\\\\p{Number}:.]+\", \"name\": \"constant.other.reference.citation.latex\" }] }, { \"begin\": \"((\\\\\\\\)(?:\\\\w*[rR]ef\\\\*?))(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.ref.latex\" }, \"2\": { \"name\": \"punctuation.definition.keyword.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"name\": \"meta.reference.label.latex\", \"patterns\": [{ \"match\": \"[\\\\p{Alphabetic}\\\\p{Number}\\\\.,:/*!^_-]\", \"name\": \"constant.other.reference.label.latex\" }] }, { \"include\": \"#definition-label\" }, { \"begin\": \"((\\\\\\\\)(?:verb|Verb|spverb)\\\\*?)\\\\s*((\\\\\\\\)scantokens)(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.verb.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" }, \"3\": { \"name\": \"support.function.verb.latex\" }, \"4\": { \"name\": \"punctuation.definition.verb.latex\" }, \"5\": { \"name\": \"punctuation.definition.begin.latex\" } }, \"contentName\": \"markup.raw.verb.latex\", \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.end.latex\" } }, \"name\": \"meta.function.verb.latex\", \"patterns\": [{ \"include\": \"$self\" }] }, { \"captures\": { \"1\": { \"name\": \"support.function.verb.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.verb.latex\" }, \"4\": { \"name\": \"markup.raw.verb.latex\" }, \"5\": { \"name\": \"punctuation.definition.verb.latex\" } }, \"match\": \"((\\\\\\\\)(?:verb|Verb|spverb)\\\\*?)\\\\s*((?<=\\\\s)\\\\S|[^a-zA-Z])(.*?)(\\\\3|$)\", \"name\": \"meta.function.verb.latex\" }, { \"captures\": { \"1\": { \"name\": \"support.function.verb.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" }, \"3\": { \"patterns\": [{ \"include\": \"#optional-arg\" }] }, \"4\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"5\": { \"name\": \"punctuation.definition.arguments.end.latex\" }, \"6\": { \"name\": \"punctuation.definition.verb.latex\" }, \"7\": { \"name\": \"markup.raw.verb.latex\" }, \"8\": { \"name\": \"punctuation.definition.verb.latex\" }, \"9\": { \"name\": \"punctuation.definition.verb.latex\" }, \"10\": { \"name\": \"markup.raw.verb.latex\" }, \"11\": { \"name\": \"punctuation.definition.verb.latex\" } }, \"match\": \"((\\\\\\\\)(?:mint|mintinline))((?:\\\\[[^\\\\[]*?\\\\])?)(\\\\{)[a-zA-Z]*(\\\\})(?:(?:([^a-zA-Z\\\\{])(.*?)(\\\\6))|(?:(\\\\{)(.*?)(\\\\})))\", \"name\": \"meta.function.verb.latex\" }, { \"captures\": { \"1\": { \"name\": \"support.function.verb.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" }, \"3\": { \"patterns\": [{ \"include\": \"#optional-arg\" }] }, \"4\": { \"name\": \"punctuation.definition.verb.latex\" }, \"5\": { \"name\": \"markup.raw.verb.latex\" }, \"6\": { \"name\": \"punctuation.definition.verb.latex\" }, \"7\": { \"name\": \"punctuation.definition.verb.latex\" }, \"8\": { \"name\": \"markup.raw.verb.latex\" }, \"9\": { \"name\": \"punctuation.definition.verb.latex\" } }, \"match\": \"((\\\\\\\\)[a-z]+inline)((?:\\\\[[^\\\\[]*?\\\\])?)(?:(?:([^a-zA-Z\\\\{])(.*?)(\\\\4))|(?:(\\\\{)(.*?)(\\\\})))\", \"name\": \"meta.function.verb.latex\" }, { \"captures\": { \"1\": { \"name\": \"support.function.verb.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" }, \"3\": { \"patterns\": [{ \"include\": \"#optional-arg\" }] }, \"4\": { \"name\": \"punctuation.definition.verb.latex\" }, \"5\": { \"name\": \"source.python\", \"patterns\": [{ \"include\": \"source.python\" }] }, \"6\": { \"name\": \"punctuation.definition.verb.latex\" }, \"7\": { \"name\": \"punctuation.definition.verb.latex\" }, \"8\": { \"name\": \"source.python\", \"patterns\": [{ \"include\": \"source.python\" }] }, \"9\": { \"name\": \"punctuation.definition.verb.latex\" } }, \"match\": \"((\\\\\\\\)(?:(?:py|pycon|pylab|pylabcon|sympy|sympycon)[cv]?|pyq|pycq|pyif))((?:\\\\[[^\\\\[]*?\\\\])?)(?:(?:([^a-zA-Z\\\\{])(.*?)(\\\\4))|(?:(\\\\{)(.*?)(\\\\})))\", \"name\": \"meta.function.verb.latex\" }, { \"captures\": { \"1\": { \"name\": \"support.function.verb.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" }, \"3\": { \"patterns\": [{ \"include\": \"#optional-arg\" }] }, \"4\": { \"name\": \"punctuation.definition.verb.latex\" }, \"5\": { \"name\": \"source.julia\", \"patterns\": [{ \"include\": \"source.julia\" }] }, \"6\": { \"name\": \"punctuation.definition.verb.latex\" }, \"7\": { \"name\": \"punctuation.definition.verb.latex\" }, \"8\": { \"name\": \"source.julia\", \"patterns\": [{ \"include\": \"source.julia\" }] }, \"9\": { \"name\": \"punctuation.definition.verb.latex\" } }, \"match\": \"((\\\\\\\\)(?:jl|julia)[cv]?)((?:\\\\[[^\\\\[]*?\\\\])?)(?:(?:([^a-zA-Z\\\\{])(.*?)(\\\\4))|(?:(\\\\{)(.*?)(\\\\})))\", \"name\": \"meta.function.verb.latex\" }, { \"match\": \"\\\\\\\\(?:newline|pagebreak|clearpage|linebreak|pause)(?:\\\\b)\", \"name\": \"keyword.control.layout.latex\" }, { \"begin\": \"\\\\\\\\\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.latex\" } }, \"end\": \"\\\\\\\\\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.latex\" } }, \"name\": \"meta.math.block.latex support.class.math.block.environment.latex\", \"patterns\": [{ \"include\": \"text.tex#math\" }, { \"include\": \"$base\" }] }, { \"begin\": \"\\\\$\\\\$\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.latex\" } }, \"end\": \"\\\\$\\\\$\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.latex\" } }, \"name\": \"meta.math.block.latex support.class.math.block.environment.latex\", \"patterns\": [{ \"match\": \"\\\\\\\\\\\\$\", \"name\": \"constant.character.escape.latex\" }, { \"include\": \"text.tex#math\" }, { \"include\": \"$base\" }] }, { \"begin\": \"\\\\$\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.tex\" } }, \"end\": \"\\\\$\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.tex\" } }, \"name\": \"meta.math.block.tex support.class.math.block.tex\", \"patterns\": [{ \"match\": \"\\\\\\\\\\\\$\", \"name\": \"constant.character.escape.latex\" }, { \"include\": \"text.tex#math\" }, { \"include\": \"$base\" }] }, { \"begin\": \"\\\\\\\\\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.latex\" } }, \"end\": \"\\\\\\\\\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.latex\" } }, \"name\": \"meta.math.block.latex support.class.math.block.environment.latex\", \"patterns\": [{ \"include\": \"text.tex#math\" }, { \"include\": \"$base\" }] }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.latex\" } }, \"match\": \"(\\\\\\\\)(text(s(terling|ixoldstyle|urd|e(ction|venoldstyle|rvicemark))|yen|n(ineoldstyle|umero|aira)|c(ircledP|o(py(left|right)|lonmonetary)|urrency|e(nt(oldstyle)?|lsius))|t(hree(superior|oldstyle|quarters(emdash)?)|i(ldelow|mes)|w(o(superior|oldstyle)|elveudash)|rademark)|interrobang(down)?|zerooldstyle|o(hm|ne(superior|half|oldstyle|quarter)|penbullet|rd(feminine|masculine))|d(i(scount|ed|v(orced)?)|o(ng|wnarrow|llar(oldstyle)?)|egree|agger(dbl)?|blhyphen(char)?)|uparrow|p(ilcrow|e(so|r(t(housand|enthousand)|iodcentered))|aragraph|m)|e(stimated|ightoldstyle|uro)|quotes(traight(dblbase|base)|ingle)|f(iveoldstyle|ouroldstyle|lorin|ractionsolidus)|won|l(not|ira|e(ftarrow|af)|quill|angle|brackdbl)|a(s(cii(caron|dieresis|acute|grave|macron|breve)|teriskcentered)|cutedbl)|r(ightarrow|e(cipe|ferencemark|gistered)|quill|angle|brackdbl)|g(uarani|ravedbl)|m(ho|inus|u(sicalnote)?|arried)|b(igcircle|orn|ullet|lank|a(ht|rdbl)|rokenbar)))\\\\b\", \"name\": \"constant.character.latex\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.column-specials.begin.latex\" }, \"2\": { \"name\": \"punctuation.definition.column-specials.end.latex\" } }, \"match\": \"(?:<|>)(\\\\{)\\\\$(\\\\})\", \"name\": \"meta.column-specials.latex\" }, { \"include\": \"text.tex\" }], \"repository\": { \"autocites-arg\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.arguments.optional.begin.latex\" }, \"2\": { \"name\": \"punctuation.definition.arguments.optional.end.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.optional.begin.latex\" }, \"4\": { \"name\": \"punctuation.definition.arguments.optional.end.latex\" }, \"5\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"6\": { \"name\": \"constant.other.reference.citation.latex\" }, \"7\": { \"name\": \"punctuation.definition.arguments.end.latex\" }, \"8\": { \"patterns\": [{ \"include\": \"#autocites-arg\" }] } }, \"match\": \"(?:(\\\\()[^\\\\)]*(\\\\))){0,2}(?:(\\\\[)[^\\\\]]*(\\\\])){0,2}(\\\\{)([\\\\p{Alphabetic}\\\\p{Number}:.]+)(\\\\})(.*)\" }] }, \"begin-env-tokenizer\": { \"captures\": { \"1\": { \"name\": \"support.function.be.latex\" }, \"2\": { \"name\": \"punctuation.definition.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"4\": { \"name\": \"variable.parameter.function.latex\" }, \"5\": { \"name\": \"punctuation.definition.arguments.end.latex\" }, \"6\": { \"name\": \"punctuation.definition.arguments.optional.begin.latex\" }, \"7\": { \"patterns\": [{ \"include\": \"$base\" }] }, \"8\": { \"name\": \"punctuation.definition.arguments.optional.end.latex\" }, \"9\": { \"name\": \"punctuation.definition.arguments.begin.latex\" }, \"10\": { \"name\": \"variable.parameter.function.latex\" }, \"11\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"match\": \"\\\\s*((\\\\\\\\)(?:begin|end))(\\\\{)([a-zA-Z]*\\\\*?)(\\\\})(?:(\\\\[)(.*)(\\\\]))?(?:(\\\\{)([^{}]*)(\\\\}))?\" }, \"definition-label\": { \"begin\": \"((\\\\\\\\)label)((?:\\\\[[^\\\\[]*?\\\\])*)(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.label.latex\" }, \"2\": { \"name\": \"punctuation.definition.keyword.latex\" }, \"3\": { \"patterns\": [{ \"include\": \"#optional-arg\" }] }, \"4\": { \"name\": \"punctuation.definition.arguments.begin.latex\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.end.latex\" } }, \"name\": \"meta.definition.label.latex\", \"patterns\": [{ \"match\": \"[\\\\p{Alphabetic}\\\\p{Number}\\\\.,:/*!^_-]\", \"name\": \"variable.parameter.definition.label.latex\" }] }, \"multiline-optional-arg\": { \"begin\": \"\\\\G\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.optional.begin.latex\" } }, \"contentName\": \"variable.parameter.function.latex\", \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.optional.end.latex\" } }, \"name\": \"meta.parameter.optional.latex\", \"patterns\": [{ \"include\": \"$self\" }] }, \"multiline-optional-arg-no-highlight\": { \"begin\": \"\\\\G\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.optional.begin.latex\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.arguments.optional.end.latex\" } }, \"name\": \"meta.parameter.optional.latex\", \"patterns\": [{ \"include\": \"$self\" }] }, \"optional-arg\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.arguments.optional.begin.latex\" }, \"2\": { \"name\": \"variable.parameter.function.latex\" }, \"3\": { \"name\": \"punctuation.definition.arguments.optional.end.latex\" } }, \"match\": \"(\\\\[)([^\\\\[]*?)(\\\\])\", \"name\": \"meta.parameter.optional.latex\" }] } }, \"scopeName\": \"text.tex.latex\", \"embeddedLangs\": [\"tex\", \"css\", \"haskell\", \"html\", \"xml\", \"java\", \"lua\", \"julia\", \"ruby\", \"javascript\", \"typescript\", \"python\", \"yaml\", \"rust\", \"scala\", \"gnuplot\"] });\nvar latex = [\n  ...tex,\n  ...css,\n  ...haskell,\n  ...html,\n  ...xml,\n  ...java,\n  ...lua,\n  ...julia,\n  ...ruby,\n  ...javascript,\n  ...typescript,\n  ...python,\n  ...yaml,\n  ...rust,\n  ...scala,\n  ...gnuplot,\n  lang\n];\n\nexport { latex as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,SAAS,QAAQ,SAAS,YAAY,CAAC,EAAE,WAAW,yIAAmJ,SAAS,4FAA4F,QAAQ,iCAAiC,GAAG,EAAE,SAAS,sDAAsD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iCAAiC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,OAAO,YAAY,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,0BAA0B,GAAG,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,uBAAuB,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,kCAAkC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,QAAQ,sBAAsB,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,gJAAgJ,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iCAAiC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,WAAW,8EAA8E,eAAe,6BAA6B,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,QAAQ,kCAAkC,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,0CAA0C,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,eAAe,qCAAqC,OAAO,oCAAoC,QAAQ,yCAAyC,YAAY,CAAC,EAAE,SAAS,WAAW,OAAO,OAAO,QAAQ,sEAAsE,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,QAAQ,qEAAqE,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,iEAAiE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,OAAO,8CAA8C,YAAY,CAAC,EAAE,WAAW,uCAAuC,GAAG,EAAE,SAAS,yCAAyC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,eAAe,6BAA6B,OAAO,uDAAuD,YAAY,CAAC,EAAE,WAAW,4BAA4B,CAAC,EAAE,GAAG,EAAE,SAAS,iDAAiD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,eAAe,cAAc,OAAO,uDAAuD,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,uCAAuC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,eAAe,cAAc,OAAO,uDAAuD,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,8CAA8C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,eAAe,kBAAkB,OAAO,uDAAuD,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,SAAS,wCAAwC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,eAAe,aAAa,OAAO,uDAAuD,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,EAAE,SAAS,uCAAuC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,eAAe,YAAY,OAAO,uDAAuD,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,EAAE,SAAS,wCAAwC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,eAAe,eAAe,OAAO,uDAAuD,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,uCAAuC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,eAAe,cAAc,OAAO,uDAAuD,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,SAAS,4CAA4C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,eAAe,gBAAgB,OAAO,uDAAuD,YAAY,CAAC,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,EAAE,SAAS,2CAA2C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,eAAe,eAAe,OAAO,uDAAuD,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,iDAAiD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,eAAe,aAAa,OAAO,uDAAuD,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,iDAAiD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,eAAe,aAAa,OAAO,uDAAuD,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,6CAA6C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,eAAe,iBAAiB,OAAO,uDAAuD,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,EAAE,SAAS,wCAAwC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,eAAe,eAAe,OAAO,uDAAuD,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,wCAAwC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,eAAe,eAAe,OAAO,uDAAuD,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,yCAAyC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,eAAe,gCAAgC,OAAO,uDAAuD,QAAQ,oCAAoC,CAAC,EAAE,GAAG,EAAE,SAAS,gFAAgF,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,OAAO,oCAAoC,YAAY,CAAC,EAAE,WAAW,uCAAuC,GAAG,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,qCAAqC,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,EAAE,GAAG,EAAE,SAAS,aAAa,eAAe,6BAA6B,OAAO,yCAAyC,YAAY,CAAC,EAAE,WAAW,4BAA4B,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,+EAA+E,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,OAAO,mCAAmC,YAAY,CAAC,EAAE,WAAW,uCAAuC,GAAG,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,qCAAqC,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,EAAE,GAAG,EAAE,SAAS,aAAa,eAAe,kBAAkB,OAAO,wCAAwC,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,gFAAgF,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,OAAO,oCAAoC,YAAY,CAAC,EAAE,WAAW,uCAAuC,GAAG,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,qCAAqC,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,EAAE,GAAG,EAAE,SAAS,aAAa,eAAe,cAAc,OAAO,yCAAyC,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,oIAAoI,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,OAAO,wFAAwF,YAAY,CAAC,EAAE,WAAW,uCAAuC,GAAG,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,qCAAqC,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,EAAE,GAAG,EAAE,SAAS,aAAa,eAAe,gBAAgB,OAAO,6FAA6F,YAAY,CAAC,EAAE,WAAW,eAAe,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,sJAAsJ,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,OAAO,0GAA0G,YAAY,CAAC,EAAE,WAAW,uCAAuC,GAAG,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,qCAAqC,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,EAAE,GAAG,EAAE,SAAS,aAAa,eAAe,gBAAgB,OAAO,+GAA+G,YAAY,CAAC,EAAE,WAAW,eAAe,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,gKAAgK,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,OAAO,oHAAoH,YAAY,CAAC,EAAE,WAAW,uCAAuC,GAAG,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,qCAAqC,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,EAAE,GAAG,EAAE,SAAS,aAAa,eAAe,iBAAiB,OAAO,yHAAyH,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,oIAAoI,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,OAAO,wFAAwF,YAAY,CAAC,EAAE,WAAW,uCAAuC,GAAG,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,qCAAqC,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,EAAE,GAAG,EAAE,SAAS,aAAa,eAAe,iBAAiB,OAAO,6FAA6F,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,sJAAsJ,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,OAAO,0GAA0G,YAAY,CAAC,EAAE,WAAW,uCAAuC,GAAG,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,qCAAqC,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,EAAE,GAAG,EAAE,SAAS,aAAa,eAAe,iBAAiB,OAAO,+GAA+G,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,sJAAsJ,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,OAAO,0GAA0G,YAAY,CAAC,EAAE,WAAW,uCAAuC,GAAG,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,qCAAqC,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,EAAE,GAAG,EAAE,SAAS,aAAa,eAAe,iBAAiB,OAAO,+GAA+G,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,kFAAkF,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,OAAO,sCAAsC,YAAY,CAAC,EAAE,WAAW,uCAAuC,GAAG,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,qCAAqC,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,EAAE,GAAG,EAAE,SAAS,aAAa,eAAe,gBAAgB,OAAO,2CAA2C,YAAY,CAAC,EAAE,WAAW,eAAe,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,oFAAoF,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,OAAO,wCAAwC,YAAY,CAAC,EAAE,WAAW,uCAAuC,GAAG,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,qCAAqC,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,EAAE,GAAG,EAAE,SAAS,aAAa,eAAe,oBAAoB,OAAO,6CAA6C,YAAY,CAAC,EAAE,WAAW,mBAAmB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,wFAAwF,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,OAAO,4CAA4C,YAAY,CAAC,EAAE,WAAW,uCAAuC,GAAG,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,qCAAqC,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,EAAE,GAAG,EAAE,SAAS,aAAa,eAAe,cAAc,OAAO,iDAAiD,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,gFAAgF,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,OAAO,oCAAoC,YAAY,CAAC,EAAE,WAAW,uCAAuC,GAAG,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,qCAAqC,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,EAAE,GAAG,EAAE,SAAS,aAAa,eAAe,kBAAkB,OAAO,yCAAyC,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,wGAAwG,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,eAAe,gCAAgC,OAAO,kCAAkC,QAAQ,oCAAoC,GAAG,EAAE,SAAS,6FAA6F,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,OAAO,cAAc,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,SAAS,QAAQ,gCAAgC,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,SAAS,8DAA8D,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,eAAe,6BAA6B,OAAO,sBAAsB,QAAQ,+BAA+B,GAAG,EAAE,SAAS,iDAAiD,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,eAAe,6BAA6B,OAAO,gCAAgC,QAAQ,+BAA+B,GAAG,EAAE,SAAS,8BAA8B,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,eAAe,6BAA6B,OAAO,wBAAwB,QAAQ,6BAA6B,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,SAAS,mBAAmB,QAAQ,iCAAiC,CAAC,EAAE,GAAG,EAAE,SAAS,qCAAqC,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,eAAe,wCAAwC,OAAO,sBAAsB,QAAQ,+BAA+B,GAAG,EAAE,SAAS,2DAA2D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,WAAW,2EAA2E,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,QAAQ,iCAAiC,YAAY,CAAC,EAAE,WAAW,uCAAuC,GAAG,EAAE,SAAS,6DAA6D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,0CAA0C,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,6CAA6C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,0CAA0C,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,SAAS,wCAAwC,QAAQ,+BAA+B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,WAAW,oKAAoK,SAAS,iCAAiC,QAAQ,qCAAqC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,SAAS,+BAA+B,QAAQ,mCAAmC,GAAG,EAAE,SAAS,0SAA0S,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,eAAe,oEAAoE,OAAO,kDAAkD,QAAQ,wCAAwC,YAAY,CAAC,EAAE,SAAS,cAAc,QAAQ,uCAAuC,GAAG,EAAE,SAAS,YAAY,QAAQ,yCAAyC,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,gDAAgD,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,eAAe,oEAAoE,OAAO,iCAAiC,QAAQ,wCAAwC,YAAY,CAAC,EAAE,SAAS,cAAc,QAAQ,uCAAuC,GAAG,EAAE,SAAS,YAAY,QAAQ,yCAAyC,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,iIAAiI,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,eAAe,uCAAuC,OAAO,wCAAwC,QAAQ,2CAA2C,YAAY,CAAC,EAAE,SAAS,cAAc,QAAQ,mCAAmC,GAAG,EAAE,SAAS,YAAY,QAAQ,sCAAsC,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,6DAA6D,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,OAAO,kCAAkC,QAAQ,wCAAwC,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,oCAAoC,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,OAAO,0CAA0C,QAAQ,wCAAwC,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,8BAA8B,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,OAAO,wBAAwB,QAAQ,yCAAyC,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,oCAAoC,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,OAAO,kCAAkC,QAAQ,wCAAwC,GAAG,EAAE,SAAS,iCAAiC,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,eAAe,yCAAyC,OAAO,2BAA2B,YAAY,CAAC,EAAE,WAAW,mCAAmC,CAAC,EAAE,GAAG,EAAE,SAAS,mCAAmC,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,OAAO,kCAAkC,QAAQ,2CAA2C,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,iCAAiC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,sGAAsG,GAAG,EAAE,SAAS,+CAA+C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,eAAe,+BAA+B,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,8CAA8C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,eAAe,8BAA8B,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,qBAAqB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,eAAe,4BAA4B,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,uBAAuB,YAAY,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,WAAW,wQAAkR,eAAe,8BAA8B,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,uBAAuB,YAAY,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,eAAe,4BAA4B,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,uBAAuB,YAAY,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,eAAe,2BAA2B,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,iBAAiB,QAAQ,wBAAwB,GAAG,EAAE,SAAS,2QAA2Q,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,wDAAwD,GAAG,KAAK,EAAE,QAAQ,sDAAsD,GAAG,KAAK,EAAE,QAAQ,wDAAwD,GAAG,KAAK,EAAE,QAAQ,sDAAsD,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,QAAQ,uBAAuB,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,SAAS,WAAW,GAAG,EAAE,SAAS,oCAAoC,QAAQ,0CAA0C,CAAC,EAAE,GAAG,EAAE,SAAS,yBAAyB,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,QAAQ,uBAAuB,YAAY,CAAC,EAAE,SAAS,mCAAmC,QAAQ,0CAA0C,CAAC,EAAE,GAAG,EAAE,SAAS,mCAAmC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,SAAS,2CAA2C,QAAQ,uCAAuC,CAAC,EAAE,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,SAAS,+DAA+D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,eAAe,yBAAyB,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,SAAS,2EAA2E,QAAQ,2BAA2B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,MAAM,EAAE,QAAQ,wBAAwB,GAAG,MAAM,EAAE,QAAQ,oCAAoC,EAAE,GAAG,SAAS,2HAA2H,QAAQ,2BAA2B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,SAAS,iGAAiG,QAAQ,2BAA2B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,iBAAiB,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,iBAAiB,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,SAAS,sJAAsJ,QAAQ,2BAA2B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,gBAAgB,YAAY,CAAC,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,gBAAgB,YAAY,CAAC,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,SAAS,sGAAsG,QAAQ,2BAA2B,GAAG,EAAE,SAAS,8DAA8D,QAAQ,+BAA+B,GAAG,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,QAAQ,oEAAoE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,UAAU,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,UAAU,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,QAAQ,oEAAoE,YAAY,CAAC,EAAE,SAAS,WAAW,QAAQ,kCAAkC,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,oDAAoD,YAAY,CAAC,EAAE,SAAS,WAAW,QAAQ,kCAAkC,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,QAAQ,oEAAoE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,SAAS,k7BAAk7B,QAAQ,2BAA2B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qDAAqD,GAAG,KAAK,EAAE,QAAQ,mDAAmD,EAAE,GAAG,SAAS,wBAAwB,QAAQ,6BAA6B,GAAG,EAAE,WAAW,WAAW,CAAC,GAAG,cAAc,EAAE,iBAAiB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wDAAwD,GAAG,KAAK,EAAE,QAAQ,sDAAsD,GAAG,KAAK,EAAE,QAAQ,wDAAwD,GAAG,KAAK,EAAE,QAAQ,sDAAsD,GAAG,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,EAAE,GAAG,SAAS,sGAAsG,CAAC,EAAE,GAAG,uBAAuB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,wDAAwD,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,sDAAsD,GAAG,KAAK,EAAE,QAAQ,+CAA+C,GAAG,MAAM,EAAE,QAAQ,oCAAoC,GAAG,MAAM,EAAE,QAAQ,6CAA6C,EAAE,GAAG,SAAS,+FAA+F,GAAG,oBAAoB,EAAE,SAAS,2CAA2C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,QAAQ,+BAA+B,YAAY,CAAC,EAAE,SAAS,2CAA2C,QAAQ,4CAA4C,CAAC,EAAE,GAAG,0BAA0B,EAAE,SAAS,UAAU,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wDAAwD,EAAE,GAAG,eAAe,qCAAqC,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,sDAAsD,EAAE,GAAG,QAAQ,iCAAiC,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,uCAAuC,EAAE,SAAS,UAAU,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wDAAwD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,sDAAsD,EAAE,GAAG,QAAQ,iCAAiC,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wDAAwD,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,sDAAsD,EAAE,GAAG,SAAS,wBAAwB,QAAQ,gCAAgC,CAAC,EAAE,EAAE,GAAG,aAAa,kBAAkB,iBAAiB,CAAC,OAAO,OAAO,WAAW,QAAQ,OAAO,QAAQ,OAAO,SAAS,QAAQ,cAAc,cAAc,UAAU,QAAQ,QAAQ,SAAS,SAAS,EAAE,CAAC;AAC5i2C,IAAI,QAAQ;AAAA,EACV,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACF;", "names": []}