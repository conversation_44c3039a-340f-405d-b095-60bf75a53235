{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/imba.mjs"], "sourcesContent": ["import typescript from './typescript.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"Imba\", \"fileTypes\": [\"imba\", \"imba2\"], \"name\": \"imba\", \"patterns\": [{ \"include\": \"#root\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.imba\" } }, \"match\": \"\\\\A(#!).*(?=$)\", \"name\": \"comment.line.shebang.imba\" }], \"repository\": { \"array-literal\": { \"begin\": \"\\\\s*(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.brace.square.imba\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.square.imba\" } }, \"name\": \"meta.array.literal.imba\", \"patterns\": [{ \"include\": \"#expr\" }, { \"include\": \"#punctuation-comma\" }] }, \"block\": { \"patterns\": [{ \"include\": \"#style-declaration\" }, { \"include\": \"#mixin-declaration\" }, { \"include\": \"#object-keys\" }, { \"include\": \"#generics-literal\" }, { \"include\": \"#tag-literal\" }, { \"include\": \"#regex\" }, { \"include\": \"#keywords\" }, { \"include\": \"#comment\" }, { \"include\": \"#literal\" }, { \"include\": \"#plain-identifiers\" }, { \"include\": \"#plain-accessors\" }, { \"include\": \"#pairs\" }, { \"include\": \"#invalid-indentation\" }] }, \"boolean-literal\": { \"patterns\": [{ \"match\": \"(?<![_$[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(true|yes)(?![\\\\?_\\\\-$[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"constant.language.boolean.true.imba\" }, { \"match\": \"(?<![_$[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(false|no)(?![\\\\?_\\\\-$[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"constant.language.boolean.false.imba\" }] }, \"brackets\": { \"patterns\": [{ \"begin\": \"{\", \"end\": \"}|(?=\\\\*/)\", \"patterns\": [{ \"include\": \"#brackets\" }] }, { \"begin\": \"\\\\[\", \"end\": \"\\\\]|(?=\\\\*/)\", \"patterns\": [{ \"include\": \"#brackets\" }] }] }, \"comment\": { \"patterns\": [{ \"begin\": \"/\\\\*\\\\*(?!/)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.imba\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.imba\" } }, \"name\": \"comment.block.documentation.imba\", \"patterns\": [{ \"include\": \"#docblock\" }] }, { \"begin\": \"(/\\\\*)(?:\\\\s*((@)internal)(?=\\\\s|(\\\\*/)))?\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.imba\" }, \"2\": { \"name\": \"storage.type.internaldeclaration.imba\" }, \"3\": { \"name\": \"punctuation.decorator.internaldeclaration.imba\" } }, \"end\": \"\\\\*/\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.imba\" } }, \"name\": \"comment.block.imba\" }, { \"begin\": \"(### \\\\@ts(?=\\\\s|$))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.imba\" } }, \"contentName\": \"source.ts.embedded.imba\", \"end\": \"###\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.imba\" } }, \"name\": \"ts.block.imba\", \"patterns\": [{ \"include\": \"source.ts\" }] }, { \"begin\": \"(###)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.imba\" } }, \"end\": \"###(?:[ \\\\t]*\\\\n)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.imba\" } }, \"name\": \"comment.block.imba\" }, { \"begin\": \"(^[ \\\\t]+)?((//|\\\\#\\\\s)(?:\\\\s*((@)internal)(?=\\\\s|$))?)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.imba\" }, \"2\": { \"name\": \"comment.line.double-slash.imba\" }, \"3\": { \"name\": \"punctuation.definition.comment.imba\" }, \"4\": { \"name\": \"storage.type.internaldeclaration.imba\" }, \"5\": { \"name\": \"punctuation.decorator.internaldeclaration.imba\" } }, \"contentName\": \"comment.line.double-slash.imba\", \"end\": \"(?=$)\" }] }, \"css-color-keywords\": { \"patterns\": [{ \"match\": \"(?i)(?<![\\\\w-])(aqua|black|blue|fuchsia|gray|green|lime|maroon|navy|olive|orange|purple|red|silver|teal|white|yellow)(?![\\\\w-])\", \"name\": \"support.constant.color.w3c-standard-color-name.css\" }, { \"match\": \"(?xi) (?<![\\\\w-])\\n(aliceblue|antiquewhite|aquamarine|azure|beige|bisque|blanchedalmond|blueviolet|brown|burlywood\\n|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan\\n|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange\\n|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise\\n|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen\\n|gainsboro|ghostwhite|gold|goldenrod|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki\\n|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow\\n|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray\\n|lightslategrey|lightsteelblue|lightyellow|limegreen|linen|magenta|mediumaquamarine|mediumblue\\n|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise\\n|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|oldlace|olivedrab|orangered\\n|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum\\n|powderblue|rebeccapurple|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell\\n|sienna|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|thistle|tomato\\n|transparent|turquoise|violet|wheat|whitesmoke|yellowgreen)\\n(?![\\\\w-])\", \"name\": \"support.constant.color.w3c-extended-color-name.css\" }, { \"match\": \"(?i)(?<![\\\\w-])currentColor(?![\\\\w-])\", \"name\": \"support.constant.color.current.css\" }] }, \"css-combinators\": { \"patterns\": [{ \"match\": \">>>|>>|>|\\\\+|~\", \"name\": \"punctuation.separator.combinator.css\" }, { \"match\": \"&\", \"name\": \"keyword.other.parent-selector.css\" }] }, \"css-commas\": { \"match\": \",\", \"name\": \"punctuation.separator.list.comma.css\" }, \"css-comment\": { \"patterns\": [{ \"match\": \"\\\\#(\\\\s.+)?(\\\\n|$)\", \"name\": \"comment.line.imba\" }, { \"match\": \"(^\\\\t+)(\\\\#(\\\\s.+)?(\\\\n|$))\", \"name\": \"comment.line.imba\" }] }, \"css-escapes\": { \"patterns\": [{ \"match\": \"\\\\\\\\[0-9a-fA-F]{1,6}\", \"name\": \"constant.character.escape.codepoint.css\" }, { \"begin\": \"\\\\\\\\$\\\\s*\", \"end\": \"^(?<!\\\\G)\", \"name\": \"constant.character.escape.newline.css\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.css\" }] }, \"css-functions\": { \"patterns\": [{ \"begin\": \"(?i)(?<![\\\\w-])(calc)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.calc.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.calc.css\", \"patterns\": [{ \"match\": \"[*/]|(?<=\\\\s|^)[-+](?=\\\\s|$)\", \"name\": \"keyword.operator.arithmetic.css\" }, { \"include\": \"#css-property-values\" }] }, { \"begin\": \"(?i)(?<![\\\\w-])(rgba?|hsla?)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.misc.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.color.css\", \"patterns\": [{ \"include\": \"#css-property-values\" }] }, { \"begin\": \"(?xi) (?<![\\\\w-])\\n(\\n(?:-webkit-|-moz-|-o-)?\\n(?:repeating-)?\\n(?:linear|radial|conic)\\n-gradient\\n)\\n(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.gradient.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.gradient.css\", \"patterns\": [{ \"match\": \"(?i)(?<![\\\\w-])(from|to|at)(?![\\\\w-])\", \"name\": \"keyword.operator.gradient.css\" }, { \"include\": \"#css-property-values\" }] }, { \"begin\": \"(?i)(?<![\\\\w-])(-webkit-gradient)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"invalid.deprecated.gradient.function.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.gradient.invalid.deprecated.gradient.css\", \"patterns\": [{ \"begin\": \"(?i)(?<![\\\\w-])(from|to|color-stop)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"invalid.deprecated.function.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"patterns\": [{ \"include\": \"#css-property-values\" }] }, { \"include\": \"#css-property-values\" }] }, { \"begin\": \"(?xi) (?<![\\\\w-])\\n(annotation|attr|blur|brightness|character-variant|contrast|counters?\\n|cross-fade|drop-shadow|element|fit-content|format|grayscale|hue-rotate\\n|image-set|invert|local|minmax|opacity|ornaments|repeat|saturate|sepia\\n|styleset|stylistic|swash|symbols)\\n(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.misc.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.misc.css\", \"patterns\": [{ \"match\": `(?i)(?<=[,\\\\s\"]|\\\\*/|^)\\\\d+x(?=[\\\\s,\"')]|/\\\\*|$)`, \"name\": \"constant.numeric.other.density.css\" }, { \"include\": \"#css-property-values\" }, { \"match\": `[^'\"),\\\\s]+`, \"name\": \"variable.parameter.misc.css\" }] }, { \"begin\": \"(?i)(?<![\\\\w-])(circle|ellipse|inset|polygon|rect)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.shape.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.shape.css\", \"patterns\": [{ \"match\": \"(?i)(?<=\\\\s|^|\\\\*/)(at|round)(?=\\\\s|/\\\\*|$)\", \"name\": \"keyword.operator.shape.css\" }, { \"include\": \"#css-property-values\" }] }, { \"begin\": \"(?i)(?<![\\\\w-])(cubic-bezier|steps)(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.timing-function.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"name\": \"meta.function.timing-function.css\", \"patterns\": [{ \"match\": \"(?i)(?<![\\\\w-])(start|end)(?=\\\\s*\\\\)|$)\", \"name\": \"support.constant.step-direction.css\" }, { \"include\": \"#css-property-values\" }] }, { \"begin\": \"(?xi) (?<![\\\\w-])\\n( (?:translate|scale|rotate)(?:[XYZ]|3D)?\\n| matrix(?:3D)?\\n| skew[XY]?\\n| perspective\\n)\\n(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.transform.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.function.end.bracket.round.css\" } }, \"patterns\": [{ \"include\": \"#css-property-values\" }] }] }, \"css-numeric-values\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.constant.css\" } }, \"match\": \"(#)(?:[0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})\\\\b\", \"name\": \"constant.other.color.rgb-value.hex.css\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.unit.percentage.css\" }, \"2\": { \"name\": \"keyword.other.unit.${2:/downcase}.css\" } }, \"match\": \"(?xi) (?<![\\\\w-])\\n[-+]?\\n\\n(?:\\n[0-9]+ (?:\\\\.[0-9]+)?\\n| \\\\.[0-9]+\\n)\\n\\n(?:\\n(?<=[0-9])\\nE\\n[-+]?\\n[0-9]+\\n)?\\n\\n(?:\\n(%)\\n| ( deg|grad|rad|turn\\n| Hz|kHz\\n| ch|cm|em|ex|fr|in|mm|mozmm|\\npc|pt|px|q|rem|vh|vmax|vmin|\\nvw\\n| dpi|dpcm|dppx\\n| s|ms\\n)\\n\\\\b\\n)?\", \"name\": \"constant.numeric.css\" }] }, \"css-property-values\": { \"patterns\": [{ \"include\": \"#css-commas\" }, { \"include\": \"#css-escapes\" }, { \"include\": \"#css-functions\" }, { \"include\": \"#css-numeric-values\" }, { \"include\": \"#css-size-keywords\" }, { \"include\": \"#css-color-keywords\" }, { \"include\": \"#string\" }, { \"match\": \"!\\\\s*important(?![\\\\w-])\", \"name\": \"keyword.other.important.css\" }] }, \"css-pseudo-classes\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" }, \"2\": { \"name\": \"invalid.illegal.colon.css\" } }, \"match\": \"(?xi)\\n(:)(:*)\\n(?: active|any-link|checked|default|defined|disabled|empty|enabled|first\\n| (?:first|last|only)-(?:child|of-type)|focus|focus-visible|focus-within\\n| fullscreen|host|hover|in-range|indeterminate|invalid|left|link\\n| optional|out-of-range|placeholder-shown|read-only|read-write\\n| required|right|root|scope|target|unresolved\\n| valid|visited\\n)(?![\\\\w-]|\\\\s*[;}])\", \"name\": \"entity.other.attribute-name.pseudo-class.css\" }, \"css-pseudo-elements\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.entity.css\" }, \"2\": { \"name\": \"punctuation.definition.entity.css\" } }, \"match\": \"(?xi)\\n(?:\\n(::?)\\n(?: after\\n| before\\n| first-letter\\n| first-line\\n| (?:-(?:ah|apple|atsc|epub|hp|khtml|moz\\n|ms|o|rim|ro|tc|wap|webkit|xv)\\n| (?:mso|prince))\\n-[a-z-]+\\n)\\n|\\n(::)\\n(?: backdrop\\n| content\\n| grammar-error\\n| marker\\n| placeholder\\n| selection\\n| shadow\\n| spelling-error\\n)\\n)\\n(?![\\\\w-]|\\\\s*[;}])\", \"name\": \"entity.other.attribute-name.pseudo-element.css\" }, \"css-selector\": { \"begin\": \"(?<=css\\\\s)(?!(?:[\\\\^\\\\@\\\\.\\\\%\\\\w\\\\$\\\\!\\\\-]+)(?:\\\\s*[\\\\:\\\\=])[^\\\\:])\", \"end\": \"(\\\\s*(?=(?:[\\\\^\\\\@\\\\.\\\\%\\\\w\\\\$\\\\!\\\\-]+)(?:\\\\s*[\\\\:\\\\=])[^\\\\:])|\\\\s*$|(?=\\\\s+\\\\#\\\\s))\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.separator.sel-properties.css\" } }, \"name\": \"meta.selector.css\", \"patterns\": [{ \"include\": \"#css-selector-innards\" }] }, \"css-selector-innards\": { \"patterns\": [{ \"include\": \"#css-commas\" }, { \"include\": \"#css-escapes\" }, { \"include\": \"#css-combinators\" }, { \"match\": \"(\\\\%[\\\\w\\\\-]+)\", \"name\": \"entity.other.attribute-name.mixin.css\" }, { \"match\": \"\\\\*\", \"name\": \"entity.name.tag.wildcard.css\" }, { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.entity.begin.bracket.square.css\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.entity.end.bracket.square.css\" } }, \"name\": \"meta.attribute-selector.css\", \"patterns\": [{ \"include\": \"#string\" }, { \"captures\": { \"1\": { \"name\": \"storage.modifier.ignore-case.css\" } }, \"match\": `(?<=[\"'\\\\s]|^|\\\\*/)\\\\s*([iI])\\\\s*(?=[\\\\s\\\\]]|/\\\\*|$)` }, { \"captures\": { \"1\": { \"name\": \"string.unquoted.attribute-value.css\" } }, \"match\": `(?x)(?<==)\\\\s*((?!/\\\\*)(?:[^\\\\\\\\\"'\\\\s\\\\]]|\\\\\\\\.)+)` }, { \"include\": \"#css-escapes\" }, { \"match\": \"[~|^$*]?=\", \"name\": \"keyword.operator.pattern.css\" }, { \"match\": \"\\\\|\", \"name\": \"punctuation.separator.css\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.namespace-prefix.css\" } }, \"match\": \"(?x)\\n# Qualified namespace prefix\\n( -?(?!\\\\d)(?:[\\\\w-]|[^\\\\\\\\x00-\\\\\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))+\\n| \\\\*\\n)\\n# Lookahead to ensure there's a valid identifier ahead\\n(?=\\n\\\\| (?!\\\\s|=|$|\\\\])\\n(?: -?(?!\\\\d)\\n|   [\\\\\\\\\\\\w-]\\n|   [^\\\\\\\\x00-\\\\\\\\x7F]\\n)\\n)\" }, { \"captures\": { \"1\": { \"name\": \"entity.other.attribute-name.css\" } }, \"match\": \"(?x)\\n(-?(?!\\\\d)(?>[\\\\w-]|[^\\\\\\\\x00-\\\\\\\\x7F]|\\\\\\\\(?:[0-9a-fA-F]{1,6}|.))+)\\n\\\\s*\\n(?=[~|^\\\\]$*=]|/\\\\*)\" }] }, { \"include\": \"#css-pseudo-classes\" }, { \"include\": \"#css-pseudo-elements\" }, { \"include\": \"#css-mixin\" }] }, \"css-size-keywords\": { \"patterns\": [{ \"match\": \"(x+s|sm-|md-|lg-|sm|md|lg|x+l|hg|x+h)(?![\\\\w-])\", \"name\": \"support.constant.size.property-value.css\" }] }, \"curly-braces\": { \"begin\": \"\\\\s*(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.brace.curly.imba\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.curly.imba\" } }, \"patterns\": [{ \"include\": \"#expr\" }, { \"include\": \"#punctuation-comma\" }] }, \"decorator\": { \"begin\": \"(?<![_$[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))\\\\@(?!\\\\@)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.decorator.imba\" } }, \"end\": \"(?=\\\\s)\", \"name\": \"meta.decorator.imba\", \"patterns\": [{ \"include\": \"#expr\" }] }, \"directives\": { \"begin\": \"^(///)\\\\s*(?=<(reference|amd-dependency|amd-module)(\\\\s+(path|types|no-default-lib|lib|name)\\\\s*=\\\\s*((\\\\'([^\\\\'\\\\\\\\]|\\\\\\\\.)*\\\\')|(\\\\\\\"([^\\\\\\\"\\\\\\\\]|\\\\\\\\.)*\\\\\\\")|(\\\\`([^\\\\`\\\\\\\\]|\\\\\\\\.)*\\\\`)))+\\\\s*/>\\\\s*$)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.imba\" } }, \"end\": \"(?=$)\", \"name\": \"comment.line.triple-slash.directive.imba\", \"patterns\": [{ \"begin\": \"(<)(reference|amd-dependency|amd-module)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.directive.imba\" }, \"2\": { \"name\": \"entity.name.tag.directive.imba\" } }, \"end\": \"/>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.directive.imba\" } }, \"name\": \"meta.tag.imba\", \"patterns\": [{ \"match\": \"path|types|no-default-lib|lib|name\", \"name\": \"entity.other.attribute-name.directive.imba\" }, { \"match\": \"=\", \"name\": \"keyword.operator.assignment.imba\" }, { \"include\": \"#string\" }] }] }, \"docblock\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"constant.language.access-type.jsdoc\" } }, \"match\": \"(?x)\\n((@)(?:access|api))\\n\\\\s+\\n(private|protected|public)\\n\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"entity.name.type.instance.jsdoc\" }, \"4\": { \"name\": \"punctuation.definition.bracket.angle.begin.jsdoc\" }, \"5\": { \"name\": \"constant.other.email.link.underline.jsdoc\" }, \"6\": { \"name\": \"punctuation.definition.bracket.angle.end.jsdoc\" } }, \"match\": \"(?x)\\n((@)author)\\n\\\\s+\\n(\\n[^@\\\\s<>*/]\\n(?:[^@<>*/]|\\\\*[^/])*\\n)\\n(?:\\n\\\\s*\\n(<)\\n([^>\\\\s]+)\\n(>)\\n)?\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"entity.name.type.instance.jsdoc\" }, \"4\": { \"name\": \"keyword.operator.control.jsdoc\" }, \"5\": { \"name\": \"entity.name.type.instance.jsdoc\" } }, \"match\": \"(?x)\\n((@)borrows) \\\\s+\\n((?:[^@\\\\s*/]|\\\\*[^/])+)\\n\\\\s+ (as) \\\\s+\\n((?:[^@\\\\s*/]|\\\\*[^/])+)\" }, { \"begin\": \"((@)example)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" } }, \"end\": \"(?=@|\\\\*/)\", \"name\": \"meta.example.jsdoc\", \"patterns\": [{ \"match\": \"^\\\\s\\\\*\\\\s+\" }, { \"begin\": \"\\\\G(<)caption(>)\", \"beginCaptures\": { \"0\": { \"name\": \"entity.name.tag.inline.jsdoc\" }, \"1\": { \"name\": \"punctuation.definition.bracket.angle.begin.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.bracket.angle.end.jsdoc\" } }, \"contentName\": \"constant.other.description.jsdoc\", \"end\": \"(</)caption(>)|(?=\\\\*/)\", \"endCaptures\": { \"0\": { \"name\": \"entity.name.tag.inline.jsdoc\" }, \"1\": { \"name\": \"punctuation.definition.bracket.angle.begin.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.bracket.angle.end.jsdoc\" } } }, { \"captures\": { \"0\": { \"name\": \"source.embedded.imba\" } }, \"match\": \"[^\\\\s@*](?:[^*]|\\\\*[^/])*\" }] }, { \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"constant.language.symbol-type.jsdoc\" } }, \"match\": \"(?x) ((@)kind) \\\\s+ (class|constant|event|external|file|function|member|mixin|module|namespace|typedef) \\\\b\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"variable.other.link.underline.jsdoc\" }, \"4\": { \"name\": \"entity.name.type.instance.jsdoc\" } }, \"match\": \"(?x)\\n((@)see)\\n\\\\s+\\n(?:\\n\\n(\\n(?=https?://)\\n(?:[^\\\\s*]|\\\\*[^/])+\\n)\\n|\\n\\n(\\n(?!\\n\\nhttps?://\\n|\\n\\n(?:\\\\[[^\\\\[\\\\]]*\\\\])?\\n{@(?:link|linkcode|linkplain|tutorial)\\\\b\\n)\\n\\n(?:[^@\\\\s*/]|\\\\*[^/])+\\n)\\n)\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"variable.other.jsdoc\" } }, \"match\": \"(?x)\\n((@)template)\\n\\\\s+\\n# One or more valid identifiers\\n(\\n[A-Za-z_$]\\n[\\\\w$.\\\\[\\\\]]*\\n(?:\\n\\\\s* , \\\\s*\\n[A-Za-z_$]\\n[\\\\w$.\\\\[\\\\]]*\\n)*\\n)\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"variable.other.jsdoc\" } }, \"match\": \"(?x)\\n(\\n(@)\\n(?:arg|argument|const|constant|member|namespace|param|var)\\n)\\n\\\\s+\\n(\\n[A-Za-z_$]\\n[\\\\w$.\\\\[\\\\]]*\\n)\" }, { \"begin\": \"((@)typedef)\\\\s+(?={)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" } }, \"end\": \"(?=\\\\s|\\\\*/|[^{}\\\\[\\\\]A-Za-z_$])\", \"patterns\": [{ \"include\": \"#jsdoctype\" }, { \"match\": \"(?:[^@\\\\s*/]|\\\\*[^/])+\", \"name\": \"entity.name.type.instance.jsdoc\" }] }, { \"begin\": \"((@)(?:arg|argument|const|constant|member|namespace|param|prop|property|var))\\\\s+(?={)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" } }, \"end\": \"(?=\\\\s|\\\\*/|[^{}\\\\[\\\\]A-Za-z_$])\", \"patterns\": [{ \"include\": \"#jsdoctype\" }, { \"match\": \"([A-Za-z_$][\\\\w$.\\\\[\\\\]]*)\", \"name\": \"variable.other.jsdoc\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.optional-value.begin.bracket.square.jsdoc\" }, \"2\": { \"name\": \"keyword.operator.assignment.jsdoc\" }, \"3\": { \"name\": \"source.embedded.imba\" }, \"4\": { \"name\": \"punctuation.definition.optional-value.end.bracket.square.jsdoc\" }, \"5\": { \"name\": \"invalid.illegal.syntax.jsdoc\" } }, \"match\": `(?x)\n(\\\\[)\\\\s*\n[\\\\w$]+\n(?:\n(?:\\\\[\\\\])?\n\\\\.\n[\\\\w$]+\n)*\n(?:\n\\\\s*\n(=)\n\\\\s*\n(\n\n(?>\n\"(?:(?:\\\\*(?!/))|(?:\\\\\\\\(?!\"))|[^*\\\\\\\\])*?\" |\n'(?:(?:\\\\*(?!/))|(?:\\\\\\\\(?!'))|[^*\\\\\\\\])*?' |\n\\\\[ (?:(?:\\\\*(?!/))|[^*])*? \\\\] |\n(?:(?:\\\\*(?!/))|\\\\s(?!\\\\s*\\\\])|\\\\[.*?(?:\\\\]|(?=\\\\*/))|[^*\\\\s\\\\[\\\\]])*\n)*\n)\n)?\n\\\\s*(?:(\\\\])((?:[^*\\\\s]|\\\\*[^\\\\s/])+)?|(?=\\\\*/))`, \"name\": \"variable.other.jsdoc\" }] }, { \"begin\": \"(?x)\\n(\\n(@)\\n(?:define|enum|exception|export|extends|lends|implements|modifies\\n|namespace|private|protected|returns?|suppress|this|throws|type\\n|yields?)\\n)\\n\\\\s+(?={)\", \"beginCaptures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" } }, \"end\": \"(?=\\\\s|\\\\*/|[^{}\\\\[\\\\]A-Za-z_$])\", \"patterns\": [{ \"include\": \"#jsdoctype\" }] }, { \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"entity.name.type.instance.jsdoc\" } }, \"match\": \"(?x)\\n(\\n(@)\\n(?:alias|augments|callback|constructs|emits|event|fires|exports?\\n|extends|external|function|func|host|lends|listens|interface|memberof!?\\n|method|module|mixes|mixin|name|requires|see|this|typedef|uses)\\n)\\n\\\\s+\\n(\\n(?:\\n[^{}@\\\\s*] | \\\\*[^/]\\n)+\\n)\" }, { \"begin\": `((@)(?:default(?:value)?|license|version))\\\\s+(([''\"]))`, \"beginCaptures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"variable.other.jsdoc\" }, \"4\": { \"name\": \"punctuation.definition.string.begin.jsdoc\" } }, \"contentName\": \"variable.other.jsdoc\", \"end\": \"(\\\\3)|(?=$|\\\\*/)\", \"endCaptures\": { \"0\": { \"name\": \"variable.other.jsdoc\" }, \"1\": { \"name\": \"punctuation.definition.string.end.jsdoc\" } } }, { \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" }, \"3\": { \"name\": \"variable.other.jsdoc\" } }, \"match\": \"((@)(?:default(?:value)?|license|tutorial|variation|version))\\\\s+([^\\\\s*]+)\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" } }, \"match\": \"(?x) (@) (?:abstract|access|alias|api|arg|argument|async|attribute|augments|author|beta|borrows|bubbles |callback|chainable|class|classdesc|code|config|const|constant|constructor|constructs|copyright |default|defaultvalue|define|deprecated|desc|description|dict|emits|enum|event|example|exception |exports?|extends|extension(?:_?for)?|external|externs|file|fileoverview|final|fires|for|func |function|generator|global|hideconstructor|host|ignore|implements|implicitCast|inherit[Dd]oc |inner|instance|interface|internal|kind|lends|license|listens|main|member|memberof!?|method |mixes|mixins?|modifies|module|name|namespace|noalias|nocollapse|nocompile|nosideeffects |override|overview|package|param|polymer(?:Behavior)?|preserve|private|prop|property|protected |public|read[Oo]nly|record|require[ds]|returns?|see|since|static|struct|submodule|summary |suppress|template|this|throws|todo|tutorial|type|typedef|unrestricted|uses|var|variation |version|virtual|writeOnce|yields?) \\\\b\", \"name\": \"storage.type.class.jsdoc\" }, { \"include\": \"#inline-tags\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.class.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.block.tag.jsdoc\" } }, \"match\": \"((@)(?:[_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?))(?=\\\\s+)\" }] }, \"expr\": { \"patterns\": [{ \"include\": \"#style-declaration\" }, { \"include\": \"#object-keys\" }, { \"include\": \"#generics-literal\" }, { \"include\": \"#tag-literal\" }, { \"include\": \"#regex\" }, { \"include\": \"#keywords\" }, { \"include\": \"#comment\" }, { \"include\": \"#literal\" }, { \"include\": \"#plain-identifiers\" }, { \"include\": \"#plain-accessors\" }, { \"include\": \"#pairs\" }] }, \"expression\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"meta.brace.round.imba\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.round.imba\" } }, \"patterns\": [{ \"include\": \"#expr\" }] }, { \"include\": \"#tag-literal\" }, { \"include\": \"#expressionWithoutIdentifiers\" }, { \"include\": \"#identifiers\" }, { \"include\": \"#expressionPunctuations\" }] }, \"expressionPunctuations\": { \"patterns\": [{ \"include\": \"#punctuation-comma\" }, { \"include\": \"#punctuation-accessor\" }] }, \"expressionWithoutIdentifiers\": { \"patterns\": [{ \"include\": \"#string\" }, { \"include\": \"#regex\" }, { \"include\": \"#comment\" }, { \"include\": \"#function-expression\" }, { \"include\": \"#class-expression\" }, { \"include\": \"#ternary-expression\" }, { \"include\": \"#new-expr\" }, { \"include\": \"#instanceof-expr\" }, { \"include\": \"#object-literal\" }, { \"include\": \"#expression-operators\" }, { \"include\": \"#literal\" }, { \"include\": \"#support-objects\" }] }, \"generics-literal\": { \"begin\": \"(?<=[\\\\w\\\\]\\\\)])\\\\<\", \"beginCaptures\": { \"1\": { \"name\": \"meta.generics.annotation.open.imba\" } }, \"end\": \"\\\\>\", \"endCaptures\": { \"0\": { \"name\": \"meta.generics.annotation.close.imba\" } }, \"name\": \"meta.generics.annotation.imba\", \"patterns\": [{ \"include\": \"#type-brackets\" }] }, \"global-literal\": { \"match\": \"(?<![_$[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(global)\\\\b(?!\\\\$)\", \"name\": \"variable.language.global.imba\" }, \"identifiers\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.accessor.imba\" }, \"2\": { \"name\": \"punctuation.accessor.optional.imba\" }, \"3\": { \"name\": \"entity.name.function.property.imba\" } }, \"match\": \"(?x)(?:(?:(\\\\.)|(\\\\.\\\\.(?!\\\\s*[[:digit:]]|\\\\s+)))\\\\s*)?([_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?)(?=\\\\s*={{functionOrArrowLookup}})\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.imba\" }, \"2\": { \"name\": \"punctuation.accessor.optional.imba\" }, \"3\": { \"name\": \"variable.other.constant.property.imba\" } }, \"match\": \"(?:(\\\\.)|(\\\\.\\\\.(?!\\\\s*[[:digit:]]|\\\\s+)))\\\\s*(\\\\#?[[:upper:]][_$[:digit:][:upper:]]*)(?![_$[:alnum:]])\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.imba\" }, \"2\": { \"name\": \"punctuation.accessor.optional.imba\" }, \"3\": { \"name\": \"variable.other.class.property.imba\" } }, \"match\": \"(?:(\\\\.)|(\\\\.\\\\.(?!\\\\s*[[:digit:]]|\\\\s+)))([[:upper:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\!]?)\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.imba\" }, \"2\": { \"name\": \"punctuation.accessor.optional.imba\" }, \"3\": { \"name\": \"variable.other.property.imba\" } }, \"match\": \"(?:(\\\\.)|(\\\\.\\\\.(?!\\\\s*[[:digit:]]|\\\\s+)))(\\\\#?[_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?)\" }, { \"match\": \"(for own|for|if|unless|when)\\\\b\", \"name\": \"keyword.other\" }, { \"match\": \"require\", \"name\": \"support.function.require\" }, { \"include\": \"#plain-identifiers\" }, { \"include\": \"#type-literal\" }, { \"include\": \"#generics-literal\" }] }, \"inline-css-selector\": { \"begin\": \"(^\\\\t+)(?!(?:[\\\\^\\\\@\\\\.\\\\%\\\\w\\\\$\\\\!\\\\-]+)(?:\\\\s*[\\\\:\\\\=]))\", \"end\": \"(\\\\s*(?=(?:[\\\\^\\\\@\\\\.\\\\%\\\\w\\\\$\\\\!\\\\-]+)(?:\\\\s*[\\\\:\\\\=])|\\\\)|\\\\])|\\\\s*$)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.separator.sel-properties.css\" } }, \"name\": \"meta.selector.css\", \"patterns\": [{ \"include\": \"#css-selector-innards\" }] }, \"inline-styles\": { \"patterns\": [{ \"include\": \"#style-property\" }, { \"include\": \"#css-property-values\" }, { \"include\": \"#style-expr\" }] }, \"inline-tags\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.bracket.square.begin.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.bracket.square.end.jsdoc\" } }, \"match\": \"(\\\\[)[^\\\\]]+(\\\\])(?={@(?:link|linkcode|linkplain|tutorial))\", \"name\": \"constant.other.description.jsdoc\" }, { \"begin\": \"({)((@)(?:link(?:code|plain)?|tutorial))\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.bracket.curly.begin.jsdoc\" }, \"2\": { \"name\": \"storage.type.class.jsdoc\" }, \"3\": { \"name\": \"punctuation.definition.inline.tag.jsdoc\" } }, \"end\": \"}|(?=\\\\*/)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.bracket.curly.end.jsdoc\" } }, \"name\": \"entity.name.type.instance.jsdoc\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.other.link.underline.jsdoc\" }, \"2\": { \"name\": \"punctuation.separator.pipe.jsdoc\" } }, \"match\": \"\\\\G((?=https?://)(?:[^|}\\\\s*]|\\\\*[/])+)(\\\\|)?\" }, { \"captures\": { \"1\": { \"name\": \"variable.other.description.jsdoc\" }, \"2\": { \"name\": \"punctuation.separator.pipe.jsdoc\" } }, \"match\": \"\\\\G((?:[^{}@\\\\s|*]|\\\\*[^/])+)(\\\\|)?\" }] }] }, \"invalid-indentation\": { \"patterns\": [{ \"match\": \"^[\\\\ ]+\", \"name\": \"invalid.whitespace\" }, { \"match\": \"^\\\\t+\\\\s+\", \"name\": \"invalid.whitespace\" }] }, \"jsdoctype\": { \"patterns\": [{ \"match\": \"\\\\G{(?:[^}*]|\\\\*[^/}])+$\", \"name\": \"invalid.illegal.type.jsdoc\" }, { \"begin\": \"\\\\G({)\", \"beginCaptures\": { \"0\": { \"name\": \"entity.name.type.instance.jsdoc\" }, \"1\": { \"name\": \"punctuation.definition.bracket.curly.begin.jsdoc\" } }, \"contentName\": \"entity.name.type.instance.jsdoc\", \"end\": \"((}))\\\\s*|(?=\\\\*/)\", \"endCaptures\": { \"1\": { \"name\": \"entity.name.type.instance.jsdoc\" }, \"2\": { \"name\": \"punctuation.definition.bracket.curly.end.jsdoc\" } }, \"patterns\": [{ \"include\": \"#brackets\" }] }] }, \"keywords\": { \"patterns\": [{ \"match\": \"(if|elif|else|unless|switch|when|then|do|import|export|for own|for|while|until|return|yield|try|catch|await|rescue|finally|throw|as|continue|break|extend|augment)(?![\\\\?_\\\\-$[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"keyword.control.imba\" }, { \"match\": \"(?<=export)\\\\s+(default)(?![\\\\?_\\\\-$[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"keyword.control.imba\" }, { \"match\": \"(?<=import)\\\\s+(type)(?=\\\\s+[\\\\w\\\\{\\\\$\\\\_])\", \"name\": \"keyword.control.imba\" }, { \"match\": \"(extend|global|abstract)\\\\s+(?=class|tag|abstract|mixin|interface)\", \"name\": \"keyword.control.imba\" }, { \"match\": `(?<=[\\\\*\\\\}\\\\w\\\\$])\\\\s+(from)(?=\\\\s+[\\\\\"\\\\'])`, \"name\": \"keyword.control.imba\" }, { \"match\": \"(def|get|set)(?![\\\\?_\\\\-$[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"storage.type.function.imba\" }, { \"match\": \"(protected|private)\\\\s+(?=def|get|set)\", \"name\": \"keyword.control.imba\" }, { \"match\": \"(tag|class|struct|mixin|interface)(?![\\\\?_\\\\-$[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"storage.type.class.imba\" }, { \"match\": \"(let|const|constructor)(?![\\\\?_\\\\-$[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"storage.type.imba\" }, { \"match\": \"(prop|attr)(?![\\\\?_\\\\-$[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"storage.type.imba\" }, { \"match\": \"(static)\\\\s+\", \"name\": \"storage.modifier.imba\" }, { \"match\": \"(declare)\\\\s+\", \"name\": \"storage.modifier.imba\" }, { \"include\": \"#ops\" }, { \"match\": \"(=|\\\\|\\\\|=|\\\\?\\\\?=|\\\\&\\\\&=|\\\\+=|\\\\-=|\\\\*=|\\\\^=|\\\\%=)\", \"name\": \"keyword.operator.assignment.imba\" }, { \"match\": \"(\\\\>\\\\=?|\\\\<\\\\=?)\", \"name\": \"keyword.operator.imba\" }, { \"match\": \"(of|delete|\\\\!?isa|typeof|\\\\!?in|new|\\\\!?is|isnt)(?![\\\\?_\\\\-$[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"keyword.operator.imba\" }] }, \"literal\": { \"patterns\": [{ \"include\": \"#number-with-unit-literal\" }, { \"include\": \"#numeric-literal\" }, { \"include\": \"#boolean-literal\" }, { \"include\": \"#null-literal\" }, { \"include\": \"#undefined-literal\" }, { \"include\": \"#numericConstant-literal\" }, { \"include\": \"#this-literal\" }, { \"include\": \"#global-literal\" }, { \"include\": \"#super-literal\" }, { \"include\": \"#type-literal\" }, { \"include\": \"#generics-literal\" }, { \"include\": \"#string\" }] }, \"mixin-css-selector\": { \"begin\": \"(\\\\%[\\\\w\\\\-]+)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.other.attribute-name.mixin.css\" } }, \"end\": \"(\\\\s*(?=(?:[\\\\^\\\\@\\\\.\\\\%\\\\w\\\\$\\\\!\\\\-]+)(?:\\\\s*[\\\\:\\\\=])[^\\\\:])|\\\\s*$|(?=\\\\s+\\\\#\\\\s))\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.separator.sel-properties.css\" } }, \"name\": \"meta.selector.css\", \"patterns\": [{ \"include\": \"#css-selector-innards\" }] }, \"mixin-css-selector-after\": { \"begin\": \"(?<=%[\\\\w\\\\-]+)(?!(?:[\\\\^\\\\@\\\\.\\\\%\\\\w\\\\$\\\\!\\\\-]+)(?:\\\\s*[\\\\:\\\\=])[^\\\\:])\", \"end\": \"(\\\\s*(?=(?:[\\\\^\\\\@\\\\.\\\\%\\\\w\\\\$\\\\!\\\\-]+)(?:\\\\s*[\\\\:\\\\=])[^\\\\:])|\\\\s*$|(?=\\\\s+\\\\#\\\\s))\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.separator.sel-properties.css\" } }, \"name\": \"meta.selector.css\", \"patterns\": [{ \"include\": \"#css-selector-innards\" }] }, \"mixin-declaration\": { \"begin\": \"^(\\\\t*)(\\\\%[\\\\w\\\\-]+)\", \"beginCaptures\": { \"2\": { \"name\": \"entity.other.attribute-name.mixin.css\" } }, \"end\": \"^(?!(\\\\1\\\\t|\\\\s*$))\", \"name\": \"meta.style.imba\", \"patterns\": [{ \"include\": \"#mixin-css-selector-after\" }, { \"include\": \"#css-comment\" }, { \"include\": \"#nested-css-selector\" }, { \"include\": \"#inline-styles\" }] }, \"nested-css-selector\": { \"begin\": \"(^\\\\t+)(?!(?:[\\\\^\\\\@\\\\.\\\\%\\\\w\\\\$\\\\!\\\\-]+)(?:\\\\s*[\\\\:\\\\=])[^\\\\:])\", \"end\": \"(\\\\s*(?=(?:[\\\\^\\\\@\\\\.\\\\%\\\\w\\\\$\\\\!\\\\-]+)(?:\\\\s*[\\\\:\\\\=])[^\\\\:])|\\\\s*$|(?=\\\\s+\\\\#\\\\s))\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.separator.sel-properties.css\" } }, \"name\": \"meta.selector.css\", \"patterns\": [{ \"include\": \"#css-selector-innards\" }] }, \"nested-style-declaration\": { \"begin\": \"^(\\\\t+)(?=[\\\\n^]*\\\\&)\", \"end\": \"^(?!(\\\\1\\\\t|\\\\s*$))\", \"name\": \"meta.style.imba\", \"patterns\": [{ \"include\": \"#nested-css-selector\" }, { \"include\": \"#inline-styles\" }] }, \"null-literal\": { \"match\": \"(?<![_$[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))null(?![\\\\?_\\\\-$[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"constant.language.null.imba\" }, \"number-with-unit-literal\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.numeric.imba\" }, \"2\": { \"name\": \"keyword.other.unit.imba\" } }, \"match\": \"([0-9]+)([a-z]+|\\\\%)\" }, { \"captures\": { \"1\": { \"name\": \"constant.numeric.decimal.imba\" }, \"2\": { \"name\": \"keyword.other.unit.imba\" } }, \"match\": \"([0-9]*\\\\.[0-9]+(?:[eE][\\\\-+]?[0-9]+)?)([a-z]+|\\\\%)\" }] }, \"numeric-literal\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"storage.type.numeric.bigint.imba\" } }, \"match\": \"\\\\b(?<!\\\\$)0(?:x|X)[0-9a-fA-F][0-9a-fA-F_]*(n)?\\\\b(?!\\\\$)\", \"name\": \"constant.numeric.hex.imba\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.numeric.bigint.imba\" } }, \"match\": \"\\\\b(?<!\\\\$)0(?:b|B)[01][01_]*(n)?\\\\b(?!\\\\$)\", \"name\": \"constant.numeric.binary.imba\" }, { \"captures\": { \"1\": { \"name\": \"storage.type.numeric.bigint.imba\" } }, \"match\": \"\\\\b(?<!\\\\$)0(?:o|O)?[0-7][0-7_]*(n)?\\\\b(?!\\\\$)\", \"name\": \"constant.numeric.octal.imba\" }, { \"captures\": { \"0\": { \"name\": \"constant.numeric.decimal.imba\" }, \"1\": { \"name\": \"meta.delimiter.decimal.period.imba\" }, \"2\": { \"name\": \"storage.type.numeric.bigint.imba\" }, \"3\": { \"name\": \"meta.delimiter.decimal.period.imba\" }, \"4\": { \"name\": \"storage.type.numeric.bigint.imba\" }, \"5\": { \"name\": \"meta.delimiter.decimal.period.imba\" }, \"6\": { \"name\": \"storage.type.numeric.bigint.imba\" }, \"7\": { \"name\": \"storage.type.numeric.bigint.imba\" }, \"8\": { \"name\": \"meta.delimiter.decimal.period.imba\" }, \"9\": { \"name\": \"storage.type.numeric.bigint.imba\" }, \"10\": { \"name\": \"meta.delimiter.decimal.period.imba\" }, \"11\": { \"name\": \"storage.type.numeric.bigint.imba\" }, \"12\": { \"name\": \"meta.delimiter.decimal.period.imba\" }, \"13\": { \"name\": \"storage.type.numeric.bigint.imba\" }, \"14\": { \"name\": \"storage.type.numeric.bigint.imba\" } }, \"match\": \"(?x)\\n(?<!\\\\$)(?:\\n(?:\\\\b[0-9][0-9_]*(\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\b)|\\n(?:\\\\b[0-9][0-9_]*(\\\\.)[eE][+-]?[0-9][0-9_]*(n)?\\\\b)|\\n(?:\\\\B(\\\\.)[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\b)|\\n(?:\\\\b[0-9][0-9_]*[eE][+-]?[0-9][0-9_]*(n)?\\\\b)|\\n(?:\\\\b[0-9][0-9_]*(\\\\.)[0-9][0-9_]*(n)?\\\\b)|\\n(?:\\\\b[0-9][0-9_]*(\\\\.)(n)?\\\\B)|\\n(?:\\\\B(\\\\.)[0-9][0-9_]*(n)?\\\\b)|\\n(?:\\\\b[0-9][0-9_]*(n)?\\\\b)\\n)(?!\\\\$)\" }] }, \"numericConstant-literal\": { \"patterns\": [{ \"match\": \"(?<![_$[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))NaN(?![\\\\?_\\\\-$[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"constant.language.nan.imba\" }, { \"match\": \"(?<![_$[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))Infinity(?![\\\\?_\\\\-$[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"constant.language.infinity.imba\" }] }, \"object-keys\": { \"patterns\": [{ \"match\": \"[_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?\\\\:\", \"name\": \"meta.object-literal.key\" }] }, \"ops\": { \"patterns\": [{ \"match\": \"\\\\.\\\\.\\\\.\", \"name\": \"keyword.operator.spread.imba\" }, { \"match\": \"\\\\*=|(?<!\\\\()/=|%=|\\\\+=|\\\\-=|\\\\?=|\\\\?\\\\?=|=\\\\?\", \"name\": \"keyword.operator.assignment.compound.imba\" }, { \"match\": \"\\\\^=\\\\?|\\\\|=\\\\?|\\\\~=\\\\?|\\\\&=|\\\\^=|<<=|>>=|>>>=|\\\\|=\", \"name\": \"keyword.operator.assignment.compound.bitwise.imba\" }, { \"match\": \"<<|>>>|>>\", \"name\": \"keyword.operator.bitwise.shift.imba\" }, { \"match\": \"===|!==|==|!=|~=\", \"name\": \"keyword.operator.comparison.imba\" }, { \"match\": \"<=|>=|<>|<|>\", \"name\": \"keyword.operator.relational.imba\" }, { \"captures\": { \"1\": { \"name\": \"keyword.operator.logical.imba\" }, \"2\": { \"name\": \"keyword.operator.arithmetic.imba\" } }, \"match\": \"(\\\\!)\\\\s*(/)(?![/*])\" }, { \"match\": \"\\\\!|&&|\\\\|\\\\||\\\\?\\\\?|or\\\\b(?=\\\\s|$)|and\\\\b(?=\\\\s|$)|\\\\@\\\\b(?=\\\\s|$)\", \"name\": \"keyword.operator.logical.imba\" }, { \"match\": \"\\\\?(?=\\\\s|$)\", \"name\": \"keyword.operator.bitwise.imba\" }, { \"match\": \"\\\\&|~|\\\\^|\\\\|\", \"name\": \"keyword.operator.ternary.imba\" }, { \"match\": \"\\\\=\", \"name\": \"keyword.operator.assignment.imba\" }, { \"match\": \"--\", \"name\": \"keyword.operator.decrement.imba\" }, { \"match\": \"\\\\+\\\\+\", \"name\": \"keyword.operator.increment.imba\" }, { \"match\": \"%|\\\\*|/|-|\\\\+\", \"name\": \"keyword.operator.arithmetic.imba\" }] }, \"pairs\": { \"patterns\": [{ \"include\": \"#curly-braces\" }, { \"include\": \"#square-braces\" }, { \"include\": \"#round-braces\" }] }, \"plain-accessors\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.accessor.imba\" }, \"2\": { \"name\": \"variable.other.property.imba\" } }, \"match\": \"(\\\\.\\\\.?)([_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?)\" }] }, \"plain-identifiers\": { \"patterns\": [{ \"match\": \"([[:upper:]][_$[:digit:][:upper:]]*)(?![_$[:alnum:]])\", \"name\": \"variable.other.constant.imba\" }, { \"match\": \"[[:upper:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\!]?\", \"name\": \"variable.other.class.imba\" }, { \"match\": \"\\\\$\\\\d+\", \"name\": \"variable.special.imba\" }, { \"match\": \"\\\\$[_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?\", \"name\": \"variable.other.internal.imba\" }, { \"match\": \"\\\\@\\\\@+[_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?\", \"name\": \"variable.other.symbol.imba\" }, { \"match\": \"[_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?\", \"name\": \"variable.other.readwrite.imba\" }, { \"match\": \"\\\\@[_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?\", \"name\": \"variable.other.instance.imba\" }, { \"match\": \"\\\\#+[_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?\", \"name\": \"variable.other.private.imba\" }, { \"match\": \"\\\\:[_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?\", \"name\": \"string.symbol.imba\" }] }, \"punctuation-accessor\": { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.imba\" }, \"2\": { \"name\": \"punctuation.accessor.optional.imba\" } }, \"match\": \"(?:(\\\\.)|(\\\\.\\\\.(?!\\\\s*[[:digit:]]|\\\\s+)))\" }, \"punctuation-comma\": { \"match\": \",\", \"name\": \"punctuation.separator.comma.imba\" }, \"punctuation-semicolon\": { \"match\": \";\", \"name\": \"punctuation.terminator.statement.imba\" }, \"qstring-double\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.imba\" } }, \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.imba\" } }, \"name\": \"string.quoted.double.imba\", \"patterns\": [{ \"include\": \"#template-substitution-element\" }, { \"include\": \"#string-character-escape\" }] }, \"qstring-single\": { \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.imba\" } }, \"end\": \"(\\\\')|((?:[^\\\\\\\\\\\\n])$)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.imba\" }, \"2\": { \"name\": \"invalid.illegal.newline.imba\" } }, \"name\": \"string.quoted.single.imba\", \"patterns\": [{ \"include\": \"#string-character-escape\" }] }, \"qstring-single-multi\": { \"begin\": \"'''\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.imba\" } }, \"end\": \"'''\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.imba\" } }, \"name\": \"string.quoted.single.imba\", \"patterns\": [{ \"include\": \"#string-character-escape\" }] }, \"regex\": { \"patterns\": [{ \"begin\": \"(?<!\\\\+\\\\+|--|})(?<=[=(:,\\\\[?+!]|^return|[^\\\\._$[:alnum:]]return|^case|[^\\\\._$[:alnum:]]case|=>|&&|\\\\|\\\\||\\\\*\\\\/)\\\\s*(\\\\/)(?![\\\\/*])(?=(?:[^\\\\/\\\\\\\\\\\\[\\\\()]|\\\\\\\\.|\\\\[([^\\\\]\\\\\\\\]|\\\\\\\\.)+\\\\]|\\\\(([^\\\\)\\\\\\\\]|\\\\\\\\.)+\\\\))+\\\\/([gimsuy]+|(?![\\\\/\\\\*])|(?=\\\\/\\\\*))(?!\\\\s*[a-zA-Z0-9_$]))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.imba\" } }, \"end\": \"(/)([gimsuy]*)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.imba\" }, \"2\": { \"name\": \"keyword.other.imba\" } }, \"name\": \"string.regexp.imba\", \"patterns\": [{ \"include\": \"#regexp\" }] }, { \"begin\": \"((?<![_$[:alnum:])\\\\]]|\\\\+\\\\+|--|}|\\\\*\\\\/)|((?<=^return|[^\\\\._$[:alnum:]]return|^case|[^\\\\._$[:alnum:]]case))\\\\s*)\\\\/(?![\\\\/*])(?=(?:[^\\\\/\\\\\\\\\\\\[]|\\\\\\\\.|\\\\[([^\\\\]\\\\\\\\]|\\\\\\\\.)+\\\\])+\\\\/([gimsuy]+|(?![\\\\/\\\\*])|(?=\\\\/\\\\*))(?!\\\\s*[a-zA-Z0-9_$]))\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.imba\" } }, \"end\": \"(/)([gimsuy]*)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.imba\" }, \"2\": { \"name\": \"keyword.other.imba\" } }, \"name\": \"string.regexp.imba\", \"patterns\": [{ \"include\": \"#regexp\" }] }] }, \"regex-character-class\": { \"patterns\": [{ \"match\": \"\\\\\\\\[wWsSdDtrnvf]|\\\\.\", \"name\": \"constant.other.character-class.regexp\" }, { \"match\": \"\\\\\\\\([0-7]{3}|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4})\", \"name\": \"constant.character.numeric.regexp\" }, { \"match\": \"\\\\\\\\c[A-Z]\", \"name\": \"constant.character.control.regexp\" }, { \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.backslash.regexp\" }] }, \"regexp\": { \"patterns\": [{ \"match\": \"\\\\\\\\[bB]|\\\\^|\\\\$\", \"name\": \"keyword.control.anchor.regexp\" }, { \"captures\": { \"0\": { \"name\": \"keyword.other.back-reference.regexp\" }, \"1\": { \"name\": \"variable.other.regexp\" } }, \"match\": \"\\\\\\\\[1-9]\\\\d*|\\\\\\\\k<([a-zA-Z_$][\\\\w$]*)>\" }, { \"match\": \"[?+*]|\\\\{(\\\\d+,\\\\d+|\\\\d+,|,\\\\d+|\\\\d+)\\\\}\\\\??\", \"name\": \"keyword.operator.quantifier.regexp\" }, { \"match\": \"\\\\|\", \"name\": \"keyword.operator.or.regexp\" }, { \"begin\": \"(\\\\()((\\\\?=)|(\\\\?!)|(\\\\?<=)|(\\\\?<!))\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.group.regexp\" }, \"2\": { \"name\": \"punctuation.definition.group.assertion.regexp\" }, \"3\": { \"name\": \"meta.assertion.look-ahead.regexp\" }, \"4\": { \"name\": \"meta.assertion.negative-look-ahead.regexp\" }, \"5\": { \"name\": \"meta.assertion.look-behind.regexp\" }, \"6\": { \"name\": \"meta.assertion.negative-look-behind.regexp\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.group.regexp\" } }, \"name\": \"meta.group.assertion.regexp\", \"patterns\": [{ \"include\": \"#regexp\" }] }, { \"begin\": \"\\\\((?:(\\\\?:)|(?:\\\\?<([a-zA-Z_$][\\\\w$]*)>))?\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.regexp\" }, \"1\": { \"name\": \"punctuation.definition.group.no-capture.regexp\" }, \"2\": { \"name\": \"variable.other.regexp\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.group.regexp\" } }, \"name\": \"meta.group.regexp\", \"patterns\": [{ \"include\": \"#regexp\" }] }, { \"begin\": \"(\\\\[)(\\\\^)?\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.character-class.regexp\" }, \"2\": { \"name\": \"keyword.operator.negation.regexp\" } }, \"end\": \"(\\\\])\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.character-class.regexp\" } }, \"name\": \"constant.other.character-class.set.regexp\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.character.numeric.regexp\" }, \"2\": { \"name\": \"constant.character.control.regexp\" }, \"3\": { \"name\": \"constant.character.escape.backslash.regexp\" }, \"4\": { \"name\": \"constant.character.numeric.regexp\" }, \"5\": { \"name\": \"constant.character.control.regexp\" }, \"6\": { \"name\": \"constant.character.escape.backslash.regexp\" } }, \"match\": \"(?:.|(\\\\\\\\(?:[0-7]{3}|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}))|(\\\\\\\\c[A-Z])|(\\\\\\\\.))\\\\-(?:[^\\\\]\\\\\\\\]|(\\\\\\\\(?:[0-7]{3}|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}))|(\\\\\\\\c[A-Z])|(\\\\\\\\.))\", \"name\": \"constant.other.character-class.range.regexp\" }, { \"include\": \"#regex-character-class\" }] }, { \"include\": \"#regex-character-class\" }] }, \"root\": { \"patterns\": [{ \"include\": \"#block\" }] }, \"round-braces\": { \"begin\": \"\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"meta.brace.round.imba\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.round.imba\" } }, \"patterns\": [{ \"include\": \"#expr\" }, { \"include\": \"#punctuation-comma\" }] }, \"single-line-comment-consuming-line-ending\": { \"begin\": \"(^[ \\\\t]+)?((//|\\\\#\\\\s)(?:\\\\s*((@)internal)(?=\\\\s|$))?)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.imba\" }, \"2\": { \"name\": \"comment.line.double-slash.imba\" }, \"3\": { \"name\": \"punctuation.definition.comment.imba\" }, \"4\": { \"name\": \"storage.type.internaldeclaration.imba\" }, \"5\": { \"name\": \"punctuation.decorator.internaldeclaration.imba\" } }, \"contentName\": \"comment.line.double-slash.imba\", \"end\": \"(?=^)\" }, \"square-braces\": { \"begin\": \"\\\\s*(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.brace.square.imba\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"meta.brace.square.imba\" } }, \"patterns\": [{ \"include\": \"#expr\" }, { \"include\": \"#punctuation-comma\" }] }, \"string\": { \"patterns\": [{ \"include\": \"#qstring-single-multi\" }, { \"include\": \"#qstring-double-multi\" }, { \"include\": \"#qstring-single\" }, { \"include\": \"#qstring-double\" }, { \"include\": \"#template\" }] }, \"string-character-escape\": { \"match\": \"\\\\\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}|u\\\\{[0-9A-Fa-f]+\\\\}|[0-2][0-7]{0,2}|3[0-6][0-7]?|37[0-7]?|[4-7][0-7]?|.|$)\", \"name\": \"constant.character.escape.imba\" }, \"style-declaration\": { \"begin\": \"^(\\\\t*)(?:(global|local|export)\\\\s+)?(?:(scoped)\\\\s+)?(css)\\\\s\", \"beginCaptures\": { \"2\": { \"name\": \"keyword.control.export.imba\" }, \"3\": { \"name\": \"storage.modifier.imba\" }, \"4\": { \"name\": \"storage.type.style.imba\" } }, \"end\": \"^(?!(\\\\1\\\\t|\\\\s*$))\", \"name\": \"meta.style.imba\", \"patterns\": [{ \"include\": \"#css-selector\" }, { \"include\": \"#css-comment\" }, { \"include\": \"#nested-css-selector\" }, { \"include\": \"#inline-styles\" }] }, \"style-expr\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.numeric.integer.decimal.css\" }, \"2\": { \"name\": \"keyword.other.unit.css\" } }, \"match\": \"(\\\\b[0-9][0-9_]*)(\\\\w+|%)?\" }, { \"match\": \"--[_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?\", \"name\": \"support.constant.property-value.var.css\" }, { \"match\": \"(x+s|sm-|md-|lg-|sm|md|lg|x+l|hg|x+h)(?![\\\\w-])\", \"name\": \"support.constant.property-value.size.css\" }, { \"match\": \"[_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?\", \"name\": \"support.constant.property-value.css\" }, { \"begin\": \"(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\)\", \"name\": \"meta.function.css\", \"patterns\": [{ \"include\": \"#style-expr\" }] }] }, \"style-property\": { \"patterns\": [{ \"begin\": \"(?=(?:[\\\\^\\\\@\\\\.\\\\%\\\\w\\\\$\\\\!\\\\-]+)(?:\\\\s*[\\\\:\\\\=]))\", \"beginCaptures\": { \"1\": { \"name\": \"support.function.calc.css\" }, \"2\": { \"name\": \"punctuation.section.function.begin.bracket.round.css\" } }, \"end\": \"\\\\s*[\\\\:\\\\=]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.separator.key-value.css\" } }, \"name\": \"meta.property-name.css\", \"patterns\": [{ \"match\": \"(?:--|\\\\$)[\\\\w\\\\-\\\\$]+\", \"name\": \"support.type.property-name.variable.css\" }, { \"match\": \"\\\\@[\\\\!\\\\<\\\\>]?[0-9]+\", \"name\": \"support.type.property-name.modifier.breakpoint.css\" }, { \"match\": \"\\\\^?\\\\@+[\\\\w\\\\-\\\\$]+\", \"name\": \"support.type.property-name.modifier.css\" }, { \"match\": \"\\\\^?\\\\.+[\\\\w\\\\-\\\\$]+\", \"name\": \"support.type.property-name.modifier.flag.css\" }, { \"match\": \"\\\\^?\\\\%+[\\\\w\\\\-\\\\$]+\", \"name\": \"support.type.property-name.modifier.state.css\" }, { \"match\": \"\\\\.\\\\.[\\\\w\\\\-\\\\$]+|\\\\^+[\\\\.\\\\@\\\\%][\\\\w\\\\-\\\\$]+\", \"name\": \"support.type.property-name.modifier.up.css\" }, { \"match\": \"\\\\.[\\\\w\\\\-\\\\$]+\", \"name\": \"support.type.property-name.modifier.is.css\" }, { \"match\": \"[\\\\w\\\\-\\\\$]+\", \"name\": \"support.type.property-name.css\" }] }] }, \"super-literal\": { \"match\": \"(?<![_$[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))super\\\\b(?!\\\\$)\", \"name\": \"variable.language.super.imba\" }, \"tag-attr-name\": { \"begin\": \"([\\\\w$_]+(?:\\\\-[\\\\w$_]+)*)\", \"beginCaptures\": { \"0\": { \"name\": \"entity.other.attribute-name.imba\" } }, \"contentName\": \"entity.other.attribute-name.imba\", \"end\": \"(?=[\\\\s\\\\.\\\\[\\\\>\\\\=])\" }, \"tag-attr-value\": { \"begin\": \"(\\\\=)\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.tag.assignment\" } }, \"contentName\": \"meta.tag.attribute-value.imba\", \"end\": \"(?=>|\\\\s)\", \"patterns\": [{ \"include\": \"#expr\" }] }, \"tag-classname\": { \"begin\": \"\\\\.\", \"contentName\": \"entity.other.attribute-name.class.css\", \"end\": \"(?=[\\\\.\\\\[\\\\>\\\\s\\\\(\\\\=])\", \"patterns\": [{ \"include\": \"#tag-interpolated-content\" }] }, \"tag-content\": { \"patterns\": [{ \"include\": \"#tag-name\" }, { \"include\": \"#tag-expr-name\" }, { \"include\": \"#tag-interpolated-content\" }, { \"include\": \"#tag-interpolated-parens\" }, { \"include\": \"#tag-interpolated-brackets\" }, { \"include\": \"#tag-event-handler\" }, { \"include\": \"#tag-mixin-name\" }, { \"include\": \"#tag-classname\" }, { \"include\": \"#tag-ref\" }, { \"include\": \"#tag-attr-value\" }, { \"include\": \"#tag-attr-name\" }, { \"include\": \"#comment\" }] }, \"tag-event-handler\": { \"begin\": \"(\\\\@[\\\\w$_]+(?:\\\\-[\\\\w$_]+)*)\", \"beginCaptures\": { \"0\": { \"name\": \"entity.other.event-name.imba\" } }, \"contentName\": \"entity.other.tag.event\", \"end\": \"(?=[\\\\[\\\\>\\\\s\\\\=])\", \"patterns\": [{ \"include\": \"#tag-interpolated-content\" }, { \"include\": \"#tag-interpolated-parens\" }, { \"begin\": \"\\\\.\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.tag\" } }, \"end\": \"(?=[\\\\.\\\\[\\\\>\\\\s\\\\=]|$)\", \"name\": \"entity.other.event-modifier.imba\", \"patterns\": [{ \"include\": \"#tag-interpolated-parens\" }, { \"include\": \"#tag-interpolated-content\" }] }] }, \"tag-expr-name\": { \"begin\": \"(?<=<)(?=[\\\\w\\\\{])\", \"contentName\": \"entity.name.tag.imba\", \"end\": \"(?=[\\\\%\\\\$\\\\#\\\\.\\\\[\\\\>\\\\s\\\\(])\", \"patterns\": [{ \"include\": \"#tag-interpolated-content\" }] }, \"tag-interpolated-brackets\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.tag.imba\" } }, \"contentName\": \"meta.embedded.line.imba\", \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.tag.imba\" } }, \"name\": \"meta.tag.expression.imba\", \"patterns\": [{ \"include\": \"#inline-css-selector\" }, { \"include\": \"#inline-styles\" }] }, \"tag-interpolated-content\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.tag.imba\" } }, \"contentName\": \"meta.embedded.line.imba\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.tag.imba\" } }, \"name\": \"meta.tag.expression.imba\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"tag-interpolated-parens\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.tag.imba\" } }, \"contentName\": \"meta.embedded.line.imba\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.tag.imba\" } }, \"name\": \"meta.tag.expression.imba\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"tag-literal\": { \"patterns\": [{ \"begin\": \"(<)(?=[\\\\%\\\\~\\\\w\\\\{\\\\[\\\\.\\\\#\\\\$\\\\@\\\\(])\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.tag.open.imba\" } }, \"contentName\": \"meta.tag.attributes.imba\", \"end\": \"(>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.tag.close.imba\" } }, \"name\": \"meta.tag.imba\", \"patterns\": [{ \"include\": \"#tag-content\" }] }] }, \"tag-mixin-name\": { \"match\": \"(\\\\%[\\\\w\\\\-]+)\", \"name\": \"entity.other.tag-mixin.imba\" }, \"tag-name\": { \"patterns\": [{ \"match\": \"(?<=<)(self|global|slot)(?=[\\\\.\\\\[\\\\>\\\\s\\\\(])\", \"name\": \"entity.name.tag.special.imba\" }] }, \"tag-ref\": { \"match\": \"(\\\\$[\\\\w\\\\-]+)\", \"name\": \"entity.other.tag-ref.imba\" }, \"template\": { \"patterns\": [{ \"begin\": \"(?=(([_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?\\\\s*\\\\??\\\\.\\\\s*)*|(\\\\??\\\\.\\\\s*)?)([_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?)({{typeArguments}}\\\\s*)?`)\", \"end\": \"(?=`)\", \"name\": \"string.template.imba\", \"patterns\": [{ \"begin\": \"(?=(([_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?\\\\s*\\\\??\\\\.\\\\s*)*|(\\\\??\\\\.\\\\s*)?)([_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?))\", \"end\": \"(?=({{typeArguments}}\\\\s*)?`)\", \"patterns\": [{ \"match\": \"([_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?)\", \"name\": \"entity.name.function.tagged-template.imba\" }] }] }, { \"begin\": \"([_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?)\\\\s*(?=({{typeArguments}}\\\\s*)`)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.tagged-template.imba\" } }, \"end\": \"(?=`)\", \"name\": \"string.template.imba\", \"patterns\": [{ \"include\": \"#type-arguments\" }] }, { \"begin\": \"([_$[:alpha:]][_$[:alnum:]]*(?:\\\\-[_$[:alnum:]]+)*[\\\\?\\\\!]?)?(`)\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.tagged-template.imba\" }, \"2\": { \"name\": \"punctuation.definition.string.template.begin.imba\" } }, \"end\": \"`\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.template.end.imba\" } }, \"name\": \"string.template.imba\", \"patterns\": [{ \"include\": \"#template-substitution-element\" }, { \"include\": \"#string-character-escape\" }] }] }, \"template-substitution-element\": { \"begin\": \"(?<!\\\\\\\\)\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.template-expression.begin.imba\" } }, \"contentName\": \"meta.embedded.line.imba\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.template-expression.end.imba\" } }, \"name\": \"meta.template.expression.imba\", \"patterns\": [{ \"include\": \"#expr\" }] }, \"this-literal\": { \"match\": \"(?<![_$[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))(this|self)\\\\b(?!\\\\$)\", \"name\": \"variable.language.this.imba\" }, \"type-annotation\": { \"patterns\": [{ \"include\": \"#type-literal\" }] }, \"type-brackets\": { \"patterns\": [{ \"begin\": \"{\", \"end\": \"}\", \"patterns\": [{ \"include\": \"#type-brackets\" }] }, { \"begin\": \"\\\\[\", \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#type-brackets\" }] }, { \"begin\": \"\\\\<\", \"end\": \"\\\\>\", \"patterns\": [{ \"include\": \"#type-brackets\" }] }, { \"begin\": \"\\\\(\", \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#type-brackets\" }] }] }, \"type-literal\": { \"begin\": \"(\\\\\\\\)\", \"beginCaptures\": { \"1\": { \"name\": \"meta.type.annotation.open.imba\" } }, \"end\": \"(?=[\\\\s\\\\]\\\\)\\\\,\\\\.\\\\=\\\\}]|$)\", \"name\": \"meta.type.annotation.imba\", \"patterns\": [{ \"include\": \"#type-brackets\" }] }, \"undefined-literal\": { \"match\": \"(?<![_$[:alnum:]])(?:(?<=\\\\.\\\\.\\\\.)|(?<!\\\\.))undefined(?![\\\\?_\\\\-$[:alnum:]])(?:(?=\\\\.\\\\.\\\\.)|(?!\\\\.))\", \"name\": \"constant.language.undefined.imba\" } }, \"scopeName\": \"source.imba\", \"embeddedLangs\": [\"typescript\"] });\nvar imba = [\n  ...typescript,\n  lang\n];\n\nexport { imba as default };\n"], "mappings": ";;;;;;AAEA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,QAAQ,aAAa,CAAC,QAAQ,OAAO,GAAG,QAAQ,QAAQ,YAAY,CAAC,EAAE,WAAW,QAAQ,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,SAAS,kBAAkB,QAAQ,4BAA4B,CAAC,GAAG,cAAc,EAAE,iBAAiB,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,SAAS,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,SAAS,2GAA2G,QAAQ,sCAAsC,GAAG,EAAE,SAAS,2GAA2G,QAAQ,uCAAuC,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,KAAK,OAAO,cAAc,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,OAAO,gBAAgB,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,SAAS,gBAAgB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,QAAQ,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,oCAAoC,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,8CAA8C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,QAAQ,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,qBAAqB,GAAG,EAAE,SAAS,wBAAwB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,eAAe,2BAA2B,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,iBAAiB,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,qBAAqB,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,qBAAqB,GAAG,EAAE,SAAS,2DAA2D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,GAAG,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,eAAe,kCAAkC,OAAO,QAAQ,CAAC,EAAE,GAAG,sBAAsB,EAAE,YAAY,CAAC,EAAE,SAAS,mIAAmI,QAAQ,qDAAqD,GAAG,EAAE,SAAS,q6CAAq6C,QAAQ,qDAAqD,GAAG,EAAE,SAAS,yCAAyC,QAAQ,qCAAqC,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,SAAS,kBAAkB,QAAQ,uCAAuC,GAAG,EAAE,SAAS,KAAK,QAAQ,oCAAoC,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,KAAK,QAAQ,uCAAuC,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,SAAS,sBAAsB,QAAQ,oBAAoB,GAAG,EAAE,SAAS,+BAA+B,QAAQ,oBAAoB,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,SAAS,wBAAwB,QAAQ,0CAA0C,GAAG,EAAE,SAAS,aAAa,OAAO,aAAa,QAAQ,wCAAwC,GAAG,EAAE,SAAS,SAAS,QAAQ,gCAAgC,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,SAAS,8BAA8B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,uDAAuD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,QAAQ,0BAA0B,YAAY,CAAC,EAAE,SAAS,gCAAgC,QAAQ,kCAAkC,GAAG,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,SAAS,qCAAqC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,uDAAuD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,SAAS,gHAAgH,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,uDAAuD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,SAAS,yCAAyC,QAAQ,gCAAgC,GAAG,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,SAAS,0CAA0C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,uDAAuD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,QAAQ,0DAA0D,YAAY,CAAC,EAAE,SAAS,4CAA4C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,uDAAuD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,SAAS,wRAAwR,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,uDAAuD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,QAAQ,0BAA0B,YAAY,CAAC,EAAE,SAAS,oDAAoD,QAAQ,qCAAqC,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,SAAS,eAAe,QAAQ,8BAA8B,CAAC,EAAE,GAAG,EAAE,SAAS,2DAA2D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,uDAAuD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,SAAS,+CAA+C,QAAQ,6BAA6B,GAAG,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,SAAS,4CAA4C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,uDAAuD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,QAAQ,qCAAqC,YAAY,CAAC,EAAE,SAAS,2CAA2C,QAAQ,sCAAsC,GAAG,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,SAAS,uHAAuH,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iCAAiC,GAAG,KAAK,EAAE,QAAQ,uDAAuD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,qDAAqD,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,CAAC,EAAE,GAAG,sBAAsB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,SAAS,4DAA4D,QAAQ,yCAAyC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,SAAS,sQAAsQ,QAAQ,uBAAuB,CAAC,EAAE,GAAG,uBAAuB,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,SAAS,4BAA4B,QAAQ,8BAA8B,CAAC,EAAE,GAAG,sBAAsB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,SAAS,8XAA8X,QAAQ,+CAA+C,GAAG,uBAAuB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,SAAS,kUAAkU,QAAQ,iDAAiD,GAAG,gBAAgB,EAAE,SAAS,wEAAwE,OAAO,wFAAwF,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,wBAAwB,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,SAAS,kBAAkB,QAAQ,wCAAwC,GAAG,EAAE,SAAS,OAAO,QAAQ,+BAA+B,GAAG,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yDAAyD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,uDAAuD,EAAE,GAAG,QAAQ,+BAA+B,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,uDAAuD,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,SAAS,qDAAqD,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,SAAS,aAAa,QAAQ,+BAA+B,GAAG,EAAE,SAAS,OAAO,QAAQ,4BAA4B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,SAAS,oQAAoQ,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,SAAS,yGAAyG,CAAC,EAAE,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,qBAAqB,EAAE,YAAY,CAAC,EAAE,SAAS,mDAAmD,QAAQ,2CAA2C,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,2DAA2D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,WAAW,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,+MAA+M,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,SAAS,QAAQ,4CAA4C,YAAY,CAAC,EAAE,SAAS,4CAA4C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,OAAO,MAAM,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,QAAQ,iBAAiB,YAAY,CAAC,EAAE,SAAS,sCAAsC,QAAQ,6CAA6C,GAAG,EAAE,SAAS,KAAK,QAAQ,mCAAmC,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,SAAS,mEAAmE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,mDAAmD,GAAG,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,SAAS,yGAAyG,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,iCAAiC,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,SAAS,8FAA8F,GAAG,EAAE,SAAS,oBAAoB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,cAAc,QAAQ,sBAAsB,YAAY,CAAC,EAAE,SAAS,cAAc,GAAG,EAAE,SAAS,oBAAoB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,mDAAmD,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,eAAe,oCAAoC,OAAO,2BAA2B,eAAe,EAAE,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,mDAAmD,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,SAAS,4BAA4B,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,SAAS,8GAA8G,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,SAAS,6MAA6M,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,SAAS,iJAAiJ,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,SAAS,sHAAsH,GAAG,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,oCAAoC,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,0BAA0B,QAAQ,kCAAkC,CAAC,EAAE,GAAG,EAAE,SAAS,0FAA0F,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,oCAAoC,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,8BAA8B,QAAQ,uBAAuB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mEAAmE,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,iEAAiE,GAAG,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mDAsB9+nB,QAAQ,uBAAuB,CAAC,EAAE,GAAG,EAAE,SAAS,6KAA6K,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,oCAAoC,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,SAAS,yQAAyQ,GAAG,EAAE,SAAS,2DAA2D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,eAAe,wBAAwB,OAAO,oBAAoB,eAAe,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,SAAS,8EAA8E,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,SAAS,u9BAAu9B,QAAQ,2BAA2B,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,SAAS,8EAA8E,CAAC,EAAE,GAAG,QAAQ,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,SAAS,CAAC,EAAE,GAAG,cAAc,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,0BAA0B,CAAC,EAAE,GAAG,0BAA0B,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,gCAAgC,EAAE,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,oBAAoB,EAAE,SAAS,uBAAuB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,iCAAiC,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,kBAAkB,EAAE,SAAS,mEAAmE,QAAQ,gCAAgC,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,SAAS,wJAAwJ,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,SAAS,0GAA0G,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,SAAS,oGAAoG,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,SAAS,6GAA6G,GAAG,EAAE,SAAS,mCAAmC,QAAQ,gBAAgB,GAAG,EAAE,SAAS,WAAW,QAAQ,2BAA2B,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,8DAA8D,OAAO,2EAA2E,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oDAAoD,GAAG,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,SAAS,+DAA+D,QAAQ,mCAAmC,GAAG,EAAE,SAAS,gDAAgD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mDAAmD,GAAG,KAAK,EAAE,QAAQ,2BAA2B,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,OAAO,cAAc,eAAe,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,QAAQ,mCAAmC,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,gDAAgD,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,sCAAsC,CAAC,EAAE,CAAC,EAAE,GAAG,uBAAuB,EAAE,YAAY,CAAC,EAAE,SAAS,WAAW,QAAQ,qBAAqB,GAAG,EAAE,SAAS,aAAa,QAAQ,qBAAqB,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,SAAS,4BAA4B,QAAQ,6BAA6B,GAAG,EAAE,SAAS,UAAU,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,mDAAmD,EAAE,GAAG,eAAe,mCAAmC,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,sNAAsN,QAAQ,uBAAuB,GAAG,EAAE,SAAS,4EAA4E,QAAQ,uBAAuB,GAAG,EAAE,SAAS,+CAA+C,QAAQ,uBAAuB,GAAG,EAAE,SAAS,sEAAsE,QAAQ,uBAAuB,GAAG,EAAE,SAAS,iDAAiD,QAAQ,uBAAuB,GAAG,EAAE,SAAS,iEAAiE,QAAQ,6BAA6B,GAAG,EAAE,SAAS,0CAA0C,QAAQ,uBAAuB,GAAG,EAAE,SAAS,sFAAsF,QAAQ,0BAA0B,GAAG,EAAE,SAAS,2EAA2E,QAAQ,oBAAoB,GAAG,EAAE,SAAS,+DAA+D,QAAQ,oBAAoB,GAAG,EAAE,SAAS,gBAAgB,QAAQ,wBAAwB,GAAG,EAAE,SAAS,iBAAiB,QAAQ,wBAAwB,GAAG,EAAE,WAAW,OAAO,GAAG,EAAE,SAAS,wDAAwD,QAAQ,mCAAmC,GAAG,EAAE,SAAS,qBAAqB,QAAQ,wBAAwB,GAAG,EAAE,SAAS,qGAAqG,QAAQ,wBAAwB,CAAC,EAAE,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,sBAAsB,EAAE,SAAS,kBAAkB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,OAAO,wFAAwF,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,4BAA4B,EAAE,SAAS,4EAA4E,OAAO,wFAAwF,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,qBAAqB,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,OAAO,uBAAuB,QAAQ,mBAAmB,YAAY,CAAC,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,oEAAoE,OAAO,wFAAwF,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,4BAA4B,EAAE,SAAS,yBAAyB,OAAO,uBAAuB,QAAQ,mBAAmB,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,qGAAqG,QAAQ,8BAA8B,GAAG,4BAA4B,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,SAAS,uBAAuB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,SAAS,sDAAsD,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,6DAA6D,QAAQ,4BAA4B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,+CAA+C,QAAQ,+BAA+B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,kDAAkD,QAAQ,8BAA8B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,MAAM,EAAE,QAAQ,qCAAqC,GAAG,MAAM,EAAE,QAAQ,mCAAmC,GAAG,MAAM,EAAE,QAAQ,qCAAqC,GAAG,MAAM,EAAE,QAAQ,mCAAmC,GAAG,MAAM,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,+YAA+Y,CAAC,EAAE,GAAG,2BAA2B,EAAE,YAAY,CAAC,EAAE,SAAS,oGAAoG,QAAQ,6BAA6B,GAAG,EAAE,SAAS,yGAAyG,QAAQ,kCAAkC,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,SAAS,iEAAiE,QAAQ,0BAA0B,CAAC,EAAE,GAAG,OAAO,EAAE,YAAY,CAAC,EAAE,SAAS,aAAa,QAAQ,+BAA+B,GAAG,EAAE,SAAS,kDAAkD,QAAQ,4CAA4C,GAAG,EAAE,SAAS,uDAAuD,QAAQ,oDAAoD,GAAG,EAAE,SAAS,aAAa,QAAQ,sCAAsC,GAAG,EAAE,SAAS,oBAAoB,QAAQ,mCAAmC,GAAG,EAAE,SAAS,gBAAgB,QAAQ,mCAAmC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,uBAAuB,GAAG,EAAE,SAAS,uEAAuE,QAAQ,gCAAgC,GAAG,EAAE,SAAS,gBAAgB,QAAQ,gCAAgC,GAAG,EAAE,SAAS,iBAAiB,QAAQ,gCAAgC,GAAG,EAAE,SAAS,OAAO,QAAQ,mCAAmC,GAAG,EAAE,SAAS,MAAM,QAAQ,kCAAkC,GAAG,EAAE,SAAS,UAAU,QAAQ,kCAAkC,GAAG,EAAE,SAAS,iBAAiB,QAAQ,mCAAmC,CAAC,EAAE,GAAG,SAAS,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,SAAS,wEAAwE,CAAC,EAAE,GAAG,qBAAqB,EAAE,YAAY,CAAC,EAAE,SAAS,yDAAyD,QAAQ,+BAA+B,GAAG,EAAE,SAAS,yDAAyD,QAAQ,4BAA4B,GAAG,EAAE,SAAS,WAAW,QAAQ,wBAAwB,GAAG,EAAE,SAAS,iEAAiE,QAAQ,+BAA+B,GAAG,EAAE,SAAS,qEAAqE,QAAQ,6BAA6B,GAAG,EAAE,SAAS,8DAA8D,QAAQ,gCAAgC,GAAG,EAAE,SAAS,iEAAiE,QAAQ,+BAA+B,GAAG,EAAE,SAAS,kEAAkE,QAAQ,8BAA8B,GAAG,EAAE,SAAS,iEAAiE,QAAQ,qBAAqB,CAAC,EAAE,GAAG,wBAAwB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,SAAS,6CAA6C,GAAG,qBAAqB,EAAE,SAAS,KAAK,QAAQ,mCAAmC,GAAG,yBAAyB,EAAE,SAAS,KAAK,QAAQ,wCAAwC,GAAG,kBAAkB,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,2BAA2B,CAAC,EAAE,GAAG,kBAAkB,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,2BAA2B,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,2BAA2B,CAAC,EAAE,GAAG,wBAAwB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,2BAA2B,CAAC,EAAE,GAAG,SAAS,EAAE,YAAY,CAAC,EAAE,SAAS,uRAAuR,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,kBAAkB,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,qBAAqB,EAAE,GAAG,QAAQ,sBAAsB,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,oPAAoP,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,kBAAkB,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,qBAAqB,EAAE,GAAG,QAAQ,sBAAsB,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,yBAAyB,EAAE,YAAY,CAAC,EAAE,SAAS,yBAAyB,QAAQ,wCAAwC,GAAG,EAAE,SAAS,kDAAkD,QAAQ,oCAAoC,GAAG,EAAE,SAAS,cAAc,QAAQ,oCAAoC,GAAG,EAAE,SAAS,SAAS,QAAQ,6CAA6C,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,SAAS,oBAAoB,QAAQ,gCAAgC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,SAAS,2CAA2C,GAAG,EAAE,SAAS,gDAAgD,QAAQ,qCAAqC,GAAG,EAAE,SAAS,OAAO,QAAQ,6BAA6B,GAAG,EAAE,SAAS,wCAAwC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,gDAAgD,GAAG,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,+BAA+B,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,+CAA+C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,iDAAiD,GAAG,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,eAAe,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,QAAQ,6CAA6C,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,SAAS,0KAA0K,QAAQ,8CAA8C,GAAG,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,QAAQ,EAAE,YAAY,CAAC,EAAE,WAAW,SAAS,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,6CAA6C,EAAE,SAAS,2DAA2D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,iCAAiC,GAAG,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,eAAe,kCAAkC,OAAO,QAAQ,GAAG,iBAAiB,EAAE,SAAS,aAAa,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,2BAA2B,EAAE,SAAS,mHAAmH,QAAQ,iCAAiC,GAAG,qBAAqB,EAAE,SAAS,kEAAkE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,OAAO,uBAAuB,QAAQ,mBAAmB,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,cAAc,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,SAAS,6BAA6B,GAAG,EAAE,SAAS,gEAAgE,QAAQ,0CAA0C,GAAG,EAAE,SAAS,mDAAmD,QAAQ,2CAA2C,GAAG,EAAE,SAAS,8DAA8D,QAAQ,sCAAsC,GAAG,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uDAAuD,EAAE,GAAG,OAAO,OAAO,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,CAAC,EAAE,SAAS,uDAAuD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,uDAAuD,EAAE,GAAG,OAAO,gBAAgB,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,0BAA0B,YAAY,CAAC,EAAE,SAAS,0BAA0B,QAAQ,0CAA0C,GAAG,EAAE,SAAS,yBAAyB,QAAQ,qDAAqD,GAAG,EAAE,SAAS,wBAAwB,QAAQ,0CAA0C,GAAG,EAAE,SAAS,wBAAwB,QAAQ,+CAA+C,GAAG,EAAE,SAAS,wBAAwB,QAAQ,gDAAgD,GAAG,EAAE,SAAS,kDAAkD,QAAQ,6CAA6C,GAAG,EAAE,SAAS,mBAAmB,QAAQ,6CAA6C,GAAG,EAAE,SAAS,gBAAgB,QAAQ,iCAAiC,CAAC,EAAE,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,gEAAgE,QAAQ,+BAA+B,GAAG,iBAAiB,EAAE,SAAS,8BAA8B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,eAAe,oCAAoC,OAAO,wBAAwB,GAAG,kBAAkB,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,eAAe,iCAAiC,OAAO,aAAa,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,OAAO,eAAe,yCAAyC,OAAO,4BAA4B,YAAY,CAAC,EAAE,WAAW,4BAA4B,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,6BAA6B,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,qBAAqB,EAAE,SAAS,iCAAiC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,eAAe,0BAA0B,OAAO,sBAAsB,YAAY,CAAC,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,2BAA2B,GAAG,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,OAAO,2BAA2B,QAAQ,oCAAoC,YAAY,CAAC,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,4BAA4B,CAAC,EAAE,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,sBAAsB,eAAe,wBAAwB,OAAO,kCAAkC,YAAY,CAAC,EAAE,WAAW,4BAA4B,CAAC,EAAE,GAAG,6BAA6B,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,eAAe,2BAA2B,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,4BAA4B,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,eAAe,2BAA2B,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,2BAA2B,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,eAAe,2BAA2B,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,SAAS,2CAA2C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,eAAe,4BAA4B,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,QAAQ,iBAAiB,YAAY,CAAC,EAAE,WAAW,eAAe,CAAC,EAAE,CAAC,EAAE,GAAG,kBAAkB,EAAE,SAAS,kBAAkB,QAAQ,8BAA8B,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,iDAAiD,QAAQ,+BAA+B,CAAC,EAAE,GAAG,WAAW,EAAE,SAAS,kBAAkB,QAAQ,4BAA4B,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,0LAA0L,OAAO,SAAS,QAAQ,wBAAwB,YAAY,CAAC,EAAE,SAAS,iKAAiK,OAAO,iCAAiC,YAAY,CAAC,EAAE,SAAS,gEAAgE,QAAQ,4CAA4C,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,gGAAgG,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,SAAS,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,EAAE,SAAS,oEAAoE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,oDAAoD,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,2BAA2B,CAAC,EAAE,CAAC,EAAE,GAAG,iCAAiC,EAAE,SAAS,gBAAgB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wDAAwD,EAAE,GAAG,eAAe,2BAA2B,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,sDAAsD,EAAE,GAAG,QAAQ,iCAAiC,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,sEAAsE,QAAQ,8BAA8B,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,SAAS,KAAK,OAAO,KAAK,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,UAAU,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,OAAO,iCAAiC,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,qBAAqB,EAAE,SAAS,0GAA0G,QAAQ,mCAAmC,EAAE,GAAG,aAAa,eAAe,iBAAiB,CAAC,YAAY,EAAE,CAAC;AAC/whC,IAAI,OAAO;AAAA,EACT,GAAG;AAAA,EACH;AACF;", "names": []}