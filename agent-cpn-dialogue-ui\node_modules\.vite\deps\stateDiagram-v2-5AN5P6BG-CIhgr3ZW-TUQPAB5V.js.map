{"version": 3, "sources": ["../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-v2-5AN5P6BG.mjs"], "sourcesContent": ["import {\n  StateDB,\n  stateDiagram_default,\n  stateRenderer_v3_unified_default,\n  styles_default\n} from \"./chunk-OW32GOEJ.mjs\";\nimport \"./chunk-BFAMUDN2.mjs\";\nimport \"./chunk-SKB7J2MH.mjs\";\nimport \"./chunk-IWUHOULB.mjs\";\nimport \"./chunk-M6DAPIYF.mjs\";\nimport \"./chunk-MXNHSMXR.mjs\";\nimport \"./chunk-JW4RIYDF.mjs\";\nimport \"./chunk-AC5SNWB5.mjs\";\nimport \"./chunk-UWXLY5YG.mjs\";\nimport \"./chunk-QESNASVV.mjs\";\nimport \"./chunk-55PJQP7W.mjs\";\nimport {\n  __name\n} from \"./chunk-3XYRH5AP.mjs\";\n\n// src/diagrams/state/stateDiagram-v2.ts\nvar diagram = {\n  parser: stateDiagram_default,\n  get db() {\n    return new StateDB(2);\n  },\n  renderer: stateRenderer_v3_unified_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ __name((cnf) => {\n    if (!cnf.state) {\n      cnf.state = {};\n    }\n    cnf.state.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;\n  }, \"init\")\n};\nexport {\n  diagram\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAqBG,IAAC,UAAU;EACZ,QAAQ;EACR,IAAI,KAAK;AACP,WAAO,IAAI,QAAQ,CAAC;EACtB;EACA,UAAU;EACV,QAAQ;EACR,MAAsB,OAAO,CAAC,QAAQ;AACpC,QAAI,CAAC,IAAI,OAAO;AACd,UAAI,QAAQ,CAAA;IACd;AACA,QAAI,MAAM,sBAAsB,IAAI;EACtC,GAAG,MAAM;AACX;", "names": []}