{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/hcl.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"HashiCorp HCL\", \"fileTypes\": [\"hcl\"], \"name\": \"hcl\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#attribute_definition\" }, { \"include\": \"#block\" }, { \"include\": \"#expressions\" }], \"repository\": { \"attribute_access\": { \"begin\": \"\\\\.(?!\\\\*)\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.accessor.hcl\" } }, \"comment\": \"Matches traversal attribute access such as .attr\", \"end\": \"[[:alpha:]][\\\\w-]*|\\\\d*\", \"endCaptures\": { \"0\": { \"patterns\": [{ \"comment\": \"Attribute name\", \"match\": \"(?!null|false|true)[[:alpha:]][\\\\w-]*\", \"name\": \"variable.other.member.hcl\" }, { \"comment\": \"Optional attribute index\", \"match\": \"\\\\d+\", \"name\": \"constant.numeric.integer.hcl\" }] } } }, \"attribute_definition\": { \"captures\": { \"1\": { \"name\": \"punctuation.section.parens.begin.hcl\" }, \"2\": { \"name\": \"variable.other.readwrite.hcl\" }, \"3\": { \"name\": \"punctuation.section.parens.end.hcl\" }, \"4\": { \"name\": \"keyword.operator.assignment.hcl\" } }, \"comment\": 'Identifier \"=\" with optional parens', \"match\": \"(\\\\()?(\\\\b(?!null\\\\b|false\\\\b|true\\\\b)[[:alpha:]][[:alnum:]_-]*)(\\\\))?\\\\s*(\\\\=(?!\\\\=|\\\\>))\\\\s*\", \"name\": \"variable.declaration.hcl\" }, \"attribute_splat\": { \"begin\": \"\\\\.\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.operator.accessor.hcl\" } }, \"comment\": \"Legacy attribute-only splat\", \"end\": \"\\\\*\", \"endCaptures\": { \"0\": { \"name\": \"keyword.operator.splat.hcl\" } } }, \"block\": { \"begin\": \"([\\\\w][\\\\-\\\\w]*)([^{\\\\r\\\\n]*)(\\\\{)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"comment\": \"Block type\", \"match\": \"\\\\b(?!null|false|true)[[:alpha:]][[:alnum:]_-]*\\\\b\", \"name\": \"entity.name.type.hcl\" }] }, \"2\": { \"patterns\": [{ \"comment\": \"Block label (String Literal)\", \"match\": '\\\\\"[^\\\\\"\\\\r\\\\n]*\\\\\"', \"name\": \"variable.other.enummember.hcl\" }, { \"comment\": \"Block label (Indentifier)\", \"match\": \"[[:alpha:]][[:alnum:]_-]*\", \"name\": \"variable.other.enummember.hcl\" }] }, \"3\": { \"name\": \"punctuation.section.block.begin.hcl\" } }, \"comment\": 'This will match HCL blocks like `thing1 \"one\" \"two\" {` or `thing2 {`', \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.block.end.hcl\" } }, \"name\": \"meta.block.hcl\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#attribute_definition\" }, { \"include\": \"#expressions\" }, { \"include\": \"#block\" }] }, \"block_inline_comments\": { \"begin\": \"/\\\\*\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.hcl\" } }, \"comment\": \"Inline comments start with the /* sequence and end with the */ sequence, and may have any characters within except the ending sequence. An inline comment is considered equivalent to a whitespace sequence\", \"end\": \"\\\\*/\", \"name\": \"comment.block.hcl\" }, \"brackets\": { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.brackets.begin.hcl\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.brackets.end.hcl\" } }, \"patterns\": [{ \"comment\": \"Splat operator\", \"match\": \"\\\\*\", \"name\": \"keyword.operator.splat.hcl\" }, { \"include\": \"#comma\" }, { \"include\": \"#comments\" }, { \"include\": \"#inline_for_expression\" }, { \"include\": \"#inline_if_expression\" }, { \"include\": \"#expressions\" }, { \"include\": \"#local_identifiers\" }] }, \"char_escapes\": { \"comment\": \"Character Escapes\", \"match\": '\\\\\\\\[nrt\"\\\\\\\\]|\\\\\\\\u(\\\\h{8}|\\\\h{4})', \"name\": \"constant.character.escape.hcl\" }, \"comma\": { \"comment\": \"Commas - used in certain expressions\", \"match\": \"\\\\,\", \"name\": \"punctuation.separator.hcl\" }, \"comments\": { \"patterns\": [{ \"include\": \"#hash_line_comments\" }, { \"include\": \"#double_slash_line_comments\" }, { \"include\": \"#block_inline_comments\" }] }, \"double_slash_line_comments\": { \"begin\": \"//\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.hcl\" } }, \"comment\": \"Line comments start with // sequence and end with the next newline sequence. A line comment is considered equivalent to a newline sequence\", \"end\": \"$\\\\n?\", \"name\": \"comment.line.double-slash.hcl\" }, \"expressions\": { \"patterns\": [{ \"include\": \"#literal_values\" }, { \"include\": \"#operators\" }, { \"include\": \"#tuple_for_expression\" }, { \"include\": \"#object_for_expression\" }, { \"include\": \"#brackets\" }, { \"include\": \"#objects\" }, { \"include\": \"#attribute_access\" }, { \"include\": \"#attribute_splat\" }, { \"include\": \"#functions\" }, { \"include\": \"#parens\" }] }, \"for_expression_body\": { \"patterns\": [{ \"comment\": \"in keyword\", \"match\": \"\\\\bin\\\\b\", \"name\": \"keyword.operator.word.hcl\" }, { \"comment\": \"if keyword\", \"match\": \"\\\\bif\\\\b\", \"name\": \"keyword.control.conditional.hcl\" }, { \"match\": \"\\\\:\", \"name\": \"keyword.operator.hcl\" }, { \"include\": \"#expressions\" }, { \"include\": \"#comments\" }, { \"include\": \"#comma\" }, { \"include\": \"#local_identifiers\" }] }, \"functions\": { \"begin\": \"(\\\\w+)(\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"match\": \"\\\\b(?!null|false|true)[[:alpha:]][[:alnum:]_-]*\\\\b\", \"name\": \"support.function.builtin.hcl\" }] }, \"2\": { \"name\": \"punctuation.section.parens.begin.hcl\" } }, \"comment\": \"Built-in function calls\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.parens.end.hcl\" } }, \"name\": \"meta.function-call.hcl\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#expressions\" }, { \"include\": \"#comma\" }] }, \"hash_line_comments\": { \"begin\": \"#\", \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.hcl\" } }, \"comment\": \"Line comments start with # sequence and end with the next newline sequence. A line comment is considered equivalent to a newline sequence\", \"end\": \"$\\\\n?\", \"name\": \"comment.line.number-sign.hcl\" }, \"hcl_type_keywords\": { \"comment\": \"Type keywords known to HCL.\", \"match\": \"\\\\b(any|string|number|bool|list|set|map|tuple|object)\\\\b\", \"name\": \"storage.type.hcl\" }, \"heredoc\": { \"begin\": \"(\\\\<\\\\<\\\\-?)\\\\s*(\\\\w+)\\\\s*$\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.operator.heredoc.hcl\" }, \"2\": { \"name\": \"keyword.control.heredoc.hcl\" } }, \"comment\": \"String Heredoc\", \"end\": \"^\\\\s*\\\\2\\\\s*$\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control.heredoc.hcl\" } }, \"name\": \"string.unquoted.heredoc.hcl\", \"patterns\": [{ \"include\": \"#string_interpolation\" }] }, \"inline_for_expression\": { \"begin\": \"(for)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.hcl\" } }, \"end\": \"\\\\n\", \"patterns\": [{ \"match\": \"\\\\=\\\\>\", \"name\": \"storage.type.function.hcl\" }, { \"include\": \"#for_expression_body\" }] }, \"inline_if_expression\": { \"begin\": \"(if)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.conditional.hcl\" } }, \"end\": \"\\\\n\", \"patterns\": [{ \"include\": \"#expressions\" }, { \"include\": \"#comments\" }, { \"include\": \"#comma\" }, { \"include\": \"#local_identifiers\" }] }, \"language_constants\": { \"comment\": \"Language Constants\", \"match\": \"\\\\b(true|false|null)\\\\b\", \"name\": \"constant.language.hcl\" }, \"literal_values\": { \"patterns\": [{ \"include\": \"#numeric_literals\" }, { \"include\": \"#language_constants\" }, { \"include\": \"#string_literals\" }, { \"include\": \"#heredoc\" }, { \"include\": \"#hcl_type_keywords\" }] }, \"local_identifiers\": { \"comment\": \"Local Identifiers\", \"match\": \"\\\\b(?!null|false|true)[[:alpha:]][[:alnum:]_-]*\\\\b\", \"name\": \"variable.other.readwrite.hcl\" }, \"numeric_literals\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.separator.exponent.hcl\" } }, \"comment\": \"Integer, no fraction, optional exponent\", \"match\": \"\\\\b\\\\d+([Ee][+-]?)\\\\d+\\\\b\", \"name\": \"constant.numeric.float.hcl\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.separator.decimal.hcl\" }, \"2\": { \"name\": \"punctuation.separator.exponent.hcl\" } }, \"comment\": \"Integer, fraction, optional exponent\", \"match\": \"\\\\b\\\\d+(\\\\.)\\\\d+(?:([Ee][+-]?)\\\\d+)?\\\\b\", \"name\": \"constant.numeric.float.hcl\" }, { \"comment\": \"Integers\", \"match\": \"\\\\b\\\\d+\\\\b\", \"name\": \"constant.numeric.integer.hcl\" }] }, \"object_for_expression\": { \"begin\": \"(\\\\{)\\\\s?(for)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.braces.begin.hcl\" }, \"2\": { \"name\": \"keyword.control.hcl\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.braces.end.hcl\" } }, \"patterns\": [{ \"match\": \"\\\\=\\\\>\", \"name\": \"storage.type.function.hcl\" }, { \"include\": \"#for_expression_body\" }] }, \"object_key_values\": { \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#literal_values\" }, { \"include\": \"#operators\" }, { \"include\": \"#tuple_for_expression\" }, { \"include\": \"#object_for_expression\" }, { \"include\": \"#heredoc\" }, { \"include\": \"#functions\" }] }, \"objects\": { \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.braces.begin.hcl\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.braces.end.hcl\" } }, \"name\": \"meta.braces.hcl\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#objects\" }, { \"include\": \"#inline_for_expression\" }, { \"include\": \"#inline_if_expression\" }, { \"captures\": { \"1\": { \"name\": \"meta.mapping.key.hcl variable.other.readwrite.hcl\" }, \"2\": { \"name\": \"keyword.operator.assignment.hcl\", \"patterns\": [{ \"match\": \"\\\\=\\\\>\", \"name\": \"storage.type.function.hcl\" }] } }, \"comment\": \"Literal, named object key\", \"match\": \"\\\\b((?!null|false|true)[[:alpha:]][[:alnum:]_-]*)\\\\s*(\\\\=\\\\>?)\\\\s*\" }, { \"captures\": { \"1\": { \"name\": \"meta.mapping.key.hcl string.quoted.double.hcl\" }, \"2\": { \"name\": \"punctuation.definition.string.begin.hcl\" }, \"3\": { \"name\": \"punctuation.definition.string.end.hcl\" }, \"4\": { \"name\": \"keyword.operator.hcl\" } }, \"comment\": \"String object key\", \"match\": '\\\\b((\").*(\"))\\\\s*(\\\\=)\\\\s*' }, { \"begin\": \"^\\\\s*\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.parens.begin.hcl\" } }, \"comment\": \"Computed object key (any expression between parens)\", \"end\": \"(\\\\))\\\\s*(=|:)\\\\s*\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.parens.end.hcl\" }, \"2\": { \"name\": \"keyword.operator.hcl\" } }, \"name\": \"meta.mapping.key.hcl\", \"patterns\": [{ \"include\": \"#attribute_access\" }, { \"include\": \"#attribute_splat\" }] }, { \"include\": \"#object_key_values\" }] }, \"operators\": { \"patterns\": [{ \"match\": \"\\\\>\\\\=\", \"name\": \"keyword.operator.hcl\" }, { \"match\": \"\\\\<\\\\=\", \"name\": \"keyword.operator.hcl\" }, { \"match\": \"\\\\=\\\\=\", \"name\": \"keyword.operator.hcl\" }, { \"match\": \"\\\\!\\\\=\", \"name\": \"keyword.operator.hcl\" }, { \"match\": \"\\\\+\", \"name\": \"keyword.operator.arithmetic.hcl\" }, { \"match\": \"\\\\-\", \"name\": \"keyword.operator.arithmetic.hcl\" }, { \"match\": \"\\\\*\", \"name\": \"keyword.operator.arithmetic.hcl\" }, { \"match\": \"\\\\/\", \"name\": \"keyword.operator.arithmetic.hcl\" }, { \"match\": \"\\\\%\", \"name\": \"keyword.operator.arithmetic.hcl\" }, { \"match\": \"\\\\&\\\\&\", \"name\": \"keyword.operator.logical.hcl\" }, { \"match\": \"\\\\|\\\\|\", \"name\": \"keyword.operator.logical.hcl\" }, { \"match\": \"\\\\!\", \"name\": \"keyword.operator.logical.hcl\" }, { \"match\": \"\\\\>\", \"name\": \"keyword.operator.hcl\" }, { \"match\": \"\\\\<\", \"name\": \"keyword.operator.hcl\" }, { \"match\": \"\\\\?\", \"name\": \"keyword.operator.hcl\" }, { \"match\": \"\\\\.\\\\.\\\\.\", \"name\": \"keyword.operator.hcl\" }, { \"match\": \"\\\\:\" }] }, \"parens\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.parens.begin.hcl\" } }, \"comment\": \"Parens - matched *after* function syntax\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.parens.end.hcl\" } }, \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#expressions\" }] }, \"string_interpolation\": { \"begin\": \"(?<![%$])([%$]{)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.other.interpolation.begin.hcl\" } }, \"comment\": \"String interpolation\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"keyword.other.interpolation.end.hcl\" } }, \"name\": \"meta.interpolation.hcl\", \"patterns\": [{ \"comment\": \"Trim left whitespace\", \"match\": \"\\\\~\\\\s\", \"name\": \"keyword.operator.template.left.trim.hcl\" }, { \"comment\": \"Trim right whitespace\", \"match\": \"\\\\s\\\\~\", \"name\": \"keyword.operator.template.right.trim.hcl\" }, { \"comment\": \"if/else/endif and for/in/endfor directives\", \"match\": \"\\\\b(if|else|endif|for|in|endfor)\\\\b\", \"name\": \"keyword.control.hcl\" }, { \"include\": \"#expressions\" }, { \"include\": \"#local_identifiers\" }] }, \"string_literals\": { \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.hcl\" } }, \"comment\": \"Strings\", \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.hcl\" } }, \"name\": \"string.quoted.double.hcl\", \"patterns\": [{ \"include\": \"#string_interpolation\" }, { \"include\": \"#char_escapes\" }] }, \"tuple_for_expression\": { \"begin\": \"(\\\\[)\\\\s?(for)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.brackets.begin.hcl\" }, \"2\": { \"name\": \"keyword.control.hcl\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.brackets.end.hcl\" } }, \"patterns\": [{ \"include\": \"#for_expression_body\" }] } }, \"scopeName\": \"source.hcl\" });\nvar hcl = [\n  lang\n];\n\nexport { hcl as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,iBAAiB,aAAa,CAAC,KAAK,GAAG,QAAQ,OAAO,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,eAAe,CAAC,GAAG,cAAc,EAAE,oBAAoB,EAAE,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,WAAW,oDAAoD,OAAO,2BAA2B,eAAe,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,kBAAkB,SAAS,yCAAyC,QAAQ,4BAA4B,GAAG,EAAE,WAAW,4BAA4B,SAAS,QAAQ,QAAQ,+BAA+B,CAAC,EAAE,EAAE,EAAE,GAAG,wBAAwB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,WAAW,uCAAuC,SAAS,kGAAkG,QAAQ,2BAA2B,GAAG,mBAAmB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,WAAW,+BAA+B,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,EAAE,GAAG,SAAS,EAAE,SAAS,sCAAsC,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,SAAS,sDAAsD,QAAQ,uBAAuB,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gCAAgC,SAAS,uBAAuB,QAAQ,gCAAgC,GAAG,EAAE,WAAW,6BAA6B,SAAS,6BAA6B,QAAQ,gCAAgC,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,WAAW,wEAAwE,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,QAAQ,kBAAkB,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,SAAS,CAAC,EAAE,GAAG,yBAAyB,EAAE,SAAS,QAAQ,YAAY,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,WAAW,+MAA+M,OAAO,QAAQ,QAAQ,oBAAoB,GAAG,YAAY,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,kBAAkB,SAAS,OAAO,QAAQ,6BAA6B,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,gBAAgB,EAAE,WAAW,qBAAqB,SAAS,uCAAuC,QAAQ,gCAAgC,GAAG,SAAS,EAAE,WAAW,wCAAwC,SAAS,OAAO,QAAQ,4BAA4B,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,8BAA8B,GAAG,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,8BAA8B,EAAE,SAAS,MAAM,YAAY,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,WAAW,8IAA8I,OAAO,SAAS,QAAQ,gCAAgC,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,uBAAuB,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,SAAS,YAAY,QAAQ,4BAA4B,GAAG,EAAE,WAAW,cAAc,SAAS,YAAY,QAAQ,kCAAkC,GAAG,EAAE,SAAS,OAAO,QAAQ,uBAAuB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,eAAe,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,sDAAsD,QAAQ,+BAA+B,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,WAAW,2BAA2B,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,QAAQ,0BAA0B,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,SAAS,CAAC,EAAE,GAAG,sBAAsB,EAAE,SAAS,KAAK,YAAY,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,WAAW,6IAA6I,OAAO,SAAS,QAAQ,+BAA+B,GAAG,qBAAqB,EAAE,WAAW,+BAA+B,SAAS,4DAA4D,QAAQ,mBAAmB,GAAG,WAAW,EAAE,SAAS,+BAA+B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,WAAW,kBAAkB,OAAO,iBAAiB,eAAe,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,+BAA+B,YAAY,CAAC,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,yBAAyB,EAAE,SAAS,YAAY,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,SAAS,UAAU,QAAQ,4BAA4B,GAAG,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,wBAAwB,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,sBAAsB,EAAE,WAAW,sBAAsB,SAAS,2BAA2B,QAAQ,wBAAwB,GAAG,kBAAkB,EAAE,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,qBAAqB,EAAE,WAAW,qBAAqB,SAAS,sDAAsD,QAAQ,+BAA+B,GAAG,oBAAoB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,WAAW,2CAA2C,SAAS,6BAA6B,QAAQ,6BAA6B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,WAAW,wCAAwC,SAAS,2CAA2C,QAAQ,6BAA6B,GAAG,EAAE,WAAW,YAAY,SAAS,cAAc,QAAQ,+BAA+B,CAAC,EAAE,GAAG,yBAAyB,EAAE,SAAS,qBAAqB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,UAAU,QAAQ,4BAA4B,GAAG,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,qBAAqB,EAAE,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,WAAW,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,QAAQ,mBAAmB,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,oDAAoD,GAAG,KAAK,EAAE,QAAQ,mCAAmC,YAAY,CAAC,EAAE,SAAS,UAAU,QAAQ,4BAA4B,CAAC,EAAE,EAAE,GAAG,WAAW,6BAA6B,SAAS,qEAAqE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,gDAAgD,GAAG,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,WAAW,qBAAqB,SAAS,6BAA6B,GAAG,EAAE,SAAS,YAAY,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,WAAW,uDAAuD,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,SAAS,UAAU,QAAQ,uBAAuB,GAAG,EAAE,SAAS,UAAU,QAAQ,uBAAuB,GAAG,EAAE,SAAS,UAAU,QAAQ,uBAAuB,GAAG,EAAE,SAAS,UAAU,QAAQ,uBAAuB,GAAG,EAAE,SAAS,OAAO,QAAQ,kCAAkC,GAAG,EAAE,SAAS,OAAO,QAAQ,kCAAkC,GAAG,EAAE,SAAS,OAAO,QAAQ,kCAAkC,GAAG,EAAE,SAAS,OAAO,QAAQ,kCAAkC,GAAG,EAAE,SAAS,OAAO,QAAQ,kCAAkC,GAAG,EAAE,SAAS,UAAU,QAAQ,+BAA+B,GAAG,EAAE,SAAS,UAAU,QAAQ,+BAA+B,GAAG,EAAE,SAAS,OAAO,QAAQ,+BAA+B,GAAG,EAAE,SAAS,OAAO,QAAQ,uBAAuB,GAAG,EAAE,SAAS,OAAO,QAAQ,uBAAuB,GAAG,EAAE,SAAS,OAAO,QAAQ,uBAAuB,GAAG,EAAE,SAAS,aAAa,QAAQ,uBAAuB,GAAG,EAAE,SAAS,MAAM,CAAC,EAAE,GAAG,UAAU,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,WAAW,4CAA4C,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,wBAAwB,EAAE,SAAS,oBAAoB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,WAAW,wBAAwB,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,0BAA0B,YAAY,CAAC,EAAE,WAAW,wBAAwB,SAAS,UAAU,QAAQ,0CAA0C,GAAG,EAAE,WAAW,yBAAyB,SAAS,UAAU,QAAQ,2CAA2C,GAAG,EAAE,WAAW,8CAA8C,SAAS,uCAAuC,QAAQ,sBAAsB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,WAAW,WAAW,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,wBAAwB,EAAE,SAAS,qBAAqB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,EAAE,GAAG,aAAa,aAAa,CAAC;AAC9vY,IAAI,MAAM;AAAA,EACR;AACF;", "names": []}