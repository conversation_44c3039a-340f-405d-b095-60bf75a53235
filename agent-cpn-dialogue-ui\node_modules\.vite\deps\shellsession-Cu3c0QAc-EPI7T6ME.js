import {
  shellscript$1
} from "./chunk-JSL22EEI.js";
import "./chunk-2LSFTFF7.js";

// node_modules/.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/vue-element-plus-x/dist/shellsession-Cu3c0QAc.js
var lang = Object.freeze(JSON.parse('{"displayName":"Shell Session","fileTypes":["sh-session"],"name":"shellsession","patterns":[{"captures":{"1":{"name":"entity.other.prompt-prefix.shell-session"},"2":{"name":"punctuation.separator.prompt.shell-session"},"3":{"name":"source.shell","patterns":[{"include":"source.shell"}]}},"match":"^(?:((?:\\\\(\\\\S+\\\\)\\\\s*)?(?:sh\\\\S*?|\\\\w+\\\\S+[:@]\\\\S+(?:\\\\s+\\\\S+)?|\\\\[\\\\S+?[:@]\\\\N+?].*?))\\\\s*)?([#$%>❯➜\\\\p{Greek}])\\\\s+(.*)$"},{"match":"^.+$","name":"meta.output.shell-session"}],"scopeName":"text.shell-session","embeddedLangs":["shellscript"],"aliases":["console"]}'));
var shellsession$1 = [
  ...shellscript$1,
  lang
];
var console = Object.freeze(Object.defineProperty({
  __proto__: null,
  default: shellsession$1
}, Symbol.toStringTag, { value: "Module" }));
var shellsession = Object.freeze(Object.defineProperty({
  __proto__: null,
  default: shellsession$1
}, Symbol.toStringTag, { value: "Module" }));
export {
  console as c,
  shellsession as s
};
//# sourceMappingURL=shellsession-Cu3c0QAc-EPI7T6ME.js.map
