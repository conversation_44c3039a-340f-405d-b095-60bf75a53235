{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/razor.mjs"], "sourcesContent": ["import html from './html.mjs';\nimport csharp from './csharp.mjs';\nimport './javascript.mjs';\nimport './css.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"ASP.NET Razor\", \"fileTypes\": [\"razor\", \"cshtml\"], \"name\": \"razor\", \"patterns\": [{ \"include\": \"#razor-control-structures\" }, { \"include\": \"text.html.basic\" }], \"repository\": { \"addTagHelper-directive\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.razor.directive.addTagHelper\" }, \"3\": { \"patterns\": [{ \"include\": \"#tagHelper-directive-argument\" }] } }, \"match\": \"(@)(addTagHelper)\\\\s+([^$]+)?\", \"name\": \"meta.directive\" }, \"attribute-directive\": { \"begin\": \"(@)(attribute)\\\\b\\\\s+\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.razor.directive.attribute\" } }, \"end\": \"(?<=\\\\])|$\", \"name\": \"meta.directive\", \"patterns\": [{ \"include\": \"source.cs#attribute-section\" }] }, \"await-prefix\": { \"match\": \"(await)\\\\s+\", \"name\": \"keyword.other.await.cs\" }, \"balanced-brackets-csharp\": { \"begin\": \"(\\\\[)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.squarebracket.open.cs\" } }, \"end\": \"(\\\\])\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.squarebracket.close.cs\" } }, \"name\": \"razor.test.balanced.brackets\", \"patterns\": [{ \"include\": \"source.cs\" }] }, \"balanced-parenthesis-csharp\": { \"begin\": \"(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"name\": \"razor.test.balanced.parenthesis\", \"patterns\": [{ \"include\": \"source.cs\" }] }, \"catch-clause\": { \"begin\": \"(?:^|(?<=}))\\\\s*(catch)\\\\b\\\\s*?(?=[\\\\n\\\\(\\\\{])\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.try.catch.cs\" } }, \"end\": \"(?<=})\", \"name\": \"meta.statement.catch.razor\", \"patterns\": [{ \"include\": \"#catch-condition\" }, { \"include\": \"source.cs#when-clause\" }, { \"include\": \"#csharp-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"catch-condition\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"source.cs#type\" }] }, \"6\": { \"name\": \"entity.name.variable.local.cs\" } }, \"match\": \"(?x)\\n(?<type-name>\\n(?:\\n(?:\\n(?:(?<identifier>@?[_[:alpha:]][_[:alnum:]]*)\\\\s*\\\\:\\\\:\\\\s*)?\\n(?<name-and-type-args>\\n\\\\g<identifier>\\\\s*\\n(?<type-args>\\\\s*<(?:[^<>]|\\\\g<type-args>)+>\\\\s*)?\\n)\\n(?:\\\\s*\\\\.\\\\s*\\\\g<name-and-type-args>)* |\\n(?<tuple>\\\\s*\\\\((?:[^\\\\(\\\\)]|\\\\g<tuple>)+\\\\))\\n)\\n(?:\\\\s*\\\\?\\\\s*)?\\n(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*)*\\n)\\n)\\\\s*\\n(?:(\\\\g<identifier>)\\\\b)?\" }] }, \"code-directive\": { \"begin\": \"(@)(code)((?=\\\\{)|\\\\s+)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.razor.directive.code\" } }, \"end\": \"(?<=})|\\\\s\", \"patterns\": [{ \"include\": \"#directive-codeblock\" }] }, \"csharp-code-block\": { \"begin\": \"(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.curlybrace.open.cs\" } }, \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.curlybrace.close.cs\" } }, \"name\": \"meta.structure.razor.csharp.codeblock\", \"patterns\": [{ \"include\": \"#razor-codeblock-body\" }] }, \"csharp-condition\": { \"begin\": \"(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"include\": \"source.cs#local-variable-declaration\" }, { \"include\": \"source.cs#expression\" }, { \"include\": \"source.cs#punctuation-comma\" }, { \"include\": \"source.cs#punctuation-semicolon\" }] }, \"directive-codeblock\": { \"begin\": \"(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.razor.directive.codeblock.open\" } }, \"contentName\": \"source.cs\", \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.razor.directive.codeblock.close\" } }, \"name\": \"meta.structure.razor.directive.codeblock\", \"patterns\": [{ \"include\": \"source.cs#class-or-struct-members\" }] }, \"directive-markupblock\": { \"begin\": \"(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.razor.directive.codeblock.open\" } }, \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.razor.directive.codeblock.close\" } }, \"name\": \"meta.structure.razor.directive.markblock\", \"patterns\": [{ \"include\": \"$self\" }] }, \"directives\": { \"patterns\": [{ \"include\": \"#code-directive\" }, { \"include\": \"#functions-directive\" }, { \"include\": \"#page-directive\" }, { \"include\": \"#addTagHelper-directive\" }, { \"include\": \"#removeTagHelper-directive\" }, { \"include\": \"#tagHelperPrefix-directive\" }, { \"include\": \"#model-directive\" }, { \"include\": \"#inherits-directive\" }, { \"include\": \"#implements-directive\" }, { \"include\": \"#namespace-directive\" }, { \"include\": \"#inject-directive\" }, { \"include\": \"#attribute-directive\" }, { \"include\": \"#section-directive\" }, { \"include\": \"#layout-directive\" }, { \"include\": \"#using-directive\" }] }, \"do-statement\": { \"begin\": \"(?:(@))(do)\\\\b\\\\s\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.loop.do.cs\" } }, \"end\": \"(?<=})\", \"name\": \"meta.statement.do.razor\", \"patterns\": [{ \"include\": \"#csharp-condition\" }, { \"include\": \"#csharp-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"do-statement-with-optional-transition\": { \"begin\": \"(?:^\\\\s*|(@))(do)\\\\b\\\\s\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.loop.do.cs\" } }, \"end\": \"(?<=})\", \"name\": \"meta.statement.do.razor\", \"patterns\": [{ \"include\": \"#csharp-condition\" }, { \"include\": \"#csharp-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"else-part\": { \"begin\": \"(?:^|(?<=}))\\\\s*(else)\\\\b\\\\s*?(?: (if))?\\\\s*?(?=[\\\\n\\\\(\\\\{])\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.conditional.else.cs\" }, \"2\": { \"name\": \"keyword.control.conditional.if.cs\" } }, \"end\": \"(?<=})\", \"name\": \"meta.statement.else.razor\", \"patterns\": [{ \"include\": \"#csharp-condition\" }, { \"include\": \"#csharp-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"escaped-transition\": { \"match\": \"@@\", \"name\": \"constant.character.escape.razor.transition\" }, \"explicit-razor-expression\": { \"begin\": \"(@)\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.control.cshtml\" }, \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"keyword.control.cshtml\" } }, \"name\": \"meta.expression.explicit.cshtml\", \"patterns\": [{ \"include\": \"source.cs#expression\" }] }, \"finally-clause\": { \"begin\": \"(?:^|(?<=}))\\\\s*(finally)\\\\b\\\\s*?(?=[\\\\n\\\\{])\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.try.finally.cs\" } }, \"end\": \"(?<=})\", \"name\": \"meta.statement.finally.razor\", \"patterns\": [{ \"include\": \"#csharp-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"for-statement\": { \"begin\": \"(?:(@))(for)\\\\b\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.loop.for.cs\" } }, \"end\": \"(?<=})\", \"name\": \"meta.statement.for.razor\", \"patterns\": [{ \"include\": \"#csharp-condition\" }, { \"include\": \"#csharp-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"for-statement-with-optional-transition\": { \"begin\": \"(?:^\\\\s*|(@))(for)\\\\b\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.loop.for.cs\" } }, \"end\": \"(?<=})\", \"name\": \"meta.statement.for.razor\", \"patterns\": [{ \"include\": \"#csharp-condition\" }, { \"include\": \"#csharp-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"foreach-condition\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.open.cs\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.parenthesis.close.cs\" } }, \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.other.var.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"source.cs#type\" }] }, \"7\": { \"name\": \"entity.name.variable.local.cs\" }, \"8\": { \"name\": \"keyword.control.loop.in.cs\" } }, \"match\": \"(?x)\\n(?:\\n(\\\\bvar\\\\b)|\\n(?<type-name>\\n(?:\\n(?:\\n(?:(?<identifier>@?[_[:alpha:]][_[:alnum:]]*)\\\\s*\\\\:\\\\:\\\\s*)?\\n(?<name-and-type-args>\\n\\\\g<identifier>\\\\s*\\n(?<type-args>\\\\s*<(?:[^<>]|\\\\g<type-args>)+>\\\\s*)?\\n)\\n(?:\\\\s*\\\\.\\\\s*\\\\g<name-and-type-args>)* |\\n(?<tuple>\\\\s*\\\\((?:[^\\\\(\\\\)]|\\\\g<tuple>)+\\\\))\\n)\\n(?:\\\\s*\\\\?\\\\s*)?\\n(?:\\\\s*\\\\[(?:\\\\s*,\\\\s*)*\\\\]\\\\s*)*\\n)\\n)\\n)\\\\s+\\n(\\\\g<identifier>)\\\\s+\\n\\\\b(in)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.var.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"source.cs#tuple-declaration-deconstruction-element-list\" }] }, \"3\": { \"name\": \"keyword.control.loop.in.cs\" } }, \"match\": \"(?x)\\n(?:\\\\b(var)\\\\b\\\\s*)?\\n(?<tuple>\\\\((?:[^\\\\(\\\\)]|\\\\g<tuple>)+\\\\))\\\\s+\\n\\\\b(in)\\\\b\" }, { \"include\": \"source.cs#expression\" }] }, \"foreach-statement\": { \"begin\": \"(?:(@)(await\\\\s+)?)(foreach)\\\\b\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"patterns\": [{ \"include\": \"#await-prefix\" }] }, \"3\": { \"name\": \"keyword.control.loop.foreach.cs\" } }, \"end\": \"(?<=})\", \"name\": \"meta.statement.foreach.razor\", \"patterns\": [{ \"include\": \"#foreach-condition\" }, { \"include\": \"#csharp-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"foreach-statement-with-optional-transition\": { \"begin\": \"(?:^\\\\s*|(@)(await\\\\s+)?)(foreach)\\\\b\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"patterns\": [{ \"include\": \"#await-prefix\" }] }, \"3\": { \"name\": \"keyword.control.loop.foreach.cs\" } }, \"end\": \"(?<=})\", \"name\": \"meta.statement.foreach.razor\", \"patterns\": [{ \"include\": \"#foreach-condition\" }, { \"include\": \"#csharp-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"functions-directive\": { \"begin\": \"(@)(functions)((?=\\\\{)|\\\\s+)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.razor.directive.functions\" } }, \"end\": \"(?<=})|\\\\s\", \"patterns\": [{ \"include\": \"#directive-codeblock\" }] }, \"if-statement\": { \"begin\": \"(?:(@))(if)\\\\b\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.conditional.if.cs\" } }, \"end\": \"(?<=})\", \"name\": \"meta.statement.if.razor\", \"patterns\": [{ \"include\": \"#csharp-condition\" }, { \"include\": \"#csharp-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"if-statement-with-optional-transition\": { \"begin\": \"(?:^\\\\s*|(@))(if)\\\\b\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.conditional.if.cs\" } }, \"end\": \"(?<=})\", \"name\": \"meta.statement.if.razor\", \"patterns\": [{ \"include\": \"#csharp-condition\" }, { \"include\": \"#csharp-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"implements-directive\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.razor.directive.implements\" }, \"3\": { \"patterns\": [{ \"include\": \"source.cs#type\" }] } }, \"match\": \"(@)(implements)\\\\s+([^$]+)?\", \"name\": \"meta.directive\" }, \"implicit-expression\": { \"begin\": \"(?<![[:alpha:][:alnum:]])(@)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] } }, \"contentName\": \"source.cs\", \"end\": `(?=[\\\\s<>\\\\{\\\\}\\\\)\\\\]'\"])`, \"name\": \"meta.expression.implicit.cshtml\", \"patterns\": [{ \"include\": \"#await-prefix\" }, { \"include\": \"#implicit-expression-body\" }] }, \"implicit-expression-accessor\": { \"match\": \"(?<=\\\\.)[_[:alpha:]][_[:alnum:]]*\", \"name\": \"variable.other.object.property.cs\" }, \"implicit-expression-accessor-start\": { \"begin\": \"([_[:alpha:]][_[:alnum:]]*)\", \"beginCaptures\": { \"1\": { \"name\": \"variable.other.object.cs\" } }, \"end\": `(?=[\\\\s<>\\\\{\\\\}\\\\)\\\\]'\"])`, \"patterns\": [{ \"include\": \"#implicit-expression-continuation\" }] }, \"implicit-expression-body\": { \"end\": `(?=[\\\\s<>\\\\{\\\\}\\\\)\\\\]'\"])`, \"patterns\": [{ \"include\": \"#implicit-expression-invocation-start\" }, { \"include\": \"#implicit-expression-accessor-start\" }] }, \"implicit-expression-continuation\": { \"end\": `(?=[\\\\s<>\\\\{\\\\}\\\\)\\\\]'\"])`, \"patterns\": [{ \"include\": \"#balanced-parenthesis-csharp\" }, { \"include\": \"#balanced-brackets-csharp\" }, { \"include\": \"#implicit-expression-invocation\" }, { \"include\": \"#implicit-expression-accessor\" }, { \"include\": \"#implicit-expression-extension\" }] }, \"implicit-expression-dot-operator\": { \"captures\": { \"1\": { \"name\": \"punctuation.accessor.cs\" } }, \"match\": \"(\\\\.)(?=[_[:alpha:]][_[:alnum:]]*)\" }, \"implicit-expression-invocation\": { \"match\": \"(?<=\\\\.)[_[:alpha:]][_[:alnum:]]*(?=\\\\()\", \"name\": \"entity.name.function.cs\" }, \"implicit-expression-invocation-start\": { \"begin\": \"([_[:alpha:]][_[:alnum:]]*)(?=\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.cs\" } }, \"end\": `(?=[\\\\s<>\\\\{\\\\}\\\\)\\\\]'\"])`, \"patterns\": [{ \"include\": \"#implicit-expression-continuation\" }] }, \"implicit-expression-null-conditional-operator\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.null-conditional.cs\" } }, \"match\": \"(\\\\?)(?=[.\\\\[])\" }, \"implicit-expression-null-forgiveness-operator\": { \"captures\": { \"1\": { \"name\": \"keyword.operator.logical.cs\" } }, \"match\": \"(\\\\!)(?=(?:\\\\.[_[:alpha:]][_[:alnum:]]*)|\\\\?|[\\\\[\\\\(])\" }, \"implicit-expression-operator\": { \"patterns\": [{ \"include\": \"#implicit-expression-dot-operator\" }, { \"include\": \"#implicit-expression-null-conditional-operator\" }, { \"include\": \"#implicit-expression-null-forgiveness-operator\" }] }, \"inherits-directive\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.razor.directive.inherits\" }, \"3\": { \"patterns\": [{ \"include\": \"source.cs#type\" }] } }, \"match\": \"(@)(inherits)\\\\s+([^$]+)?\", \"name\": \"meta.directive\" }, \"inject-directive\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.razor.directive.inject\" }, \"3\": { \"patterns\": [{ \"include\": \"source.cs#type\" }] }, \"4\": { \"name\": \"entity.name.variable.property.cs\" } }, \"match\": \"(@)(inject)\\\\s*([\\\\S\\\\s]+?)?\\\\s*([_[:alpha:]][_[:alnum:]]*)?\\\\s*(?=$)\", \"name\": \"meta.directive\" }, \"layout-directive\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.razor.directive.layout\" }, \"3\": { \"patterns\": [{ \"include\": \"source.cs#type\" }] } }, \"match\": \"(@)(layout)\\\\s+([^$]+)?\", \"name\": \"meta.directive\" }, \"lock-statement\": { \"begin\": \"(?:(@))(lock)\\\\b\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.other.lock.cs\" } }, \"end\": \"(?<=})\", \"name\": \"meta.statement.lock.razor\", \"patterns\": [{ \"include\": \"#csharp-condition\" }, { \"include\": \"#csharp-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"lock-statement-with-optional-transition\": { \"begin\": \"(?:^\\\\s*|(@))(lock)\\\\b\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.other.lock.cs\" } }, \"end\": \"(?<=})\", \"name\": \"meta.statement.lock.razor\", \"patterns\": [{ \"include\": \"#csharp-condition\" }, { \"include\": \"#csharp-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"model-directive\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.razor.directive.model\" }, \"3\": { \"patterns\": [{ \"include\": \"source.cs#type\" }] } }, \"match\": \"(@)(model)\\\\s+([^$]+)?\", \"name\": \"meta.directive\" }, \"namespace-directive\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.razor.directive.namespace\" }, \"3\": { \"patterns\": [{ \"include\": \"#namespace-directive-argument\" }] } }, \"match\": \"(@)(namespace)\\\\s+([^\\\\s]+)?\", \"name\": \"meta.directive\" }, \"namespace-directive-argument\": { \"captures\": { \"1\": { \"name\": \"entity.name.type.namespace.cs\" }, \"2\": { \"name\": \"punctuation.accessor.cs\" } }, \"match\": \"([_[:alpha:]][_[:alnum:]]*)(\\\\.)?\" }, \"non-void-tag\": { \"begin\": \"(?=<(!)?([^/\\\\s>]+)(\\\\s|/?>))\", \"end\": \"(</)(\\\\2)\\\\s*(>)|(/>)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"entity.name.tag.html\" }, \"3\": { \"name\": \"punctuation.definition.tag.end.html\" }, \"4\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"patterns\": [{ \"begin\": \"(<)(!)?([^/\\\\s>]+)(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"constant.character.escape.razor.tagHelperOptOut\" }, \"3\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \"(?=/?>)\", \"patterns\": [{ \"include\": \"#razor-control-structures\" }, { \"include\": \"text.html.basic#attribute\" }] }, { \"begin\": \">\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"end\": \"(?=</)\", \"patterns\": [{ \"include\": \"#wellformed-html\" }, { \"include\": \"$self\" }] }] }, \"optionally-transitioned-csharp-control-structures\": { \"patterns\": [{ \"include\": \"#using-statement-with-optional-transition\" }, { \"include\": \"#if-statement-with-optional-transition\" }, { \"include\": \"#else-part\" }, { \"include\": \"#foreach-statement-with-optional-transition\" }, { \"include\": \"#for-statement-with-optional-transition\" }, { \"include\": \"#while-statement\" }, { \"include\": \"#switch-statement-with-optional-transition\" }, { \"include\": \"#lock-statement-with-optional-transition\" }, { \"include\": \"#do-statement-with-optional-transition\" }, { \"include\": \"#try-statement-with-optional-transition\" }] }, \"optionally-transitioned-razor-control-structures\": { \"patterns\": [{ \"include\": \"#razor-comment\" }, { \"include\": \"#razor-codeblock\" }, { \"include\": \"#explicit-razor-expression\" }, { \"include\": \"#escaped-transition\" }, { \"include\": \"#directives\" }, { \"include\": \"#optionally-transitioned-csharp-control-structures\" }, { \"include\": \"#implicit-expression\" }] }, \"page-directive\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.razor.directive.page\" }, \"3\": { \"patterns\": [{ \"include\": \"source.cs#string-literal\" }] } }, \"match\": \"(@)(page)\\\\s+([^$]+)?\", \"name\": \"meta.directive\" }, \"razor-codeblock\": { \"begin\": \"(@)(\\\\{)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.razor.directive.codeblock.open\" } }, \"contentName\": \"source.cs\", \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.razor.directive.codeblock.close\" } }, \"name\": \"meta.structure.razor.codeblock\", \"patterns\": [{ \"include\": \"#razor-codeblock-body\" }] }, \"razor-codeblock-body\": { \"patterns\": [{ \"include\": \"#text-tag\" }, { \"include\": \"#wellformed-html\" }, { \"include\": \"#razor-single-line-markup\" }, { \"include\": \"#optionally-transitioned-razor-control-structures\" }, { \"include\": \"source.cs\" }] }, \"razor-comment\": { \"begin\": \"(@)(\\\\*)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.razor.comment.star\" } }, \"contentName\": \"comment.block.razor\", \"end\": \"(\\\\*)(@)\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.razor.comment.star\" }, \"2\": { \"patterns\": [{ \"include\": \"#transition\" }] } }, \"name\": \"meta.comment.razor\" }, \"razor-control-structures\": { \"patterns\": [{ \"include\": \"#razor-comment\" }, { \"include\": \"#razor-codeblock\" }, { \"include\": \"#explicit-razor-expression\" }, { \"include\": \"#escaped-transition\" }, { \"include\": \"#directives\" }, { \"include\": \"#transitioned-csharp-control-structures\" }, { \"include\": \"#implicit-expression\" }] }, \"razor-single-line-markup\": { \"captures\": { \"1\": { \"name\": \"keyword.control.razor.singleLineMarkup\" }, \"2\": { \"patterns\": [{ \"include\": \"#razor-control-structures\" }, { \"include\": \"text.html.basic\" }] } }, \"match\": \"(\\\\@\\\\:)([^$]*)$\" }, \"removeTagHelper-directive\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.razor.directive.removeTagHelper\" }, \"3\": { \"patterns\": [{ \"include\": \"#tagHelper-directive-argument\" }] } }, \"match\": \"(@)(removeTagHelper)\\\\s+([^$]+)?\", \"name\": \"meta.directive\" }, \"section-directive\": { \"begin\": \"(@)(section)\\\\b\\\\s+([_[:alpha:]][_[:alnum:]]*)?\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.razor.directive.section\" }, \"3\": { \"name\": \"variable.other.razor.directive.sectionName\" } }, \"end\": \"(?<=})\", \"name\": \"meta.directive.block\", \"patterns\": [{ \"include\": \"#directive-markupblock\" }] }, \"switch-code-block\": { \"begin\": \"(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.curlybrace.open.cs\" } }, \"end\": \"(\\\\})\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.curlybrace.close.cs\" } }, \"name\": \"meta.structure.razor.csharp.codeblock.switch\", \"patterns\": [{ \"include\": \"source.cs#switch-label\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"switch-statement\": { \"begin\": \"(?:(@))(switch)\\\\b\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.switch.cs\" } }, \"end\": \"(?<=})\", \"name\": \"meta.statement.switch.razor\", \"patterns\": [{ \"include\": \"#csharp-condition\" }, { \"include\": \"#switch-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"switch-statement-with-optional-transition\": { \"begin\": \"(?:^\\\\s*|(@))(switch)\\\\b\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.switch.cs\" } }, \"end\": \"(?<=})\", \"name\": \"meta.statement.switch.razor\", \"patterns\": [{ \"include\": \"#csharp-condition\" }, { \"include\": \"#switch-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"tagHelper-directive-argument\": { \"patterns\": [{ \"include\": \"source.cs#string-literal\" }, { \"include\": \"#unquoted-string-argument\" }] }, \"tagHelperPrefix-directive\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.razor.directive.tagHelperPrefix\" }, \"3\": { \"patterns\": [{ \"include\": \"#tagHelper-directive-argument\" }] } }, \"match\": \"(@)(tagHelperPrefix)\\\\s+([^$]+)?\", \"name\": \"meta.directive\" }, \"text-tag\": { \"begin\": \"(<text\\\\s*>)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.cshtml.transition.textTag.open\" } }, \"end\": \"(</text>)\", \"endCaptures\": { \"1\": { \"name\": \"keyword.control.cshtml.transition.textTag.close\" } }, \"patterns\": [{ \"include\": \"#wellformed-html\" }, { \"include\": \"$self\" }] }, \"transition\": { \"match\": \"@\", \"name\": \"keyword.control.cshtml.transition\" }, \"transitioned-csharp-control-structures\": { \"patterns\": [{ \"include\": \"#using-statement\" }, { \"include\": \"#if-statement\" }, { \"include\": \"#else-part\" }, { \"include\": \"#foreach-statement\" }, { \"include\": \"#for-statement\" }, { \"include\": \"#while-statement\" }, { \"include\": \"#switch-statement\" }, { \"include\": \"#lock-statement\" }, { \"include\": \"#do-statement\" }, { \"include\": \"#try-statement\" }] }, \"try-block\": { \"begin\": \"(?:(@))(try)\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.try.cs\" } }, \"end\": \"(?<=})\", \"name\": \"meta.statement.try.razor\", \"patterns\": [{ \"include\": \"#csharp-condition\" }, { \"include\": \"#csharp-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"try-block-with-optional-transition\": { \"begin\": \"(?:^\\\\s*|(@))(try)\\\\b\\\\s*\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.try.cs\" } }, \"end\": \"(?<=})\", \"name\": \"meta.statement.try.razor\", \"patterns\": [{ \"include\": \"#csharp-condition\" }, { \"include\": \"#csharp-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"try-statement\": { \"patterns\": [{ \"include\": \"#try-block\" }, { \"include\": \"#catch-clause\" }, { \"include\": \"#finally-clause\" }] }, \"try-statement-with-optional-transition\": { \"patterns\": [{ \"include\": \"#try-block-with-optional-transition\" }, { \"include\": \"#catch-clause\" }, { \"include\": \"#finally-clause\" }] }, \"unquoted-string-argument\": { \"match\": \"[^$]+\", \"name\": \"string.quoted.double.cs\" }, \"using-alias-directive\": { \"captures\": { \"1\": { \"name\": \"entity.name.type.alias.cs\" }, \"2\": { \"name\": \"keyword.operator.assignment.cs\" }, \"3\": { \"patterns\": [{ \"include\": \"source.cs#type\" }] } }, \"match\": \"([_[:alpha:]][_[:alnum:]]*)\\\\b\\\\s*(=)\\\\s*(.+)\\\\s*\" }, \"using-directive\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.other.using.cs\" }, \"3\": { \"patterns\": [{ \"include\": \"#using-static-directive\" }, { \"include\": \"#using-alias-directive\" }, { \"include\": \"#using-standard-directive\" }] }, \"4\": { \"name\": \"keyword.control.razor.optionalSemicolon\" } }, \"match\": \"(@)(using)\\\\b\\\\s+(?!\\\\(|\\\\s)(.+?)?(;)?$\", \"name\": \"meta.directive\" }, \"using-standard-directive\": { \"captures\": { \"1\": { \"name\": \"entity.name.type.namespace.cs\" } }, \"match\": \"([_[:alpha:]][_[:alnum:]]*)\\\\s*\" }, \"using-statement\": { \"begin\": \"(?:(@))(using)\\\\b\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.other.using.cs\" } }, \"end\": \"(?<=})\", \"name\": \"meta.statement.using.razor\", \"patterns\": [{ \"include\": \"#csharp-condition\" }, { \"include\": \"#csharp-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"using-statement-with-optional-transition\": { \"begin\": \"(?:^\\\\s*|(@))(using)\\\\b\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.other.using.cs\" } }, \"end\": \"(?<=})\", \"name\": \"meta.statement.using.razor\", \"patterns\": [{ \"include\": \"#csharp-condition\" }, { \"include\": \"#csharp-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] }, \"using-static-directive\": { \"captures\": { \"1\": { \"name\": \"keyword.other.static.cs\" }, \"2\": { \"patterns\": [{ \"include\": \"source.cs#type\" }] } }, \"match\": \"(static)\\\\b\\\\s+(.+)\" }, \"void-tag\": { \"begin\": \"(?i)(<)(!)?(area|base|br|col|command|embed|hr|img|input|keygen|link|meta|param|source|track|wbr)(?=\\\\s|/?>)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.tag.begin.html\" }, \"2\": { \"name\": \"constant.character.escape.razor.tagHelperOptOut\" }, \"3\": { \"name\": \"entity.name.tag.html\" } }, \"end\": \"/?>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.tag.end.html\" } }, \"name\": \"meta.tag.structure.$3.void.html\", \"patterns\": [{ \"include\": \"text.html.basic#attribute\" }] }, \"wellformed-html\": { \"patterns\": [{ \"include\": \"#void-tag\" }, { \"include\": \"#non-void-tag\" }] }, \"while-statement\": { \"begin\": \"(?:(@)|^\\\\s*|(?<=})\\\\s*)(while)\\\\b\\\\s*(?=\\\\()\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#transition\" }] }, \"2\": { \"name\": \"keyword.control.loop.while.cs\" } }, \"end\": \"(?<=})|(;)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.terminator.statement.cs\" } }, \"name\": \"meta.statement.while.razor\", \"patterns\": [{ \"include\": \"#csharp-condition\" }, { \"include\": \"#csharp-code-block\" }, { \"include\": \"#razor-codeblock-body\" }] } }, \"scopeName\": \"text.aspnetcorerazor\", \"embeddedLangs\": [\"html\", \"csharp\"] });\nvar razor = [\n  ...html,\n  ...csharp,\n  lang\n];\n\nexport { razor as default };\n"], "mappings": ";;;;;;;;;;;AAKA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,iBAAiB,aAAa,CAAC,SAAS,QAAQ,GAAG,QAAQ,SAAS,YAAY,CAAC,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,kBAAkB,CAAC,GAAG,cAAc,EAAE,0BAA0B,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,EAAE,GAAG,SAAS,iCAAiC,QAAQ,iBAAiB,GAAG,uBAAuB,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,cAAc,QAAQ,kBAAkB,YAAY,CAAC,EAAE,WAAW,8BAA8B,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,eAAe,QAAQ,yBAAyB,GAAG,4BAA4B,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,+BAA+B,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,QAAQ,mCAAmC,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,kDAAkD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,OAAO,UAAU,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,SAAS,0XAA0X,CAAC,EAAE,GAAG,kBAAkB,EAAE,SAAS,2BAA2B,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,OAAO,cAAc,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,qBAAqB,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,QAAQ,yCAAyC,YAAY,CAAC,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,oBAAoB,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,uCAAuC,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,8BAA8B,GAAG,EAAE,WAAW,kCAAkC,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,eAAe,aAAa,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,QAAQ,4CAA4C,YAAY,CAAC,EAAE,WAAW,oCAAoC,CAAC,EAAE,GAAG,yBAAyB,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,QAAQ,4CAA4C,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,cAAc,EAAE,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,6BAA6B,GAAG,EAAE,WAAW,6BAA6B,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,qBAAqB,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,UAAU,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,yCAAyC,EAAE,SAAS,2BAA2B,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,OAAO,UAAU,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,gEAAgE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,UAAU,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,sBAAsB,EAAE,SAAS,MAAM,QAAQ,6CAA6C,GAAG,6BAA6B,EAAE,SAAS,UAAU,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yBAAyB,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,QAAQ,mCAAmC,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,kBAAkB,EAAE,SAAS,iDAAiD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,OAAO,UAAU,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,8BAA8B,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,UAAU,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,0CAA0C,EAAE,SAAS,oCAAoC,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,OAAO,UAAU,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,qBAAqB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,SAAS,wZAAwZ,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,0DAA0D,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,SAAS,wFAAwF,GAAG,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,qBAAqB,EAAE,SAAS,8CAA8C,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,UAAU,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,8CAA8C,EAAE,SAAS,oDAAoD,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,OAAO,UAAU,QAAQ,gCAAgC,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,uBAAuB,EAAE,SAAS,gCAAgC,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,OAAO,cAAc,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,6BAA6B,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,UAAU,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,yCAAyC,EAAE,SAAS,mCAAmC,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,UAAU,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,wBAAwB,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,EAAE,GAAG,SAAS,+BAA+B,QAAQ,iBAAiB,GAAG,uBAAuB,EAAE,SAAS,gCAAgC,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,EAAE,GAAG,eAAe,aAAa,OAAO,6BAA6B,QAAQ,mCAAmC,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,4BAA4B,CAAC,EAAE,GAAG,gCAAgC,EAAE,SAAS,qCAAqC,QAAQ,oCAAoC,GAAG,sCAAsC,EAAE,SAAS,+BAA+B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,OAAO,6BAA6B,YAAY,CAAC,EAAE,WAAW,oCAAoC,CAAC,EAAE,GAAG,4BAA4B,EAAE,OAAO,6BAA6B,YAAY,CAAC,EAAE,WAAW,wCAAwC,GAAG,EAAE,WAAW,sCAAsC,CAAC,EAAE,GAAG,oCAAoC,EAAE,OAAO,6BAA6B,YAAY,CAAC,EAAE,WAAW,+BAA+B,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,kCAAkC,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,iCAAiC,CAAC,EAAE,GAAG,oCAAoC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,SAAS,qCAAqC,GAAG,kCAAkC,EAAE,SAAS,4CAA4C,QAAQ,0BAA0B,GAAG,wCAAwC,EAAE,SAAS,sCAAsC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,OAAO,6BAA6B,YAAY,CAAC,EAAE,WAAW,oCAAoC,CAAC,EAAE,GAAG,iDAAiD,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,kBAAkB,GAAG,iDAAiD,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,SAAS,yDAAyD,GAAG,gCAAgC,EAAE,YAAY,CAAC,EAAE,WAAW,oCAAoC,GAAG,EAAE,WAAW,iDAAiD,GAAG,EAAE,WAAW,iDAAiD,CAAC,EAAE,GAAG,sBAAsB,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,EAAE,GAAG,SAAS,6BAA6B,QAAQ,iBAAiB,GAAG,oBAAoB,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,SAAS,yEAAyE,QAAQ,iBAAiB,GAAG,oBAAoB,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,EAAE,GAAG,SAAS,2BAA2B,QAAQ,iBAAiB,GAAG,kBAAkB,EAAE,SAAS,+BAA+B,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,OAAO,UAAU,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,2CAA2C,EAAE,SAAS,qCAAqC,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,OAAO,UAAU,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,EAAE,GAAG,SAAS,0BAA0B,QAAQ,iBAAiB,GAAG,uBAAuB,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,EAAE,GAAG,SAAS,gCAAgC,QAAQ,iBAAiB,GAAG,gCAAgC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,gCAAgC,GAAG,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,SAAS,oCAAoC,GAAG,gBAAgB,EAAE,SAAS,iCAAiC,OAAO,yBAAyB,eAAe,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,sCAAsC,GAAG,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,iCAAiC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,kDAAkD,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,4BAA4B,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,OAAO,UAAU,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,qDAAqD,EAAE,YAAY,CAAC,EAAE,WAAW,4CAA4C,GAAG,EAAE,WAAW,yCAAyC,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,8CAA8C,GAAG,EAAE,WAAW,0CAA0C,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,6CAA6C,GAAG,EAAE,WAAW,2CAA2C,GAAG,EAAE,WAAW,yCAAyC,GAAG,EAAE,WAAW,0CAA0C,CAAC,EAAE,GAAG,oDAAoD,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,6BAA6B,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,qDAAqD,GAAG,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,2BAA2B,CAAC,EAAE,EAAE,GAAG,SAAS,yBAAyB,QAAQ,iBAAiB,GAAG,mBAAmB,EAAE,SAAS,YAAY,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,eAAe,aAAa,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,QAAQ,kCAAkC,YAAY,CAAC,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,wBAAwB,EAAE,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,oDAAoD,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,iBAAiB,EAAE,SAAS,YAAY,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,eAAe,uBAAuB,OAAO,YAAY,eAAe,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,EAAE,GAAG,QAAQ,qBAAqB,GAAG,4BAA4B,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,6BAA6B,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,0CAA0C,GAAG,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,4BAA4B,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,kBAAkB,CAAC,EAAE,EAAE,GAAG,SAAS,mBAAmB,GAAG,6BAA6B,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,kDAAkD,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,EAAE,GAAG,SAAS,oCAAoC,QAAQ,iBAAiB,GAAG,qBAAqB,EAAE,SAAS,mDAAmD,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,UAAU,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,yBAAyB,CAAC,EAAE,GAAG,qBAAqB,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,kCAAkC,EAAE,GAAG,QAAQ,gDAAgD,YAAY,CAAC,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,oBAAoB,EAAE,SAAS,iCAAiC,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,OAAO,UAAU,QAAQ,+BAA+B,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,6CAA6C,EAAE,SAAS,uCAAuC,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,OAAO,UAAU,QAAQ,+BAA+B,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,gCAAgC,EAAE,YAAY,CAAC,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,4BAA4B,CAAC,EAAE,GAAG,6BAA6B,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,kDAAkD,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,gCAAgC,CAAC,EAAE,EAAE,GAAG,SAAS,oCAAoC,QAAQ,iBAAiB,GAAG,YAAY,EAAE,SAAS,gBAAgB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,aAAa,eAAe,EAAE,KAAK,EAAE,QAAQ,kDAAkD,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,KAAK,QAAQ,oCAAoC,GAAG,0CAA0C,EAAE,YAAY,CAAC,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,uBAAuB,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,UAAU,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,sCAAsC,EAAE,SAAS,6BAA6B,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,UAAU,QAAQ,4BAA4B,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,0CAA0C,EAAE,YAAY,CAAC,EAAE,WAAW,sCAAsC,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,kBAAkB,CAAC,EAAE,GAAG,4BAA4B,EAAE,SAAS,SAAS,QAAQ,0BAA0B,GAAG,yBAAyB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,iCAAiC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,EAAE,GAAG,SAAS,oDAAoD,GAAG,mBAAmB,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,yBAAyB,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,4BAA4B,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,0CAA0C,EAAE,GAAG,SAAS,2CAA2C,QAAQ,iBAAiB,GAAG,4BAA4B,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,SAAS,kCAAkC,GAAG,mBAAmB,EAAE,SAAS,gCAAgC,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,UAAU,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,4CAA4C,EAAE,SAAS,sCAAsC,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,UAAU,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,0BAA0B,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0BAA0B,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,EAAE,GAAG,SAAS,sBAAsB,GAAG,YAAY,EAAE,SAAS,+GAA+G,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,GAAG,KAAK,EAAE,QAAQ,kDAAkD,GAAG,KAAK,EAAE,QAAQ,uBAAuB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,mCAAmC,YAAY,CAAC,EAAE,WAAW,4BAA4B,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,gBAAgB,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,iDAAiD,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,gCAAgC,EAAE,GAAG,OAAO,cAAc,eAAe,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,EAAE,GAAG,aAAa,wBAAwB,iBAAiB,CAAC,QAAQ,QAAQ,EAAE,CAAC;AACt4zB,IAAI,QAAQ;AAAA,EACV,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACF;", "names": []}