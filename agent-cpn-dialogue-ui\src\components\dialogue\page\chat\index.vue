<script setup>
import { useRoute } from 'vue-router';
import { computed, provide } from 'vue';
import { storeToRefs } from 'pinia';
import ChatDefaul from '@dialogue/page/chat/layouts/chatDefaul/index.vue';
import ChatWithId from '@dialogue/page/chat/layouts/chatWithId/index.vue';
import { agentTree, agentHeader, SelectionToolbar, componentMap } from '@/components/dialogue/index.js';
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';

import RightPanel from './layouts/RightPanel/index.vue';
import { supersonicChatUrl, supersonicChatAgentId } from '@/config/env';
const route = useRoute();
const sessionId = computed(() => route.params?.id);
const supersonicStatusRef = ref(false);
const supersonicChatUrlRef = ref('');

// 获取右侧面板状态
const rightPanelStore = useRightPanelStore();
const { isRightVisible } = storeToRefs(rightPanelStore);

// 监听路由变化
watch(
    () => route,
    newParams => {
        // 根据newParams调整组件状态或渲染内容
        let agentId = newParams.query.agentId;
        if (agentId) {
            supersonicStatusRef.value = true;
        } else {
            supersonicStatusRef.value = false;
            agentId = supersonicChatAgentId;
        }
        supersonicChatUrlRef.value = supersonicChatUrl + '?agentId=' + agentId + '&accessSource=1';
    },
    { deep: true }
);

// 提供组件映射表以供子组件使用
provide('componentMap', componentMap);
</script>

<template>
    <el-container class="container_agent">
        <el-aside width="auto" style="overflow: visible">
            <agentTree></agentTree>
        </el-aside>
        <el-container>
            <el-header style="padding: 0">
                <agentHeader></agentHeader>
            </el-header>
            <el-main class="chat-container">
                <template v-if="!supersonicStatusRef">
                    <keep-alive>
                        <ChatDefaul v-if="!sessionId" />
                        <ChatWithId v-else />
                    </keep-alive>
                </template>
                <template v-else>
                    <iframe :src="supersonicChatUrlRef" class="border-none w-full h-full" />
                </template>
            </el-main>
        </el-container>
        <el-aside class="right-panel" v-if="isRightVisible">
            <!-- 右侧内容区域 -->
            <RightPanel />
        </el-aside>
        <SelectionToolbar />
    </el-container>
</template>

<style lang="scss" scoped>
.container_agent {
    width: 100%;
    height: 100%;
    overflow: auto;
    position: relative;
    background: url('/static/dialogue/index_bj.png') 100% 100%;
}

.chat-container {
    flex: 1;
}
.right-panel {
    border-left: 1px solid #e6e6e6;
    transition: width 0.3s ease-in-out;
    width: 750px;
}
</style>
