{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/git-commit.mjs"], "sourcesContent": ["import diff from './diff.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"Git Commit Message\", \"name\": \"git-commit\", \"patterns\": [{ \"begin\": \"(?=^diff\\\\ \\\\-\\\\-git)\", \"comment\": \"diff presented at the end of the commit message when using commit -v.\", \"contentName\": \"source.diff\", \"end\": \"\\\\z\", \"name\": \"meta.embedded.diff.git-commit\", \"patterns\": [{ \"include\": \"source.diff\" }] }, { \"begin\": \"^(?!#)\", \"comment\": \"User supplied message\", \"end\": \"^(?=#)\", \"name\": \"meta.scope.message.git-commit\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"invalid.deprecated.line-too-long.git-commit\" }, \"2\": { \"name\": \"invalid.illegal.line-too-long.git-commit\" } }, \"comment\": \"Mark > 50 lines as deprecated, > 72 as illegal\", \"match\": \"\\\\G.{0,50}(.{0,22}(.*))$\", \"name\": \"meta.scope.subject.git-commit\" }] }, { \"begin\": \"^(?=#)\", \"comment\": \"Git supplied metadata in a number of lines starting with #\", \"contentName\": \"comment.line.number-sign.git-commit\", \"end\": \"^(?!#)\", \"name\": \"meta.scope.metadata.git-commit\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"markup.changed.git-commit\" } }, \"match\": \"^#\\\\t((modified|renamed):.*)$\" }, { \"captures\": { \"1\": { \"name\": \"markup.inserted.git-commit\" } }, \"match\": \"^#\\\\t(new file:.*)$\" }, { \"captures\": { \"1\": { \"name\": \"markup.deleted.git-commit\" } }, \"match\": \"^#\\\\t(deleted.*)$\" }, { \"captures\": { \"1\": { \"name\": \"keyword.other.file-type.git-commit\" }, \"2\": { \"name\": \"string.unquoted.filename.git-commit\" } }, \"comment\": \"Fallback for non-English git commit template\", \"match\": \"^#\\\\t([^:]+): *(.*)$\" }] }], \"scopeName\": \"text.git-commit\", \"embeddedLangs\": [\"diff\"] });\nvar gitCommit = [\n  ...diff,\n  lang\n];\n\nexport { gitCommit as default };\n"], "mappings": ";;;;;;AAEA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,sBAAsB,QAAQ,cAAc,YAAY,CAAC,EAAE,SAAS,yBAAyB,WAAW,yEAAyE,eAAe,eAAe,OAAO,OAAO,QAAQ,iCAAiC,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,UAAU,WAAW,yBAAyB,OAAO,UAAU,QAAQ,iCAAiC,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,WAAW,kDAAkD,SAAS,4BAA4B,QAAQ,gCAAgC,CAAC,EAAE,GAAG,EAAE,SAAS,UAAU,WAAW,8DAA8D,eAAe,uCAAuC,OAAO,UAAU,QAAQ,kCAAkC,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,SAAS,gCAAgC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,SAAS,sBAAsB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,SAAS,oBAAoB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,WAAW,gDAAgD,SAAS,uBAAuB,CAAC,EAAE,CAAC,GAAG,aAAa,mBAAmB,iBAAiB,CAAC,MAAM,EAAE,CAAC;AACxiD,IAAI,YAAY;AAAA,EACd,GAAG;AAAA,EACH;AACF;", "names": []}