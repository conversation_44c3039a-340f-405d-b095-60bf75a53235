{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/themes/night-owl.mjs"], "sourcesContent": ["var nightOwl = Object.freeze({\n  \"colors\": {\n    \"activityBar.background\": \"#011627\",\n    \"activityBar.border\": \"#011627\",\n    \"activityBar.dropBackground\": \"#5f7e97\",\n    \"activityBar.foreground\": \"#5f7e97\",\n    \"activityBarBadge.background\": \"#44596b\",\n    \"activityBarBadge.foreground\": \"#ffffff\",\n    \"badge.background\": \"#5f7e97\",\n    \"badge.foreground\": \"#ffffff\",\n    \"breadcrumb.activeSelectionForeground\": \"#FFFFFF\",\n    \"breadcrumb.focusForeground\": \"#ffffff\",\n    \"breadcrumb.foreground\": \"#A599E9\",\n    \"breadcrumbPicker.background\": \"#001122\",\n    \"button.background\": \"#7e57c2cc\",\n    \"button.foreground\": \"#ffffffcc\",\n    \"button.hoverBackground\": \"#7e57c2\",\n    \"contrastBorder\": \"#122d42\",\n    \"debugExceptionWidget.background\": \"#011627\",\n    \"debugExceptionWidget.border\": \"#5f7e97\",\n    \"debugToolBar.background\": \"#011627\",\n    \"diffEditor.insertedTextBackground\": \"#99b76d23\",\n    \"diffEditor.insertedTextBorder\": \"#c5e47833\",\n    \"diffEditor.removedTextBackground\": \"#ef535033\",\n    \"diffEditor.removedTextBorder\": \"#ef53504d\",\n    \"dropdown.background\": \"#011627\",\n    \"dropdown.border\": \"#5f7e97\",\n    \"dropdown.foreground\": \"#ffffffcc\",\n    \"editor.background\": \"#011627\",\n    \"editor.findMatchBackground\": \"#5f7e9779\",\n    \"editor.findMatchHighlightBackground\": \"#1085bb5d\",\n    \"editor.findRangeHighlightBackground\": null,\n    \"editor.foreground\": \"#d6deeb\",\n    \"editor.hoverHighlightBackground\": \"#7e57c25a\",\n    \"editor.inactiveSelectionBackground\": \"#7e57c25a\",\n    \"editor.lineHighlightBackground\": \"#0003\",\n    \"editor.lineHighlightBorder\": null,\n    \"editor.rangeHighlightBackground\": \"#7e57c25a\",\n    \"editor.selectionBackground\": \"#1d3b53\",\n    \"editor.selectionHighlightBackground\": \"#5f7e9779\",\n    \"editor.wordHighlightBackground\": \"#f6bbe533\",\n    \"editor.wordHighlightStrongBackground\": \"#e2a2f433\",\n    \"editorBracketMatch.background\": \"#5f7e974d\",\n    \"editorBracketMatch.border\": null,\n    \"editorCodeLens.foreground\": \"#5e82ceb4\",\n    \"editorCursor.foreground\": \"#80a4c2\",\n    \"editorError.border\": null,\n    \"editorError.foreground\": \"#EF5350\",\n    \"editorGroup.border\": \"#011627\",\n    \"editorGroup.dropBackground\": \"#7e57c273\",\n    \"editorGroup.emptyBackground\": \"#011627\",\n    \"editorGroupHeader.noTabsBackground\": \"#011627\",\n    \"editorGroupHeader.tabsBackground\": \"#011627\",\n    \"editorGroupHeader.tabsBorder\": \"#262A39\",\n    \"editorGutter.addedBackground\": \"#9CCC65\",\n    \"editorGutter.background\": \"#011627\",\n    \"editorGutter.deletedBackground\": \"#EF5350\",\n    \"editorGutter.modifiedBackground\": \"#e2b93d\",\n    \"editorHoverWidget.background\": \"#011627\",\n    \"editorHoverWidget.border\": \"#5f7e97\",\n    \"editorIndentGuide.activeBackground\": \"#7E97AC\",\n    \"editorIndentGuide.background\": \"#5e81ce52\",\n    \"editorLineNumber.activeForeground\": \"#C5E4FD\",\n    \"editorLineNumber.foreground\": \"#4b6479\",\n    \"editorLink.activeForeground\": null,\n    \"editorMarkerNavigation.background\": \"#0b2942\",\n    \"editorMarkerNavigationError.background\": \"#EF5350\",\n    \"editorMarkerNavigationWarning.background\": \"#FFCA28\",\n    \"editorOverviewRuler.commonContentForeground\": \"#7e57c2\",\n    \"editorOverviewRuler.currentContentForeground\": \"#7e57c2\",\n    \"editorOverviewRuler.incomingContentForeground\": \"#7e57c2\",\n    \"editorRuler.foreground\": \"#5e81ce52\",\n    \"editorSuggestWidget.background\": \"#2C3043\",\n    \"editorSuggestWidget.border\": \"#2B2F40\",\n    \"editorSuggestWidget.foreground\": \"#d6deeb\",\n    \"editorSuggestWidget.highlightForeground\": \"#ffffff\",\n    \"editorSuggestWidget.selectedBackground\": \"#5f7e97\",\n    \"editorWarning.border\": null,\n    \"editorWarning.foreground\": \"#b39554\",\n    \"editorWhitespace.foreground\": null,\n    \"editorWidget.background\": \"#021320\",\n    \"editorWidget.border\": \"#5f7e97\",\n    \"errorForeground\": \"#EF5350\",\n    \"extensionButton.prominentBackground\": \"#7e57c2cc\",\n    \"extensionButton.prominentForeground\": \"#ffffffcc\",\n    \"extensionButton.prominentHoverBackground\": \"#7e57c2\",\n    \"focusBorder\": \"#122d42\",\n    \"foreground\": \"#d6deeb\",\n    \"gitDecoration.conflictingResourceForeground\": \"#ffeb95cc\",\n    \"gitDecoration.deletedResourceForeground\": \"#EF535090\",\n    \"gitDecoration.ignoredResourceForeground\": \"#395a75\",\n    \"gitDecoration.modifiedResourceForeground\": \"#a2bffc\",\n    \"gitDecoration.untrackedResourceForeground\": \"#c5e478ff\",\n    \"input.background\": \"#0b253a\",\n    \"input.border\": \"#5f7e97\",\n    \"input.foreground\": \"#ffffffcc\",\n    \"input.placeholderForeground\": \"#5f7e97\",\n    \"inputOption.activeBorder\": \"#ffffffcc\",\n    \"inputValidation.errorBackground\": \"#AB0300F2\",\n    \"inputValidation.errorBorder\": \"#EF5350\",\n    \"inputValidation.infoBackground\": \"#00589EF2\",\n    \"inputValidation.infoBorder\": \"#64B5F6\",\n    \"inputValidation.warningBackground\": \"#675700F2\",\n    \"inputValidation.warningBorder\": \"#FFCA28\",\n    \"list.activeSelectionBackground\": \"#234d708c\",\n    \"list.activeSelectionForeground\": \"#ffffff\",\n    \"list.dropBackground\": \"#011627\",\n    \"list.focusBackground\": \"#010d18\",\n    \"list.focusForeground\": \"#ffffff\",\n    \"list.highlightForeground\": \"#ffffff\",\n    \"list.hoverBackground\": \"#011627\",\n    \"list.hoverForeground\": \"#ffffff\",\n    \"list.inactiveSelectionBackground\": \"#0e293f\",\n    \"list.inactiveSelectionForeground\": \"#5f7e97\",\n    \"list.invalidItemForeground\": \"#975f94\",\n    \"merge.border\": null,\n    \"merge.currentContentBackground\": null,\n    \"merge.currentHeaderBackground\": \"#5f7e97\",\n    \"merge.incomingContentBackground\": null,\n    \"merge.incomingHeaderBackground\": \"#7e57c25a\",\n    \"meta.objectliteral.js\": \"#82AAFF\",\n    \"notificationCenter.border\": \"#262a39\",\n    \"notificationLink.foreground\": \"#80CBC4\",\n    \"notificationToast.border\": \"#262a39\",\n    \"notifications.background\": \"#01111d\",\n    \"notifications.border\": \"#262a39\",\n    \"notifications.foreground\": \"#ffffffcc\",\n    \"panel.background\": \"#011627\",\n    \"panel.border\": \"#5f7e97\",\n    \"panelTitle.activeBorder\": \"#5f7e97\",\n    \"panelTitle.activeForeground\": \"#ffffffcc\",\n    \"panelTitle.inactiveForeground\": \"#d6deeb80\",\n    \"peekView.border\": \"#5f7e97\",\n    \"peekViewEditor.background\": \"#011627\",\n    \"peekViewEditor.matchHighlightBackground\": \"#7e57c25a\",\n    \"peekViewResult.background\": \"#011627\",\n    \"peekViewResult.fileForeground\": \"#5f7e97\",\n    \"peekViewResult.lineForeground\": \"#5f7e97\",\n    \"peekViewResult.matchHighlightBackground\": \"#ffffffcc\",\n    \"peekViewResult.selectionBackground\": \"#2E3250\",\n    \"peekViewResult.selectionForeground\": \"#5f7e97\",\n    \"peekViewTitle.background\": \"#011627\",\n    \"peekViewTitleDescription.foreground\": \"#697098\",\n    \"peekViewTitleLabel.foreground\": \"#5f7e97\",\n    \"pickerGroup.border\": \"#011627\",\n    \"pickerGroup.foreground\": \"#d1aaff\",\n    \"progress.background\": \"#7e57c2\",\n    \"punctuation.definition.generic.begin.html\": \"#ef5350f2\",\n    \"scrollbar.shadow\": \"#010b14\",\n    \"scrollbarSlider.activeBackground\": \"#084d8180\",\n    \"scrollbarSlider.background\": \"#084d8180\",\n    \"scrollbarSlider.hoverBackground\": \"#084d8180\",\n    \"selection.background\": \"#4373c2\",\n    \"sideBar.background\": \"#011627\",\n    \"sideBar.border\": \"#011627\",\n    \"sideBar.foreground\": \"#89a4bb\",\n    \"sideBarSectionHeader.background\": \"#011627\",\n    \"sideBarSectionHeader.foreground\": \"#5f7e97\",\n    \"sideBarTitle.foreground\": \"#5f7e97\",\n    \"source.elm\": \"#5f7e97\",\n    \"statusBar.background\": \"#011627\",\n    \"statusBar.border\": \"#262A39\",\n    \"statusBar.debuggingBackground\": \"#202431\",\n    \"statusBar.debuggingBorder\": \"#1F2330\",\n    \"statusBar.debuggingForeground\": null,\n    \"statusBar.foreground\": \"#5f7e97\",\n    \"statusBar.noFolderBackground\": \"#011627\",\n    \"statusBar.noFolderBorder\": \"#25293A\",\n    \"statusBar.noFolderForeground\": null,\n    \"statusBarItem.activeBackground\": \"#202431\",\n    \"statusBarItem.hoverBackground\": \"#202431\",\n    \"statusBarItem.prominentBackground\": \"#202431\",\n    \"statusBarItem.prominentHoverBackground\": \"#202431\",\n    \"string.quoted.single.js\": \"#ffffff\",\n    \"tab.activeBackground\": \"#0b2942\",\n    \"tab.activeBorder\": \"#262A39\",\n    \"tab.activeForeground\": \"#d2dee7\",\n    \"tab.border\": \"#272B3B\",\n    \"tab.inactiveBackground\": \"#01111d\",\n    \"tab.inactiveForeground\": \"#5f7e97\",\n    \"tab.unfocusedActiveBorder\": \"#262A39\",\n    \"tab.unfocusedActiveForeground\": \"#5f7e97\",\n    \"tab.unfocusedInactiveForeground\": \"#5f7e97\",\n    \"terminal.ansiBlack\": \"#011627\",\n    \"terminal.ansiBlue\": \"#82AAFF\",\n    \"terminal.ansiBrightBlack\": \"#575656\",\n    \"terminal.ansiBrightBlue\": \"#82AAFF\",\n    \"terminal.ansiBrightCyan\": \"#7fdbca\",\n    \"terminal.ansiBrightGreen\": \"#22da6e\",\n    \"terminal.ansiBrightMagenta\": \"#C792EA\",\n    \"terminal.ansiBrightRed\": \"#EF5350\",\n    \"terminal.ansiBrightWhite\": \"#ffffff\",\n    \"terminal.ansiBrightYellow\": \"#ffeb95\",\n    \"terminal.ansiCyan\": \"#21c7a8\",\n    \"terminal.ansiGreen\": \"#22da6e\",\n    \"terminal.ansiMagenta\": \"#C792EA\",\n    \"terminal.ansiRed\": \"#EF5350\",\n    \"terminal.ansiWhite\": \"#ffffff\",\n    \"terminal.ansiYellow\": \"#c5e478\",\n    \"terminal.selectionBackground\": \"#1b90dd4d\",\n    \"terminalCursor.background\": \"#234d70\",\n    \"textCodeBlock.background\": \"#4f4f4f\",\n    \"titleBar.activeBackground\": \"#011627\",\n    \"titleBar.activeForeground\": \"#eeefff\",\n    \"titleBar.inactiveBackground\": \"#010e1a\",\n    \"titleBar.inactiveForeground\": null,\n    \"walkThrough.embeddedEditorBackground\": \"#011627\",\n    \"welcomePage.buttonBackground\": \"#011627\",\n    \"welcomePage.buttonHoverBackground\": \"#011627\",\n    \"widget.shadow\": \"#011627\"\n  },\n  \"displayName\": \"Night Owl\",\n  \"name\": \"night-owl\",\n  \"semanticHighlighting\": false,\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"markup.changed\",\n        \"meta.diff.header.git\",\n        \"meta.diff.header.from-file\",\n        \"meta.diff.header.to-file\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#a2bffc\"\n      }\n    },\n    {\n      \"scope\": \"markup.deleted.diff\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#EF535090\"\n      }\n    },\n    {\n      \"scope\": \"markup.inserted.diff\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c5e478ff\"\n      }\n    },\n    {\n      \"settings\": {\n        \"background\": \"#011627\",\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": \"comment\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#637777\"\n      }\n    },\n    {\n      \"scope\": \"string\",\n      \"settings\": {\n        \"foreground\": \"#ecc48d\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.quoted\",\n        \"variable.other.readwrite.js\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ecc48d\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.math\",\n      \"settings\": {\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.numeric\",\n        \"constant.character.numeric\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#F78C6C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.language\",\n        \"punctuation.definition.constant\",\n        \"variable.other.constant\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.character\",\n        \"constant.other\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": \"constant.character.escape\",\n      \"settings\": {\n        \"foreground\": \"#F78C6C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.regexp\",\n        \"string.regexp keyword.other\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5ca7e4\"\n      }\n    },\n    {\n      \"scope\": \"meta.function punctuation.separator.comma\",\n      \"settings\": {\n        \"foreground\": \"#5f7e97\"\n      }\n    },\n    {\n      \"scope\": \"variable\",\n      \"settings\": {\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.accessor\",\n        \"keyword\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage\",\n        \"meta.var.expr\",\n        \"meta.class meta.method.declaration meta.var.expr storage.type.js\",\n        \"storage.type.property.js\",\n        \"storage.type.property.ts\",\n        \"storage.type.property.tsx\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": \"storage.type\",\n      \"settings\": {\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": \"storage.type.function.arrow.js\",\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.class\",\n        \"meta.class entity.name.type.class\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffcb8b\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.inherited-class\",\n      \"settings\": {\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.tag\",\n        \"meta.tag\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7fdbca\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag\",\n        \"meta.tag.other.html\",\n        \"meta.tag.other.js\",\n        \"meta.tag.other.tsx\",\n        \"entity.name.tag.tsx\",\n        \"entity.name.tag.js\",\n        \"entity.name.tag\",\n        \"meta.tag.js\",\n        \"meta.tag.tsx\",\n        \"meta.tag.html\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#caece6\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.custom\",\n      \"settings\": {\n        \"foreground\": \"#f78c6c\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.function\",\n        \"support.constant\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.meta.property-value\",\n      \"settings\": {\n        \"foreground\": \"#7fdbca\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type\",\n        \"support.class\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": \"support.variable.dom\",\n      \"settings\": {\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": \"invalid\",\n      \"settings\": {\n        \"background\": \"#ff2c83\",\n        \"foreground\": \"#ffffff\"\n      }\n    },\n    {\n      \"scope\": \"invalid.deprecated\",\n      \"settings\": {\n        \"background\": \"#d3423e\",\n        \"foreground\": \"#ffffff\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#7fdbca\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.relational\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.assignment\",\n      \"settings\": {\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.arithmetic\",\n      \"settings\": {\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.bitwise\",\n      \"settings\": {\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.increment\",\n      \"settings\": {\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.ternary\",\n      \"settings\": {\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": \"comment.line.double-slash\",\n      \"settings\": {\n        \"foreground\": \"#637777\"\n      }\n    },\n    {\n      \"scope\": \"object\",\n      \"settings\": {\n        \"foreground\": \"#cdebf7\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.null\",\n      \"settings\": {\n        \"foreground\": \"#ff5874\"\n      }\n    },\n    {\n      \"scope\": \"meta.brace\",\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": \"meta.delimiter.period\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.string\",\n      \"settings\": {\n        \"foreground\": \"#d9f5dd\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.string.begin.markdown\",\n      \"settings\": {\n        \"foreground\": \"#ff5874\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.boolean\",\n      \"settings\": {\n        \"foreground\": \"#ff5874\"\n      }\n    },\n    {\n      \"scope\": \"object.comma\",\n      \"settings\": {\n        \"foreground\": \"#ffffff\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.function\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#7fdbca\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.type.vendor.property-name\",\n        \"support.constant.vendor.property-value\",\n        \"support.type.property-name\",\n        \"meta.property-list entity.name.tag\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#80CBC4\"\n      }\n    },\n    {\n      \"scope\": \"meta.property-list entity.name.tag.reference\",\n      \"settings\": {\n        \"foreground\": \"#57eaf1\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.color.rgb-value punctuation.definition.constant\",\n      \"settings\": {\n        \"foreground\": \"#F78C6C\"\n      }\n    },\n    {\n      \"scope\": \"constant.other.color\",\n      \"settings\": {\n        \"foreground\": \"#FFEB95\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.unit\",\n      \"settings\": {\n        \"foreground\": \"#FFEB95\"\n      }\n    },\n    {\n      \"scope\": \"meta.selector\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.id\",\n      \"settings\": {\n        \"foreground\": \"#FAD430\"\n      }\n    },\n    {\n      \"scope\": \"meta.property-name\",\n      \"settings\": {\n        \"foreground\": \"#80CBC4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag.doctype\",\n        \"meta.tag.sgml.doctype\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.parameters\",\n      \"settings\": {\n        \"foreground\": \"#d9f5dd\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control.operator\",\n      \"settings\": {\n        \"foreground\": \"#7fdbca\"\n      }\n    },\n    {\n      \"scope\": \"keyword.operator.logical\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.instance\",\n        \"variable.other.instance\",\n        \"variable.readwrite.instance\",\n        \"variable.other.readwrite.instance\",\n        \"variable.other.property\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#baebe2\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.object.property\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#faf39f\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.object.js\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.operator.comparison\",\n        \"keyword.control.flow.js\",\n        \"keyword.control.flow.ts\",\n        \"keyword.control.flow.tsx\",\n        \"keyword.control.ruby\",\n        \"keyword.control.module.ruby\",\n        \"keyword.control.class.ruby\",\n        \"keyword.control.def.ruby\",\n        \"keyword.control.loop.js\",\n        \"keyword.control.loop.ts\",\n        \"keyword.control.import.js\",\n        \"keyword.control.import.ts\",\n        \"keyword.control.import.tsx\",\n        \"keyword.control.from.js\",\n        \"keyword.control.from.ts\",\n        \"keyword.control.from.tsx\",\n        \"keyword.operator.instanceof.js\",\n        \"keyword.operator.expression.instanceof.ts\",\n        \"keyword.operator.expression.instanceof.tsx\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.control.conditional.js\",\n        \"keyword.control.conditional.ts\",\n        \"keyword.control.switch.js\",\n        \"keyword.control.switch.ts\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant\",\n        \"keyword.other.special-method\",\n        \"keyword.other.new\",\n        \"keyword.other.debugger\",\n        \"keyword.control\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7fdbca\"\n      }\n    },\n    {\n      \"scope\": \"support.function\",\n      \"settings\": {\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": \"invalid.broken\",\n      \"settings\": {\n        \"background\": \"#F78C6C\",\n        \"foreground\": \"#020e14\"\n      }\n    },\n    {\n      \"scope\": \"invalid.unimplemented\",\n      \"settings\": {\n        \"background\": \"#8BD649\",\n        \"foreground\": \"#ffffff\"\n      }\n    },\n    {\n      \"scope\": \"invalid.illegal\",\n      \"settings\": {\n        \"background\": \"#ec5f67\",\n        \"foreground\": \"#ffffff\"\n      }\n    },\n    {\n      \"scope\": \"variable.language\",\n      \"settings\": {\n        \"foreground\": \"#7fdbca\"\n      }\n    },\n    {\n      \"scope\": \"support.variable.property\",\n      \"settings\": {\n        \"foreground\": \"#7fdbca\"\n      }\n    },\n    {\n      \"scope\": \"variable.function\",\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": \"variable.interpolation\",\n      \"settings\": {\n        \"foreground\": \"#ec5f67\"\n      }\n    },\n    {\n      \"scope\": \"meta.function-call\",\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.section.embedded\",\n      \"settings\": {\n        \"foreground\": \"#d3423e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.terminator.expression\",\n        \"punctuation.definition.arguments\",\n        \"punctuation.definition.array\",\n        \"punctuation.section.array\",\n        \"meta.array\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.list.begin\",\n        \"punctuation.definition.list.end\",\n        \"punctuation.separator.arguments\",\n        \"punctuation.definition.list\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d9f5dd\"\n      }\n    },\n    {\n      \"scope\": \"string.template meta.template.expression\",\n      \"settings\": {\n        \"foreground\": \"#d3423e\"\n      }\n    },\n    {\n      \"scope\": \"string.template punctuation.definition.string\",\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": \"italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": \"bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": \"quote\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#697098\"\n      }\n    },\n    {\n      \"scope\": \"raw\",\n      \"settings\": {\n        \"foreground\": \"#80CBC4\"\n      }\n    },\n    {\n      \"scope\": \"variable.assignment.coffee\",\n      \"settings\": {\n        \"foreground\": \"#31e1eb\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.function.coffee\",\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": \"variable.assignment.coffee\",\n      \"settings\": {\n        \"foreground\": \"#7fdbca\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.readwrite.cs\",\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type.class.cs\",\n        \"storage.type.cs\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffcb8b\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.namespace.cs\",\n      \"settings\": {\n        \"foreground\": \"#B2CCD6\"\n      }\n    },\n    {\n      \"scope\": \"string.unquoted.preprocessor.message.cs\",\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.separator.hash.cs\",\n        \"keyword.preprocessor.region.cs\",\n        \"keyword.preprocessor.endregion.cs\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#ffcb8b\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.object.cs\",\n      \"settings\": {\n        \"foreground\": \"#B2CCD6\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.type.enum.cs\",\n      \"settings\": {\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.interpolated.single.dart\",\n        \"string.interpolated.double.dart\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FFCB8B\"\n      }\n    },\n    {\n      \"scope\": \"support.class.dart\",\n      \"settings\": {\n        \"foreground\": \"#FFCB8B\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag.css\",\n        \"entity.name.tag.less\",\n        \"entity.name.tag.custom.css\",\n        \"support.constant.property-value.css\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#ff6363\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag.wildcard.css\",\n        \"entity.name.tag.wildcard.less\",\n        \"entity.name.tag.wildcard.scss\",\n        \"entity.name.tag.wildcard.sass\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7fdbca\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.unit.css\",\n      \"settings\": {\n        \"foreground\": \"#FFEB95\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.attribute-selector.css entity.other.attribute-name.attribute\",\n        \"variable.other.readwrite.js\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F78C6C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.elixir support.type.elixir\",\n        \"source.elixir meta.module.elixir entity.name.class.elixir\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": \"source.elixir entity.name.function\",\n      \"settings\": {\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.elixir constant.other.symbol.elixir\",\n        \"source.elixir constant.other.keywords.elixir\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": \"source.elixir punctuation.definition.string\",\n      \"settings\": {\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.elixir variable.other.readwrite.module.elixir\",\n        \"source.elixir variable.other.readwrite.module.elixir punctuation.definition.variable.elixir\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": \"source.elixir .punctuation.binary.elixir\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": \"constant.keyword.clojure\",\n      \"settings\": {\n        \"foreground\": \"#7fdbca\"\n      }\n    },\n    {\n      \"scope\": \"source.go meta.function-call.go\",\n      \"settings\": {\n        \"foreground\": \"#DDDDDD\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.go keyword.package.go\",\n        \"source.go keyword.import.go\",\n        \"source.go keyword.function.go\",\n        \"source.go keyword.type.go\",\n        \"source.go keyword.struct.go\",\n        \"source.go keyword.interface.go\",\n        \"source.go keyword.const.go\",\n        \"source.go keyword.var.go\",\n        \"source.go keyword.map.go\",\n        \"source.go keyword.channel.go\",\n        \"source.go keyword.control.go\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.go constant.language.go\",\n        \"source.go constant.other.placeholder.go\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff5874\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.function.preprocessor.cpp\",\n        \"entity.scope.name.cpp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7fdbcaff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.namespace-block.cpp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e0dec6\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.type.language.primitive.cpp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff5874\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.preprocessor.macro.cpp\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffcb8b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.readwrite.powershell\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.function.powershell\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7fdbcaff\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.attribute-name.id.html\",\n      \"settings\": {\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.definition.tag.html\",\n      \"settings\": {\n        \"foreground\": \"#6ae9f0\"\n      }\n    },\n    {\n      \"scope\": \"meta.tag.sgml.doctype.html\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": \"meta.class entity.name.type.class.js\",\n      \"settings\": {\n        \"foreground\": \"#ffcb8b\"\n      }\n    },\n    {\n      \"scope\": \"meta.method.declaration storage.type.js\",\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": \"terminator.js\",\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": \"meta.js punctuation.definition.js\",\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type.instance.jsdoc\",\n        \"entity.name.type.instance.phpdoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5f7e97\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.jsdoc\",\n        \"variable.other.phpdoc\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#78ccf0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.meta.import.js\",\n        \"meta.import.js variable.other\",\n        \"variable.other.meta.export.js\",\n        \"meta.export.js variable.other\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": \"variable.parameter.function.js\",\n      \"settings\": {\n        \"foreground\": \"#7986E7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.object.js\",\n        \"variable.other.object.jsx\",\n        \"variable.object.property.js\",\n        \"variable.object.property.jsx\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.js\",\n        \"variable.other.js\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type.js\",\n        \"entity.name.type.module.js\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#ffcb8b\"\n      }\n    },\n    {\n      \"scope\": \"support.class.js\",\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.json\",\n      \"settings\": {\n        \"foreground\": \"#7fdbca\"\n      }\n    },\n    {\n      \"scope\": \"support.constant.json\",\n      \"settings\": {\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": \"meta.structure.dictionary.value.json string.quoted.double\",\n      \"settings\": {\n        \"foreground\": \"#c789d6\"\n      }\n    },\n    {\n      \"scope\": \"string.quoted.double.json punctuation.definition.string.json\",\n      \"settings\": {\n        \"foreground\": \"#80CBC4\"\n      }\n    },\n    {\n      \"scope\": \"meta.structure.dictionary.json meta.structure.dictionary.value constant.language\",\n      \"settings\": {\n        \"foreground\": \"#ff5874\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.object.js\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#7fdbca\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.ruby\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type.class.ruby\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ecc48d\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.symbol.hashkey.ruby\",\n      \"settings\": {\n        \"foreground\": \"#7fdbca\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.symbol.ruby\",\n      \"settings\": {\n        \"foreground\": \"#7fdbca\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.less\",\n      \"settings\": {\n        \"foreground\": \"#7fdbca\"\n      }\n    },\n    {\n      \"scope\": \"keyword.other.unit.css\",\n      \"settings\": {\n        \"foreground\": \"#FFEB95\"\n      }\n    },\n    {\n      \"scope\": \"meta.attribute-selector.less entity.other.attribute-name.attribute\",\n      \"settings\": {\n        \"foreground\": \"#F78C6C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.heading.markdown\",\n        \"markup.heading.setext.1.markdown\",\n        \"markup.heading.setext.2.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#82b1ff\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": \"markup.quote.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#697098\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw.markdown\",\n      \"settings\": {\n        \"foreground\": \"#80CBC4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.underline.link.markdown\",\n        \"markup.underline.link.image.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ff869a\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string.other.link.title.markdown\",\n        \"string.other.link.description.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.string.markdown\",\n        \"punctuation.definition.string.begin.markdown\",\n        \"punctuation.definition.string.end.markdown\",\n        \"meta.link.inline.markdown punctuation.definition.string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#82b1ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.metadata.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7fdbca\"\n      }\n    },\n    {\n      \"scope\": [\n        \"beginning.punctuation.definition.list.markdown\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#82b1ff\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw.string.markdown\",\n      \"settings\": {\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.php\",\n        \"variable.other.property.php\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bec5d4\"\n      }\n    },\n    {\n      \"scope\": \"support.class.php\",\n      \"settings\": {\n        \"foreground\": \"#ffcb8b\"\n      }\n    },\n    {\n      \"scope\": \"meta.function-call.php punctuation\",\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.global.php\",\n      \"settings\": {\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": \"variable.other.global.php punctuation.definition.variable\",\n      \"settings\": {\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": \"constant.language.python\",\n      \"settings\": {\n        \"foreground\": \"#ff5874\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter.function.python\",\n        \"meta.function-call.arguments.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.function-call.python\",\n        \"meta.function-call.generic.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#B2CCD6\"\n      }\n    },\n    {\n      \"scope\": \"punctuation.python\",\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.function.decorator.python\",\n      \"settings\": {\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": \"source.python variable.language.special\",\n      \"settings\": {\n        \"foreground\": \"#8EACE3\"\n      }\n    },\n    {\n      \"scope\": \"keyword.control\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c792ea\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.scss\",\n        \"variable.sass\",\n        \"variable.parameter.url.scss\",\n        \"variable.parameter.url.sass\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c5e478\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css.scss meta.at-rule variable\",\n        \"source.css.sass meta.at-rule variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"source.css.scss meta.at-rule variable\",\n        \"source.css.sass meta.at-rule variable\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#bec5d4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.attribute-selector.scss entity.other.attribute-name.attribute\",\n        \"meta.attribute-selector.sass entity.other.attribute-name.attribute\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F78C6C\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.tag.scss\",\n        \"entity.name.tag.sass\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#7fdbca\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword.other.unit.scss\",\n        \"keyword.other.unit.sass\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#FFEB95\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.readwrite.alias.ts\",\n        \"variable.other.readwrite.alias.tsx\",\n        \"variable.other.readwrite.ts\",\n        \"variable.other.readwrite.tsx\",\n        \"variable.other.object.ts\",\n        \"variable.other.object.tsx\",\n        \"variable.object.property.ts\",\n        \"variable.object.property.tsx\",\n        \"variable.other.ts\",\n        \"variable.other.tsx\",\n        \"variable.tsx\",\n        \"variable.ts\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type.ts\",\n        \"entity.name.type.tsx\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffcb8b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.class.node.ts\",\n        \"support.class.node.tsx\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.type.parameters.ts entity.name.type\",\n        \"meta.type.parameters.tsx entity.name.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#5f7e97\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.import.ts punctuation.definition.block\",\n        \"meta.import.tsx punctuation.definition.block\",\n        \"meta.export.ts punctuation.definition.block\",\n        \"meta.export.tsx punctuation.definition.block\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.decorator punctuation.decorator.ts\",\n        \"meta.decorator punctuation.decorator.tsx\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": \"meta.tag.js meta.jsx.children.tsx\",\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": \"entity.name.tag.yaml\",\n      \"settings\": {\n        \"foreground\": \"#7fdbca\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other.readwrite.js\",\n        \"variable.parameter\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d7dbe0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.class.component.js\",\n        \"support.class.component.tsx\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#f78c6c\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.jsx.children\",\n        \"meta.jsx.children.js\",\n        \"meta.jsx.children.tsx\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#d6deeb\"\n      }\n    },\n    {\n      \"scope\": \"meta.class entity.name.type.class.tsx\",\n      \"settings\": {\n        \"foreground\": \"#ffcb8b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.type.tsx\",\n        \"entity.name.type.module.tsx\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ffcb8b\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.class.ts meta.var.expr.ts storage.type.ts\",\n        \"meta.class.tsx meta.var.expr.tsx storage.type.tsx\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#C792EA\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.method.declaration storage.type.ts\",\n        \"meta.method.declaration storage.type.tsx\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#82AAFF\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.property-list.css meta.property-value.css variable.other.less\",\n        \"meta.property-list.scss variable.scss\",\n        \"meta.property-list.sass variable.sass\",\n        \"meta.brace\",\n        \"keyword.operator.operator\",\n        \"keyword.operator.or.regexp\",\n        \"keyword.operator.expression.in\",\n        \"keyword.operator.relational\",\n        \"keyword.operator.assignment\",\n        \"keyword.operator.comparison\",\n        \"keyword.operator.type\",\n        \"keyword.operator\",\n        \"keyword\",\n        \"punctuation.definintion.string\",\n        \"punctuation\",\n        \"variable.other.readwrite.js\",\n        \"storage.type\",\n        \"source.css\",\n        \"string.quoted\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { nightOwl as default };\n"], "mappings": ";;;AAAA,IAAI,WAAW,OAAO,OAAO;AAAA,EAC3B,UAAU;AAAA,IACR,0BAA0B;AAAA,IAC1B,sBAAsB;AAAA,IACtB,8BAA8B;AAAA,IAC9B,0BAA0B;AAAA,IAC1B,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,wCAAwC;AAAA,IACxC,8BAA8B;AAAA,IAC9B,yBAAyB;AAAA,IACzB,+BAA+B;AAAA,IAC/B,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,0BAA0B;AAAA,IAC1B,kBAAkB;AAAA,IAClB,mCAAmC;AAAA,IACnC,+BAA+B;AAAA,IAC/B,2BAA2B;AAAA,IAC3B,qCAAqC;AAAA,IACrC,iCAAiC;AAAA,IACjC,oCAAoC;AAAA,IACpC,gCAAgC;AAAA,IAChC,uBAAuB;AAAA,IACvB,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,IACvB,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,uCAAuC;AAAA,IACvC,uCAAuC;AAAA,IACvC,qBAAqB;AAAA,IACrB,mCAAmC;AAAA,IACnC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,mCAAmC;AAAA,IACnC,8BAA8B;AAAA,IAC9B,uCAAuC;AAAA,IACvC,kCAAkC;AAAA,IAClC,wCAAwC;AAAA,IACxC,iCAAiC;AAAA,IACjC,6BAA6B;AAAA,IAC7B,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,sBAAsB;AAAA,IACtB,0BAA0B;AAAA,IAC1B,sBAAsB;AAAA,IACtB,8BAA8B;AAAA,IAC9B,+BAA+B;AAAA,IAC/B,sCAAsC;AAAA,IACtC,oCAAoC;AAAA,IACpC,gCAAgC;AAAA,IAChC,gCAAgC;AAAA,IAChC,2BAA2B;AAAA,IAC3B,kCAAkC;AAAA,IAClC,mCAAmC;AAAA,IACnC,gCAAgC;AAAA,IAChC,4BAA4B;AAAA,IAC5B,sCAAsC;AAAA,IACtC,gCAAgC;AAAA,IAChC,qCAAqC;AAAA,IACrC,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,qCAAqC;AAAA,IACrC,0CAA0C;AAAA,IAC1C,4CAA4C;AAAA,IAC5C,+CAA+C;AAAA,IAC/C,gDAAgD;AAAA,IAChD,iDAAiD;AAAA,IACjD,0BAA0B;AAAA,IAC1B,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,kCAAkC;AAAA,IAClC,2CAA2C;AAAA,IAC3C,0CAA0C;AAAA,IAC1C,wBAAwB;AAAA,IACxB,4BAA4B;AAAA,IAC5B,+BAA+B;AAAA,IAC/B,2BAA2B;AAAA,IAC3B,uBAAuB;AAAA,IACvB,mBAAmB;AAAA,IACnB,uCAAuC;AAAA,IACvC,uCAAuC;AAAA,IACvC,4CAA4C;AAAA,IAC5C,eAAe;AAAA,IACf,cAAc;AAAA,IACd,+CAA+C;AAAA,IAC/C,2CAA2C;AAAA,IAC3C,2CAA2C;AAAA,IAC3C,4CAA4C;AAAA,IAC5C,6CAA6C;AAAA,IAC7C,oBAAoB;AAAA,IACpB,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,mCAAmC;AAAA,IACnC,+BAA+B;AAAA,IAC/B,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,qCAAqC;AAAA,IACrC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,uBAAuB;AAAA,IACvB,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,4BAA4B;AAAA,IAC5B,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,oCAAoC;AAAA,IACpC,oCAAoC;AAAA,IACpC,8BAA8B;AAAA,IAC9B,gBAAgB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,mCAAmC;AAAA,IACnC,kCAAkC;AAAA,IAClC,yBAAyB;AAAA,IACzB,6BAA6B;AAAA,IAC7B,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,wBAAwB;AAAA,IACxB,4BAA4B;AAAA,IAC5B,oBAAoB;AAAA,IACpB,gBAAgB;AAAA,IAChB,2BAA2B;AAAA,IAC3B,+BAA+B;AAAA,IAC/B,iCAAiC;AAAA,IACjC,mBAAmB;AAAA,IACnB,6BAA6B;AAAA,IAC7B,2CAA2C;AAAA,IAC3C,6BAA6B;AAAA,IAC7B,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,4BAA4B;AAAA,IAC5B,uCAAuC;AAAA,IACvC,iCAAiC;AAAA,IACjC,sBAAsB;AAAA,IACtB,0BAA0B;AAAA,IAC1B,uBAAuB;AAAA,IACvB,6CAA6C;AAAA,IAC7C,oBAAoB;AAAA,IACpB,oCAAoC;AAAA,IACpC,8BAA8B;AAAA,IAC9B,mCAAmC;AAAA,IACnC,wBAAwB;AAAA,IACxB,sBAAsB;AAAA,IACtB,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,mCAAmC;AAAA,IACnC,mCAAmC;AAAA,IACnC,2BAA2B;AAAA,IAC3B,cAAc;AAAA,IACd,wBAAwB;AAAA,IACxB,oBAAoB;AAAA,IACpB,iCAAiC;AAAA,IACjC,6BAA6B;AAAA,IAC7B,iCAAiC;AAAA,IACjC,wBAAwB;AAAA,IACxB,gCAAgC;AAAA,IAChC,4BAA4B;AAAA,IAC5B,gCAAgC;AAAA,IAChC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,qCAAqC;AAAA,IACrC,0CAA0C;AAAA,IAC1C,2BAA2B;AAAA,IAC3B,wBAAwB;AAAA,IACxB,oBAAoB;AAAA,IACpB,wBAAwB;AAAA,IACxB,cAAc;AAAA,IACd,0BAA0B;AAAA,IAC1B,0BAA0B;AAAA,IAC1B,6BAA6B;AAAA,IAC7B,iCAAiC;AAAA,IACjC,mCAAmC;AAAA,IACnC,sBAAsB;AAAA,IACtB,qBAAqB;AAAA,IACrB,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,4BAA4B;AAAA,IAC5B,8BAA8B;AAAA,IAC9B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,6BAA6B;AAAA,IAC7B,qBAAqB;AAAA,IACrB,sBAAsB;AAAA,IACtB,wBAAwB;AAAA,IACxB,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,uBAAuB;AAAA,IACvB,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,6BAA6B;AAAA,IAC7B,6BAA6B;AAAA,IAC7B,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,wCAAwC;AAAA,IACxC,gCAAgC;AAAA,IAChC,qCAAqC;AAAA,IACrC,iBAAiB;AAAA,EACnB;AAAA,EACA,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,wBAAwB;AAAA,EACxB,eAAe;AAAA,IACb;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,YAAY;AAAA,QACV,cAAc;AAAA,QACd,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,QACd,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,QACd,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,QACd,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,QACd,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,QACd,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AACV,CAAC;", "names": []}