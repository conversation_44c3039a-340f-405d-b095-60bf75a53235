{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/themes/red.mjs"], "sourcesContent": ["var red = Object.freeze({\n  \"colors\": {\n    \"activityBar.background\": \"#580000\",\n    \"badge.background\": \"#cc3333\",\n    \"button.background\": \"#833\",\n    \"debugToolBar.background\": \"#660000\",\n    \"dropdown.background\": \"#580000\",\n    \"editor.background\": \"#390000\",\n    \"editor.foreground\": \"#F8F8F8\",\n    \"editor.hoverHighlightBackground\": \"#ff000044\",\n    \"editor.lineHighlightBackground\": \"#ff000033\",\n    \"editor.selectionBackground\": \"#750000\",\n    \"editor.selectionHighlightBackground\": \"#f5500039\",\n    \"editorCursor.foreground\": \"#970000\",\n    \"editorGroup.border\": \"#ff666633\",\n    \"editorGroupHeader.tabsBackground\": \"#330000\",\n    \"editorHoverWidget.background\": \"#300000\",\n    \"editorLineNumber.activeForeground\": \"#ffbbbb88\",\n    \"editorLineNumber.foreground\": \"#ff777788\",\n    \"editorLink.activeForeground\": \"#FFD0AA\",\n    \"editorSuggestWidget.background\": \"#300000\",\n    \"editorSuggestWidget.border\": \"#220000\",\n    \"editorWhitespace.foreground\": \"#c10000\",\n    \"editorWidget.background\": \"#300000\",\n    \"errorForeground\": \"#ffeaea\",\n    \"extensionButton.prominentBackground\": \"#cc3333\",\n    \"extensionButton.prominentHoverBackground\": \"#cc333388\",\n    \"focusBorder\": \"#ff6666aa\",\n    \"input.background\": \"#580000\",\n    \"inputOption.activeBorder\": \"#cc0000\",\n    \"inputValidation.infoBackground\": \"#550000\",\n    \"inputValidation.infoBorder\": \"#DB7E58\",\n    \"list.activeSelectionBackground\": \"#880000\",\n    \"list.dropBackground\": \"#662222\",\n    \"list.highlightForeground\": \"#ff4444\",\n    \"list.hoverBackground\": \"#800000\",\n    \"list.inactiveSelectionBackground\": \"#770000\",\n    \"minimap.selectionHighlight\": \"#750000\",\n    \"peekView.border\": \"#ff000044\",\n    \"peekViewEditor.background\": \"#300000\",\n    \"peekViewResult.background\": \"#400000\",\n    \"peekViewTitle.background\": \"#550000\",\n    \"pickerGroup.border\": \"#ff000033\",\n    \"pickerGroup.foreground\": \"#cc9999\",\n    \"ports.iconRunningProcessForeground\": \"#DB7E58\",\n    \"progressBar.background\": \"#cc3333\",\n    \"quickInputList.focusBackground\": \"#660000\",\n    \"selection.background\": \"#ff777788\",\n    \"sideBar.background\": \"#330000\",\n    \"statusBar.background\": \"#700000\",\n    \"statusBar.noFolderBackground\": \"#700000\",\n    \"statusBarItem.remoteBackground\": \"#c33\",\n    \"tab.activeBackground\": \"#490000\",\n    \"tab.inactiveBackground\": \"#300a0a\",\n    \"tab.lastPinnedBorder\": \"#ff000044\",\n    \"titleBar.activeBackground\": \"#770000\",\n    \"titleBar.inactiveBackground\": \"#772222\"\n  },\n  \"displayName\": \"Red\",\n  \"name\": \"red\",\n  \"semanticHighlighting\": true,\n  \"tokenColors\": [\n    {\n      \"settings\": {\n        \"foreground\": \"#F8F8F8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.embedded\",\n        \"source.groovy.embedded\",\n        \"string meta.image.inline.markdown\",\n        \"variable.legacy.builtin.python\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#F8F8F8\"\n      }\n    },\n    {\n      \"scope\": \"comment\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#e7c0c0ff\"\n      }\n    },\n    {\n      \"scope\": \"constant\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#994646ff\"\n      }\n    },\n    {\n      \"scope\": \"keyword\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#f12727ff\"\n      }\n    },\n    {\n      \"scope\": \"entity\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#fec758ff\"\n      }\n    },\n    {\n      \"scope\": \"storage\",\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#ff6262ff\"\n      }\n    },\n    {\n      \"scope\": \"string\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#cd8d8dff\"\n      }\n    },\n    {\n      \"scope\": \"support\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#9df39fff\"\n      }\n    },\n    {\n      \"scope\": \"variable\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#fb9a4bff\"\n      }\n    },\n    {\n      \"scope\": \"invalid\",\n      \"settings\": {\n        \"foreground\": \"#ffffffff\"\n      }\n    },\n    {\n      \"scope\": \"entity.other.inherited-class\",\n      \"settings\": {\n        \"fontStyle\": \"underline\",\n        \"foreground\": \"#aa5507ff\"\n      }\n    },\n    {\n      \"scope\": \"constant.character\",\n      \"settings\": {\n        \"foreground\": \"#ec0d1e\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string constant\",\n        \"constant.character.escape\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#ffe862ff\"\n      }\n    },\n    {\n      \"scope\": \"string.regexp\",\n      \"settings\": {\n        \"foreground\": \"#ffb454ff\"\n      }\n    },\n    {\n      \"scope\": \"string variable\",\n      \"settings\": {\n        \"foreground\": \"#edef7dff\"\n      }\n    },\n    {\n      \"scope\": \"support.function\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#ffb454ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant\",\n        \"support.variable\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#eb939aff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"declaration.sgml.html declaration.doctype\",\n        \"declaration.sgml.html declaration.doctype entity\",\n        \"declaration.sgml.html declaration.doctype string\",\n        \"declaration.xml-processing\",\n        \"declaration.xml-processing entity\",\n        \"declaration.xml-processing string\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#73817dff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"declaration.tag\",\n        \"declaration.tag entity\",\n        \"meta.tag\",\n        \"meta.tag entity\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#ec0d1eff\"\n      }\n    },\n    {\n      \"scope\": \"meta.selector.css entity.name.tag\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#aa5507ff\"\n      }\n    },\n    {\n      \"scope\": \"meta.selector.css entity.other.attribute-name.id\",\n      \"settings\": {\n        \"foreground\": \"#fec758ff\"\n      }\n    },\n    {\n      \"scope\": \"meta.selector.css entity.other.attribute-name.class\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#41a83eff\"\n      }\n    },\n    {\n      \"scope\": \"support.type.property-name.css\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#96dd3bff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.property-group support.constant.property-value.css\",\n        \"meta.property-value support.constant.property-value.css\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ffe862ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.property-value support.constant.named-color.css\",\n        \"meta.property-value constant\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#ffe862ff\"\n      }\n    },\n    {\n      \"scope\": \"meta.preprocessor.at-rule keyword.control.at-rule\",\n      \"settings\": {\n        \"foreground\": \"#fd6209ff\"\n      }\n    },\n    {\n      \"scope\": \"meta.constructor.argument.css\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#ec9799ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.diff\",\n        \"meta.diff.header\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#f8f8f8ff\"\n      }\n    },\n    {\n      \"scope\": \"markup.deleted\",\n      \"settings\": {\n        \"foreground\": \"#ec9799ff\"\n      }\n    },\n    {\n      \"scope\": \"markup.changed\",\n      \"settings\": {\n        \"foreground\": \"#f8f8f8ff\"\n      }\n    },\n    {\n      \"scope\": \"markup.inserted\",\n      \"settings\": {\n        \"foreground\": \"#41a83eff\"\n      }\n    },\n    {\n      \"scope\": \"markup.quote\",\n      \"settings\": {\n        \"foreground\": \"#f12727ff\"\n      }\n    },\n    {\n      \"scope\": \"markup.list\",\n      \"settings\": {\n        \"foreground\": \"#ff6262ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.bold\",\n        \"markup.italic\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#fb9a4bff\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": \"markup.strikethrough\",\n      \"settings\": {\n        \"fontStyle\": \"strikethrough\"\n      }\n    },\n    {\n      \"scope\": \"markup.inline.raw\",\n      \"settings\": {\n        \"fontStyle\": \"\",\n        \"foreground\": \"#cd8d8dff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.heading\",\n        \"markup.heading.setext\",\n        \"punctuation.definition.heading\",\n        \"entity.name.section\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"bold\",\n        \"foreground\": \"#fec758ff\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.template-expression.begin\",\n        \"punctuation.definition.template-expression.end\",\n        \"punctuation.section.embedded\",\n        \".format.placeholder\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ec0d1e\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { red as default };\n"], "mappings": ";;;AAAA,IAAI,MAAM,OAAO,OAAO;AAAA,EACtB,UAAU;AAAA,IACR,0BAA0B;AAAA,IAC1B,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,2BAA2B;AAAA,IAC3B,uBAAuB;AAAA,IACvB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,mCAAmC;AAAA,IACnC,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,uCAAuC;AAAA,IACvC,2BAA2B;AAAA,IAC3B,sBAAsB;AAAA,IACtB,oCAAoC;AAAA,IACpC,gCAAgC;AAAA,IAChC,qCAAqC;AAAA,IACrC,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,+BAA+B;AAAA,IAC/B,2BAA2B;AAAA,IAC3B,mBAAmB;AAAA,IACnB,uCAAuC;AAAA,IACvC,4CAA4C;AAAA,IAC5C,eAAe;AAAA,IACf,oBAAoB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,kCAAkC;AAAA,IAClC,uBAAuB;AAAA,IACvB,4BAA4B;AAAA,IAC5B,wBAAwB;AAAA,IACxB,oCAAoC;AAAA,IACpC,8BAA8B;AAAA,IAC9B,mBAAmB;AAAA,IACnB,6BAA6B;AAAA,IAC7B,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sBAAsB;AAAA,IACtB,0BAA0B;AAAA,IAC1B,sCAAsC;AAAA,IACtC,0BAA0B;AAAA,IAC1B,kCAAkC;AAAA,IAClC,wBAAwB;AAAA,IACxB,sBAAsB;AAAA,IACtB,wBAAwB;AAAA,IACxB,gCAAgC;AAAA,IAChC,kCAAkC;AAAA,IAClC,wBAAwB;AAAA,IACxB,0BAA0B;AAAA,IAC1B,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,+BAA+B;AAAA,EACjC;AAAA,EACA,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,wBAAwB;AAAA,EACxB,eAAe;AAAA,IACb;AAAA,MACE,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AACV,CAAC;", "names": []}