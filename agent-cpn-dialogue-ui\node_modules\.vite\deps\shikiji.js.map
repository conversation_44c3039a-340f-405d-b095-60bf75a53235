{"version": 3, "sources": ["../../.pnpm/shikiji-core@0.10.2/node_modules/shikiji-core/dist/wasm-inlined.mjs", "../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs.mjs", "../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/themes.mjs", "../../.pnpm/shikiji-core@0.10.2/node_modules/shikiji-core/dist/types.mjs", "../../.pnpm/shikiji-core@0.10.2/node_modules/shikiji-core/dist/textmate.mjs", "../../.pnpm/shikiji-core@0.10.2/node_modules/shikiji-core/dist/index.mjs", "../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/bundle-full.mjs", "../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/theme-css-variables.mjs"], "sourcesContent": ["const getWasm = async (info) => {\n    // @ts-expect-error this will be compiled to ArrayBuffer\n    const binray = await import('./onig.mjs').then(m => m.default);\n    return WebAssembly.instantiate(binray, info).then(wasm => wasm.instance.exports);\n};\n\nexport { getWasm as default };\n", "const bundledLanguagesInfo = [\n  {\n    \"id\": \"abap\",\n    \"name\": \"ABAP\",\n    \"import\": () => import('./langs/abap.mjs')\n  },\n  {\n    \"id\": \"actionscript-3\",\n    \"name\": \"ActionScript\",\n    \"import\": () => import('./langs/actionscript-3.mjs')\n  },\n  {\n    \"id\": \"ada\",\n    \"name\": \"Ada\",\n    \"import\": () => import('./langs/ada.mjs')\n  },\n  {\n    \"id\": \"angular-html\",\n    \"name\": \"Angular HTML\",\n    \"import\": () => import('./langs/angular-html.mjs').then(function (n) { return n.e; })\n  },\n  {\n    \"id\": \"angular-ts\",\n    \"name\": \"Angular TypeScript\",\n    \"import\": () => import('./langs/angular-ts.mjs')\n  },\n  {\n    \"id\": \"apache\",\n    \"name\": \"Apache Conf\",\n    \"import\": () => import('./langs/apache.mjs')\n  },\n  {\n    \"id\": \"apex\",\n    \"name\": \"Apex\",\n    \"import\": () => import('./langs/apex.mjs')\n  },\n  {\n    \"id\": \"apl\",\n    \"name\": \"APL\",\n    \"import\": () => import('./langs/apl.mjs')\n  },\n  {\n    \"id\": \"applescript\",\n    \"name\": \"AppleScript\",\n    \"import\": () => import('./langs/applescript.mjs')\n  },\n  {\n    \"id\": \"ara\",\n    \"name\": \"Ara\",\n    \"import\": () => import('./langs/ara.mjs')\n  },\n  {\n    \"id\": \"asm\",\n    \"name\": \"Assembly\",\n    \"import\": () => import('./langs/asm.mjs')\n  },\n  {\n    \"id\": \"astro\",\n    \"name\": \"Astro\",\n    \"import\": () => import('./langs/astro.mjs')\n  },\n  {\n    \"id\": \"awk\",\n    \"name\": \"AWK\",\n    \"import\": () => import('./langs/awk.mjs')\n  },\n  {\n    \"id\": \"ballerina\",\n    \"name\": \"Ballerina\",\n    \"import\": () => import('./langs/ballerina.mjs')\n  },\n  {\n    \"id\": \"bat\",\n    \"name\": \"Batch File\",\n    \"aliases\": [\n      \"batch\"\n    ],\n    \"import\": () => import('./langs/bat.mjs')\n  },\n  {\n    \"id\": \"beancount\",\n    \"name\": \"Beancount\",\n    \"import\": () => import('./langs/beancount.mjs')\n  },\n  {\n    \"id\": \"berry\",\n    \"name\": \"Berry\",\n    \"aliases\": [\n      \"be\"\n    ],\n    \"import\": () => import('./langs/berry.mjs')\n  },\n  {\n    \"id\": \"bibtex\",\n    \"name\": \"BibTeX\",\n    \"import\": () => import('./langs/bibtex.mjs')\n  },\n  {\n    \"id\": \"bicep\",\n    \"name\": \"Bicep\",\n    \"import\": () => import('./langs/bicep.mjs')\n  },\n  {\n    \"id\": \"blade\",\n    \"name\": \"Blade\",\n    \"import\": () => import('./langs/blade.mjs')\n  },\n  {\n    \"id\": \"c\",\n    \"name\": \"C\",\n    \"import\": () => import('./langs/c.mjs')\n  },\n  {\n    \"id\": \"cadence\",\n    \"name\": \"Cadence\",\n    \"aliases\": [\n      \"cdc\"\n    ],\n    \"import\": () => import('./langs/cadence.mjs')\n  },\n  {\n    \"id\": \"clarity\",\n    \"name\": \"Clarity\",\n    \"import\": () => import('./langs/clarity.mjs')\n  },\n  {\n    \"id\": \"clojure\",\n    \"name\": \"Clojure\",\n    \"aliases\": [\n      \"clj\"\n    ],\n    \"import\": () => import('./langs/clojure.mjs')\n  },\n  {\n    \"id\": \"cmake\",\n    \"name\": \"CMake\",\n    \"import\": () => import('./langs/cmake.mjs')\n  },\n  {\n    \"id\": \"cobol\",\n    \"name\": \"COBOL\",\n    \"import\": () => import('./langs/cobol.mjs')\n  },\n  {\n    \"id\": \"codeql\",\n    \"name\": \"CodeQL\",\n    \"aliases\": [\n      \"ql\"\n    ],\n    \"import\": () => import('./langs/codeql.mjs')\n  },\n  {\n    \"id\": \"coffee\",\n    \"name\": \"CoffeeScript\",\n    \"aliases\": [\n      \"coffeescript\"\n    ],\n    \"import\": () => import('./langs/coffee.mjs')\n  },\n  {\n    \"id\": \"cpp\",\n    \"name\": \"C++\",\n    \"aliases\": [\n      \"c++\"\n    ],\n    \"import\": () => import('./langs/cpp.mjs')\n  },\n  {\n    \"id\": \"crystal\",\n    \"name\": \"Crystal\",\n    \"import\": () => import('./langs/crystal.mjs')\n  },\n  {\n    \"id\": \"csharp\",\n    \"name\": \"C#\",\n    \"aliases\": [\n      \"c#\",\n      \"cs\"\n    ],\n    \"import\": () => import('./langs/csharp.mjs')\n  },\n  {\n    \"id\": \"css\",\n    \"name\": \"CSS\",\n    \"import\": () => import('./langs/css.mjs')\n  },\n  {\n    \"id\": \"csv\",\n    \"name\": \"csv syntax\",\n    \"import\": () => import('./langs/csv.mjs')\n  },\n  {\n    \"id\": \"cue\",\n    \"name\": \"CUE\",\n    \"import\": () => import('./langs/cue.mjs')\n  },\n  {\n    \"id\": \"cypher\",\n    \"name\": \"Cypher\",\n    \"aliases\": [\n      \"cql\"\n    ],\n    \"import\": () => import('./langs/cypher.mjs')\n  },\n  {\n    \"id\": \"d\",\n    \"name\": \"D\",\n    \"import\": () => import('./langs/d.mjs')\n  },\n  {\n    \"id\": \"dart\",\n    \"name\": \"Dart\",\n    \"import\": () => import('./langs/dart.mjs')\n  },\n  {\n    \"id\": \"dax\",\n    \"name\": \"DAX\",\n    \"import\": () => import('./langs/dax.mjs')\n  },\n  {\n    \"id\": \"diff\",\n    \"name\": \"Diff\",\n    \"import\": () => import('./langs/diff.mjs')\n  },\n  {\n    \"id\": \"docker\",\n    \"name\": \"Dockerfile\",\n    \"aliases\": [\n      \"dockerfile\"\n    ],\n    \"import\": () => import('./langs/docker.mjs')\n  },\n  {\n    \"id\": \"dream-maker\",\n    \"name\": \"Dream Maker\",\n    \"import\": () => import('./langs/dream-maker.mjs')\n  },\n  {\n    \"id\": \"elixir\",\n    \"name\": \"Elixir\",\n    \"import\": () => import('./langs/elixir.mjs')\n  },\n  {\n    \"id\": \"elm\",\n    \"name\": \"Elm\",\n    \"import\": () => import('./langs/elm.mjs')\n  },\n  {\n    \"id\": \"erb\",\n    \"name\": \"ERB\",\n    \"import\": () => import('./langs/erb.mjs')\n  },\n  {\n    \"id\": \"erlang\",\n    \"name\": \"Erlang\",\n    \"aliases\": [\n      \"erl\"\n    ],\n    \"import\": () => import('./langs/erlang.mjs')\n  },\n  {\n    \"id\": \"fish\",\n    \"name\": \"Fish\",\n    \"import\": () => import('./langs/fish.mjs')\n  },\n  {\n    \"id\": \"fsharp\",\n    \"name\": \"F#\",\n    \"aliases\": [\n      \"f#\",\n      \"fs\"\n    ],\n    \"import\": () => import('./langs/fsharp.mjs')\n  },\n  {\n    \"id\": \"gdresource\",\n    \"name\": \"GDResource\",\n    \"import\": () => import('./langs/gdresource.mjs')\n  },\n  {\n    \"id\": \"gdscript\",\n    \"name\": \"GDScript\",\n    \"import\": () => import('./langs/gdscript.mjs')\n  },\n  {\n    \"id\": \"gdshader\",\n    \"name\": \"GDShader\",\n    \"import\": () => import('./langs/gdshader.mjs')\n  },\n  {\n    \"id\": \"gherkin\",\n    \"name\": \"Gherkin\",\n    \"import\": () => import('./langs/gherkin.mjs')\n  },\n  {\n    \"id\": \"git-commit\",\n    \"name\": \"Git Commit Message\",\n    \"import\": () => import('./langs/git-commit.mjs')\n  },\n  {\n    \"id\": \"git-rebase\",\n    \"name\": \"Git Rebase Message\",\n    \"import\": () => import('./langs/git-rebase.mjs')\n  },\n  {\n    \"id\": \"glimmer-js\",\n    \"name\": \"Glimmer JS\",\n    \"aliases\": [\n      \"gjs\"\n    ],\n    \"import\": () => import('./langs/glimmer-js.mjs')\n  },\n  {\n    \"id\": \"glimmer-ts\",\n    \"name\": \"Glimmer TS\",\n    \"aliases\": [\n      \"gts\"\n    ],\n    \"import\": () => import('./langs/glimmer-ts.mjs')\n  },\n  {\n    \"id\": \"glsl\",\n    \"name\": \"GLSL\",\n    \"import\": () => import('./langs/glsl.mjs')\n  },\n  {\n    \"id\": \"gnuplot\",\n    \"name\": \"Gnuplot\",\n    \"import\": () => import('./langs/gnuplot.mjs')\n  },\n  {\n    \"id\": \"go\",\n    \"name\": \"Go\",\n    \"import\": () => import('./langs/go.mjs')\n  },\n  {\n    \"id\": \"graphql\",\n    \"name\": \"GraphQL\",\n    \"aliases\": [\n      \"gql\"\n    ],\n    \"import\": () => import('./langs/graphql.mjs')\n  },\n  {\n    \"id\": \"groovy\",\n    \"name\": \"Groovy\",\n    \"import\": () => import('./langs/groovy.mjs')\n  },\n  {\n    \"id\": \"hack\",\n    \"name\": \"Hack\",\n    \"import\": () => import('./langs/hack.mjs')\n  },\n  {\n    \"id\": \"haml\",\n    \"name\": \"Ruby Haml\",\n    \"import\": () => import('./langs/haml.mjs')\n  },\n  {\n    \"id\": \"handlebars\",\n    \"name\": \"Handlebars\",\n    \"aliases\": [\n      \"hbs\"\n    ],\n    \"import\": () => import('./langs/handlebars.mjs')\n  },\n  {\n    \"id\": \"haskell\",\n    \"name\": \"Haskell\",\n    \"aliases\": [\n      \"hs\"\n    ],\n    \"import\": () => import('./langs/haskell.mjs')\n  },\n  {\n    \"id\": \"hcl\",\n    \"name\": \"HashiCorp HCL\",\n    \"import\": () => import('./langs/hcl.mjs')\n  },\n  {\n    \"id\": \"hjson\",\n    \"name\": \"Hjson\",\n    \"import\": () => import('./langs/hjson.mjs')\n  },\n  {\n    \"id\": \"hlsl\",\n    \"name\": \"HLSL\",\n    \"import\": () => import('./langs/hlsl.mjs')\n  },\n  {\n    \"id\": \"html\",\n    \"name\": \"HTML\",\n    \"import\": () => import('./langs/html.mjs')\n  },\n  {\n    \"id\": \"http\",\n    \"name\": \"HTTP\",\n    \"import\": () => import('./langs/http.mjs')\n  },\n  {\n    \"id\": \"imba\",\n    \"name\": \"Imba\",\n    \"import\": () => import('./langs/imba.mjs')\n  },\n  {\n    \"id\": \"ini\",\n    \"name\": \"INI\",\n    \"aliases\": [\n      \"properties\"\n    ],\n    \"import\": () => import('./langs/ini.mjs')\n  },\n  {\n    \"id\": \"java\",\n    \"name\": \"Java\",\n    \"import\": () => import('./langs/java.mjs')\n  },\n  {\n    \"id\": \"javascript\",\n    \"name\": \"JavaScript\",\n    \"aliases\": [\n      \"js\"\n    ],\n    \"import\": () => import('./langs/javascript.mjs')\n  },\n  {\n    \"id\": \"jinja\",\n    \"name\": \"Jinja\",\n    \"import\": () => import('./langs/jinja.mjs')\n  },\n  {\n    \"id\": \"jison\",\n    \"name\": \"Jison\",\n    \"import\": () => import('./langs/jison.mjs')\n  },\n  {\n    \"id\": \"json\",\n    \"name\": \"JSON\",\n    \"import\": () => import('./langs/json.mjs')\n  },\n  {\n    \"id\": \"json5\",\n    \"name\": \"JSON5\",\n    \"import\": () => import('./langs/json5.mjs')\n  },\n  {\n    \"id\": \"jsonc\",\n    \"name\": \"JSON with Comments\",\n    \"import\": () => import('./langs/jsonc.mjs')\n  },\n  {\n    \"id\": \"jsonl\",\n    \"name\": \"JSON Lines\",\n    \"import\": () => import('./langs/jsonl.mjs')\n  },\n  {\n    \"id\": \"jsonnet\",\n    \"name\": \"Jsonnet\",\n    \"import\": () => import('./langs/jsonnet.mjs')\n  },\n  {\n    \"id\": \"jssm\",\n    \"name\": \"JSSM\",\n    \"aliases\": [\n      \"fsl\"\n    ],\n    \"import\": () => import('./langs/jssm.mjs')\n  },\n  {\n    \"id\": \"jsx\",\n    \"name\": \"JSX\",\n    \"import\": () => import('./langs/jsx.mjs')\n  },\n  {\n    \"id\": \"julia\",\n    \"name\": \"Julia\",\n    \"import\": () => import('./langs/julia.mjs')\n  },\n  {\n    \"id\": \"kotlin\",\n    \"name\": \"Kotlin\",\n    \"aliases\": [\n      \"kt\",\n      \"kts\"\n    ],\n    \"import\": () => import('./langs/kotlin.mjs')\n  },\n  {\n    \"id\": \"kusto\",\n    \"name\": \"Kusto\",\n    \"aliases\": [\n      \"kql\"\n    ],\n    \"import\": () => import('./langs/kusto.mjs')\n  },\n  {\n    \"id\": \"latex\",\n    \"name\": \"LaTeX\",\n    \"import\": () => import('./langs/latex.mjs')\n  },\n  {\n    \"id\": \"less\",\n    \"name\": \"Less\",\n    \"import\": () => import('./langs/less.mjs')\n  },\n  {\n    \"id\": \"liquid\",\n    \"name\": \"Liquid\",\n    \"import\": () => import('./langs/liquid.mjs')\n  },\n  {\n    \"id\": \"lisp\",\n    \"name\": \"Lisp\",\n    \"import\": () => import('./langs/lisp.mjs')\n  },\n  {\n    \"id\": \"logo\",\n    \"name\": \"Logo\",\n    \"import\": () => import('./langs/logo.mjs')\n  },\n  {\n    \"id\": \"lua\",\n    \"name\": \"Lua\",\n    \"import\": () => import('./langs/lua.mjs')\n  },\n  {\n    \"id\": \"make\",\n    \"name\": \"Makefile\",\n    \"aliases\": [\n      \"makefile\"\n    ],\n    \"import\": () => import('./langs/make.mjs')\n  },\n  {\n    \"id\": \"markdown\",\n    \"name\": \"Markdown\",\n    \"aliases\": [\n      \"md\"\n    ],\n    \"import\": () => import('./langs/markdown.mjs')\n  },\n  {\n    \"id\": \"marko\",\n    \"name\": \"Marko\",\n    \"import\": () => import('./langs/marko.mjs')\n  },\n  {\n    \"id\": \"matlab\",\n    \"name\": \"MATLAB\",\n    \"import\": () => import('./langs/matlab.mjs')\n  },\n  {\n    \"id\": \"mdc\",\n    \"name\": \"MDC\",\n    \"import\": () => import('./langs/mdc.mjs')\n  },\n  {\n    \"id\": \"mdx\",\n    \"name\": \"MDX\",\n    \"import\": () => import('./langs/mdx.mjs')\n  },\n  {\n    \"id\": \"mermaid\",\n    \"name\": \"Mermaid\",\n    \"import\": () => import('./langs/mermaid.mjs')\n  },\n  {\n    \"id\": \"mojo\",\n    \"name\": \"Mojo\",\n    \"import\": () => import('./langs/mojo.mjs')\n  },\n  {\n    \"id\": \"narrat\",\n    \"name\": \"Narrat Language\",\n    \"aliases\": [\n      \"nar\"\n    ],\n    \"import\": () => import('./langs/narrat.mjs')\n  },\n  {\n    \"id\": \"nextflow\",\n    \"name\": \"Nextflow\",\n    \"aliases\": [\n      \"nf\"\n    ],\n    \"import\": () => import('./langs/nextflow.mjs')\n  },\n  {\n    \"id\": \"nginx\",\n    \"name\": \"Nginx\",\n    \"import\": () => import('./langs/nginx.mjs')\n  },\n  {\n    \"id\": \"nim\",\n    \"name\": \"Nim\",\n    \"import\": () => import('./langs/nim.mjs')\n  },\n  {\n    \"id\": \"nix\",\n    \"name\": \"Nix\",\n    \"import\": () => import('./langs/nix.mjs')\n  },\n  {\n    \"id\": \"nushell\",\n    \"name\": \"nushell\",\n    \"aliases\": [\n      \"nu\"\n    ],\n    \"import\": () => import('./langs/nushell.mjs')\n  },\n  {\n    \"id\": \"objective-c\",\n    \"name\": \"Objective-C\",\n    \"aliases\": [\n      \"objc\"\n    ],\n    \"import\": () => import('./langs/objective-c.mjs')\n  },\n  {\n    \"id\": \"objective-cpp\",\n    \"name\": \"Objective-C++\",\n    \"import\": () => import('./langs/objective-cpp.mjs')\n  },\n  {\n    \"id\": \"ocaml\",\n    \"name\": \"OCaml\",\n    \"import\": () => import('./langs/ocaml.mjs')\n  },\n  {\n    \"id\": \"pascal\",\n    \"name\": \"Pascal\",\n    \"import\": () => import('./langs/pascal.mjs')\n  },\n  {\n    \"id\": \"perl\",\n    \"name\": \"Perl\",\n    \"import\": () => import('./langs/perl.mjs')\n  },\n  {\n    \"id\": \"php\",\n    \"name\": \"PHP\",\n    \"import\": () => import('./langs/php.mjs')\n  },\n  {\n    \"id\": \"plsql\",\n    \"name\": \"PL/SQL\",\n    \"import\": () => import('./langs/plsql.mjs')\n  },\n  {\n    \"id\": \"postcss\",\n    \"name\": \"PostCSS\",\n    \"import\": () => import('./langs/postcss.mjs')\n  },\n  {\n    \"id\": \"powerquery\",\n    \"name\": \"PowerQuery\",\n    \"import\": () => import('./langs/powerquery.mjs')\n  },\n  {\n    \"id\": \"powershell\",\n    \"name\": \"PowerShell\",\n    \"aliases\": [\n      \"ps\",\n      \"ps1\"\n    ],\n    \"import\": () => import('./langs/powershell.mjs')\n  },\n  {\n    \"id\": \"prisma\",\n    \"name\": \"Prisma\",\n    \"import\": () => import('./langs/prisma.mjs')\n  },\n  {\n    \"id\": \"prolog\",\n    \"name\": \"Prolog\",\n    \"import\": () => import('./langs/prolog.mjs')\n  },\n  {\n    \"id\": \"proto\",\n    \"name\": \"Protocol Buffer 3\",\n    \"import\": () => import('./langs/proto.mjs')\n  },\n  {\n    \"id\": \"pug\",\n    \"name\": \"Pug\",\n    \"aliases\": [\n      \"jade\"\n    ],\n    \"import\": () => import('./langs/pug.mjs')\n  },\n  {\n    \"id\": \"puppet\",\n    \"name\": \"Puppet\",\n    \"import\": () => import('./langs/puppet.mjs')\n  },\n  {\n    \"id\": \"purescript\",\n    \"name\": \"PureScript\",\n    \"import\": () => import('./langs/purescript.mjs')\n  },\n  {\n    \"id\": \"python\",\n    \"name\": \"Python\",\n    \"aliases\": [\n      \"py\"\n    ],\n    \"import\": () => import('./langs/python.mjs')\n  },\n  {\n    \"id\": \"r\",\n    \"name\": \"R\",\n    \"import\": () => import('./langs/r.mjs')\n  },\n  {\n    \"id\": \"raku\",\n    \"name\": \"Raku\",\n    \"aliases\": [\n      \"perl6\"\n    ],\n    \"import\": () => import('./langs/raku.mjs')\n  },\n  {\n    \"id\": \"razor\",\n    \"name\": \"ASP.NET Razor\",\n    \"import\": () => import('./langs/razor.mjs')\n  },\n  {\n    \"id\": \"reg\",\n    \"name\": \"Windows Registry Script\",\n    \"import\": () => import('./langs/reg.mjs')\n  },\n  {\n    \"id\": \"rel\",\n    \"name\": \"Rel\",\n    \"import\": () => import('./langs/rel.mjs')\n  },\n  {\n    \"id\": \"riscv\",\n    \"name\": \"RISC-V\",\n    \"import\": () => import('./langs/riscv.mjs')\n  },\n  {\n    \"id\": \"rst\",\n    \"name\": \"reStructuredText\",\n    \"import\": () => import('./langs/rst.mjs')\n  },\n  {\n    \"id\": \"ruby\",\n    \"name\": \"Ruby\",\n    \"aliases\": [\n      \"rb\"\n    ],\n    \"import\": () => import('./langs/ruby.mjs')\n  },\n  {\n    \"id\": \"rust\",\n    \"name\": \"Rust\",\n    \"aliases\": [\n      \"rs\"\n    ],\n    \"import\": () => import('./langs/rust.mjs')\n  },\n  {\n    \"id\": \"sas\",\n    \"name\": \"SAS\",\n    \"import\": () => import('./langs/sas.mjs')\n  },\n  {\n    \"id\": \"sass\",\n    \"name\": \"Sass\",\n    \"import\": () => import('./langs/sass.mjs')\n  },\n  {\n    \"id\": \"scala\",\n    \"name\": \"Scala\",\n    \"import\": () => import('./langs/scala.mjs')\n  },\n  {\n    \"id\": \"scheme\",\n    \"name\": \"Scheme\",\n    \"import\": () => import('./langs/scheme.mjs')\n  },\n  {\n    \"id\": \"scss\",\n    \"name\": \"SCSS\",\n    \"import\": () => import('./langs/scss.mjs')\n  },\n  {\n    \"id\": \"shaderlab\",\n    \"name\": \"ShaderLab\",\n    \"aliases\": [\n      \"shader\"\n    ],\n    \"import\": () => import('./langs/shaderlab.mjs')\n  },\n  {\n    \"id\": \"shellscript\",\n    \"name\": \"Shell\",\n    \"aliases\": [\n      \"bash\",\n      \"sh\",\n      \"shell\",\n      \"zsh\"\n    ],\n    \"import\": () => import('./langs/shellscript.mjs')\n  },\n  {\n    \"id\": \"shellsession\",\n    \"name\": \"Shell Session\",\n    \"aliases\": [\n      \"console\"\n    ],\n    \"import\": () => import('./langs/shellsession.mjs')\n  },\n  {\n    \"id\": \"smalltalk\",\n    \"name\": \"Smalltalk\",\n    \"import\": () => import('./langs/smalltalk.mjs')\n  },\n  {\n    \"id\": \"solidity\",\n    \"name\": \"Solidity\",\n    \"import\": () => import('./langs/solidity.mjs')\n  },\n  {\n    \"id\": \"sparql\",\n    \"name\": \"SPARQL\",\n    \"import\": () => import('./langs/sparql.mjs')\n  },\n  {\n    \"id\": \"splunk\",\n    \"name\": \"Splunk Query Language\",\n    \"aliases\": [\n      \"spl\"\n    ],\n    \"import\": () => import('./langs/splunk.mjs')\n  },\n  {\n    \"id\": \"sql\",\n    \"name\": \"SQL\",\n    \"import\": () => import('./langs/sql.mjs')\n  },\n  {\n    \"id\": \"ssh-config\",\n    \"name\": \"SSH Config\",\n    \"import\": () => import('./langs/ssh-config.mjs')\n  },\n  {\n    \"id\": \"stata\",\n    \"name\": \"Stata\",\n    \"import\": () => import('./langs/stata.mjs')\n  },\n  {\n    \"id\": \"stylus\",\n    \"name\": \"Stylus\",\n    \"aliases\": [\n      \"styl\"\n    ],\n    \"import\": () => import('./langs/stylus.mjs')\n  },\n  {\n    \"id\": \"svelte\",\n    \"name\": \"Svelte\",\n    \"import\": () => import('./langs/svelte.mjs')\n  },\n  {\n    \"id\": \"swift\",\n    \"name\": \"Swift\",\n    \"import\": () => import('./langs/swift.mjs')\n  },\n  {\n    \"id\": \"system-verilog\",\n    \"name\": \"SystemVerilog\",\n    \"import\": () => import('./langs/system-verilog.mjs')\n  },\n  {\n    \"id\": \"tasl\",\n    \"name\": \"Tasl\",\n    \"import\": () => import('./langs/tasl.mjs')\n  },\n  {\n    \"id\": \"tcl\",\n    \"name\": \"Tcl\",\n    \"import\": () => import('./langs/tcl.mjs')\n  },\n  {\n    \"id\": \"tex\",\n    \"name\": \"TeX\",\n    \"import\": () => import('./langs/tex.mjs')\n  },\n  {\n    \"id\": \"toml\",\n    \"name\": \"TOML\",\n    \"import\": () => import('./langs/toml.mjs')\n  },\n  {\n    \"id\": \"tsx\",\n    \"name\": \"TSX\",\n    \"import\": () => import('./langs/tsx.mjs')\n  },\n  {\n    \"id\": \"turtle\",\n    \"name\": \"Turtle\",\n    \"import\": () => import('./langs/turtle.mjs')\n  },\n  {\n    \"id\": \"twig\",\n    \"name\": \"Twig\",\n    \"import\": () => import('./langs/twig.mjs')\n  },\n  {\n    \"id\": \"typescript\",\n    \"name\": \"TypeScript\",\n    \"aliases\": [\n      \"ts\"\n    ],\n    \"import\": () => import('./langs/typescript.mjs')\n  },\n  {\n    \"id\": \"v\",\n    \"name\": \"V\",\n    \"import\": () => import('./langs/v.mjs')\n  },\n  {\n    \"id\": \"vb\",\n    \"name\": \"Visual Basic\",\n    \"aliases\": [\n      \"cmd\"\n    ],\n    \"import\": () => import('./langs/vb.mjs')\n  },\n  {\n    \"id\": \"verilog\",\n    \"name\": \"Verilog\",\n    \"import\": () => import('./langs/verilog.mjs')\n  },\n  {\n    \"id\": \"vhdl\",\n    \"name\": \"VHDL\",\n    \"import\": () => import('./langs/vhdl.mjs')\n  },\n  {\n    \"id\": \"viml\",\n    \"name\": \"Vim Script\",\n    \"aliases\": [\n      \"vim\",\n      \"vimscript\"\n    ],\n    \"import\": () => import('./langs/viml.mjs')\n  },\n  {\n    \"id\": \"vue\",\n    \"name\": \"Vue\",\n    \"import\": () => import('./langs/vue.mjs')\n  },\n  {\n    \"id\": \"vue-html\",\n    \"name\": \"Vue HTML\",\n    \"import\": () => import('./langs/vue-html.mjs')\n  },\n  {\n    \"id\": \"vyper\",\n    \"name\": \"Vyper\",\n    \"aliases\": [\n      \"vy\"\n    ],\n    \"import\": () => import('./langs/vyper.mjs')\n  },\n  {\n    \"id\": \"wasm\",\n    \"name\": \"WebAssembly\",\n    \"import\": () => import('./langs/wasm.mjs')\n  },\n  {\n    \"id\": \"wenyan\",\n    \"name\": \"Wenyan\",\n    \"aliases\": [\n      \"\\u6587\\u8A00\"\n    ],\n    \"import\": () => import('./langs/wenyan.mjs')\n  },\n  {\n    \"id\": \"wgsl\",\n    \"name\": \"WGSL\",\n    \"import\": () => import('./langs/wgsl.mjs')\n  },\n  {\n    \"id\": \"wolfram\",\n    \"name\": \"Wolfram\",\n    \"aliases\": [\n      \"wl\"\n    ],\n    \"import\": () => import('./langs/wolfram.mjs')\n  },\n  {\n    \"id\": \"xml\",\n    \"name\": \"XML\",\n    \"import\": () => import('./langs/xml.mjs')\n  },\n  {\n    \"id\": \"xsl\",\n    \"name\": \"XSL\",\n    \"import\": () => import('./langs/xsl.mjs')\n  },\n  {\n    \"id\": \"yaml\",\n    \"name\": \"YAML\",\n    \"aliases\": [\n      \"yml\"\n    ],\n    \"import\": () => import('./langs/yaml.mjs')\n  },\n  {\n    \"id\": \"zenscript\",\n    \"name\": \"ZenScript\",\n    \"import\": () => import('./langs/zenscript.mjs')\n  },\n  {\n    \"id\": \"zig\",\n    \"name\": \"zig\",\n    \"import\": () => import('./langs/zig.mjs')\n  }\n];\nconst bundledLanguagesBase = Object.fromEntries(bundledLanguagesInfo.map((i) => [i.id, i.import]));\nconst bundledLanguagesAlias = Object.fromEntries(bundledLanguagesInfo.flatMap((i) => i.aliases?.map((a) => [a, i.import]) || []));\nconst bundledLanguages = {\n  ...bundledLanguagesBase,\n  ...bundledLanguagesAlias\n};\n\nexport { bundledLanguages, bundledLanguagesAlias, bundledLanguagesBase, bundledLanguagesInfo };\n", "const bundledThemesInfo = [\n  {\n    \"id\": \"andromeeda\",\n    \"displayName\": \"Andromeeda\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/andromeeda.mjs')\n  },\n  {\n    \"id\": \"aurora-x\",\n    \"displayName\": \"Aurora X\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/aurora-x.mjs')\n  },\n  {\n    \"id\": \"ayu-dark\",\n    \"displayName\": \"Ayu Dark\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/ayu-dark.mjs')\n  },\n  {\n    \"id\": \"catppuccin-frappe\",\n    \"displayName\": \"Catppuccin Frapp\\xE9\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/catppuccin-frappe.mjs')\n  },\n  {\n    \"id\": \"catppuccin-latte\",\n    \"displayName\": \"Catppuccin Latte\",\n    \"type\": \"light\",\n    \"import\": () => import('./themes/catppuccin-latte.mjs')\n  },\n  {\n    \"id\": \"catppuccin-macchiato\",\n    \"displayName\": \"Catppuccin Macchiato\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/catppuccin-macchiato.mjs')\n  },\n  {\n    \"id\": \"catppuccin-mocha\",\n    \"displayName\": \"Catppuccin Mocha\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/catppuccin-mocha.mjs')\n  },\n  {\n    \"id\": \"dark-plus\",\n    \"displayName\": \"Dark Plus\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/dark-plus.mjs')\n  },\n  {\n    \"id\": \"dracula\",\n    \"displayName\": \"Dracula\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/dracula.mjs')\n  },\n  {\n    \"id\": \"dracula-soft\",\n    \"displayName\": \"Dracula Soft\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/dracula-soft.mjs')\n  },\n  {\n    \"id\": \"github-dark\",\n    \"displayName\": \"GitHub Dark\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/github-dark.mjs')\n  },\n  {\n    \"id\": \"github-dark-dimmed\",\n    \"displayName\": \"GitHub Dark Dimmed\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/github-dark-dimmed.mjs')\n  },\n  {\n    \"id\": \"github-light\",\n    \"displayName\": \"GitHub Light\",\n    \"type\": \"light\",\n    \"import\": () => import('./themes/github-light.mjs')\n  },\n  {\n    \"id\": \"light-plus\",\n    \"displayName\": \"Light Plus\",\n    \"type\": \"light\",\n    \"import\": () => import('./themes/light-plus.mjs')\n  },\n  {\n    \"id\": \"material-theme\",\n    \"displayName\": \"Material Theme\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/material-theme.mjs')\n  },\n  {\n    \"id\": \"material-theme-darker\",\n    \"displayName\": \"Material Theme Darker\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/material-theme-darker.mjs')\n  },\n  {\n    \"id\": \"material-theme-lighter\",\n    \"displayName\": \"Material Theme Lighter\",\n    \"type\": \"light\",\n    \"import\": () => import('./themes/material-theme-lighter.mjs')\n  },\n  {\n    \"id\": \"material-theme-ocean\",\n    \"displayName\": \"Material Theme Ocean\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/material-theme-ocean.mjs')\n  },\n  {\n    \"id\": \"material-theme-palenight\",\n    \"displayName\": \"Material Theme Palenight\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/material-theme-palenight.mjs')\n  },\n  {\n    \"id\": \"min-dark\",\n    \"displayName\": \"Min Dark\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/min-dark.mjs')\n  },\n  {\n    \"id\": \"min-light\",\n    \"displayName\": \"Min Light\",\n    \"type\": \"light\",\n    \"import\": () => import('./themes/min-light.mjs')\n  },\n  {\n    \"id\": \"monokai\",\n    \"displayName\": \"Monokai\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/monokai.mjs')\n  },\n  {\n    \"id\": \"night-owl\",\n    \"displayName\": \"Night Owl\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/night-owl.mjs')\n  },\n  {\n    \"id\": \"nord\",\n    \"displayName\": \"Nord\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/nord.mjs')\n  },\n  {\n    \"id\": \"one-dark-pro\",\n    \"displayName\": \"One Dark Pro\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/one-dark-pro.mjs')\n  },\n  {\n    \"id\": \"poimandres\",\n    \"displayName\": \"Poimandres\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/poimandres.mjs')\n  },\n  {\n    \"id\": \"red\",\n    \"displayName\": \"Red\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/red.mjs')\n  },\n  {\n    \"id\": \"rose-pine\",\n    \"displayName\": \"Ros\\xE9 Pine\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/rose-pine.mjs')\n  },\n  {\n    \"id\": \"rose-pine-dawn\",\n    \"displayName\": \"Ros\\xE9 Pine Dawn\",\n    \"type\": \"light\",\n    \"import\": () => import('./themes/rose-pine-dawn.mjs')\n  },\n  {\n    \"id\": \"rose-pine-moon\",\n    \"displayName\": \"Ros\\xE9 Pine Moon\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/rose-pine-moon.mjs')\n  },\n  {\n    \"id\": \"slack-dark\",\n    \"displayName\": \"Slack Dark\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/slack-dark.mjs')\n  },\n  {\n    \"id\": \"slack-ochin\",\n    \"displayName\": \"Slack Ochin\",\n    \"type\": \"light\",\n    \"import\": () => import('./themes/slack-ochin.mjs')\n  },\n  {\n    \"id\": \"solarized-dark\",\n    \"displayName\": \"Solarized Dark\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/solarized-dark.mjs')\n  },\n  {\n    \"id\": \"solarized-light\",\n    \"displayName\": \"Solarized Light\",\n    \"type\": \"light\",\n    \"import\": () => import('./themes/solarized-light.mjs')\n  },\n  {\n    \"id\": \"synthwave-84\",\n    \"displayName\": \"Synthwave '84\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/synthwave-84.mjs')\n  },\n  {\n    \"id\": \"tokyo-night\",\n    \"displayName\": \"Tokyo Night\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/tokyo-night.mjs')\n  },\n  {\n    \"id\": \"vitesse-black\",\n    \"displayName\": \"Vitesse Black\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/vitesse-black.mjs')\n  },\n  {\n    \"id\": \"vitesse-dark\",\n    \"displayName\": \"Vitesse Dark\",\n    \"type\": \"dark\",\n    \"import\": () => import('./themes/vitesse-dark.mjs')\n  },\n  {\n    \"id\": \"vitesse-light\",\n    \"displayName\": \"Vitesse Light\",\n    \"type\": \"light\",\n    \"import\": () => import('./themes/vitesse-light.mjs')\n  }\n];\nconst bundledThemes = Object.fromEntries(bundledThemesInfo.map((i) => [i.id, i.import]));\n\nexport { bundledThemes, bundledThemesInfo };\n", "var FontStyle;\n(function (FontStyle) {\n    FontStyle[FontStyle[\"NotSet\"] = -1] = \"NotSet\";\n    FontStyle[FontStyle[\"None\"] = 0] = \"None\";\n    FontStyle[FontStyle[\"Italic\"] = 1] = \"Italic\";\n    FontStyle[FontStyle[\"Bold\"] = 2] = \"Bold\";\n    FontStyle[FontStyle[\"Underline\"] = 4] = \"Underline\";\n})(FontStyle || (FontStyle = {}));\n\nexport { FontStyle };\n", "import { FontStyle } from './types.mjs';\n\n/*---------------------------------------------------------\n * Copyright (C) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------*/\n({\n    InDebugMode: (typeof process !== 'undefined' && !!process.env['VSCODE_TEXTMATE_DEBUG'])\n});\n\n/*---------------------------------------------------------\n * Copyright (C) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------*/\nvar EncodedTokenAttributes;\n(function (EncodedTokenAttributes) {\n    function toBinaryStr(encodedTokenAttributes) {\n        return encodedTokenAttributes.toString(2).padStart(32, \"0\");\n    }\n    EncodedTokenAttributes.toBinaryStr = toBinaryStr;\n    function print(encodedTokenAttributes) {\n        const languageId = EncodedTokenAttributes.getLanguageId(encodedTokenAttributes);\n        const tokenType = EncodedTokenAttributes.getTokenType(encodedTokenAttributes);\n        const fontStyle = EncodedTokenAttributes.getFontStyle(encodedTokenAttributes);\n        const foreground = EncodedTokenAttributes.getForeground(encodedTokenAttributes);\n        const background = EncodedTokenAttributes.getBackground(encodedTokenAttributes);\n        console.log({\n            languageId: languageId,\n            tokenType: tokenType,\n            fontStyle: fontStyle,\n            foreground: foreground,\n            background: background,\n        });\n    }\n    EncodedTokenAttributes.print = print;\n    function getLanguageId(encodedTokenAttributes) {\n        return ((encodedTokenAttributes & 255 /* EncodedTokenDataConsts.LANGUAGEID_MASK */) >>>\n            0 /* EncodedTokenDataConsts.LANGUAGEID_OFFSET */);\n    }\n    EncodedTokenAttributes.getLanguageId = getLanguageId;\n    function getTokenType(encodedTokenAttributes) {\n        return ((encodedTokenAttributes & 768 /* EncodedTokenDataConsts.TOKEN_TYPE_MASK */) >>>\n            8 /* EncodedTokenDataConsts.TOKEN_TYPE_OFFSET */);\n    }\n    EncodedTokenAttributes.getTokenType = getTokenType;\n    function containsBalancedBrackets(encodedTokenAttributes) {\n        return (encodedTokenAttributes & 1024 /* EncodedTokenDataConsts.BALANCED_BRACKETS_MASK */) !== 0;\n    }\n    EncodedTokenAttributes.containsBalancedBrackets = containsBalancedBrackets;\n    function getFontStyle(encodedTokenAttributes) {\n        return ((encodedTokenAttributes & 30720 /* EncodedTokenDataConsts.FONT_STYLE_MASK */) >>>\n            11 /* EncodedTokenDataConsts.FONT_STYLE_OFFSET */);\n    }\n    EncodedTokenAttributes.getFontStyle = getFontStyle;\n    function getForeground(encodedTokenAttributes) {\n        return ((encodedTokenAttributes & 16744448 /* EncodedTokenDataConsts.FOREGROUND_MASK */) >>>\n            15 /* EncodedTokenDataConsts.FOREGROUND_OFFSET */);\n    }\n    EncodedTokenAttributes.getForeground = getForeground;\n    function getBackground(encodedTokenAttributes) {\n        return ((encodedTokenAttributes & 4278190080 /* EncodedTokenDataConsts.BACKGROUND_MASK */) >>>\n            24 /* EncodedTokenDataConsts.BACKGROUND_OFFSET */);\n    }\n    EncodedTokenAttributes.getBackground = getBackground;\n    /**\n     * Updates the fields in `metadata`.\n     * A value of `0`, `NotSet` or `null` indicates that the corresponding field should be left as is.\n     */\n    function set(encodedTokenAttributes, languageId, tokenType, containsBalancedBrackets, fontStyle, foreground, background) {\n        let _languageId = EncodedTokenAttributes.getLanguageId(encodedTokenAttributes);\n        let _tokenType = EncodedTokenAttributes.getTokenType(encodedTokenAttributes);\n        let _containsBalancedBracketsBit = EncodedTokenAttributes.containsBalancedBrackets(encodedTokenAttributes) ? 1 : 0;\n        let _fontStyle = EncodedTokenAttributes.getFontStyle(encodedTokenAttributes);\n        let _foreground = EncodedTokenAttributes.getForeground(encodedTokenAttributes);\n        let _background = EncodedTokenAttributes.getBackground(encodedTokenAttributes);\n        if (languageId !== 0) {\n            _languageId = languageId;\n        }\n        if (tokenType !== 8 /* OptionalStandardTokenType.NotSet */) {\n            _tokenType = fromOptionalTokenType(tokenType);\n        }\n        if (containsBalancedBrackets !== null) {\n            _containsBalancedBracketsBit = containsBalancedBrackets ? 1 : 0;\n        }\n        if (fontStyle !== -1 /* FontStyle.NotSet */) {\n            _fontStyle = fontStyle;\n        }\n        if (foreground !== 0) {\n            _foreground = foreground;\n        }\n        if (background !== 0) {\n            _background = background;\n        }\n        return (((_languageId << 0 /* EncodedTokenDataConsts.LANGUAGEID_OFFSET */) |\n            (_tokenType << 8 /* EncodedTokenDataConsts.TOKEN_TYPE_OFFSET */) |\n            (_containsBalancedBracketsBit <<\n                10 /* EncodedTokenDataConsts.BALANCED_BRACKETS_OFFSET */) |\n            (_fontStyle << 11 /* EncodedTokenDataConsts.FONT_STYLE_OFFSET */) |\n            (_foreground << 15 /* EncodedTokenDataConsts.FOREGROUND_OFFSET */) |\n            (_background << 24 /* EncodedTokenDataConsts.BACKGROUND_OFFSET */)) >>>\n            0);\n    }\n    EncodedTokenAttributes.set = set;\n})(EncodedTokenAttributes || (EncodedTokenAttributes = {}));\nfunction toOptionalTokenType(standardType) {\n    return standardType;\n}\nfunction fromOptionalTokenType(standardType) {\n    return standardType;\n}\n\n/*---------------------------------------------------------\n * Copyright (C) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------*/\nfunction createMatchers(selector, matchesName) {\n    const results = [];\n    const tokenizer = newTokenizer(selector);\n    let token = tokenizer.next();\n    while (token !== null) {\n        let priority = 0;\n        if (token.length === 2 && token.charAt(1) === ':') {\n            switch (token.charAt(0)) {\n                case 'R':\n                    priority = 1;\n                    break;\n                case 'L':\n                    priority = -1;\n                    break;\n                default:\n                    console.log(`Unknown priority ${token} in scope selector`);\n            }\n            token = tokenizer.next();\n        }\n        let matcher = parseConjunction();\n        results.push({ matcher, priority });\n        if (token !== ',') {\n            break;\n        }\n        token = tokenizer.next();\n    }\n    return results;\n    function parseOperand() {\n        if (token === '-') {\n            token = tokenizer.next();\n            const expressionToNegate = parseOperand();\n            return matcherInput => !!expressionToNegate && !expressionToNegate(matcherInput);\n        }\n        if (token === '(') {\n            token = tokenizer.next();\n            const expressionInParents = parseInnerExpression();\n            if (token === ')') {\n                token = tokenizer.next();\n            }\n            return expressionInParents;\n        }\n        if (isIdentifier(token)) {\n            const identifiers = [];\n            do {\n                identifiers.push(token);\n                token = tokenizer.next();\n            } while (isIdentifier(token));\n            return matcherInput => matchesName(identifiers, matcherInput);\n        }\n        return null;\n    }\n    function parseConjunction() {\n        const matchers = [];\n        let matcher = parseOperand();\n        while (matcher) {\n            matchers.push(matcher);\n            matcher = parseOperand();\n        }\n        return matcherInput => matchers.every(matcher => matcher(matcherInput)); // and\n    }\n    function parseInnerExpression() {\n        const matchers = [];\n        let matcher = parseConjunction();\n        while (matcher) {\n            matchers.push(matcher);\n            if (token === '|' || token === ',') {\n                do {\n                    token = tokenizer.next();\n                } while (token === '|' || token === ','); // ignore subsequent commas\n            }\n            else {\n                break;\n            }\n            matcher = parseConjunction();\n        }\n        return matcherInput => matchers.some(matcher => matcher(matcherInput)); // or\n    }\n}\nfunction isIdentifier(token) {\n    return !!token && !!token.match(/[\\w\\.:]+/);\n}\nfunction newTokenizer(input) {\n    let regex = /([LR]:|[\\w\\.:][\\w\\.:\\-]*|[\\,\\|\\-\\(\\)])/g;\n    let match = regex.exec(input);\n    return {\n        next: () => {\n            if (!match) {\n                return null;\n            }\n            const res = match[0];\n            match = regex.exec(input);\n            return res;\n        }\n    };\n}\n\n/*---------------------------------------------------------\n * Copyright (C) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------*/\nfunction disposeOnigString(str) {\n    if (typeof str.dispose === 'function') {\n        str.dispose();\n    }\n}\n\n/*---------------------------------------------------------\n * Copyright (C) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------*/\nfunction clone(something) {\n    return doClone(something);\n}\nfunction doClone(something) {\n    if (Array.isArray(something)) {\n        return cloneArray(something);\n    }\n    if (typeof something === 'object') {\n        return cloneObj(something);\n    }\n    return something;\n}\nfunction cloneArray(arr) {\n    let r = [];\n    for (let i = 0, len = arr.length; i < len; i++) {\n        r[i] = doClone(arr[i]);\n    }\n    return r;\n}\nfunction cloneObj(obj) {\n    let r = {};\n    for (let key in obj) {\n        r[key] = doClone(obj[key]);\n    }\n    return r;\n}\nfunction mergeObjects(target, ...sources) {\n    sources.forEach(source => {\n        for (let key in source) {\n            target[key] = source[key];\n        }\n    });\n    return target;\n}\nfunction basename(path) {\n    const idx = ~path.lastIndexOf('/') || ~path.lastIndexOf('\\\\');\n    if (idx === 0) {\n        return path;\n    }\n    else if (~idx === path.length - 1) {\n        return basename(path.substring(0, path.length - 1));\n    }\n    else {\n        return path.substr(~idx + 1);\n    }\n}\nlet CAPTURING_REGEX_SOURCE = /\\$(\\d+)|\\${(\\d+):\\/(downcase|upcase)}/g;\nclass RegexSource {\n    static hasCaptures(regexSource) {\n        if (regexSource === null) {\n            return false;\n        }\n        CAPTURING_REGEX_SOURCE.lastIndex = 0;\n        return CAPTURING_REGEX_SOURCE.test(regexSource);\n    }\n    static replaceCaptures(regexSource, captureSource, captureIndices) {\n        return regexSource.replace(CAPTURING_REGEX_SOURCE, (match, index, commandIndex, command) => {\n            let capture = captureIndices[parseInt(index || commandIndex, 10)];\n            if (capture) {\n                let result = captureSource.substring(capture.start, capture.end);\n                // Remove leading dots that would make the selector invalid\n                while (result[0] === '.') {\n                    result = result.substring(1);\n                }\n                switch (command) {\n                    case 'downcase':\n                        return result.toLowerCase();\n                    case 'upcase':\n                        return result.toUpperCase();\n                    default:\n                        return result;\n                }\n            }\n            else {\n                return match;\n            }\n        });\n    }\n}\nfunction strcmp(a, b) {\n    if (a < b) {\n        return -1;\n    }\n    if (a > b) {\n        return 1;\n    }\n    return 0;\n}\nfunction strArrCmp(a, b) {\n    if (a === null && b === null) {\n        return 0;\n    }\n    if (!a) {\n        return -1;\n    }\n    if (!b) {\n        return 1;\n    }\n    let len1 = a.length;\n    let len2 = b.length;\n    if (len1 === len2) {\n        for (let i = 0; i < len1; i++) {\n            let res = strcmp(a[i], b[i]);\n            if (res !== 0) {\n                return res;\n            }\n        }\n        return 0;\n    }\n    return len1 - len2;\n}\nfunction isValidHexColor(hex) {\n    if (/^#[0-9a-f]{6}$/i.test(hex)) {\n        // #rrggbb\n        return true;\n    }\n    if (/^#[0-9a-f]{8}$/i.test(hex)) {\n        // #rrggbbaa\n        return true;\n    }\n    if (/^#[0-9a-f]{3}$/i.test(hex)) {\n        // #rgb\n        return true;\n    }\n    if (/^#[0-9a-f]{4}$/i.test(hex)) {\n        // #rgba\n        return true;\n    }\n    return false;\n}\n/**\n * Escapes regular expression characters in a given string\n */\nfunction escapeRegExpCharacters(value) {\n    return value.replace(/[\\-\\\\\\{\\}\\*\\+\\?\\|\\^\\$\\.\\,\\[\\]\\(\\)\\#\\s]/g, '\\\\$&');\n}\nclass CachedFn {\n    fn;\n    cache = new Map();\n    constructor(fn) {\n        this.fn = fn;\n    }\n    get(key) {\n        if (this.cache.has(key)) {\n            return this.cache.get(key);\n        }\n        const value = this.fn(key);\n        this.cache.set(key, value);\n        return value;\n    }\n}\n\n/*---------------------------------------------------------\n * Copyright (C) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------*/\n/**\n * References the top level rule of a grammar with the given scope name.\n*/\nclass TopLevelRuleReference {\n    scopeName;\n    constructor(scopeName) {\n        this.scopeName = scopeName;\n    }\n    toKey() {\n        return this.scopeName;\n    }\n}\n/**\n * References a rule of a grammar in the top level repository section with the given name.\n*/\nclass TopLevelRepositoryRuleReference {\n    scopeName;\n    ruleName;\n    constructor(scopeName, ruleName) {\n        this.scopeName = scopeName;\n        this.ruleName = ruleName;\n    }\n    toKey() {\n        return `${this.scopeName}#${this.ruleName}`;\n    }\n}\nclass ExternalReferenceCollector {\n    _references = [];\n    _seenReferenceKeys = new Set();\n    get references() {\n        return this._references;\n    }\n    visitedRule = new Set();\n    add(reference) {\n        const key = reference.toKey();\n        if (this._seenReferenceKeys.has(key)) {\n            return;\n        }\n        this._seenReferenceKeys.add(key);\n        this._references.push(reference);\n    }\n}\nclass ScopeDependencyProcessor {\n    repo;\n    initialScopeName;\n    seenFullScopeRequests = new Set();\n    seenPartialScopeRequests = new Set();\n    Q;\n    constructor(repo, initialScopeName) {\n        this.repo = repo;\n        this.initialScopeName = initialScopeName;\n        this.seenFullScopeRequests.add(this.initialScopeName);\n        this.Q = [new TopLevelRuleReference(this.initialScopeName)];\n    }\n    processQueue() {\n        const q = this.Q;\n        this.Q = [];\n        const deps = new ExternalReferenceCollector();\n        for (const dep of q) {\n            collectReferencesOfReference(dep, this.initialScopeName, this.repo, deps);\n        }\n        for (const dep of deps.references) {\n            if (dep instanceof TopLevelRuleReference) {\n                if (this.seenFullScopeRequests.has(dep.scopeName)) {\n                    // already processed\n                    continue;\n                }\n                this.seenFullScopeRequests.add(dep.scopeName);\n                this.Q.push(dep);\n            }\n            else {\n                if (this.seenFullScopeRequests.has(dep.scopeName)) {\n                    // already processed in full\n                    continue;\n                }\n                if (this.seenPartialScopeRequests.has(dep.toKey())) {\n                    // already processed\n                    continue;\n                }\n                this.seenPartialScopeRequests.add(dep.toKey());\n                this.Q.push(dep);\n            }\n        }\n    }\n}\nfunction collectReferencesOfReference(reference, baseGrammarScopeName, repo, result) {\n    const selfGrammar = repo.lookup(reference.scopeName);\n    if (!selfGrammar) {\n        if (reference.scopeName === baseGrammarScopeName) {\n            throw new Error(`No grammar provided for <${baseGrammarScopeName}>`);\n        }\n        return;\n    }\n    const baseGrammar = repo.lookup(baseGrammarScopeName);\n    if (reference instanceof TopLevelRuleReference) {\n        collectExternalReferencesInTopLevelRule({ baseGrammar, selfGrammar }, result);\n    }\n    else {\n        collectExternalReferencesInTopLevelRepositoryRule(reference.ruleName, { baseGrammar, selfGrammar, repository: selfGrammar.repository }, result);\n    }\n    const injections = repo.injections(reference.scopeName);\n    if (injections) {\n        for (const injection of injections) {\n            result.add(new TopLevelRuleReference(injection));\n        }\n    }\n}\nfunction collectExternalReferencesInTopLevelRepositoryRule(ruleName, context, result) {\n    if (context.repository && context.repository[ruleName]) {\n        const rule = context.repository[ruleName];\n        collectExternalReferencesInRules([rule], context, result);\n    }\n}\nfunction collectExternalReferencesInTopLevelRule(context, result) {\n    if (context.selfGrammar.patterns && Array.isArray(context.selfGrammar.patterns)) {\n        collectExternalReferencesInRules(context.selfGrammar.patterns, { ...context, repository: context.selfGrammar.repository }, result);\n    }\n    if (context.selfGrammar.injections) {\n        collectExternalReferencesInRules(Object.values(context.selfGrammar.injections), { ...context, repository: context.selfGrammar.repository }, result);\n    }\n}\nfunction collectExternalReferencesInRules(rules, context, result) {\n    for (const rule of rules) {\n        if (result.visitedRule.has(rule)) {\n            continue;\n        }\n        result.visitedRule.add(rule);\n        const patternRepository = rule.repository ? mergeObjects({}, context.repository, rule.repository) : context.repository;\n        if (Array.isArray(rule.patterns)) {\n            collectExternalReferencesInRules(rule.patterns, { ...context, repository: patternRepository }, result);\n        }\n        const include = rule.include;\n        if (!include) {\n            continue;\n        }\n        const reference = parseInclude(include);\n        switch (reference.kind) {\n            case 0 /* IncludeReferenceKind.Base */:\n                collectExternalReferencesInTopLevelRule({ ...context, selfGrammar: context.baseGrammar }, result);\n                break;\n            case 1 /* IncludeReferenceKind.Self */:\n                collectExternalReferencesInTopLevelRule(context, result);\n                break;\n            case 2 /* IncludeReferenceKind.RelativeReference */:\n                collectExternalReferencesInTopLevelRepositoryRule(reference.ruleName, { ...context, repository: patternRepository }, result);\n                break;\n            case 3 /* IncludeReferenceKind.TopLevelReference */:\n            case 4 /* IncludeReferenceKind.TopLevelRepositoryReference */:\n                const selfGrammar = reference.scopeName === context.selfGrammar.scopeName\n                    ? context.selfGrammar\n                    : reference.scopeName === context.baseGrammar.scopeName\n                        ? context.baseGrammar\n                        : undefined;\n                if (selfGrammar) {\n                    const newContext = { baseGrammar: context.baseGrammar, selfGrammar, repository: patternRepository };\n                    if (reference.kind === 4 /* IncludeReferenceKind.TopLevelRepositoryReference */) {\n                        collectExternalReferencesInTopLevelRepositoryRule(reference.ruleName, newContext, result);\n                    }\n                    else {\n                        collectExternalReferencesInTopLevelRule(newContext, result);\n                    }\n                }\n                else {\n                    if (reference.kind === 4 /* IncludeReferenceKind.TopLevelRepositoryReference */) {\n                        result.add(new TopLevelRepositoryRuleReference(reference.scopeName, reference.ruleName));\n                    }\n                    else {\n                        result.add(new TopLevelRuleReference(reference.scopeName));\n                    }\n                }\n                break;\n        }\n    }\n}\nclass BaseReference {\n    kind = 0 /* IncludeReferenceKind.Base */;\n}\nclass SelfReference {\n    kind = 1 /* IncludeReferenceKind.Self */;\n}\nclass RelativeReference {\n    ruleName;\n    kind = 2 /* IncludeReferenceKind.RelativeReference */;\n    constructor(ruleName) {\n        this.ruleName = ruleName;\n    }\n}\nclass TopLevelReference {\n    scopeName;\n    kind = 3 /* IncludeReferenceKind.TopLevelReference */;\n    constructor(scopeName) {\n        this.scopeName = scopeName;\n    }\n}\nclass TopLevelRepositoryReference {\n    scopeName;\n    ruleName;\n    kind = 4 /* IncludeReferenceKind.TopLevelRepositoryReference */;\n    constructor(scopeName, ruleName) {\n        this.scopeName = scopeName;\n        this.ruleName = ruleName;\n    }\n}\nfunction parseInclude(include) {\n    if (include === '$base') {\n        return new BaseReference();\n    }\n    else if (include === '$self') {\n        return new SelfReference();\n    }\n    const indexOfSharp = include.indexOf(\"#\");\n    if (indexOfSharp === -1) {\n        return new TopLevelReference(include);\n    }\n    else if (indexOfSharp === 0) {\n        return new RelativeReference(include.substring(1));\n    }\n    else {\n        const scopeName = include.substring(0, indexOfSharp);\n        const ruleName = include.substring(indexOfSharp + 1);\n        return new TopLevelRepositoryReference(scopeName, ruleName);\n    }\n}\n\n/*---------------------------------------------------------\n * Copyright (C) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------*/\nconst HAS_BACK_REFERENCES = /\\\\(\\d+)/;\nconst BACK_REFERENCING_END = /\\\\(\\d+)/g;\n// This is a special constant to indicate that the end regexp matched.\nconst endRuleId = -1;\n// This is a special constant to indicate that the while regexp matched.\nconst whileRuleId = -2;\nfunction ruleIdFromNumber(id) {\n    return id;\n}\nfunction ruleIdToNumber(id) {\n    return id;\n}\nclass Rule {\n    $location;\n    id;\n    _nameIsCapturing;\n    _name;\n    _contentNameIsCapturing;\n    _contentName;\n    constructor($location, id, name, contentName) {\n        this.$location = $location;\n        this.id = id;\n        this._name = name || null;\n        this._nameIsCapturing = RegexSource.hasCaptures(this._name);\n        this._contentName = contentName || null;\n        this._contentNameIsCapturing = RegexSource.hasCaptures(this._contentName);\n    }\n    get debugName() {\n        const location = this.$location ? `${basename(this.$location.filename)}:${this.$location.line}` : 'unknown';\n        return `${this.constructor.name}#${this.id} @ ${location}`;\n    }\n    getName(lineText, captureIndices) {\n        if (!this._nameIsCapturing || this._name === null || lineText === null || captureIndices === null) {\n            return this._name;\n        }\n        return RegexSource.replaceCaptures(this._name, lineText, captureIndices);\n    }\n    getContentName(lineText, captureIndices) {\n        if (!this._contentNameIsCapturing || this._contentName === null) {\n            return this._contentName;\n        }\n        return RegexSource.replaceCaptures(this._contentName, lineText, captureIndices);\n    }\n}\nclass CaptureRule extends Rule {\n    retokenizeCapturedWithRuleId;\n    constructor($location, id, name, contentName, retokenizeCapturedWithRuleId) {\n        super($location, id, name, contentName);\n        this.retokenizeCapturedWithRuleId = retokenizeCapturedWithRuleId;\n    }\n    dispose() {\n        // nothing to dispose\n    }\n    collectPatterns(grammar, out) {\n        throw new Error('Not supported!');\n    }\n    compile(grammar, endRegexSource) {\n        throw new Error('Not supported!');\n    }\n    compileAG(grammar, endRegexSource, allowA, allowG) {\n        throw new Error('Not supported!');\n    }\n}\nclass MatchRule extends Rule {\n    _match;\n    captures;\n    _cachedCompiledPatterns;\n    constructor($location, id, name, match, captures) {\n        super($location, id, name, null);\n        this._match = new RegExpSource(match, this.id);\n        this.captures = captures;\n        this._cachedCompiledPatterns = null;\n    }\n    dispose() {\n        if (this._cachedCompiledPatterns) {\n            this._cachedCompiledPatterns.dispose();\n            this._cachedCompiledPatterns = null;\n        }\n    }\n    get debugMatchRegExp() {\n        return `${this._match.source}`;\n    }\n    collectPatterns(grammar, out) {\n        out.push(this._match);\n    }\n    compile(grammar, endRegexSource) {\n        return this._getCachedCompiledPatterns(grammar).compile(grammar);\n    }\n    compileAG(grammar, endRegexSource, allowA, allowG) {\n        return this._getCachedCompiledPatterns(grammar).compileAG(grammar, allowA, allowG);\n    }\n    _getCachedCompiledPatterns(grammar) {\n        if (!this._cachedCompiledPatterns) {\n            this._cachedCompiledPatterns = new RegExpSourceList();\n            this.collectPatterns(grammar, this._cachedCompiledPatterns);\n        }\n        return this._cachedCompiledPatterns;\n    }\n}\nclass IncludeOnlyRule extends Rule {\n    hasMissingPatterns;\n    patterns;\n    _cachedCompiledPatterns;\n    constructor($location, id, name, contentName, patterns) {\n        super($location, id, name, contentName);\n        this.patterns = patterns.patterns;\n        this.hasMissingPatterns = patterns.hasMissingPatterns;\n        this._cachedCompiledPatterns = null;\n    }\n    dispose() {\n        if (this._cachedCompiledPatterns) {\n            this._cachedCompiledPatterns.dispose();\n            this._cachedCompiledPatterns = null;\n        }\n    }\n    collectPatterns(grammar, out) {\n        for (const pattern of this.patterns) {\n            const rule = grammar.getRule(pattern);\n            rule.collectPatterns(grammar, out);\n        }\n    }\n    compile(grammar, endRegexSource) {\n        return this._getCachedCompiledPatterns(grammar).compile(grammar);\n    }\n    compileAG(grammar, endRegexSource, allowA, allowG) {\n        return this._getCachedCompiledPatterns(grammar).compileAG(grammar, allowA, allowG);\n    }\n    _getCachedCompiledPatterns(grammar) {\n        if (!this._cachedCompiledPatterns) {\n            this._cachedCompiledPatterns = new RegExpSourceList();\n            this.collectPatterns(grammar, this._cachedCompiledPatterns);\n        }\n        return this._cachedCompiledPatterns;\n    }\n}\nclass BeginEndRule extends Rule {\n    _begin;\n    beginCaptures;\n    _end;\n    endHasBackReferences;\n    endCaptures;\n    applyEndPatternLast;\n    hasMissingPatterns;\n    patterns;\n    _cachedCompiledPatterns;\n    constructor($location, id, name, contentName, begin, beginCaptures, end, endCaptures, applyEndPatternLast, patterns) {\n        super($location, id, name, contentName);\n        this._begin = new RegExpSource(begin, this.id);\n        this.beginCaptures = beginCaptures;\n        this._end = new RegExpSource(end ? end : '\\uFFFF', -1);\n        this.endHasBackReferences = this._end.hasBackReferences;\n        this.endCaptures = endCaptures;\n        this.applyEndPatternLast = applyEndPatternLast || false;\n        this.patterns = patterns.patterns;\n        this.hasMissingPatterns = patterns.hasMissingPatterns;\n        this._cachedCompiledPatterns = null;\n    }\n    dispose() {\n        if (this._cachedCompiledPatterns) {\n            this._cachedCompiledPatterns.dispose();\n            this._cachedCompiledPatterns = null;\n        }\n    }\n    get debugBeginRegExp() {\n        return `${this._begin.source}`;\n    }\n    get debugEndRegExp() {\n        return `${this._end.source}`;\n    }\n    getEndWithResolvedBackReferences(lineText, captureIndices) {\n        return this._end.resolveBackReferences(lineText, captureIndices);\n    }\n    collectPatterns(grammar, out) {\n        out.push(this._begin);\n    }\n    compile(grammar, endRegexSource) {\n        return this._getCachedCompiledPatterns(grammar, endRegexSource).compile(grammar);\n    }\n    compileAG(grammar, endRegexSource, allowA, allowG) {\n        return this._getCachedCompiledPatterns(grammar, endRegexSource).compileAG(grammar, allowA, allowG);\n    }\n    _getCachedCompiledPatterns(grammar, endRegexSource) {\n        if (!this._cachedCompiledPatterns) {\n            this._cachedCompiledPatterns = new RegExpSourceList();\n            for (const pattern of this.patterns) {\n                const rule = grammar.getRule(pattern);\n                rule.collectPatterns(grammar, this._cachedCompiledPatterns);\n            }\n            if (this.applyEndPatternLast) {\n                this._cachedCompiledPatterns.push(this._end.hasBackReferences ? this._end.clone() : this._end);\n            }\n            else {\n                this._cachedCompiledPatterns.unshift(this._end.hasBackReferences ? this._end.clone() : this._end);\n            }\n        }\n        if (this._end.hasBackReferences) {\n            if (this.applyEndPatternLast) {\n                this._cachedCompiledPatterns.setSource(this._cachedCompiledPatterns.length() - 1, endRegexSource);\n            }\n            else {\n                this._cachedCompiledPatterns.setSource(0, endRegexSource);\n            }\n        }\n        return this._cachedCompiledPatterns;\n    }\n}\nclass BeginWhileRule extends Rule {\n    _begin;\n    beginCaptures;\n    whileCaptures;\n    _while;\n    whileHasBackReferences;\n    hasMissingPatterns;\n    patterns;\n    _cachedCompiledPatterns;\n    _cachedCompiledWhilePatterns;\n    constructor($location, id, name, contentName, begin, beginCaptures, _while, whileCaptures, patterns) {\n        super($location, id, name, contentName);\n        this._begin = new RegExpSource(begin, this.id);\n        this.beginCaptures = beginCaptures;\n        this.whileCaptures = whileCaptures;\n        this._while = new RegExpSource(_while, whileRuleId);\n        this.whileHasBackReferences = this._while.hasBackReferences;\n        this.patterns = patterns.patterns;\n        this.hasMissingPatterns = patterns.hasMissingPatterns;\n        this._cachedCompiledPatterns = null;\n        this._cachedCompiledWhilePatterns = null;\n    }\n    dispose() {\n        if (this._cachedCompiledPatterns) {\n            this._cachedCompiledPatterns.dispose();\n            this._cachedCompiledPatterns = null;\n        }\n        if (this._cachedCompiledWhilePatterns) {\n            this._cachedCompiledWhilePatterns.dispose();\n            this._cachedCompiledWhilePatterns = null;\n        }\n    }\n    get debugBeginRegExp() {\n        return `${this._begin.source}`;\n    }\n    get debugWhileRegExp() {\n        return `${this._while.source}`;\n    }\n    getWhileWithResolvedBackReferences(lineText, captureIndices) {\n        return this._while.resolveBackReferences(lineText, captureIndices);\n    }\n    collectPatterns(grammar, out) {\n        out.push(this._begin);\n    }\n    compile(grammar, endRegexSource) {\n        return this._getCachedCompiledPatterns(grammar).compile(grammar);\n    }\n    compileAG(grammar, endRegexSource, allowA, allowG) {\n        return this._getCachedCompiledPatterns(grammar).compileAG(grammar, allowA, allowG);\n    }\n    _getCachedCompiledPatterns(grammar) {\n        if (!this._cachedCompiledPatterns) {\n            this._cachedCompiledPatterns = new RegExpSourceList();\n            for (const pattern of this.patterns) {\n                const rule = grammar.getRule(pattern);\n                rule.collectPatterns(grammar, this._cachedCompiledPatterns);\n            }\n        }\n        return this._cachedCompiledPatterns;\n    }\n    compileWhile(grammar, endRegexSource) {\n        return this._getCachedCompiledWhilePatterns(grammar, endRegexSource).compile(grammar);\n    }\n    compileWhileAG(grammar, endRegexSource, allowA, allowG) {\n        return this._getCachedCompiledWhilePatterns(grammar, endRegexSource).compileAG(grammar, allowA, allowG);\n    }\n    _getCachedCompiledWhilePatterns(grammar, endRegexSource) {\n        if (!this._cachedCompiledWhilePatterns) {\n            this._cachedCompiledWhilePatterns = new RegExpSourceList();\n            this._cachedCompiledWhilePatterns.push(this._while.hasBackReferences ? this._while.clone() : this._while);\n        }\n        if (this._while.hasBackReferences) {\n            this._cachedCompiledWhilePatterns.setSource(0, endRegexSource ? endRegexSource : '\\uFFFF');\n        }\n        return this._cachedCompiledWhilePatterns;\n    }\n}\nclass RuleFactory {\n    static createCaptureRule(helper, $location, name, contentName, retokenizeCapturedWithRuleId) {\n        return helper.registerRule((id) => {\n            return new CaptureRule($location, id, name, contentName, retokenizeCapturedWithRuleId);\n        });\n    }\n    static getCompiledRuleId(desc, helper, repository) {\n        if (!desc.id) {\n            helper.registerRule((id) => {\n                desc.id = id;\n                if (desc.match) {\n                    return new MatchRule(desc.$vscodeTextmateLocation, desc.id, desc.name, desc.match, RuleFactory._compileCaptures(desc.captures, helper, repository));\n                }\n                if (typeof desc.begin === 'undefined') {\n                    if (desc.repository) {\n                        repository = mergeObjects({}, repository, desc.repository);\n                    }\n                    let patterns = desc.patterns;\n                    if (typeof patterns === 'undefined' && desc.include) {\n                        patterns = [{ include: desc.include }];\n                    }\n                    return new IncludeOnlyRule(desc.$vscodeTextmateLocation, desc.id, desc.name, desc.contentName, RuleFactory._compilePatterns(patterns, helper, repository));\n                }\n                if (desc.while) {\n                    return new BeginWhileRule(desc.$vscodeTextmateLocation, desc.id, desc.name, desc.contentName, desc.begin, RuleFactory._compileCaptures(desc.beginCaptures || desc.captures, helper, repository), desc.while, RuleFactory._compileCaptures(desc.whileCaptures || desc.captures, helper, repository), RuleFactory._compilePatterns(desc.patterns, helper, repository));\n                }\n                return new BeginEndRule(desc.$vscodeTextmateLocation, desc.id, desc.name, desc.contentName, desc.begin, RuleFactory._compileCaptures(desc.beginCaptures || desc.captures, helper, repository), desc.end, RuleFactory._compileCaptures(desc.endCaptures || desc.captures, helper, repository), desc.applyEndPatternLast, RuleFactory._compilePatterns(desc.patterns, helper, repository));\n            });\n        }\n        return desc.id;\n    }\n    static _compileCaptures(captures, helper, repository) {\n        let r = [];\n        if (captures) {\n            // Find the maximum capture id\n            let maximumCaptureId = 0;\n            for (const captureId in captures) {\n                if (captureId === '$vscodeTextmateLocation') {\n                    continue;\n                }\n                const numericCaptureId = parseInt(captureId, 10);\n                if (numericCaptureId > maximumCaptureId) {\n                    maximumCaptureId = numericCaptureId;\n                }\n            }\n            // Initialize result\n            for (let i = 0; i <= maximumCaptureId; i++) {\n                r[i] = null;\n            }\n            // Fill out result\n            for (const captureId in captures) {\n                if (captureId === '$vscodeTextmateLocation') {\n                    continue;\n                }\n                const numericCaptureId = parseInt(captureId, 10);\n                let retokenizeCapturedWithRuleId = 0;\n                if (captures[captureId].patterns) {\n                    retokenizeCapturedWithRuleId = RuleFactory.getCompiledRuleId(captures[captureId], helper, repository);\n                }\n                r[numericCaptureId] = RuleFactory.createCaptureRule(helper, captures[captureId].$vscodeTextmateLocation, captures[captureId].name, captures[captureId].contentName, retokenizeCapturedWithRuleId);\n            }\n        }\n        return r;\n    }\n    static _compilePatterns(patterns, helper, repository) {\n        let r = [];\n        if (patterns) {\n            for (let i = 0, len = patterns.length; i < len; i++) {\n                const pattern = patterns[i];\n                let ruleId = -1;\n                if (pattern.include) {\n                    const reference = parseInclude(pattern.include);\n                    switch (reference.kind) {\n                        case 0 /* IncludeReferenceKind.Base */:\n                        case 1 /* IncludeReferenceKind.Self */:\n                            ruleId = RuleFactory.getCompiledRuleId(repository[pattern.include], helper, repository);\n                            break;\n                        case 2 /* IncludeReferenceKind.RelativeReference */:\n                            // Local include found in `repository`\n                            let localIncludedRule = repository[reference.ruleName];\n                            if (localIncludedRule) {\n                                ruleId = RuleFactory.getCompiledRuleId(localIncludedRule, helper, repository);\n                            }\n                            break;\n                        case 3 /* IncludeReferenceKind.TopLevelReference */:\n                        case 4 /* IncludeReferenceKind.TopLevelRepositoryReference */:\n                            const externalGrammarName = reference.scopeName;\n                            const externalGrammarInclude = reference.kind === 4 /* IncludeReferenceKind.TopLevelRepositoryReference */\n                                ? reference.ruleName\n                                : null;\n                            // External include\n                            const externalGrammar = helper.getExternalGrammar(externalGrammarName, repository);\n                            if (externalGrammar) {\n                                if (externalGrammarInclude) {\n                                    let externalIncludedRule = externalGrammar.repository[externalGrammarInclude];\n                                    if (externalIncludedRule) {\n                                        ruleId = RuleFactory.getCompiledRuleId(externalIncludedRule, helper, externalGrammar.repository);\n                                    }\n                                }\n                                else {\n                                    ruleId = RuleFactory.getCompiledRuleId(externalGrammar.repository.$self, helper, externalGrammar.repository);\n                                }\n                            }\n                            break;\n                    }\n                }\n                else {\n                    ruleId = RuleFactory.getCompiledRuleId(pattern, helper, repository);\n                }\n                if (ruleId !== -1) {\n                    const rule = helper.getRule(ruleId);\n                    let skipRule = false;\n                    if (rule instanceof IncludeOnlyRule || rule instanceof BeginEndRule || rule instanceof BeginWhileRule) {\n                        if (rule.hasMissingPatterns && rule.patterns.length === 0) {\n                            skipRule = true;\n                        }\n                    }\n                    if (skipRule) {\n                        // console.log('REMOVING RULE ENTIRELY DUE TO EMPTY PATTERNS THAT ARE MISSING');\n                        continue;\n                    }\n                    r.push(ruleId);\n                }\n            }\n        }\n        return {\n            patterns: r,\n            hasMissingPatterns: ((patterns ? patterns.length : 0) !== r.length)\n        };\n    }\n}\nclass RegExpSource {\n    source;\n    ruleId;\n    hasAnchor;\n    hasBackReferences;\n    _anchorCache;\n    constructor(regExpSource, ruleId) {\n        if (regExpSource) {\n            const len = regExpSource.length;\n            let lastPushedPos = 0;\n            let output = [];\n            let hasAnchor = false;\n            for (let pos = 0; pos < len; pos++) {\n                const ch = regExpSource.charAt(pos);\n                if (ch === '\\\\') {\n                    if (pos + 1 < len) {\n                        const nextCh = regExpSource.charAt(pos + 1);\n                        if (nextCh === 'z') {\n                            output.push(regExpSource.substring(lastPushedPos, pos));\n                            output.push('$(?!\\\\n)(?<!\\\\n)');\n                            lastPushedPos = pos + 2;\n                        }\n                        else if (nextCh === 'A' || nextCh === 'G') {\n                            hasAnchor = true;\n                        }\n                        pos++;\n                    }\n                }\n            }\n            this.hasAnchor = hasAnchor;\n            if (lastPushedPos === 0) {\n                // No \\z hit\n                this.source = regExpSource;\n            }\n            else {\n                output.push(regExpSource.substring(lastPushedPos, len));\n                this.source = output.join('');\n            }\n        }\n        else {\n            this.hasAnchor = false;\n            this.source = regExpSource;\n        }\n        if (this.hasAnchor) {\n            this._anchorCache = this._buildAnchorCache();\n        }\n        else {\n            this._anchorCache = null;\n        }\n        this.ruleId = ruleId;\n        this.hasBackReferences = HAS_BACK_REFERENCES.test(this.source);\n        // console.log('input: ' + regExpSource + ' => ' + this.source + ', ' + this.hasAnchor);\n    }\n    clone() {\n        return new RegExpSource(this.source, this.ruleId);\n    }\n    setSource(newSource) {\n        if (this.source === newSource) {\n            return;\n        }\n        this.source = newSource;\n        if (this.hasAnchor) {\n            this._anchorCache = this._buildAnchorCache();\n        }\n    }\n    resolveBackReferences(lineText, captureIndices) {\n        let capturedValues = captureIndices.map((capture) => {\n            return lineText.substring(capture.start, capture.end);\n        });\n        BACK_REFERENCING_END.lastIndex = 0;\n        return this.source.replace(BACK_REFERENCING_END, (match, g1) => {\n            return escapeRegExpCharacters(capturedValues[parseInt(g1, 10)] || '');\n        });\n    }\n    _buildAnchorCache() {\n        let A0_G0_result = [];\n        let A0_G1_result = [];\n        let A1_G0_result = [];\n        let A1_G1_result = [];\n        let pos, len, ch, nextCh;\n        for (pos = 0, len = this.source.length; pos < len; pos++) {\n            ch = this.source.charAt(pos);\n            A0_G0_result[pos] = ch;\n            A0_G1_result[pos] = ch;\n            A1_G0_result[pos] = ch;\n            A1_G1_result[pos] = ch;\n            if (ch === '\\\\') {\n                if (pos + 1 < len) {\n                    nextCh = this.source.charAt(pos + 1);\n                    if (nextCh === 'A') {\n                        A0_G0_result[pos + 1] = '\\uFFFF';\n                        A0_G1_result[pos + 1] = '\\uFFFF';\n                        A1_G0_result[pos + 1] = 'A';\n                        A1_G1_result[pos + 1] = 'A';\n                    }\n                    else if (nextCh === 'G') {\n                        A0_G0_result[pos + 1] = '\\uFFFF';\n                        A0_G1_result[pos + 1] = 'G';\n                        A1_G0_result[pos + 1] = '\\uFFFF';\n                        A1_G1_result[pos + 1] = 'G';\n                    }\n                    else {\n                        A0_G0_result[pos + 1] = nextCh;\n                        A0_G1_result[pos + 1] = nextCh;\n                        A1_G0_result[pos + 1] = nextCh;\n                        A1_G1_result[pos + 1] = nextCh;\n                    }\n                    pos++;\n                }\n            }\n        }\n        return {\n            A0_G0: A0_G0_result.join(''),\n            A0_G1: A0_G1_result.join(''),\n            A1_G0: A1_G0_result.join(''),\n            A1_G1: A1_G1_result.join('')\n        };\n    }\n    resolveAnchors(allowA, allowG) {\n        if (!this.hasAnchor || !this._anchorCache) {\n            return this.source;\n        }\n        if (allowA) {\n            if (allowG) {\n                return this._anchorCache.A1_G1;\n            }\n            else {\n                return this._anchorCache.A1_G0;\n            }\n        }\n        else {\n            if (allowG) {\n                return this._anchorCache.A0_G1;\n            }\n            else {\n                return this._anchorCache.A0_G0;\n            }\n        }\n    }\n}\nclass RegExpSourceList {\n    _items;\n    _hasAnchors;\n    _cached;\n    _anchorCache;\n    constructor() {\n        this._items = [];\n        this._hasAnchors = false;\n        this._cached = null;\n        this._anchorCache = {\n            A0_G0: null,\n            A0_G1: null,\n            A1_G0: null,\n            A1_G1: null\n        };\n    }\n    dispose() {\n        this._disposeCaches();\n    }\n    _disposeCaches() {\n        if (this._cached) {\n            this._cached.dispose();\n            this._cached = null;\n        }\n        if (this._anchorCache.A0_G0) {\n            this._anchorCache.A0_G0.dispose();\n            this._anchorCache.A0_G0 = null;\n        }\n        if (this._anchorCache.A0_G1) {\n            this._anchorCache.A0_G1.dispose();\n            this._anchorCache.A0_G1 = null;\n        }\n        if (this._anchorCache.A1_G0) {\n            this._anchorCache.A1_G0.dispose();\n            this._anchorCache.A1_G0 = null;\n        }\n        if (this._anchorCache.A1_G1) {\n            this._anchorCache.A1_G1.dispose();\n            this._anchorCache.A1_G1 = null;\n        }\n    }\n    push(item) {\n        this._items.push(item);\n        this._hasAnchors = this._hasAnchors || item.hasAnchor;\n    }\n    unshift(item) {\n        this._items.unshift(item);\n        this._hasAnchors = this._hasAnchors || item.hasAnchor;\n    }\n    length() {\n        return this._items.length;\n    }\n    setSource(index, newSource) {\n        if (this._items[index].source !== newSource) {\n            // bust the cache\n            this._disposeCaches();\n            this._items[index].setSource(newSource);\n        }\n    }\n    compile(onigLib) {\n        if (!this._cached) {\n            let regExps = this._items.map(e => e.source);\n            this._cached = new CompiledRule(onigLib, regExps, this._items.map(e => e.ruleId));\n        }\n        return this._cached;\n    }\n    compileAG(onigLib, allowA, allowG) {\n        if (!this._hasAnchors) {\n            return this.compile(onigLib);\n        }\n        else {\n            if (allowA) {\n                if (allowG) {\n                    if (!this._anchorCache.A1_G1) {\n                        this._anchorCache.A1_G1 = this._resolveAnchors(onigLib, allowA, allowG);\n                    }\n                    return this._anchorCache.A1_G1;\n                }\n                else {\n                    if (!this._anchorCache.A1_G0) {\n                        this._anchorCache.A1_G0 = this._resolveAnchors(onigLib, allowA, allowG);\n                    }\n                    return this._anchorCache.A1_G0;\n                }\n            }\n            else {\n                if (allowG) {\n                    if (!this._anchorCache.A0_G1) {\n                        this._anchorCache.A0_G1 = this._resolveAnchors(onigLib, allowA, allowG);\n                    }\n                    return this._anchorCache.A0_G1;\n                }\n                else {\n                    if (!this._anchorCache.A0_G0) {\n                        this._anchorCache.A0_G0 = this._resolveAnchors(onigLib, allowA, allowG);\n                    }\n                    return this._anchorCache.A0_G0;\n                }\n            }\n        }\n    }\n    _resolveAnchors(onigLib, allowA, allowG) {\n        let regExps = this._items.map(e => e.resolveAnchors(allowA, allowG));\n        return new CompiledRule(onigLib, regExps, this._items.map(e => e.ruleId));\n    }\n}\nclass CompiledRule {\n    regExps;\n    rules;\n    scanner;\n    constructor(onigLib, regExps, rules) {\n        this.regExps = regExps;\n        this.rules = rules;\n        this.scanner = onigLib.createOnigScanner(regExps);\n    }\n    dispose() {\n        if (typeof this.scanner.dispose === \"function\") {\n            this.scanner.dispose();\n        }\n    }\n    toString() {\n        const r = [];\n        for (let i = 0, len = this.rules.length; i < len; i++) {\n            r.push(\"   - \" + this.rules[i] + \": \" + this.regExps[i]);\n        }\n        return r.join(\"\\n\");\n    }\n    findNextMatchSync(string, startPosition, options) {\n        const result = this.scanner.findNextMatchSync(string, startPosition, options);\n        if (!result) {\n            return null;\n        }\n        return {\n            ruleId: this.rules[result.index],\n            captureIndices: result.captureIndices,\n        };\n    }\n}\n\n/*---------------------------------------------------------\n * Copyright (C) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------*/\nclass Theme {\n    _colorMap;\n    _defaults;\n    _root;\n    static createFromRawTheme(source, colorMap) {\n        return this.createFromParsedTheme(parseTheme(source), colorMap);\n    }\n    static createFromParsedTheme(source, colorMap) {\n        return resolveParsedThemeRules(source, colorMap);\n    }\n    _cachedMatchRoot = new CachedFn((scopeName) => this._root.match(scopeName));\n    constructor(_colorMap, _defaults, _root) {\n        this._colorMap = _colorMap;\n        this._defaults = _defaults;\n        this._root = _root;\n    }\n    getColorMap() {\n        return this._colorMap.getColorMap();\n    }\n    getDefaults() {\n        return this._defaults;\n    }\n    match(scopePath) {\n        if (scopePath === null) {\n            return this._defaults;\n        }\n        const scopeName = scopePath.scopeName;\n        const matchingTrieElements = this._cachedMatchRoot.get(scopeName);\n        const effectiveRule = matchingTrieElements.find((v) => _scopePathMatchesParentScopes(scopePath.parent, v.parentScopes));\n        if (!effectiveRule) {\n            return null;\n        }\n        return new StyleAttributes(effectiveRule.fontStyle, effectiveRule.foreground, effectiveRule.background);\n    }\n}\nclass ScopeStack {\n    parent;\n    scopeName;\n    static push(path, scopeNames) {\n        for (const name of scopeNames) {\n            path = new ScopeStack(path, name);\n        }\n        return path;\n    }\n    static from(...segments) {\n        let result = null;\n        for (let i = 0; i < segments.length; i++) {\n            result = new ScopeStack(result, segments[i]);\n        }\n        return result;\n    }\n    constructor(parent, scopeName) {\n        this.parent = parent;\n        this.scopeName = scopeName;\n    }\n    push(scopeName) {\n        return new ScopeStack(this, scopeName);\n    }\n    getSegments() {\n        let item = this;\n        const result = [];\n        while (item) {\n            result.push(item.scopeName);\n            item = item.parent;\n        }\n        result.reverse();\n        return result;\n    }\n    toString() {\n        return this.getSegments().join(' ');\n    }\n    extends(other) {\n        if (this === other) {\n            return true;\n        }\n        if (this.parent === null) {\n            return false;\n        }\n        return this.parent.extends(other);\n    }\n    getExtensionIfDefined(base) {\n        const result = [];\n        let item = this;\n        while (item && item !== base) {\n            result.push(item.scopeName);\n            item = item.parent;\n        }\n        return item === base ? result.reverse() : undefined;\n    }\n}\nfunction _scopePathMatchesParentScopes(scopePath, parentScopes) {\n    if (parentScopes === null) {\n        return true;\n    }\n    let index = 0;\n    let scopePattern = parentScopes[index];\n    while (scopePath) {\n        if (_matchesScope(scopePath.scopeName, scopePattern)) {\n            index++;\n            if (index === parentScopes.length) {\n                return true;\n            }\n            scopePattern = parentScopes[index];\n        }\n        scopePath = scopePath.parent;\n    }\n    return false;\n}\nfunction _matchesScope(scopeName, scopePattern) {\n    return scopePattern === scopeName || (scopeName.startsWith(scopePattern) && scopeName[scopePattern.length] === '.');\n}\nclass StyleAttributes {\n    fontStyle;\n    foregroundId;\n    backgroundId;\n    constructor(fontStyle, foregroundId, backgroundId) {\n        this.fontStyle = fontStyle;\n        this.foregroundId = foregroundId;\n        this.backgroundId = backgroundId;\n    }\n}\n/**\n * Parse a raw theme into rules.\n */\nfunction parseTheme(source) {\n    if (!source) {\n        return [];\n    }\n    if (!source.settings || !Array.isArray(source.settings)) {\n        return [];\n    }\n    let settings = source.settings;\n    let result = [], resultLen = 0;\n    for (let i = 0, len = settings.length; i < len; i++) {\n        let entry = settings[i];\n        if (!entry.settings) {\n            continue;\n        }\n        let scopes;\n        if (typeof entry.scope === 'string') {\n            let _scope = entry.scope;\n            // remove leading commas\n            _scope = _scope.replace(/^[,]+/, '');\n            // remove trailing commans\n            _scope = _scope.replace(/[,]+$/, '');\n            scopes = _scope.split(',');\n        }\n        else if (Array.isArray(entry.scope)) {\n            scopes = entry.scope;\n        }\n        else {\n            scopes = [''];\n        }\n        let fontStyle = -1 /* FontStyle.NotSet */;\n        if (typeof entry.settings.fontStyle === 'string') {\n            fontStyle = 0 /* FontStyle.None */;\n            let segments = entry.settings.fontStyle.split(' ');\n            for (let j = 0, lenJ = segments.length; j < lenJ; j++) {\n                let segment = segments[j];\n                switch (segment) {\n                    case 'italic':\n                        fontStyle = fontStyle | 1 /* FontStyle.Italic */;\n                        break;\n                    case 'bold':\n                        fontStyle = fontStyle | 2 /* FontStyle.Bold */;\n                        break;\n                    case 'underline':\n                        fontStyle = fontStyle | 4 /* FontStyle.Underline */;\n                        break;\n                    case 'strikethrough':\n                        fontStyle = fontStyle | 8 /* FontStyle.Strikethrough */;\n                        break;\n                }\n            }\n        }\n        let foreground = null;\n        if (typeof entry.settings.foreground === 'string' && isValidHexColor(entry.settings.foreground)) {\n            foreground = entry.settings.foreground;\n        }\n        let background = null;\n        if (typeof entry.settings.background === 'string' && isValidHexColor(entry.settings.background)) {\n            background = entry.settings.background;\n        }\n        for (let j = 0, lenJ = scopes.length; j < lenJ; j++) {\n            let _scope = scopes[j].trim();\n            let segments = _scope.split(' ');\n            let scope = segments[segments.length - 1];\n            let parentScopes = null;\n            if (segments.length > 1) {\n                parentScopes = segments.slice(0, segments.length - 1);\n                parentScopes.reverse();\n            }\n            result[resultLen++] = new ParsedThemeRule(scope, parentScopes, i, fontStyle, foreground, background);\n        }\n    }\n    return result;\n}\nclass ParsedThemeRule {\n    scope;\n    parentScopes;\n    index;\n    fontStyle;\n    foreground;\n    background;\n    constructor(scope, parentScopes, index, fontStyle, foreground, background) {\n        this.scope = scope;\n        this.parentScopes = parentScopes;\n        this.index = index;\n        this.fontStyle = fontStyle;\n        this.foreground = foreground;\n        this.background = background;\n    }\n}\n/**\n * Resolve rules (i.e. inheritance).\n */\nfunction resolveParsedThemeRules(parsedThemeRules, _colorMap) {\n    // Sort rules lexicographically, and then by index if necessary\n    parsedThemeRules.sort((a, b) => {\n        let r = strcmp(a.scope, b.scope);\n        if (r !== 0) {\n            return r;\n        }\n        r = strArrCmp(a.parentScopes, b.parentScopes);\n        if (r !== 0) {\n            return r;\n        }\n        return a.index - b.index;\n    });\n    // Determine defaults\n    let defaultFontStyle = 0 /* FontStyle.None */;\n    let defaultForeground = '#000000';\n    let defaultBackground = '#ffffff';\n    while (parsedThemeRules.length >= 1 && parsedThemeRules[0].scope === '') {\n        let incomingDefaults = parsedThemeRules.shift();\n        if (incomingDefaults.fontStyle !== -1 /* FontStyle.NotSet */) {\n            defaultFontStyle = incomingDefaults.fontStyle;\n        }\n        if (incomingDefaults.foreground !== null) {\n            defaultForeground = incomingDefaults.foreground;\n        }\n        if (incomingDefaults.background !== null) {\n            defaultBackground = incomingDefaults.background;\n        }\n    }\n    let colorMap = new ColorMap(_colorMap);\n    let defaults = new StyleAttributes(defaultFontStyle, colorMap.getId(defaultForeground), colorMap.getId(defaultBackground));\n    let root = new ThemeTrieElement(new ThemeTrieElementRule(0, null, -1 /* FontStyle.NotSet */, 0, 0), []);\n    for (let i = 0, len = parsedThemeRules.length; i < len; i++) {\n        let rule = parsedThemeRules[i];\n        root.insert(0, rule.scope, rule.parentScopes, rule.fontStyle, colorMap.getId(rule.foreground), colorMap.getId(rule.background));\n    }\n    return new Theme(colorMap, defaults, root);\n}\nclass ColorMap {\n    _isFrozen;\n    _lastColorId;\n    _id2color;\n    _color2id;\n    constructor(_colorMap) {\n        this._lastColorId = 0;\n        this._id2color = [];\n        this._color2id = Object.create(null);\n        if (Array.isArray(_colorMap)) {\n            this._isFrozen = true;\n            for (let i = 0, len = _colorMap.length; i < len; i++) {\n                this._color2id[_colorMap[i]] = i;\n                this._id2color[i] = _colorMap[i];\n            }\n        }\n        else {\n            this._isFrozen = false;\n        }\n    }\n    getId(color) {\n        if (color === null) {\n            return 0;\n        }\n        color = color.toUpperCase();\n        let value = this._color2id[color];\n        if (value) {\n            return value;\n        }\n        if (this._isFrozen) {\n            throw new Error(`Missing color in color map - ${color}`);\n        }\n        value = ++this._lastColorId;\n        this._color2id[color] = value;\n        this._id2color[value] = color;\n        return value;\n    }\n    getColorMap() {\n        return this._id2color.slice(0);\n    }\n}\nclass ThemeTrieElementRule {\n    scopeDepth;\n    parentScopes;\n    fontStyle;\n    foreground;\n    background;\n    constructor(scopeDepth, parentScopes, fontStyle, foreground, background) {\n        this.scopeDepth = scopeDepth;\n        this.parentScopes = parentScopes;\n        this.fontStyle = fontStyle;\n        this.foreground = foreground;\n        this.background = background;\n    }\n    clone() {\n        return new ThemeTrieElementRule(this.scopeDepth, this.parentScopes, this.fontStyle, this.foreground, this.background);\n    }\n    static cloneArr(arr) {\n        let r = [];\n        for (let i = 0, len = arr.length; i < len; i++) {\n            r[i] = arr[i].clone();\n        }\n        return r;\n    }\n    acceptOverwrite(scopeDepth, fontStyle, foreground, background) {\n        if (this.scopeDepth > scopeDepth) {\n            console.log('how did this happen?');\n        }\n        else {\n            this.scopeDepth = scopeDepth;\n        }\n        // console.log('TODO -> my depth: ' + this.scopeDepth + ', overwriting depth: ' + scopeDepth);\n        if (fontStyle !== -1 /* FontStyle.NotSet */) {\n            this.fontStyle = fontStyle;\n        }\n        if (foreground !== 0) {\n            this.foreground = foreground;\n        }\n        if (background !== 0) {\n            this.background = background;\n        }\n    }\n}\nclass ThemeTrieElement {\n    _mainRule;\n    _children;\n    _rulesWithParentScopes;\n    constructor(_mainRule, rulesWithParentScopes = [], _children = {}) {\n        this._mainRule = _mainRule;\n        this._children = _children;\n        this._rulesWithParentScopes = rulesWithParentScopes;\n    }\n    static _sortBySpecificity(arr) {\n        if (arr.length === 1) {\n            return arr;\n        }\n        arr.sort(this._cmpBySpecificity);\n        return arr;\n    }\n    static _cmpBySpecificity(a, b) {\n        if (a.scopeDepth === b.scopeDepth) {\n            const aParentScopes = a.parentScopes;\n            const bParentScopes = b.parentScopes;\n            let aParentScopesLen = aParentScopes === null ? 0 : aParentScopes.length;\n            let bParentScopesLen = bParentScopes === null ? 0 : bParentScopes.length;\n            if (aParentScopesLen === bParentScopesLen) {\n                for (let i = 0; i < aParentScopesLen; i++) {\n                    const aLen = aParentScopes[i].length;\n                    const bLen = bParentScopes[i].length;\n                    if (aLen !== bLen) {\n                        return bLen - aLen;\n                    }\n                }\n            }\n            return bParentScopesLen - aParentScopesLen;\n        }\n        return b.scopeDepth - a.scopeDepth;\n    }\n    match(scope) {\n        if (scope === '') {\n            return ThemeTrieElement._sortBySpecificity([].concat(this._mainRule).concat(this._rulesWithParentScopes));\n        }\n        let dotIndex = scope.indexOf('.');\n        let head;\n        let tail;\n        if (dotIndex === -1) {\n            head = scope;\n            tail = '';\n        }\n        else {\n            head = scope.substring(0, dotIndex);\n            tail = scope.substring(dotIndex + 1);\n        }\n        if (this._children.hasOwnProperty(head)) {\n            return this._children[head].match(tail);\n        }\n        return ThemeTrieElement._sortBySpecificity([].concat(this._mainRule).concat(this._rulesWithParentScopes));\n    }\n    insert(scopeDepth, scope, parentScopes, fontStyle, foreground, background) {\n        if (scope === '') {\n            this._doInsertHere(scopeDepth, parentScopes, fontStyle, foreground, background);\n            return;\n        }\n        let dotIndex = scope.indexOf('.');\n        let head;\n        let tail;\n        if (dotIndex === -1) {\n            head = scope;\n            tail = '';\n        }\n        else {\n            head = scope.substring(0, dotIndex);\n            tail = scope.substring(dotIndex + 1);\n        }\n        let child;\n        if (this._children.hasOwnProperty(head)) {\n            child = this._children[head];\n        }\n        else {\n            child = new ThemeTrieElement(this._mainRule.clone(), ThemeTrieElementRule.cloneArr(this._rulesWithParentScopes));\n            this._children[head] = child;\n        }\n        child.insert(scopeDepth + 1, tail, parentScopes, fontStyle, foreground, background);\n    }\n    _doInsertHere(scopeDepth, parentScopes, fontStyle, foreground, background) {\n        if (parentScopes === null) {\n            // Merge into the main rule\n            this._mainRule.acceptOverwrite(scopeDepth, fontStyle, foreground, background);\n            return;\n        }\n        // Try to merge into existing rule\n        for (let i = 0, len = this._rulesWithParentScopes.length; i < len; i++) {\n            let rule = this._rulesWithParentScopes[i];\n            if (strArrCmp(rule.parentScopes, parentScopes) === 0) {\n                // bingo! => we get to merge this into an existing one\n                rule.acceptOverwrite(scopeDepth, fontStyle, foreground, background);\n                return;\n            }\n        }\n        // Must add a new rule\n        // Inherit from main rule\n        if (fontStyle === -1 /* FontStyle.NotSet */) {\n            fontStyle = this._mainRule.fontStyle;\n        }\n        if (foreground === 0) {\n            foreground = this._mainRule.foreground;\n        }\n        if (background === 0) {\n            background = this._mainRule.background;\n        }\n        this._rulesWithParentScopes.push(new ThemeTrieElementRule(scopeDepth, parentScopes, fontStyle, foreground, background));\n    }\n}\n\n/*---------------------------------------------------------\n * Copyright (C) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------*/\nclass BasicScopeAttributes {\n    languageId;\n    tokenType;\n    constructor(languageId, tokenType) {\n        this.languageId = languageId;\n        this.tokenType = tokenType;\n    }\n}\nclass BasicScopeAttributesProvider {\n    _defaultAttributes;\n    _embeddedLanguagesMatcher;\n    constructor(initialLanguageId, embeddedLanguages) {\n        this._defaultAttributes = new BasicScopeAttributes(initialLanguageId, 8 /* OptionalStandardTokenType.NotSet */);\n        this._embeddedLanguagesMatcher = new ScopeMatcher(Object.entries(embeddedLanguages || {}));\n    }\n    getDefaultAttributes() {\n        return this._defaultAttributes;\n    }\n    getBasicScopeAttributes(scopeName) {\n        if (scopeName === null) {\n            return BasicScopeAttributesProvider._NULL_SCOPE_METADATA;\n        }\n        return this._getBasicScopeAttributes.get(scopeName);\n    }\n    static _NULL_SCOPE_METADATA = new BasicScopeAttributes(0, 0);\n    _getBasicScopeAttributes = new CachedFn((scopeName) => {\n        const languageId = this._scopeToLanguage(scopeName);\n        const standardTokenType = this._toStandardTokenType(scopeName);\n        return new BasicScopeAttributes(languageId, standardTokenType);\n    });\n    /**\n     * Given a produced TM scope, return the language that token describes or null if unknown.\n     * e.g. source.html => html, source.css.embedded.html => css, punctuation.definition.tag.html => null\n     */\n    _scopeToLanguage(scope) {\n        return this._embeddedLanguagesMatcher.match(scope) || 0;\n    }\n    _toStandardTokenType(scopeName) {\n        const m = scopeName.match(BasicScopeAttributesProvider.STANDARD_TOKEN_TYPE_REGEXP);\n        if (!m) {\n            return 8 /* OptionalStandardTokenType.NotSet */;\n        }\n        switch (m[1]) {\n            case \"comment\":\n                return 1 /* OptionalStandardTokenType.Comment */;\n            case \"string\":\n                return 2 /* OptionalStandardTokenType.String */;\n            case \"regex\":\n                return 3 /* OptionalStandardTokenType.RegEx */;\n            case \"meta.embedded\":\n                return 0 /* OptionalStandardTokenType.Other */;\n        }\n        throw new Error(\"Unexpected match for standard token type!\");\n    }\n    static STANDARD_TOKEN_TYPE_REGEXP = /\\b(comment|string|regex|meta\\.embedded)\\b/;\n}\nclass ScopeMatcher {\n    values;\n    scopesRegExp;\n    constructor(values) {\n        if (values.length === 0) {\n            this.values = null;\n            this.scopesRegExp = null;\n        }\n        else {\n            this.values = new Map(values);\n            // create the regex\n            const escapedScopes = values.map(([scopeName, value]) => escapeRegExpCharacters(scopeName));\n            escapedScopes.sort();\n            escapedScopes.reverse(); // Longest scope first\n            this.scopesRegExp = new RegExp(`^((${escapedScopes.join(\")|(\")}))($|\\\\.)`, \"\");\n        }\n    }\n    match(scope) {\n        if (!this.scopesRegExp) {\n            return undefined;\n        }\n        const m = scope.match(this.scopesRegExp);\n        if (!m) {\n            // no scopes matched\n            return undefined;\n        }\n        return this.values.get(m[1]);\n    }\n}\n\n/*---------------------------------------------------------\n * Copyright (C) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------*/\nclass TokenizeStringResult {\n    stack;\n    stoppedEarly;\n    constructor(stack, stoppedEarly) {\n        this.stack = stack;\n        this.stoppedEarly = stoppedEarly;\n    }\n}\n/**\n * Tokenize a string\n * @param grammar\n * @param lineText\n * @param isFirstLine\n * @param linePos\n * @param stack\n * @param lineTokens\n * @param checkWhileConditions\n * @param timeLimit Use `0` to indicate no time limit\n * @returns the StackElement or StackElement.TIME_LIMIT_REACHED if the time limit has been reached\n */\nfunction _tokenizeString(grammar, lineText, isFirstLine, linePos, stack, lineTokens, checkWhileConditions, timeLimit) {\n    const lineLength = lineText.content.length;\n    let STOP = false;\n    let anchorPosition = -1;\n    if (checkWhileConditions) {\n        const whileCheckResult = _checkWhileConditions(grammar, lineText, isFirstLine, linePos, stack, lineTokens);\n        stack = whileCheckResult.stack;\n        linePos = whileCheckResult.linePos;\n        isFirstLine = whileCheckResult.isFirstLine;\n        anchorPosition = whileCheckResult.anchorPosition;\n    }\n    const startTime = Date.now();\n    while (!STOP) {\n        if (timeLimit !== 0) {\n            const elapsedTime = Date.now() - startTime;\n            if (elapsedTime > timeLimit) {\n                return new TokenizeStringResult(stack, true);\n            }\n        }\n        scanNext(); // potentially modifies linePos && anchorPosition\n    }\n    return new TokenizeStringResult(stack, false);\n    function scanNext() {\n        const r = matchRuleOrInjections(grammar, lineText, isFirstLine, linePos, stack, anchorPosition);\n        if (!r) {\n            // No match\n            lineTokens.produce(stack, lineLength);\n            STOP = true;\n            return;\n        }\n        const captureIndices = r.captureIndices;\n        const matchedRuleId = r.matchedRuleId;\n        const hasAdvanced = captureIndices && captureIndices.length > 0\n            ? captureIndices[0].end > linePos\n            : false;\n        if (matchedRuleId === endRuleId) {\n            // We matched the `end` for this rule => pop it\n            const poppedRule = stack.getRule(grammar);\n            lineTokens.produce(stack, captureIndices[0].start);\n            stack = stack.withContentNameScopesList(stack.nameScopesList);\n            handleCaptures(grammar, lineText, isFirstLine, stack, lineTokens, poppedRule.endCaptures, captureIndices);\n            lineTokens.produce(stack, captureIndices[0].end);\n            // pop\n            const popped = stack;\n            stack = stack.parent;\n            anchorPosition = popped.getAnchorPos();\n            if (!hasAdvanced && popped.getEnterPos() === linePos) {\n                // See https://github.com/Microsoft/vscode-textmate/issues/12\n                // Let's assume this was a mistake by the grammar author and the intent was to continue in this state\n                stack = popped;\n                lineTokens.produce(stack, lineLength);\n                STOP = true;\n                return;\n            }\n        }\n        else {\n            // We matched a rule!\n            const _rule = grammar.getRule(matchedRuleId);\n            lineTokens.produce(stack, captureIndices[0].start);\n            const beforePush = stack;\n            // push it on the stack rule\n            const scopeName = _rule.getName(lineText.content, captureIndices);\n            const nameScopesList = stack.contentNameScopesList.pushAttributed(scopeName, grammar);\n            stack = stack.push(matchedRuleId, linePos, anchorPosition, captureIndices[0].end === lineLength, null, nameScopesList, nameScopesList);\n            if (_rule instanceof BeginEndRule) {\n                const pushedRule = _rule;\n                handleCaptures(grammar, lineText, isFirstLine, stack, lineTokens, pushedRule.beginCaptures, captureIndices);\n                lineTokens.produce(stack, captureIndices[0].end);\n                anchorPosition = captureIndices[0].end;\n                const contentName = pushedRule.getContentName(lineText.content, captureIndices);\n                const contentNameScopesList = nameScopesList.pushAttributed(contentName, grammar);\n                stack = stack.withContentNameScopesList(contentNameScopesList);\n                if (pushedRule.endHasBackReferences) {\n                    stack = stack.withEndRule(pushedRule.getEndWithResolvedBackReferences(lineText.content, captureIndices));\n                }\n                if (!hasAdvanced && beforePush.hasSameRuleAs(stack)) {\n                    stack = stack.pop();\n                    lineTokens.produce(stack, lineLength);\n                    STOP = true;\n                    return;\n                }\n            }\n            else if (_rule instanceof BeginWhileRule) {\n                const pushedRule = _rule;\n                handleCaptures(grammar, lineText, isFirstLine, stack, lineTokens, pushedRule.beginCaptures, captureIndices);\n                lineTokens.produce(stack, captureIndices[0].end);\n                anchorPosition = captureIndices[0].end;\n                const contentName = pushedRule.getContentName(lineText.content, captureIndices);\n                const contentNameScopesList = nameScopesList.pushAttributed(contentName, grammar);\n                stack = stack.withContentNameScopesList(contentNameScopesList);\n                if (pushedRule.whileHasBackReferences) {\n                    stack = stack.withEndRule(pushedRule.getWhileWithResolvedBackReferences(lineText.content, captureIndices));\n                }\n                if (!hasAdvanced && beforePush.hasSameRuleAs(stack)) {\n                    stack = stack.pop();\n                    lineTokens.produce(stack, lineLength);\n                    STOP = true;\n                    return;\n                }\n            }\n            else {\n                const matchingRule = _rule;\n                handleCaptures(grammar, lineText, isFirstLine, stack, lineTokens, matchingRule.captures, captureIndices);\n                lineTokens.produce(stack, captureIndices[0].end);\n                // pop rule immediately since it is a MatchRule\n                stack = stack.pop();\n                if (!hasAdvanced) {\n                    stack = stack.safePop();\n                    lineTokens.produce(stack, lineLength);\n                    STOP = true;\n                    return;\n                }\n            }\n        }\n        if (captureIndices[0].end > linePos) {\n            // Advance stream\n            linePos = captureIndices[0].end;\n            isFirstLine = false;\n        }\n    }\n}\n/**\n * Walk the stack from bottom to top, and check each while condition in this order.\n * If any fails, cut off the entire stack above the failed while condition. While conditions\n * may also advance the linePosition.\n */\nfunction _checkWhileConditions(grammar, lineText, isFirstLine, linePos, stack, lineTokens) {\n    let anchorPosition = (stack.beginRuleCapturedEOL ? 0 : -1);\n    const whileRules = [];\n    for (let node = stack; node; node = node.pop()) {\n        const nodeRule = node.getRule(grammar);\n        if (nodeRule instanceof BeginWhileRule) {\n            whileRules.push({\n                rule: nodeRule,\n                stack: node\n            });\n        }\n    }\n    for (let whileRule = whileRules.pop(); whileRule; whileRule = whileRules.pop()) {\n        const { ruleScanner, findOptions } = prepareRuleWhileSearch(whileRule.rule, grammar, whileRule.stack.endRule, isFirstLine, linePos === anchorPosition);\n        const r = ruleScanner.findNextMatchSync(lineText, linePos, findOptions);\n        if (r) {\n            const matchedRuleId = r.ruleId;\n            if (matchedRuleId !== whileRuleId) {\n                // we shouldn't end up here\n                stack = whileRule.stack.pop();\n                break;\n            }\n            if (r.captureIndices && r.captureIndices.length) {\n                lineTokens.produce(whileRule.stack, r.captureIndices[0].start);\n                handleCaptures(grammar, lineText, isFirstLine, whileRule.stack, lineTokens, whileRule.rule.whileCaptures, r.captureIndices);\n                lineTokens.produce(whileRule.stack, r.captureIndices[0].end);\n                anchorPosition = r.captureIndices[0].end;\n                if (r.captureIndices[0].end > linePos) {\n                    linePos = r.captureIndices[0].end;\n                    isFirstLine = false;\n                }\n            }\n        }\n        else {\n            stack = whileRule.stack.pop();\n            break;\n        }\n    }\n    return { stack: stack, linePos: linePos, anchorPosition: anchorPosition, isFirstLine: isFirstLine };\n}\nfunction matchRuleOrInjections(grammar, lineText, isFirstLine, linePos, stack, anchorPosition) {\n    // Look for normal grammar rule\n    const matchResult = matchRule(grammar, lineText, isFirstLine, linePos, stack, anchorPosition);\n    // Look for injected rules\n    const injections = grammar.getInjections();\n    if (injections.length === 0) {\n        // No injections whatsoever => early return\n        return matchResult;\n    }\n    const injectionResult = matchInjections(injections, grammar, lineText, isFirstLine, linePos, stack, anchorPosition);\n    if (!injectionResult) {\n        // No injections matched => early return\n        return matchResult;\n    }\n    if (!matchResult) {\n        // Only injections matched => early return\n        return injectionResult;\n    }\n    // Decide if `matchResult` or `injectionResult` should win\n    const matchResultScore = matchResult.captureIndices[0].start;\n    const injectionResultScore = injectionResult.captureIndices[0].start;\n    if (injectionResultScore < matchResultScore || (injectionResult.priorityMatch && injectionResultScore === matchResultScore)) {\n        // injection won!\n        return injectionResult;\n    }\n    return matchResult;\n}\nfunction matchRule(grammar, lineText, isFirstLine, linePos, stack, anchorPosition) {\n    const rule = stack.getRule(grammar);\n    const { ruleScanner, findOptions } = prepareRuleSearch(rule, grammar, stack.endRule, isFirstLine, linePos === anchorPosition);\n    const r = ruleScanner.findNextMatchSync(lineText, linePos, findOptions);\n    if (r) {\n        return {\n            captureIndices: r.captureIndices,\n            matchedRuleId: r.ruleId\n        };\n    }\n    return null;\n}\nfunction matchInjections(injections, grammar, lineText, isFirstLine, linePos, stack, anchorPosition) {\n    // The lower the better\n    let bestMatchRating = Number.MAX_VALUE;\n    let bestMatchCaptureIndices = null;\n    let bestMatchRuleId;\n    let bestMatchResultPriority = 0;\n    const scopes = stack.contentNameScopesList.getScopeNames();\n    for (let i = 0, len = injections.length; i < len; i++) {\n        const injection = injections[i];\n        if (!injection.matcher(scopes)) {\n            // injection selector doesn't match stack\n            continue;\n        }\n        const rule = grammar.getRule(injection.ruleId);\n        const { ruleScanner, findOptions } = prepareRuleSearch(rule, grammar, null, isFirstLine, linePos === anchorPosition);\n        const matchResult = ruleScanner.findNextMatchSync(lineText, linePos, findOptions);\n        if (!matchResult) {\n            continue;\n        }\n        const matchRating = matchResult.captureIndices[0].start;\n        if (matchRating >= bestMatchRating) {\n            // Injections are sorted by priority, so the previous injection had a better or equal priority\n            continue;\n        }\n        bestMatchRating = matchRating;\n        bestMatchCaptureIndices = matchResult.captureIndices;\n        bestMatchRuleId = matchResult.ruleId;\n        bestMatchResultPriority = injection.priority;\n        if (bestMatchRating === linePos) {\n            // No more need to look at the rest of the injections.\n            break;\n        }\n    }\n    if (bestMatchCaptureIndices) {\n        return {\n            priorityMatch: bestMatchResultPriority === -1,\n            captureIndices: bestMatchCaptureIndices,\n            matchedRuleId: bestMatchRuleId\n        };\n    }\n    return null;\n}\nfunction prepareRuleSearch(rule, grammar, endRegexSource, allowA, allowG) {\n    const ruleScanner = rule.compileAG(grammar, endRegexSource, allowA, allowG);\n    return { ruleScanner, findOptions: 0 /* FindOption.None */ };\n}\nfunction prepareRuleWhileSearch(rule, grammar, endRegexSource, allowA, allowG) {\n    const ruleScanner = rule.compileWhileAG(grammar, endRegexSource, allowA, allowG);\n    return { ruleScanner, findOptions: 0 /* FindOption.None */ };\n}\nfunction handleCaptures(grammar, lineText, isFirstLine, stack, lineTokens, captures, captureIndices) {\n    if (captures.length === 0) {\n        return;\n    }\n    const lineTextContent = lineText.content;\n    const len = Math.min(captures.length, captureIndices.length);\n    const localStack = [];\n    const maxEnd = captureIndices[0].end;\n    for (let i = 0; i < len; i++) {\n        const captureRule = captures[i];\n        if (captureRule === null) {\n            // Not interested\n            continue;\n        }\n        const captureIndex = captureIndices[i];\n        if (captureIndex.length === 0) {\n            // Nothing really captured\n            continue;\n        }\n        if (captureIndex.start > maxEnd) {\n            // Capture going beyond consumed string\n            break;\n        }\n        // pop captures while needed\n        while (localStack.length > 0 && localStack[localStack.length - 1].endPos <= captureIndex.start) {\n            // pop!\n            lineTokens.produceFromScopes(localStack[localStack.length - 1].scopes, localStack[localStack.length - 1].endPos);\n            localStack.pop();\n        }\n        if (localStack.length > 0) {\n            lineTokens.produceFromScopes(localStack[localStack.length - 1].scopes, captureIndex.start);\n        }\n        else {\n            lineTokens.produce(stack, captureIndex.start);\n        }\n        if (captureRule.retokenizeCapturedWithRuleId) {\n            // the capture requires additional matching\n            const scopeName = captureRule.getName(lineTextContent, captureIndices);\n            const nameScopesList = stack.contentNameScopesList.pushAttributed(scopeName, grammar);\n            const contentName = captureRule.getContentName(lineTextContent, captureIndices);\n            const contentNameScopesList = nameScopesList.pushAttributed(contentName, grammar);\n            const stackClone = stack.push(captureRule.retokenizeCapturedWithRuleId, captureIndex.start, -1, false, null, nameScopesList, contentNameScopesList);\n            const onigSubStr = grammar.createOnigString(lineTextContent.substring(0, captureIndex.end));\n            _tokenizeString(grammar, onigSubStr, (isFirstLine && captureIndex.start === 0), captureIndex.start, stackClone, lineTokens, false, /* no time limit */ 0);\n            disposeOnigString(onigSubStr);\n            continue;\n        }\n        const captureRuleScopeName = captureRule.getName(lineTextContent, captureIndices);\n        if (captureRuleScopeName !== null) {\n            // push\n            const base = localStack.length > 0 ? localStack[localStack.length - 1].scopes : stack.contentNameScopesList;\n            const captureRuleScopesList = base.pushAttributed(captureRuleScopeName, grammar);\n            localStack.push(new LocalStackElement(captureRuleScopesList, captureIndex.end));\n        }\n    }\n    while (localStack.length > 0) {\n        // pop!\n        lineTokens.produceFromScopes(localStack[localStack.length - 1].scopes, localStack[localStack.length - 1].endPos);\n        localStack.pop();\n    }\n}\nclass LocalStackElement {\n    scopes;\n    endPos;\n    constructor(scopes, endPos) {\n        this.scopes = scopes;\n        this.endPos = endPos;\n    }\n}\n\n/*---------------------------------------------------------\n * Copyright (C) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------*/\nfunction createGrammar(scopeName, grammar, initialLanguage, embeddedLanguages, tokenTypes, balancedBracketSelectors, grammarRepository, onigLib) {\n    return new Grammar(scopeName, grammar, initialLanguage, embeddedLanguages, tokenTypes, balancedBracketSelectors, grammarRepository, onigLib); //TODO\n}\nfunction collectInjections(result, selector, rule, ruleFactoryHelper, grammar) {\n    const matchers = createMatchers(selector, nameMatcher);\n    const ruleId = RuleFactory.getCompiledRuleId(rule, ruleFactoryHelper, grammar.repository);\n    for (const matcher of matchers) {\n        result.push({\n            debugSelector: selector,\n            matcher: matcher.matcher,\n            ruleId: ruleId,\n            grammar: grammar,\n            priority: matcher.priority\n        });\n    }\n}\nfunction nameMatcher(identifers, scopes) {\n    if (scopes.length < identifers.length) {\n        return false;\n    }\n    let lastIndex = 0;\n    return identifers.every(identifier => {\n        for (let i = lastIndex; i < scopes.length; i++) {\n            if (scopesAreMatching(scopes[i], identifier)) {\n                lastIndex = i + 1;\n                return true;\n            }\n        }\n        return false;\n    });\n}\nfunction scopesAreMatching(thisScopeName, scopeName) {\n    if (!thisScopeName) {\n        return false;\n    }\n    if (thisScopeName === scopeName) {\n        return true;\n    }\n    const len = scopeName.length;\n    return thisScopeName.length > len && thisScopeName.substr(0, len) === scopeName && thisScopeName[len] === '.';\n}\nclass Grammar {\n    _rootScopeName;\n    balancedBracketSelectors;\n    _onigLib;\n    _rootId;\n    _lastRuleId;\n    _ruleId2desc;\n    _includedGrammars;\n    _grammarRepository;\n    _grammar;\n    _injections;\n    _basicScopeAttributesProvider;\n    _tokenTypeMatchers;\n    get themeProvider() { return this._grammarRepository; }\n    constructor(_rootScopeName, grammar, initialLanguage, embeddedLanguages, tokenTypes, balancedBracketSelectors, grammarRepository, _onigLib) {\n        this._rootScopeName = _rootScopeName;\n        this.balancedBracketSelectors = balancedBracketSelectors;\n        this._onigLib = _onigLib;\n        this._basicScopeAttributesProvider = new BasicScopeAttributesProvider(initialLanguage, embeddedLanguages);\n        this._rootId = -1;\n        this._lastRuleId = 0;\n        this._ruleId2desc = [null];\n        this._includedGrammars = {};\n        this._grammarRepository = grammarRepository;\n        this._grammar = initGrammar(grammar, null);\n        this._injections = null;\n        this._tokenTypeMatchers = [];\n        if (tokenTypes) {\n            for (const selector of Object.keys(tokenTypes)) {\n                const matchers = createMatchers(selector, nameMatcher);\n                for (const matcher of matchers) {\n                    this._tokenTypeMatchers.push({\n                        matcher: matcher.matcher,\n                        type: tokenTypes[selector],\n                    });\n                }\n            }\n        }\n    }\n    dispose() {\n        for (const rule of this._ruleId2desc) {\n            if (rule) {\n                rule.dispose();\n            }\n        }\n    }\n    createOnigScanner(sources) {\n        return this._onigLib.createOnigScanner(sources);\n    }\n    createOnigString(sources) {\n        return this._onigLib.createOnigString(sources);\n    }\n    getMetadataForScope(scope) {\n        return this._basicScopeAttributesProvider.getBasicScopeAttributes(scope);\n    }\n    _collectInjections() {\n        const grammarRepository = {\n            lookup: (scopeName) => {\n                if (scopeName === this._rootScopeName) {\n                    return this._grammar;\n                }\n                return this.getExternalGrammar(scopeName);\n            },\n            injections: (scopeName) => {\n                return this._grammarRepository.injections(scopeName);\n            },\n        };\n        const result = [];\n        const scopeName = this._rootScopeName;\n        const grammar = grammarRepository.lookup(scopeName);\n        if (grammar) {\n            // add injections from the current grammar\n            const rawInjections = grammar.injections;\n            if (rawInjections) {\n                for (let expression in rawInjections) {\n                    collectInjections(result, expression, rawInjections[expression], this, grammar);\n                }\n            }\n            // add injection grammars contributed for the current scope\n            const injectionScopeNames = this._grammarRepository.injections(scopeName);\n            if (injectionScopeNames) {\n                injectionScopeNames.forEach((injectionScopeName) => {\n                    const injectionGrammar = this.getExternalGrammar(injectionScopeName);\n                    if (injectionGrammar) {\n                        const selector = injectionGrammar.injectionSelector;\n                        if (selector) {\n                            collectInjections(result, selector, injectionGrammar, this, injectionGrammar);\n                        }\n                    }\n                });\n            }\n        }\n        result.sort((i1, i2) => i1.priority - i2.priority); // sort by priority\n        return result;\n    }\n    getInjections() {\n        if (this._injections === null) {\n            this._injections = this._collectInjections();\n        }\n        return this._injections;\n    }\n    registerRule(factory) {\n        const id = ++this._lastRuleId;\n        const result = factory(ruleIdFromNumber(id));\n        this._ruleId2desc[id] = result;\n        return result;\n    }\n    getRule(ruleId) {\n        return this._ruleId2desc[ruleIdToNumber(ruleId)];\n    }\n    getExternalGrammar(scopeName, repository) {\n        if (this._includedGrammars[scopeName]) {\n            return this._includedGrammars[scopeName];\n        }\n        else if (this._grammarRepository) {\n            const rawIncludedGrammar = this._grammarRepository.lookup(scopeName);\n            if (rawIncludedGrammar) {\n                // console.log('LOADED GRAMMAR ' + pattern.include);\n                this._includedGrammars[scopeName] = initGrammar(rawIncludedGrammar, repository && repository.$base);\n                return this._includedGrammars[scopeName];\n            }\n        }\n        return undefined;\n    }\n    tokenizeLine(lineText, prevState, timeLimit = 0) {\n        const r = this._tokenize(lineText, prevState, false, timeLimit);\n        return {\n            tokens: r.lineTokens.getResult(r.ruleStack, r.lineLength),\n            ruleStack: r.ruleStack,\n            stoppedEarly: r.stoppedEarly,\n        };\n    }\n    tokenizeLine2(lineText, prevState, timeLimit = 0) {\n        const r = this._tokenize(lineText, prevState, true, timeLimit);\n        return {\n            tokens: r.lineTokens.getBinaryResult(r.ruleStack, r.lineLength),\n            ruleStack: r.ruleStack,\n            stoppedEarly: r.stoppedEarly,\n        };\n    }\n    _tokenize(lineText, prevState, emitBinaryTokens, timeLimit) {\n        if (this._rootId === -1) {\n            this._rootId = RuleFactory.getCompiledRuleId(this._grammar.repository.$self, this, this._grammar.repository);\n            // This ensures ids are deterministic, and thus equal in renderer and webworker.\n            this.getInjections();\n        }\n        let isFirstLine;\n        if (!prevState || prevState === StateStackImpl.NULL) {\n            isFirstLine = true;\n            const rawDefaultMetadata = this._basicScopeAttributesProvider.getDefaultAttributes();\n            const defaultStyle = this.themeProvider.getDefaults();\n            const defaultMetadata = EncodedTokenAttributes.set(0, rawDefaultMetadata.languageId, rawDefaultMetadata.tokenType, null, defaultStyle.fontStyle, defaultStyle.foregroundId, defaultStyle.backgroundId);\n            const rootScopeName = this.getRule(this._rootId).getName(null, null);\n            let scopeList;\n            if (rootScopeName) {\n                scopeList = AttributedScopeStack.createRootAndLookUpScopeName(rootScopeName, defaultMetadata, this);\n            }\n            else {\n                scopeList = AttributedScopeStack.createRoot(\"unknown\", defaultMetadata);\n            }\n            prevState = new StateStackImpl(null, this._rootId, -1, -1, false, null, scopeList, scopeList);\n        }\n        else {\n            isFirstLine = false;\n            prevState.reset();\n        }\n        lineText = lineText + \"\\n\";\n        const onigLineText = this.createOnigString(lineText);\n        const lineLength = onigLineText.content.length;\n        const lineTokens = new LineTokens(emitBinaryTokens, lineText, this._tokenTypeMatchers, this.balancedBracketSelectors);\n        const r = _tokenizeString(this, onigLineText, isFirstLine, 0, prevState, lineTokens, true, timeLimit);\n        disposeOnigString(onigLineText);\n        return {\n            lineLength: lineLength,\n            lineTokens: lineTokens,\n            ruleStack: r.stack,\n            stoppedEarly: r.stoppedEarly,\n        };\n    }\n}\nfunction initGrammar(grammar, base) {\n    grammar = clone(grammar);\n    grammar.repository = grammar.repository || {};\n    grammar.repository.$self = {\n        $vscodeTextmateLocation: grammar.$vscodeTextmateLocation,\n        patterns: grammar.patterns,\n        name: grammar.scopeName\n    };\n    grammar.repository.$base = base || grammar.repository.$self;\n    return grammar;\n}\nclass AttributedScopeStack {\n    parent;\n    scopePath;\n    tokenAttributes;\n    static fromExtension(namesScopeList, contentNameScopesList) {\n        let current = namesScopeList;\n        let scopeNames = namesScopeList?.scopePath ?? null;\n        for (const frame of contentNameScopesList) {\n            scopeNames = ScopeStack.push(scopeNames, frame.scopeNames);\n            current = new AttributedScopeStack(current, scopeNames, frame.encodedTokenAttributes);\n        }\n        return current;\n    }\n    static createRoot(scopeName, tokenAttributes) {\n        return new AttributedScopeStack(null, new ScopeStack(null, scopeName), tokenAttributes);\n    }\n    static createRootAndLookUpScopeName(scopeName, tokenAttributes, grammar) {\n        const rawRootMetadata = grammar.getMetadataForScope(scopeName);\n        const scopePath = new ScopeStack(null, scopeName);\n        const rootStyle = grammar.themeProvider.themeMatch(scopePath);\n        const resolvedTokenAttributes = AttributedScopeStack.mergeAttributes(tokenAttributes, rawRootMetadata, rootStyle);\n        return new AttributedScopeStack(null, scopePath, resolvedTokenAttributes);\n    }\n    get scopeName() { return this.scopePath.scopeName; }\n    /**\n     * Invariant:\n     * ```\n     * if (parent && !scopePath.extends(parent.scopePath)) {\n     * \tthrow new Error();\n     * }\n     * ```\n     */\n    constructor(parent, scopePath, tokenAttributes) {\n        this.parent = parent;\n        this.scopePath = scopePath;\n        this.tokenAttributes = tokenAttributes;\n    }\n    toString() {\n        return this.getScopeNames().join(' ');\n    }\n    equals(other) {\n        return AttributedScopeStack.equals(this, other);\n    }\n    static equals(a, b) {\n        do {\n            if (a === b) {\n                return true;\n            }\n            if (!a && !b) {\n                // End of list reached for both\n                return true;\n            }\n            if (!a || !b) {\n                // End of list reached only for one\n                return false;\n            }\n            if (a.scopeName !== b.scopeName || a.tokenAttributes !== b.tokenAttributes) {\n                return false;\n            }\n            // Go to previous pair\n            a = a.parent;\n            b = b.parent;\n        } while (true);\n    }\n    static mergeAttributes(existingTokenAttributes, basicScopeAttributes, styleAttributes) {\n        let fontStyle = -1 /* FontStyle.NotSet */;\n        let foreground = 0;\n        let background = 0;\n        if (styleAttributes !== null) {\n            fontStyle = styleAttributes.fontStyle;\n            foreground = styleAttributes.foregroundId;\n            background = styleAttributes.backgroundId;\n        }\n        return EncodedTokenAttributes.set(existingTokenAttributes, basicScopeAttributes.languageId, basicScopeAttributes.tokenType, null, fontStyle, foreground, background);\n    }\n    pushAttributed(scopePath, grammar) {\n        if (scopePath === null) {\n            return this;\n        }\n        if (scopePath.indexOf(' ') === -1) {\n            // This is the common case and much faster\n            return AttributedScopeStack._pushAttributed(this, scopePath, grammar);\n        }\n        const scopes = scopePath.split(/ /g);\n        let result = this;\n        for (const scope of scopes) {\n            result = AttributedScopeStack._pushAttributed(result, scope, grammar);\n        }\n        return result;\n    }\n    static _pushAttributed(target, scopeName, grammar) {\n        const rawMetadata = grammar.getMetadataForScope(scopeName);\n        const newPath = target.scopePath.push(scopeName);\n        const scopeThemeMatchResult = grammar.themeProvider.themeMatch(newPath);\n        const metadata = AttributedScopeStack.mergeAttributes(target.tokenAttributes, rawMetadata, scopeThemeMatchResult);\n        return new AttributedScopeStack(target, newPath, metadata);\n    }\n    getScopeNames() {\n        return this.scopePath.getSegments();\n    }\n    getExtensionIfDefined(base) {\n        const result = [];\n        let self = this;\n        while (self && self !== base) {\n            result.push({\n                encodedTokenAttributes: self.tokenAttributes,\n                scopeNames: self.scopePath.getExtensionIfDefined(self.parent?.scopePath ?? null),\n            });\n            self = self.parent;\n        }\n        return self === base ? result.reverse() : undefined;\n    }\n}\n/**\n * Represents a \"pushed\" state on the stack (as a linked list element).\n */\nclass StateStackImpl {\n    parent;\n    ruleId;\n    beginRuleCapturedEOL;\n    endRule;\n    nameScopesList;\n    contentNameScopesList;\n    _stackElementBrand = undefined;\n    // TODO remove me\n    static NULL = new StateStackImpl(null, 0, 0, 0, false, null, null, null);\n    /**\n     * The position on the current line where this state was pushed.\n     * This is relevant only while tokenizing a line, to detect endless loops.\n     * Its value is meaningless across lines.\n     */\n    _enterPos;\n    /**\n     * The captured anchor position when this stack element was pushed.\n     * This is relevant only while tokenizing a line, to restore the anchor position when popping.\n     * Its value is meaningless across lines.\n     */\n    _anchorPos;\n    /**\n     * The depth of the stack.\n     */\n    depth;\n    /**\n     * Invariant:\n     * ```\n     * if (contentNameScopesList !== nameScopesList && contentNameScopesList?.parent !== nameScopesList) {\n     * \tthrow new Error();\n     * }\n     * if (this.parent && !nameScopesList.extends(this.parent.contentNameScopesList)) {\n     * \tthrow new Error();\n     * }\n     * ```\n     */\n    constructor(\n    /**\n     * The previous state on the stack (or null for the root state).\n     */\n    parent, \n    /**\n     * The state (rule) that this element represents.\n     */\n    ruleId, enterPos, anchorPos, \n    /**\n     * The state has entered and captured \\n. This means that the next line should have an anchorPosition of 0.\n     */\n    beginRuleCapturedEOL, \n    /**\n     * The \"pop\" (end) condition for this state in case that it was dynamically generated through captured text.\n     */\n    endRule, \n    /**\n     * The list of scopes containing the \"name\" for this state.\n     */\n    nameScopesList, \n    /**\n     * The list of scopes containing the \"contentName\" (besides \"name\") for this state.\n     * This list **must** contain as an element `scopeName`.\n     */\n    contentNameScopesList) {\n        this.parent = parent;\n        this.ruleId = ruleId;\n        this.beginRuleCapturedEOL = beginRuleCapturedEOL;\n        this.endRule = endRule;\n        this.nameScopesList = nameScopesList;\n        this.contentNameScopesList = contentNameScopesList;\n        this.depth = this.parent ? this.parent.depth + 1 : 1;\n        this._enterPos = enterPos;\n        this._anchorPos = anchorPos;\n    }\n    equals(other) {\n        if (other === null) {\n            return false;\n        }\n        return StateStackImpl._equals(this, other);\n    }\n    static _equals(a, b) {\n        if (a === b) {\n            return true;\n        }\n        if (!this._structuralEquals(a, b)) {\n            return false;\n        }\n        return AttributedScopeStack.equals(a.contentNameScopesList, b.contentNameScopesList);\n    }\n    /**\n     * A structural equals check. Does not take into account `scopes`.\n     */\n    static _structuralEquals(a, b) {\n        do {\n            if (a === b) {\n                return true;\n            }\n            if (!a && !b) {\n                // End of list reached for both\n                return true;\n            }\n            if (!a || !b) {\n                // End of list reached only for one\n                return false;\n            }\n            if (a.depth !== b.depth ||\n                a.ruleId !== b.ruleId ||\n                a.endRule !== b.endRule) {\n                return false;\n            }\n            // Go to previous pair\n            a = a.parent;\n            b = b.parent;\n        } while (true);\n    }\n    clone() {\n        return this;\n    }\n    static _reset(el) {\n        while (el) {\n            el._enterPos = -1;\n            el._anchorPos = -1;\n            el = el.parent;\n        }\n    }\n    reset() {\n        StateStackImpl._reset(this);\n    }\n    pop() {\n        return this.parent;\n    }\n    safePop() {\n        if (this.parent) {\n            return this.parent;\n        }\n        return this;\n    }\n    push(ruleId, enterPos, anchorPos, beginRuleCapturedEOL, endRule, nameScopesList, contentNameScopesList) {\n        return new StateStackImpl(this, ruleId, enterPos, anchorPos, beginRuleCapturedEOL, endRule, nameScopesList, contentNameScopesList);\n    }\n    getEnterPos() {\n        return this._enterPos;\n    }\n    getAnchorPos() {\n        return this._anchorPos;\n    }\n    getRule(grammar) {\n        return grammar.getRule(this.ruleId);\n    }\n    toString() {\n        const r = [];\n        this._writeString(r, 0);\n        return \"[\" + r.join(\",\") + \"]\";\n    }\n    _writeString(res, outIndex) {\n        if (this.parent) {\n            outIndex = this.parent._writeString(res, outIndex);\n        }\n        res[outIndex++] = `(${this.ruleId}, ${this.nameScopesList?.toString()}, ${this.contentNameScopesList?.toString()})`;\n        return outIndex;\n    }\n    withContentNameScopesList(contentNameScopeStack) {\n        if (this.contentNameScopesList === contentNameScopeStack) {\n            return this;\n        }\n        return this.parent.push(this.ruleId, this._enterPos, this._anchorPos, this.beginRuleCapturedEOL, this.endRule, this.nameScopesList, contentNameScopeStack);\n    }\n    withEndRule(endRule) {\n        if (this.endRule === endRule) {\n            return this;\n        }\n        return new StateStackImpl(this.parent, this.ruleId, this._enterPos, this._anchorPos, this.beginRuleCapturedEOL, endRule, this.nameScopesList, this.contentNameScopesList);\n    }\n    // Used to warn of endless loops\n    hasSameRuleAs(other) {\n        let el = this;\n        while (el && el._enterPos === other._enterPos) {\n            if (el.ruleId === other.ruleId) {\n                return true;\n            }\n            el = el.parent;\n        }\n        return false;\n    }\n    toStateStackFrame() {\n        return {\n            ruleId: ruleIdToNumber(this.ruleId),\n            beginRuleCapturedEOL: this.beginRuleCapturedEOL,\n            endRule: this.endRule,\n            nameScopesList: this.nameScopesList?.getExtensionIfDefined(this.parent?.nameScopesList ?? null) ?? [],\n            contentNameScopesList: this.contentNameScopesList?.getExtensionIfDefined(this.nameScopesList) ?? [],\n        };\n    }\n    static pushFrame(self, frame) {\n        const namesScopeList = AttributedScopeStack.fromExtension(self?.nameScopesList ?? null, frame.nameScopesList);\n        return new StateStackImpl(self, ruleIdFromNumber(frame.ruleId), frame.enterPos ?? -1, frame.anchorPos ?? -1, frame.beginRuleCapturedEOL, frame.endRule, namesScopeList, AttributedScopeStack.fromExtension(namesScopeList, frame.contentNameScopesList));\n    }\n}\nclass BalancedBracketSelectors {\n    balancedBracketScopes;\n    unbalancedBracketScopes;\n    allowAny = false;\n    constructor(balancedBracketScopes, unbalancedBracketScopes) {\n        this.balancedBracketScopes = balancedBracketScopes.flatMap((selector) => {\n            if (selector === '*') {\n                this.allowAny = true;\n                return [];\n            }\n            return createMatchers(selector, nameMatcher).map((m) => m.matcher);\n        });\n        this.unbalancedBracketScopes = unbalancedBracketScopes.flatMap((selector) => createMatchers(selector, nameMatcher).map((m) => m.matcher));\n    }\n    get matchesAlways() {\n        return this.allowAny && this.unbalancedBracketScopes.length === 0;\n    }\n    get matchesNever() {\n        return this.balancedBracketScopes.length === 0 && !this.allowAny;\n    }\n    match(scopes) {\n        for (const excluder of this.unbalancedBracketScopes) {\n            if (excluder(scopes)) {\n                return false;\n            }\n        }\n        for (const includer of this.balancedBracketScopes) {\n            if (includer(scopes)) {\n                return true;\n            }\n        }\n        return this.allowAny;\n    }\n}\nclass LineTokens {\n    balancedBracketSelectors;\n    _emitBinaryTokens;\n    /**\n     * defined only if `false`.\n     */\n    _lineText;\n    /**\n     * used only if `_emitBinaryTokens` is false.\n     */\n    _tokens;\n    /**\n     * used only if `_emitBinaryTokens` is true.\n     */\n    _binaryTokens;\n    _lastTokenEndIndex;\n    _tokenTypeOverrides;\n    constructor(emitBinaryTokens, lineText, tokenTypeOverrides, balancedBracketSelectors) {\n        this.balancedBracketSelectors = balancedBracketSelectors;\n        this._emitBinaryTokens = emitBinaryTokens;\n        this._tokenTypeOverrides = tokenTypeOverrides;\n        {\n            this._lineText = null;\n        }\n        this._tokens = [];\n        this._binaryTokens = [];\n        this._lastTokenEndIndex = 0;\n    }\n    produce(stack, endIndex) {\n        this.produceFromScopes(stack.contentNameScopesList, endIndex);\n    }\n    produceFromScopes(scopesList, endIndex) {\n        if (this._lastTokenEndIndex >= endIndex) {\n            return;\n        }\n        if (this._emitBinaryTokens) {\n            let metadata = scopesList?.tokenAttributes ?? 0;\n            let containsBalancedBrackets = false;\n            if (this.balancedBracketSelectors?.matchesAlways) {\n                containsBalancedBrackets = true;\n            }\n            if (this._tokenTypeOverrides.length > 0 || (this.balancedBracketSelectors && !this.balancedBracketSelectors.matchesAlways && !this.balancedBracketSelectors.matchesNever)) {\n                // Only generate scope array when required to improve performance\n                const scopes = scopesList?.getScopeNames() ?? [];\n                for (const tokenType of this._tokenTypeOverrides) {\n                    if (tokenType.matcher(scopes)) {\n                        metadata = EncodedTokenAttributes.set(metadata, 0, toOptionalTokenType(tokenType.type), null, -1 /* FontStyle.NotSet */, 0, 0);\n                    }\n                }\n                if (this.balancedBracketSelectors) {\n                    containsBalancedBrackets = this.balancedBracketSelectors.match(scopes);\n                }\n            }\n            if (containsBalancedBrackets) {\n                metadata = EncodedTokenAttributes.set(metadata, 0, 8 /* OptionalStandardTokenType.NotSet */, containsBalancedBrackets, -1 /* FontStyle.NotSet */, 0, 0);\n            }\n            if (this._binaryTokens.length > 0 && this._binaryTokens[this._binaryTokens.length - 1] === metadata) {\n                // no need to push a token with the same metadata\n                this._lastTokenEndIndex = endIndex;\n                return;\n            }\n            this._binaryTokens.push(this._lastTokenEndIndex);\n            this._binaryTokens.push(metadata);\n            this._lastTokenEndIndex = endIndex;\n            return;\n        }\n        const scopes = scopesList?.getScopeNames() ?? [];\n        this._tokens.push({\n            startIndex: this._lastTokenEndIndex,\n            endIndex: endIndex,\n            // value: lineText.substring(lastTokenEndIndex, endIndex),\n            scopes: scopes\n        });\n        this._lastTokenEndIndex = endIndex;\n    }\n    getResult(stack, lineLength) {\n        if (this._tokens.length > 0 && this._tokens[this._tokens.length - 1].startIndex === lineLength - 1) {\n            // pop produced token for newline\n            this._tokens.pop();\n        }\n        if (this._tokens.length === 0) {\n            this._lastTokenEndIndex = -1;\n            this.produce(stack, lineLength);\n            this._tokens[this._tokens.length - 1].startIndex = 0;\n        }\n        return this._tokens;\n    }\n    getBinaryResult(stack, lineLength) {\n        if (this._binaryTokens.length > 0 && this._binaryTokens[this._binaryTokens.length - 2] === lineLength - 1) {\n            // pop produced token for newline\n            this._binaryTokens.pop();\n            this._binaryTokens.pop();\n        }\n        if (this._binaryTokens.length === 0) {\n            this._lastTokenEndIndex = -1;\n            this.produce(stack, lineLength);\n            this._binaryTokens[this._binaryTokens.length - 2] = 0;\n        }\n        const result = new Uint32Array(this._binaryTokens.length);\n        for (let i = 0, len = this._binaryTokens.length; i < len; i++) {\n            result[i] = this._binaryTokens[i];\n        }\n        return result;\n    }\n}\n\n/*---------------------------------------------------------\n * Copyright (C) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------*/\nclass SyncRegistry {\n    _onigLibPromise;\n    _grammars = new Map();\n    _rawGrammars = new Map();\n    _injectionGrammars = new Map();\n    _theme;\n    constructor(theme, _onigLibPromise) {\n        this._onigLibPromise = _onigLibPromise;\n        this._theme = theme;\n    }\n    dispose() {\n        for (const grammar of this._grammars.values()) {\n            grammar.dispose();\n        }\n    }\n    setTheme(theme) {\n        this._theme = theme;\n    }\n    getColorMap() {\n        return this._theme.getColorMap();\n    }\n    /**\n     * Add `grammar` to registry and return a list of referenced scope names\n     */\n    addGrammar(grammar, injectionScopeNames) {\n        this._rawGrammars.set(grammar.scopeName, grammar);\n        if (injectionScopeNames) {\n            this._injectionGrammars.set(grammar.scopeName, injectionScopeNames);\n        }\n    }\n    /**\n     * Lookup a raw grammar.\n     */\n    lookup(scopeName) {\n        return this._rawGrammars.get(scopeName);\n    }\n    /**\n     * Returns the injections for the given grammar\n     */\n    injections(targetScope) {\n        return this._injectionGrammars.get(targetScope);\n    }\n    /**\n     * Get the default theme settings\n     */\n    getDefaults() {\n        return this._theme.getDefaults();\n    }\n    /**\n     * Match a scope in the theme.\n     */\n    themeMatch(scopePath) {\n        return this._theme.match(scopePath);\n    }\n    /**\n     * Lookup a grammar.\n     */\n    async grammarForScopeName(scopeName, initialLanguage, embeddedLanguages, tokenTypes, balancedBracketSelectors) {\n        if (!this._grammars.has(scopeName)) {\n            let rawGrammar = this._rawGrammars.get(scopeName);\n            if (!rawGrammar) {\n                return null;\n            }\n            this._grammars.set(scopeName, createGrammar(scopeName, rawGrammar, initialLanguage, embeddedLanguages, tokenTypes, balancedBracketSelectors, this, await this._onigLibPromise));\n        }\n        return this._grammars.get(scopeName);\n    }\n}\n\n/*---------------------------------------------------------\n * Copyright (C) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------*/\n/**\n * The registry that will hold all grammars.\n */\nlet Registry$1 = class Registry {\n    _options;\n    _syncRegistry;\n    _ensureGrammarCache;\n    constructor(options) {\n        this._options = options;\n        this._syncRegistry = new SyncRegistry(Theme.createFromRawTheme(options.theme, options.colorMap), options.onigLib);\n        this._ensureGrammarCache = new Map();\n    }\n    dispose() {\n        this._syncRegistry.dispose();\n    }\n    /**\n     * Change the theme. Once called, no previous `ruleStack` should be used anymore.\n     */\n    setTheme(theme, colorMap) {\n        this._syncRegistry.setTheme(Theme.createFromRawTheme(theme, colorMap));\n    }\n    /**\n     * Returns a lookup array for color ids.\n     */\n    getColorMap() {\n        return this._syncRegistry.getColorMap();\n    }\n    /**\n     * Load the grammar for `scopeName` and all referenced included grammars asynchronously.\n     * Please do not use language id 0.\n     */\n    loadGrammarWithEmbeddedLanguages(initialScopeName, initialLanguage, embeddedLanguages) {\n        return this.loadGrammarWithConfiguration(initialScopeName, initialLanguage, { embeddedLanguages });\n    }\n    /**\n     * Load the grammar for `scopeName` and all referenced included grammars asynchronously.\n     * Please do not use language id 0.\n     */\n    loadGrammarWithConfiguration(initialScopeName, initialLanguage, configuration) {\n        return this._loadGrammar(initialScopeName, initialLanguage, configuration.embeddedLanguages, configuration.tokenTypes, new BalancedBracketSelectors(configuration.balancedBracketSelectors || [], configuration.unbalancedBracketSelectors || []));\n    }\n    /**\n     * Load the grammar for `scopeName` and all referenced included grammars asynchronously.\n     */\n    loadGrammar(initialScopeName) {\n        return this._loadGrammar(initialScopeName, 0, null, null, null);\n    }\n    async _loadGrammar(initialScopeName, initialLanguage, embeddedLanguages, tokenTypes, balancedBracketSelectors) {\n        const dependencyProcessor = new ScopeDependencyProcessor(this._syncRegistry, initialScopeName);\n        while (dependencyProcessor.Q.length > 0) {\n            await Promise.all(dependencyProcessor.Q.map((request) => this._loadSingleGrammar(request.scopeName)));\n            dependencyProcessor.processQueue();\n        }\n        return this._grammarForScopeName(initialScopeName, initialLanguage, embeddedLanguages, tokenTypes, balancedBracketSelectors);\n    }\n    async _loadSingleGrammar(scopeName) {\n        if (!this._ensureGrammarCache.has(scopeName)) {\n            this._ensureGrammarCache.set(scopeName, this._doLoadSingleGrammar(scopeName));\n        }\n        return this._ensureGrammarCache.get(scopeName);\n    }\n    async _doLoadSingleGrammar(scopeName) {\n        const grammar = await this._options.loadGrammar(scopeName);\n        if (grammar) {\n            const injections = typeof this._options.getInjections === \"function\" ? this._options.getInjections(scopeName) : undefined;\n            this._syncRegistry.addGrammar(grammar, injections);\n        }\n    }\n    /**\n     * Adds a rawGrammar.\n     */\n    async addGrammar(rawGrammar, injections = [], initialLanguage = 0, embeddedLanguages = null) {\n        this._syncRegistry.addGrammar(rawGrammar, injections);\n        return (await this._grammarForScopeName(rawGrammar.scopeName, initialLanguage, embeddedLanguages));\n    }\n    /**\n     * Get the grammar for `scopeName`. The grammar must first be created via `loadGrammar` or `addGrammar`.\n     */\n    _grammarForScopeName(scopeName, initialLanguage = 0, embeddedLanguages = null, tokenTypes = null, balancedBracketSelectors = null) {\n        return this._syncRegistry.grammarForScopeName(scopeName, initialLanguage, embeddedLanguages, tokenTypes, balancedBracketSelectors);\n    }\n};\nconst INITIAL = StateStackImpl.NULL;\n\n/**\n * Helpers to manage the \"collapsed\" metadata of an entire StackElement stack.\n * The following assumptions have been made:\n *  - languageId < 256 => needs 8 bits\n *  - unique color count < 512 => needs 9 bits\n *\n * The binary format is:\n * - -------------------------------------------\n *     3322 2222 2222 1111 1111 1100 0000 0000\n *     1098 7654 3210 9876 5432 1098 7654 3210\n * - -------------------------------------------\n *     xxxx xxxx xxxx xxxx xxxx xxxx xxxx xxxx\n *     bbbb bbbb bfff ffff ffFF FTTT LLLL LLLL\n * - -------------------------------------------\n *  - L = LanguageId (8 bits)\n *  - T = StandardTokenType (3 bits)\n *  - F = FontStyle (3 bits)\n *  - f = foreground color (9 bits)\n *  - b = background color (9 bits)\n */\nconst MetadataConsts = {\n    LANGUAGEID_MASK: 0b00000000000000000000000011111111,\n    TOKEN_TYPE_MASK: 0b00000000000000000000001100000000,\n    BALANCED_BRACKETS_MASK: 0b00000000000000000000010000000000,\n    FONT_STYLE_MASK: 0b00000000000000000011100000000000,\n    FOREGROUND_MASK: 0b00000000011111111100000000000000,\n    BACKGROUND_MASK: 0b11111111100000000000000000000000,\n    LANGUAGEID_OFFSET: 0,\n    TOKEN_TYPE_OFFSET: 8,\n    BALANCED_BRACKETS_OFFSET: 10,\n    FONT_STYLE_OFFSET: 11,\n    FOREGROUND_OFFSET: 15,\n    BACKGROUND_OFFSET: 24,\n};\nclass StackElementMetadata {\n    static toBinaryStr(metadata) {\n        let r = metadata.toString(2);\n        while (r.length < 32)\n            r = `0${r}`;\n        return r;\n    }\n    // public static printMetadata(metadata: number): void {\n    //   const languageId = StackElementMetadata.getLanguageId(metadata)\n    //   const tokenType = StackElementMetadata.getTokenType(metadata)\n    //   const fontStyle = StackElementMetadata.getFontStyle(metadata)\n    //   const foreground = StackElementMetadata.getForeground(metadata)\n    //   const background = StackElementMetadata.getBackground(metadata)\n    //   console.log({\n    //     languageId,\n    //     tokenType,\n    //     fontStyle,\n    //     foreground,\n    //     background,\n    //   })\n    // }\n    static getLanguageId(metadata) {\n        return (metadata & MetadataConsts.LANGUAGEID_MASK) >>> MetadataConsts.LANGUAGEID_OFFSET;\n    }\n    static getTokenType(metadata) {\n        return (metadata & MetadataConsts.TOKEN_TYPE_MASK) >>> MetadataConsts.TOKEN_TYPE_OFFSET;\n    }\n    static getFontStyle(metadata) {\n        return (metadata & MetadataConsts.FONT_STYLE_MASK) >>> MetadataConsts.FONT_STYLE_OFFSET;\n    }\n    static getForeground(metadata) {\n        return (metadata & MetadataConsts.FOREGROUND_MASK) >>> MetadataConsts.FOREGROUND_OFFSET;\n    }\n    static getBackground(metadata) {\n        return (metadata & MetadataConsts.BACKGROUND_MASK) >>> MetadataConsts.BACKGROUND_OFFSET;\n    }\n    static containsBalancedBrackets(metadata) {\n        return (metadata & MetadataConsts.BALANCED_BRACKETS_MASK) !== 0;\n    }\n    static set(metadata, languageId, tokenType, fontStyle, foreground, background) {\n        let _languageId = StackElementMetadata.getLanguageId(metadata);\n        let _tokenType = StackElementMetadata.getTokenType(metadata);\n        let _fontStyle = StackElementMetadata.getFontStyle(metadata);\n        let _foreground = StackElementMetadata.getForeground(metadata);\n        let _background = StackElementMetadata.getBackground(metadata);\n        const _containsBalancedBracketsBit = StackElementMetadata.containsBalancedBrackets(metadata)\n            ? 1\n            : 0;\n        if (languageId !== 0)\n            _languageId = languageId;\n        if (tokenType !== 0 /* TemporaryStandardTokenType.Other */) {\n            _tokenType\n                = tokenType === 8 /* TemporaryStandardTokenType.MetaEmbedded */ ? 0 /* StandardTokenType.Other */ : tokenType;\n        }\n        if (fontStyle !== FontStyle.NotSet)\n            _fontStyle = fontStyle;\n        if (foreground !== 0)\n            _foreground = foreground;\n        if (background !== 0)\n            _background = background;\n        return (((_languageId << MetadataConsts.LANGUAGEID_OFFSET)\n            | (_tokenType << MetadataConsts.TOKEN_TYPE_OFFSET)\n            | (_fontStyle << MetadataConsts.FONT_STYLE_OFFSET)\n            | (_containsBalancedBracketsBit << MetadataConsts.BALANCED_BRACKETS_OFFSET)\n            | (_foreground << MetadataConsts.FOREGROUND_OFFSET)\n            | (_background << MetadataConsts.BACKGROUND_OFFSET))\n            >>> 0);\n    }\n}\n\nexport { INITIAL, Registry$1 as Registry, StackElementMetadata };\n", "import { FontStyle } from './types.mjs';\nimport { StackElementMetadata, INITIAL, Registry as Registry$1 } from './textmate.mjs';\n\nfunction toArray(x) {\n    return Array.isArray(x) ? x : [x];\n}\n/**\n * Slipt a string into lines, each line preserves the line ending.\n */\nfunction splitLines(str) {\n    return Array.from(str.matchAll(/^.*$/mg)).map(x => [x[0], x.index]);\n}\n/**\n * Check if the language is plaintext that is ignored by <PERSON><PERSON><PERSON>.\n *\n * Hard-coded plain text languages: `plaintext`, `txt`, `text`, `plain`.\n */\nfunction isPlainLang(lang) {\n    return !lang || ['plaintext', 'txt', 'text', 'plain'].includes(lang);\n}\n/**\n * Check if the language is specially handled or bypassed by <PERSON><PERSON><PERSON>.\n *\n * Hard-coded languages: `ansi` and plaintexts like `plaintext`, `txt`, `text`, `plain`.\n */\nfunction isSpecialLang(lang) {\n    return lang === 'ansi' || isPlainLang(lang);\n}\n/**\n * Check if the theme is specially handled or bypassed by <PERSON><PERSON><PERSON>.\n *\n * Hard-coded themes: `none`.\n */\nfunction isNoneTheme(theme) {\n    return theme === 'none';\n}\n/**\n * Check if the theme is specially handled or bypassed by Shikiji.\n *\n * Hard-coded themes: `none`.\n */\nfunction isSpecialTheme(theme) {\n    return isNoneTheme(theme);\n}\n/**\n * Utility to append class to a hast node\n *\n * If the `property.class` is a string, it will be splitted by space and converted to an array.\n */\nfunction addClassToHast(node, className) {\n    if (!className)\n        return;\n    node.properties ||= {};\n    node.properties.class ||= [];\n    if (typeof node.properties.class === 'string')\n        node.properties.class = node.properties.class.split(/\\s+/g);\n    if (!Array.isArray(node.properties.class))\n        node.properties.class = [];\n    const targets = Array.isArray(className) ? className : className.split(/\\s+/g);\n    for (const c of targets) {\n        if (c && !node.properties.class.includes(c))\n            node.properties.class.push(c);\n    }\n}\n/**\n * Split a token into multiple tokens by given offsets.\n *\n * The offsets are relative to the token, and should be sorted.\n */\nfunction splitToken(token, offsets) {\n    let lastOffset = 0;\n    const tokens = [];\n    for (const offset of offsets) {\n        if (offset > lastOffset) {\n            tokens.push({\n                ...token,\n                content: token.content.slice(lastOffset, offset),\n                offset: token.offset + lastOffset,\n            });\n        }\n        lastOffset = offset;\n    }\n    if (lastOffset < token.content.length) {\n        tokens.push({\n            ...token,\n            content: token.content.slice(lastOffset),\n            offset: token.offset + lastOffset,\n        });\n    }\n    return tokens;\n}\nfunction applyColorReplacements(color, replacements) {\n    return replacements?.[color.toLowerCase()] || color;\n}\n/**\n * @deprecated Use `isPlainLang` instead.\n */\nconst isPlaintext = isPlainLang;\n\n// src/colors.ts\nvar namedColors = [\n  \"black\",\n  \"red\",\n  \"green\",\n  \"yellow\",\n  \"blue\",\n  \"magenta\",\n  \"cyan\",\n  \"white\",\n  \"brightBlack\",\n  \"brightRed\",\n  \"brightGreen\",\n  \"brightYellow\",\n  \"brightBlue\",\n  \"brightMagenta\",\n  \"brightCyan\",\n  \"brightWhite\"\n];\n\n// src/decorations.ts\nvar decorations = {\n  1: \"bold\",\n  2: \"dim\",\n  3: \"italic\",\n  4: \"underline\",\n  7: \"reverse\",\n  9: \"strikethrough\"\n};\n\n// src/parser.ts\nfunction findSequence(value, position) {\n  const nextEscape = value.indexOf(\"\\x1B[\", position);\n  if (nextEscape !== -1) {\n    const nextClose = value.indexOf(\"m\", nextEscape);\n    return {\n      sequence: value.substring(nextEscape + 2, nextClose).split(\";\"),\n      startPosition: nextEscape,\n      position: nextClose + 1\n    };\n  }\n  return {\n    position: value.length\n  };\n}\nfunction parseColor(sequence, index) {\n  let offset = 1;\n  const colorMode = sequence[index + offset++];\n  let color;\n  if (colorMode === \"2\") {\n    const rgb = [\n      sequence[index + offset++],\n      sequence[index + offset++],\n      sequence[index + offset]\n    ].map((x) => Number.parseInt(x));\n    if (rgb.length === 3 && !rgb.some((x) => Number.isNaN(x))) {\n      color = {\n        type: \"rgb\",\n        rgb\n      };\n    }\n  } else if (colorMode === \"5\") {\n    const colorIndex = Number.parseInt(sequence[index + offset]);\n    if (!Number.isNaN(colorIndex)) {\n      color = { type: \"table\", index: Number(colorIndex) };\n    }\n  }\n  return [offset, color];\n}\nfunction parseSequence(sequence) {\n  const commands = [];\n  for (let i = 0; i < sequence.length; i++) {\n    const code = sequence[i];\n    const codeInt = Number.parseInt(code);\n    if (Number.isNaN(codeInt))\n      continue;\n    if (codeInt === 0) {\n      commands.push({ type: \"resetAll\" });\n    } else if (codeInt <= 9) {\n      const decoration = decorations[codeInt];\n      if (decoration) {\n        commands.push({\n          type: \"setDecoration\",\n          value: decorations[codeInt]\n        });\n      }\n    } else if (codeInt <= 29) {\n      const decoration = decorations[codeInt - 20];\n      if (decoration) {\n        commands.push({\n          type: \"resetDecoration\",\n          value: decoration\n        });\n      }\n    } else if (codeInt <= 37) {\n      commands.push({\n        type: \"setForegroundColor\",\n        value: { type: \"named\", name: namedColors[codeInt - 30] }\n      });\n    } else if (codeInt === 38) {\n      const [offset, color] = parseColor(sequence, i);\n      if (color) {\n        commands.push({\n          type: \"setForegroundColor\",\n          value: color\n        });\n      }\n      i += offset;\n    } else if (codeInt === 39) {\n      commands.push({\n        type: \"resetForegroundColor\"\n      });\n    } else if (codeInt <= 47) {\n      commands.push({\n        type: \"setBackgroundColor\",\n        value: { type: \"named\", name: namedColors[codeInt - 40] }\n      });\n    } else if (codeInt === 48) {\n      const [offset, color] = parseColor(sequence, i);\n      if (color) {\n        commands.push({\n          type: \"setBackgroundColor\",\n          value: color\n        });\n      }\n      i += offset;\n    } else if (codeInt === 49) {\n      commands.push({\n        type: \"resetBackgroundColor\"\n      });\n    } else if (codeInt >= 90 && codeInt <= 97) {\n      commands.push({\n        type: \"setForegroundColor\",\n        value: { type: \"named\", name: namedColors[codeInt - 90 + 8] }\n      });\n    } else if (codeInt >= 100 && codeInt <= 107) {\n      commands.push({\n        type: \"setBackgroundColor\",\n        value: { type: \"named\", name: namedColors[codeInt - 100 + 8] }\n      });\n    }\n  }\n  return commands;\n}\nfunction createAnsiSequenceParser() {\n  let foreground = null;\n  let background = null;\n  let decorations2 = /* @__PURE__ */ new Set();\n  return {\n    parse(value) {\n      const tokens = [];\n      let position = 0;\n      do {\n        const findResult = findSequence(value, position);\n        const text = findResult.sequence ? value.substring(position, findResult.startPosition) : value.substring(position);\n        if (text.length > 0) {\n          tokens.push({\n            value: text,\n            foreground,\n            background,\n            decorations: new Set(decorations2)\n          });\n        }\n        if (findResult.sequence) {\n          const commands = parseSequence(findResult.sequence);\n          for (const styleToken of commands) {\n            if (styleToken.type === \"resetAll\") {\n              foreground = null;\n              background = null;\n              decorations2.clear();\n            } else if (styleToken.type === \"resetForegroundColor\") {\n              foreground = null;\n            } else if (styleToken.type === \"resetBackgroundColor\") {\n              background = null;\n            } else if (styleToken.type === \"resetDecoration\") {\n              decorations2.delete(styleToken.value);\n            }\n          }\n          for (const styleToken of commands) {\n            if (styleToken.type === \"setForegroundColor\") {\n              foreground = styleToken.value;\n            } else if (styleToken.type === \"setBackgroundColor\") {\n              background = styleToken.value;\n            } else if (styleToken.type === \"setDecoration\") {\n              decorations2.add(styleToken.value);\n            }\n          }\n        }\n        position = findResult.position;\n      } while (position < value.length);\n      return tokens;\n    }\n  };\n}\n\n// src/palette.ts\nvar defaultNamedColorsMap = {\n  black: \"#000000\",\n  red: \"#bb0000\",\n  green: \"#00bb00\",\n  yellow: \"#bbbb00\",\n  blue: \"#0000bb\",\n  magenta: \"#ff00ff\",\n  cyan: \"#00bbbb\",\n  white: \"#eeeeee\",\n  brightBlack: \"#555555\",\n  brightRed: \"#ff5555\",\n  brightGreen: \"#00ff00\",\n  brightYellow: \"#ffff55\",\n  brightBlue: \"#5555ff\",\n  brightMagenta: \"#ff55ff\",\n  brightCyan: \"#55ffff\",\n  brightWhite: \"#ffffff\"\n};\nfunction createColorPalette(namedColorsMap = defaultNamedColorsMap) {\n  function namedColor(name) {\n    return namedColorsMap[name];\n  }\n  function rgbColor(rgb) {\n    return `#${rgb.map((x) => Math.max(0, Math.min(x, 255)).toString(16).padStart(2, \"0\")).join(\"\")}`;\n  }\n  let colorTable;\n  function getColorTable() {\n    if (colorTable) {\n      return colorTable;\n    }\n    colorTable = [];\n    for (let i = 0; i < namedColors.length; i++) {\n      colorTable.push(namedColor(namedColors[i]));\n    }\n    let levels = [0, 95, 135, 175, 215, 255];\n    for (let r = 0; r < 6; r++) {\n      for (let g = 0; g < 6; g++) {\n        for (let b = 0; b < 6; b++) {\n          colorTable.push(rgbColor([levels[r], levels[g], levels[b]]));\n        }\n      }\n    }\n    let level = 8;\n    for (let i = 0; i < 24; i++, level += 10) {\n      colorTable.push(rgbColor([level, level, level]));\n    }\n    return colorTable;\n  }\n  function tableColor(index) {\n    return getColorTable()[index];\n  }\n  function value(color) {\n    switch (color.type) {\n      case \"named\":\n        return namedColor(color.name);\n      case \"rgb\":\n        return rgbColor(color.rgb);\n      case \"table\":\n        return tableColor(color.index);\n    }\n  }\n  return {\n    value\n  };\n}\n\nfunction tokenizeAnsiWithTheme(theme, fileContents, options) {\n    const colorReplacements = {\n        ...theme.colorReplacements,\n        ...options?.colorReplacements,\n    };\n    const lines = splitLines(fileContents);\n    const colorPalette = createColorPalette(Object.fromEntries(namedColors.map(name => [\n        name,\n        theme.colors?.[`terminal.ansi${name[0].toUpperCase()}${name.substring(1)}`],\n    ])));\n    const parser = createAnsiSequenceParser();\n    return lines.map(line => parser.parse(line[0]).map((token) => {\n        let color;\n        if (token.decorations.has('reverse'))\n            color = token.background ? colorPalette.value(token.background) : theme.bg;\n        else\n            color = token.foreground ? colorPalette.value(token.foreground) : theme.fg;\n        color = applyColorReplacements(color, colorReplacements);\n        if (token.decorations.has('dim'))\n            color = dimColor(color);\n        let fontStyle = FontStyle.None;\n        if (token.decorations.has('bold'))\n            fontStyle |= FontStyle.Bold;\n        if (token.decorations.has('italic'))\n            fontStyle |= FontStyle.Italic;\n        if (token.decorations.has('underline'))\n            fontStyle |= FontStyle.Underline;\n        return {\n            content: token.value,\n            offset: line[1], // TODO: more accurate offset? might need to fork ansi-sequence-parser\n            color,\n            fontStyle,\n        };\n    }));\n}\n/**\n * Adds 50% alpha to a hex color string or the \"-dim\" postfix to a CSS variable\n */\nfunction dimColor(color) {\n    const hexMatch = color.match(/#([0-9a-f]{3})([0-9a-f]{3})?([0-9a-f]{2})?/);\n    if (hexMatch) {\n        if (hexMatch[3]) {\n            // convert from #rrggbbaa to #rrggbb(aa/2)\n            const alpha = Math.round(Number.parseInt(hexMatch[3], 16) / 2)\n                .toString(16)\n                .padStart(2, '0');\n            return `#${hexMatch[1]}${hexMatch[2]}${alpha}`;\n        }\n        else if (hexMatch[2]) {\n            // convert from #rrggbb to #rrggbb80\n            return `#${hexMatch[1]}${hexMatch[2]}80`;\n        }\n        else {\n            // convert from #rgb to #rrggbb80\n            return `#${Array.from(hexMatch[1])\n                .map(x => `${x}${x}`)\n                .join('')}80`;\n        }\n    }\n    const cssVarMatch = color.match(/var\\((--[\\w-]+-ansi-[\\w-]+)\\)/);\n    if (cssVarMatch)\n        return `var(${cssVarMatch[1]}-dim)`;\n    return color;\n}\n\nfunction codeToThemedTokens(internal, code, options = {}) {\n    const { lang = 'text', theme: themeName = internal.getLoadedThemes()[0], } = options;\n    if (isPlainLang(lang) || isNoneTheme(themeName))\n        return splitLines(code).map(line => [{ content: line[0], offset: line[1] }]);\n    const { theme, colorMap } = internal.setTheme(themeName);\n    if (lang === 'ansi')\n        return tokenizeAnsiWithTheme(theme, code, options);\n    const _grammar = internal.getLangGrammar(lang);\n    return tokenizeWithTheme(code, _grammar, theme, colorMap, options);\n}\nfunction tokenizeWithTheme(code, grammar, theme, colorMap, options) {\n    const colorReplacements = {\n        ...theme.colorReplacements,\n        ...options?.colorReplacements,\n    };\n    const lines = splitLines(code);\n    let ruleStack = INITIAL;\n    let actual = [];\n    const final = [];\n    for (let i = 0, len = lines.length; i < len; i++) {\n        const [line, lineOffset] = lines[i];\n        if (line === '') {\n            actual = [];\n            final.push([]);\n            continue;\n        }\n        let resultWithScopes;\n        let tokensWithScopes;\n        let tokensWithScopesIndex;\n        if (options.includeExplanation) {\n            resultWithScopes = grammar.tokenizeLine(line, ruleStack);\n            tokensWithScopes = resultWithScopes.tokens;\n            tokensWithScopesIndex = 0;\n        }\n        const result = grammar.tokenizeLine2(line, ruleStack);\n        const tokensLength = result.tokens.length / 2;\n        for (let j = 0; j < tokensLength; j++) {\n            const startIndex = result.tokens[2 * j];\n            const nextStartIndex = j + 1 < tokensLength ? result.tokens[2 * j + 2] : line.length;\n            if (startIndex === nextStartIndex)\n                continue;\n            const metadata = result.tokens[2 * j + 1];\n            const foreground = StackElementMetadata.getForeground(metadata);\n            const foregroundColor = applyColorReplacements(colorMap[foreground], colorReplacements);\n            const fontStyle = StackElementMetadata.getFontStyle(metadata);\n            const token = {\n                content: line.substring(startIndex, nextStartIndex),\n                offset: lineOffset + startIndex,\n                color: foregroundColor,\n                fontStyle,\n            };\n            if (options.includeExplanation) {\n                token.explanation = [];\n                let offset = 0;\n                while (startIndex + offset < nextStartIndex) {\n                    const tokenWithScopes = tokensWithScopes[tokensWithScopesIndex];\n                    const tokenWithScopesText = line.substring(tokenWithScopes.startIndex, tokenWithScopes.endIndex);\n                    offset += tokenWithScopesText.length;\n                    token.explanation.push({\n                        content: tokenWithScopesText,\n                        scopes: explainThemeScopes(theme, tokenWithScopes.scopes),\n                    });\n                    tokensWithScopesIndex += 1;\n                }\n            }\n            actual.push(token);\n        }\n        final.push(actual);\n        actual = [];\n        ruleStack = result.ruleStack;\n    }\n    return final;\n}\nfunction explainThemeScopes(theme, scopes) {\n    const result = [];\n    for (let i = 0, len = scopes.length; i < len; i++) {\n        const parentScopes = scopes.slice(0, i);\n        const scope = scopes[i];\n        result[i] = {\n            scopeName: scope,\n            themeMatches: explainThemeScope(theme, scope, parentScopes),\n        };\n    }\n    return result;\n}\nfunction matchesOne(selector, scope) {\n    const selectorPrefix = `${selector}.`;\n    if (selector === scope || scope.substring(0, selectorPrefix.length) === selectorPrefix)\n        return true;\n    return false;\n}\nfunction matches(selector, selectorParentScopes, scope, parentScopes) {\n    if (!matchesOne(selector, scope))\n        return false;\n    let selectorParentIndex = selectorParentScopes.length - 1;\n    let parentIndex = parentScopes.length - 1;\n    while (selectorParentIndex >= 0 && parentIndex >= 0) {\n        if (matchesOne(selectorParentScopes[selectorParentIndex], parentScopes[parentIndex]))\n            selectorParentIndex -= 1;\n        parentIndex -= 1;\n    }\n    if (selectorParentIndex === -1)\n        return true;\n    return false;\n}\nfunction explainThemeScope(theme, scope, parentScopes) {\n    const result = [];\n    let resultLen = 0;\n    for (let i = 0, len = theme.settings.length; i < len; i++) {\n        const setting = theme.settings[i];\n        let selectors;\n        if (typeof setting.scope === 'string')\n            selectors = setting.scope.split(/,/).map(scope => scope.trim());\n        else if (Array.isArray(setting.scope))\n            selectors = setting.scope;\n        else\n            continue;\n        for (let j = 0, lenJ = selectors.length; j < lenJ; j++) {\n            const rawSelector = selectors[j];\n            const rawSelectorPieces = rawSelector.split(/ /);\n            const selector = rawSelectorPieces[rawSelectorPieces.length - 1];\n            const selectorParentScopes = rawSelectorPieces.slice(0, rawSelectorPieces.length - 1);\n            if (matches(selector, selectorParentScopes, scope, parentScopes)) {\n                // match!\n                result[resultLen++] = setting;\n                // break the loop\n                j = lenJ;\n            }\n        }\n    }\n    return result;\n}\n\n/**\n * Get tokens with multiple themes\n */\nfunction codeToTokensWithThemes(internal, code, options) {\n    const themes = Object.entries(options.themes)\n        .filter(i => i[1])\n        .map(i => ({ color: i[0], theme: i[1] }));\n    const tokens = syncThemesTokenization(...themes.map(t => codeToThemedTokens(internal, code, {\n        ...options,\n        theme: t.theme,\n        includeExplanation: false,\n    })));\n    const mergedTokens = tokens[0]\n        .map((line, lineIdx) => line\n        .map((_token, tokenIdx) => {\n        const mergedToken = {\n            content: _token.content,\n            variants: {},\n            offset: _token.offset,\n        };\n        tokens.forEach((t, themeIdx) => {\n            const { content: _, explanation: __, offset: ___, ...styles } = t[lineIdx][tokenIdx];\n            mergedToken.variants[themes[themeIdx].color] = styles;\n        });\n        return mergedToken;\n    }));\n    return mergedTokens;\n}\n/**\n * Break tokens from multiple themes into same tokenization.\n *\n * For example, given two themes that tokenize `console.log(\"hello\")` as:\n *\n * - `console . log (\" hello \")` (6 tokens)\n * - `console .log ( \"hello\" )` (5 tokens)\n *\n * This function will return:\n *\n * - `console . log ( \" hello \" )` (8 tokens)\n * - `console . log ( \" hello \" )` (8 tokens)\n */\nfunction syncThemesTokenization(...themes) {\n    const outThemes = themes.map(() => []);\n    const count = themes.length;\n    for (let i = 0; i < themes[0].length; i++) {\n        const lines = themes.map(t => t[i]);\n        const outLines = outThemes.map(() => []);\n        outThemes.forEach((t, i) => t.push(outLines[i]));\n        const indexes = lines.map(() => 0);\n        const current = lines.map(l => l[0]);\n        while (current.every(t => t)) {\n            const minLength = Math.min(...current.map(t => t.content.length));\n            for (let n = 0; n < count; n++) {\n                const token = current[n];\n                if (token.content.length === minLength) {\n                    outLines[n].push(token);\n                    indexes[n] += 1;\n                    current[n] = lines[n][indexes[n]];\n                }\n                else {\n                    outLines[n].push({\n                        ...token,\n                        content: token.content.slice(0, minLength),\n                    });\n                    current[n] = {\n                        ...token,\n                        content: token.content.slice(minLength),\n                        offset: token.offset + minLength,\n                    };\n                }\n            }\n        }\n    }\n    return outThemes;\n}\n\nfunction codeToHast(internal, code, options, transformerContext = {\n    meta: {},\n    options,\n    codeToHast: (_code, _options) => codeToHast(internal, _code, _options),\n}) {\n    let input = code;\n    for (const transformer of options.transformers || [])\n        input = transformer.preprocess?.call(transformerContext, input, options) || input;\n    let bg;\n    let fg;\n    let tokens;\n    let themeName;\n    let rootStyle;\n    if ('themes' in options) {\n        const { defaultColor = 'light', cssVariablePrefix = '--shiki-', } = options;\n        const themes = Object.entries(options.themes)\n            .filter(i => i[1])\n            .map(i => ({ color: i[0], theme: i[1] }))\n            .sort((a, b) => a.color === defaultColor ? -1 : b.color === defaultColor ? 1 : 0);\n        if (themes.length === 0)\n            throw new Error('[shikiji] `themes` option must not be empty');\n        const themeTokens = codeToTokensWithThemes(internal, input, options);\n        if (defaultColor && !themes.find(t => t.color === defaultColor))\n            throw new Error(`[shikiji] \\`themes\\` option must contain the defaultColor key \\`${defaultColor}\\``);\n        const themeRegs = themes.map(t => internal.getTheme(t.theme));\n        const themesOrder = themes.map(t => t.color);\n        tokens = themeTokens\n            .map(line => line.map(token => mergeToken(token, themesOrder, cssVariablePrefix, defaultColor)));\n        fg = themes.map((t, idx) => (idx === 0 && defaultColor ? '' : `${cssVariablePrefix + t.color}:`) + (themeRegs[idx].fg || 'inherit')).join(';');\n        bg = themes.map((t, idx) => (idx === 0 && defaultColor ? '' : `${cssVariablePrefix + t.color}-bg:`) + (themeRegs[idx].bg || 'inherit')).join(';');\n        themeName = `shiki-themes ${themeRegs.map(t => t.name).join(' ')}`;\n        rootStyle = defaultColor ? undefined : [fg, bg].join(';');\n    }\n    else if ('theme' in options) {\n        tokens = codeToThemedTokens(internal, input, {\n            ...options,\n            includeExplanation: false,\n        });\n        const _theme = internal.getTheme(options.theme);\n        bg = _theme.bg;\n        fg = _theme.fg;\n        themeName = _theme.name;\n    }\n    else {\n        throw new Error('[shikiji] Invalid options, either `theme` or `themes` must be provided');\n    }\n    const { mergeWhitespaces = true, } = options;\n    if (mergeWhitespaces === true)\n        tokens = mergeWhitespaceTokens(tokens);\n    else if (mergeWhitespaces === 'never')\n        tokens = splitWhitespaceTokens(tokens);\n    for (const transformer of options.transformers || [])\n        tokens = transformer.tokens?.call(transformerContext, tokens) || tokens;\n    return tokensToHast(tokens, {\n        ...options,\n        fg,\n        bg,\n        themeName,\n        rootStyle,\n    }, transformerContext);\n}\nfunction mergeToken(merged, variantsOrder, cssVariablePrefix, defaultColor) {\n    const token = {\n        content: merged.content,\n        explanation: merged.explanation,\n        offset: merged.offset,\n    };\n    const styles = variantsOrder.map(t => getTokenStyleObject(merged.variants[t]));\n    // Get all style keys, for themes that missing some style, we put `inherit` to override as needed\n    const styleKeys = new Set(styles.flatMap(t => Object.keys(t)));\n    const mergedStyles = styles.reduce((acc, cur, idx) => {\n        for (const key of styleKeys) {\n            const value = cur[key] || 'inherit';\n            if (idx === 0 && defaultColor) {\n                acc[key] = value;\n            }\n            else {\n                const varKey = cssVariablePrefix + variantsOrder[idx] + (key === 'color' ? '' : `-${key}`);\n                if (acc[key])\n                    acc[key] += `;${varKey}:${value}`;\n                else\n                    acc[key] = `${varKey}:${value}`;\n            }\n        }\n        return acc;\n    }, {});\n    token.htmlStyle = defaultColor\n        ? stringifyTokenStyle(mergedStyles)\n        : Object.values(mergedStyles).join(';');\n    return token;\n}\nfunction tokensToHast(tokens, options, transformerContext) {\n    const { transformers = [], } = options;\n    const lines = [];\n    const tree = {\n        type: 'root',\n        children: [],\n    };\n    let preNode = {\n        type: 'element',\n        tagName: 'pre',\n        properties: {\n            class: `shiki ${options.themeName || ''}`,\n            style: options.rootStyle || `background-color:${options.bg};color:${options.fg}`,\n            tabindex: '0',\n            ...Object.fromEntries(Array.from(Object.entries(options.meta || {}))\n                .filter(([key]) => !key.startsWith('_'))),\n        },\n        children: [],\n    };\n    let codeNode = {\n        type: 'element',\n        tagName: 'code',\n        properties: {},\n        children: lines,\n    };\n    const lineNodes = [];\n    const context = {\n        ...transformerContext,\n        get tokens() {\n            return tokens;\n        },\n        get options() {\n            return options;\n        },\n        get root() {\n            return tree;\n        },\n        get pre() {\n            return preNode;\n        },\n        get code() {\n            return codeNode;\n        },\n        get lines() {\n            return lineNodes;\n        },\n    };\n    tokens.forEach((line, idx) => {\n        if (idx)\n            lines.push({ type: 'text', value: '\\n' });\n        let lineNode = {\n            type: 'element',\n            tagName: 'span',\n            properties: { class: 'line' },\n            children: [],\n        };\n        let col = 0;\n        for (const token of line) {\n            let tokenNode = {\n                type: 'element',\n                tagName: 'span',\n                properties: {},\n                children: [{ type: 'text', value: token.content }],\n            };\n            const style = token.htmlStyle || stringifyTokenStyle(getTokenStyleObject(token));\n            if (style)\n                tokenNode.properties.style = style;\n            for (const transformer of transformers)\n                tokenNode = (transformer?.span || transformer?.token)?.call(context, tokenNode, idx + 1, col, lineNode) || tokenNode;\n            lineNode.children.push(tokenNode);\n            col += token.content.length;\n        }\n        for (const transformer of transformers)\n            lineNode = transformer?.line?.call(context, lineNode, idx + 1) || lineNode;\n        lineNodes.push(lineNode);\n        lines.push(lineNode);\n    });\n    for (const transformer of transformers)\n        codeNode = transformer?.code?.call(context, codeNode) || codeNode;\n    preNode.children.push(codeNode);\n    for (const transformer of transformers)\n        preNode = transformer?.pre?.call(context, preNode) || preNode;\n    tree.children.push(preNode);\n    let result = tree;\n    for (const transformer of transformers)\n        result = transformer?.root?.call(context, result) || result;\n    return result;\n}\nfunction getTokenStyleObject(token) {\n    const styles = {};\n    if (token.color)\n        styles.color = token.color;\n    if (token.fontStyle) {\n        if (token.fontStyle & FontStyle.Italic)\n            styles['font-style'] = 'italic';\n        if (token.fontStyle & FontStyle.Bold)\n            styles['font-weight'] = 'bold';\n        if (token.fontStyle & FontStyle.Underline)\n            styles['text-decoration'] = 'underline';\n    }\n    return styles;\n}\nfunction stringifyTokenStyle(token) {\n    return Object.entries(token).map(([key, value]) => `${key}:${value}`).join(';');\n}\nfunction mergeWhitespaceTokens(tokens) {\n    return tokens.map((line) => {\n        const newLine = [];\n        let carryOnContent = '';\n        let firstOffset = 0;\n        line.forEach((token, idx) => {\n            const isUnderline = token.fontStyle && token.fontStyle & FontStyle.Underline;\n            const couldMerge = !isUnderline;\n            if (couldMerge && token.content.match(/^\\s+$/) && line[idx + 1]) {\n                if (!firstOffset)\n                    firstOffset = token.offset;\n                carryOnContent += token.content;\n            }\n            else {\n                if (carryOnContent) {\n                    if (couldMerge) {\n                        newLine.push({\n                            ...token,\n                            content: carryOnContent + token.content,\n                        });\n                    }\n                    else {\n                        newLine.push({\n                            content: carryOnContent,\n                            offset: firstOffset,\n                        }, token);\n                    }\n                    carryOnContent = '';\n                }\n                else {\n                    newLine.push(token);\n                }\n            }\n        });\n        return newLine;\n    });\n}\nfunction splitWhitespaceTokens(tokens) {\n    return tokens.map((line) => {\n        return line.flatMap((token) => {\n            if (token.content.match(/^\\s+$/))\n                return token;\n            const match = token.content.match(/^(\\s*)(.*?)(\\s*)$/);\n            if (!match)\n                return token;\n            const [, leading, content, trailing] = match;\n            if (!leading && !trailing)\n                return token;\n            const expanded = [{\n                    ...token,\n                    offset: token.offset + leading.length,\n                    content,\n                }];\n            if (leading) {\n                expanded.unshift({\n                    content: leading,\n                    offset: token.offset,\n                });\n            }\n            if (trailing) {\n                expanded.push({\n                    content: trailing,\n                    offset: token.offset + leading.length + content.length,\n                });\n            }\n            return expanded;\n        });\n    });\n}\n\n/**\n * List of HTML void tag names.\n *\n * @type {Array<string>}\n */\nconst htmlVoidElements = [\n  'area',\n  'base',\n  'basefont',\n  'bgsound',\n  'br',\n  'col',\n  'command',\n  'embed',\n  'frame',\n  'hr',\n  'image',\n  'img',\n  'input',\n  'keygen',\n  'link',\n  'meta',\n  'param',\n  'source',\n  'track',\n  'wbr'\n];\n\n/**\n * @typedef {import('./info.js').Info} Info\n * @typedef {Record<string, Info>} Properties\n * @typedef {Record<string, string>} Normal\n */\n\nclass Schema {\n  /**\n   * @constructor\n   * @param {Properties} property\n   * @param {Normal} normal\n   * @param {string} [space]\n   */\n  constructor(property, normal, space) {\n    this.property = property;\n    this.normal = normal;\n    if (space) {\n      this.space = space;\n    }\n  }\n}\n\n/** @type {Properties} */\nSchema.prototype.property = {};\n/** @type {Normal} */\nSchema.prototype.normal = {};\n/** @type {string|null} */\nSchema.prototype.space = null;\n\n/**\n * @typedef {import('./schema.js').Properties} Properties\n * @typedef {import('./schema.js').Normal} Normal\n */\n\n\n/**\n * @param {Schema[]} definitions\n * @param {string} [space]\n * @returns {Schema}\n */\nfunction merge(definitions, space) {\n  /** @type {Properties} */\n  const property = {};\n  /** @type {Normal} */\n  const normal = {};\n  let index = -1;\n\n  while (++index < definitions.length) {\n    Object.assign(property, definitions[index].property);\n    Object.assign(normal, definitions[index].normal);\n  }\n\n  return new Schema(property, normal, space)\n}\n\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction normalize(value) {\n  return value.toLowerCase()\n}\n\nclass Info {\n  /**\n   * @constructor\n   * @param {string} property\n   * @param {string} attribute\n   */\n  constructor(property, attribute) {\n    /** @type {string} */\n    this.property = property;\n    /** @type {string} */\n    this.attribute = attribute;\n  }\n}\n\n/** @type {string|null} */\nInfo.prototype.space = null;\nInfo.prototype.boolean = false;\nInfo.prototype.booleanish = false;\nInfo.prototype.overloadedBoolean = false;\nInfo.prototype.number = false;\nInfo.prototype.commaSeparated = false;\nInfo.prototype.spaceSeparated = false;\nInfo.prototype.commaOrSpaceSeparated = false;\nInfo.prototype.mustUseProperty = false;\nInfo.prototype.defined = false;\n\nlet powers = 0;\n\nconst boolean = increment();\nconst booleanish = increment();\nconst overloadedBoolean = increment();\nconst number = increment();\nconst spaceSeparated = increment();\nconst commaSeparated = increment();\nconst commaOrSpaceSeparated = increment();\n\nfunction increment() {\n  return 2 ** ++powers\n}\n\nvar types = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  boolean: boolean,\n  booleanish: booleanish,\n  commaOrSpaceSeparated: commaOrSpaceSeparated,\n  commaSeparated: commaSeparated,\n  number: number,\n  overloadedBoolean: overloadedBoolean,\n  spaceSeparated: spaceSeparated\n});\n\n/** @type {Array<keyof types>} */\n// @ts-expect-error: hush.\nconst checks = Object.keys(types);\n\nclass DefinedInfo extends Info {\n  /**\n   * @constructor\n   * @param {string} property\n   * @param {string} attribute\n   * @param {number|null} [mask]\n   * @param {string} [space]\n   */\n  constructor(property, attribute, mask, space) {\n    let index = -1;\n\n    super(property, attribute);\n\n    mark(this, 'space', space);\n\n    if (typeof mask === 'number') {\n      while (++index < checks.length) {\n        const check = checks[index];\n        mark(this, checks[index], (mask & types[check]) === types[check]);\n      }\n    }\n  }\n}\n\nDefinedInfo.prototype.defined = true;\n\n/**\n * @param {DefinedInfo} values\n * @param {string} key\n * @param {unknown} value\n */\nfunction mark(values, key, value) {\n  if (value) {\n    // @ts-expect-error: assume `value` matches the expected value of `key`.\n    values[key] = value;\n  }\n}\n\n/**\n * @typedef {import('./schema.js').Properties} Properties\n * @typedef {import('./schema.js').Normal} Normal\n *\n * @typedef {Record<string, string>} Attributes\n *\n * @typedef {Object} Definition\n * @property {Record<string, number|null>} properties\n * @property {(attributes: Attributes, property: string) => string} transform\n * @property {string} [space]\n * @property {Attributes} [attributes]\n * @property {Array<string>} [mustUseProperty]\n */\n\n\nconst own$3 = {}.hasOwnProperty;\n\n/**\n * @param {Definition} definition\n * @returns {Schema}\n */\nfunction create(definition) {\n  /** @type {Properties} */\n  const property = {};\n  /** @type {Normal} */\n  const normal = {};\n  /** @type {string} */\n  let prop;\n\n  for (prop in definition.properties) {\n    if (own$3.call(definition.properties, prop)) {\n      const value = definition.properties[prop];\n      const info = new DefinedInfo(\n        prop,\n        definition.transform(definition.attributes || {}, prop),\n        value,\n        definition.space\n      );\n\n      if (\n        definition.mustUseProperty &&\n        definition.mustUseProperty.includes(prop)\n      ) {\n        info.mustUseProperty = true;\n      }\n\n      property[prop] = info;\n\n      normal[normalize(prop)] = prop;\n      normal[normalize(info.attribute)] = prop;\n    }\n  }\n\n  return new Schema(property, normal, definition.space)\n}\n\nconst xlink = create({\n  space: 'xlink',\n  transform(_, prop) {\n    return 'xlink:' + prop.slice(5).toLowerCase()\n  },\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  }\n});\n\nconst xml = create({\n  space: 'xml',\n  transform(_, prop) {\n    return 'xml:' + prop.slice(3).toLowerCase()\n  },\n  properties: {xmlLang: null, xmlBase: null, xmlSpace: null}\n});\n\n/**\n * @param {Record<string, string>} attributes\n * @param {string} attribute\n * @returns {string}\n */\nfunction caseSensitiveTransform(attributes, attribute) {\n  return attribute in attributes ? attributes[attribute] : attribute\n}\n\n/**\n * @param {Record<string, string>} attributes\n * @param {string} property\n * @returns {string}\n */\nfunction caseInsensitiveTransform(attributes, property) {\n  return caseSensitiveTransform(attributes, property.toLowerCase())\n}\n\nconst xmlns = create({\n  space: 'xmlns',\n  attributes: {xmlnsxlink: 'xmlns:xlink'},\n  transform: caseInsensitiveTransform,\n  properties: {xmlns: null, xmlnsXLink: null}\n});\n\nconst aria = create({\n  transform(_, prop) {\n    return prop === 'role' ? prop : 'aria-' + prop.slice(4).toLowerCase()\n  },\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: booleanish,\n    ariaChecked: booleanish,\n    ariaColCount: number,\n    ariaColIndex: number,\n    ariaColSpan: number,\n    ariaControls: spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: booleanish,\n    ariaDropEffect: spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: booleanish,\n    ariaFlowTo: spaceSeparated,\n    ariaGrabbed: booleanish,\n    ariaHasPopup: null,\n    ariaHidden: booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: spaceSeparated,\n    ariaLevel: number,\n    ariaLive: null,\n    ariaModal: booleanish,\n    ariaMultiLine: booleanish,\n    ariaMultiSelectable: booleanish,\n    ariaOrientation: null,\n    ariaOwns: spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: number,\n    ariaPressed: booleanish,\n    ariaReadOnly: booleanish,\n    ariaRelevant: null,\n    ariaRequired: booleanish,\n    ariaRoleDescription: spaceSeparated,\n    ariaRowCount: number,\n    ariaRowIndex: number,\n    ariaRowSpan: number,\n    ariaSelected: booleanish,\n    ariaSetSize: number,\n    ariaSort: null,\n    ariaValueMax: number,\n    ariaValueMin: number,\n    ariaValueNow: number,\n    ariaValueText: null,\n    role: null\n  }\n});\n\nconst html$3 = create({\n  space: 'html',\n  attributes: {\n    acceptcharset: 'accept-charset',\n    classname: 'class',\n    htmlfor: 'for',\n    httpequiv: 'http-equiv'\n  },\n  transform: caseInsensitiveTransform,\n  mustUseProperty: ['checked', 'multiple', 'muted', 'selected'],\n  properties: {\n    // Standard Properties.\n    abbr: null,\n    accept: commaSeparated,\n    acceptCharset: spaceSeparated,\n    accessKey: spaceSeparated,\n    action: null,\n    allow: null,\n    allowFullScreen: boolean,\n    allowPaymentRequest: boolean,\n    allowUserMedia: boolean,\n    alt: null,\n    as: null,\n    async: boolean,\n    autoCapitalize: null,\n    autoComplete: spaceSeparated,\n    autoFocus: boolean,\n    autoPlay: boolean,\n    capture: boolean,\n    charSet: null,\n    checked: boolean,\n    cite: null,\n    className: spaceSeparated,\n    cols: number,\n    colSpan: null,\n    content: null,\n    contentEditable: booleanish,\n    controls: boolean,\n    controlsList: spaceSeparated,\n    coords: number | commaSeparated,\n    crossOrigin: null,\n    data: null,\n    dateTime: null,\n    decoding: null,\n    default: boolean,\n    defer: boolean,\n    dir: null,\n    dirName: null,\n    disabled: boolean,\n    download: overloadedBoolean,\n    draggable: booleanish,\n    encType: null,\n    enterKeyHint: null,\n    form: null,\n    formAction: null,\n    formEncType: null,\n    formMethod: null,\n    formNoValidate: boolean,\n    formTarget: null,\n    headers: spaceSeparated,\n    height: number,\n    hidden: boolean,\n    high: number,\n    href: null,\n    hrefLang: null,\n    htmlFor: spaceSeparated,\n    httpEquiv: spaceSeparated,\n    id: null,\n    imageSizes: null,\n    imageSrcSet: null,\n    inputMode: null,\n    integrity: null,\n    is: null,\n    isMap: boolean,\n    itemId: null,\n    itemProp: spaceSeparated,\n    itemRef: spaceSeparated,\n    itemScope: boolean,\n    itemType: spaceSeparated,\n    kind: null,\n    label: null,\n    lang: null,\n    language: null,\n    list: null,\n    loading: null,\n    loop: boolean,\n    low: number,\n    manifest: null,\n    max: null,\n    maxLength: number,\n    media: null,\n    method: null,\n    min: null,\n    minLength: number,\n    multiple: boolean,\n    muted: boolean,\n    name: null,\n    nonce: null,\n    noModule: boolean,\n    noValidate: boolean,\n    onAbort: null,\n    onAfterPrint: null,\n    onAuxClick: null,\n    onBeforeMatch: null,\n    onBeforePrint: null,\n    onBeforeUnload: null,\n    onBlur: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onContextLost: null,\n    onContextMenu: null,\n    onContextRestored: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFormData: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLanguageChange: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadEnd: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMessageError: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRejectionHandled: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onScrollEnd: null,\n    onSecurityPolicyViolation: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onSlotChange: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnhandledRejection: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onWheel: null,\n    open: boolean,\n    optimum: number,\n    pattern: null,\n    ping: spaceSeparated,\n    placeholder: null,\n    playsInline: boolean,\n    poster: null,\n    preload: null,\n    readOnly: boolean,\n    referrerPolicy: null,\n    rel: spaceSeparated,\n    required: boolean,\n    reversed: boolean,\n    rows: number,\n    rowSpan: number,\n    sandbox: spaceSeparated,\n    scope: null,\n    scoped: boolean,\n    seamless: boolean,\n    selected: boolean,\n    shape: null,\n    size: number,\n    sizes: null,\n    slot: null,\n    span: number,\n    spellCheck: booleanish,\n    src: null,\n    srcDoc: null,\n    srcLang: null,\n    srcSet: null,\n    start: number,\n    step: null,\n    style: null,\n    tabIndex: number,\n    target: null,\n    title: null,\n    translate: null,\n    type: null,\n    typeMustMatch: boolean,\n    useMap: null,\n    value: booleanish,\n    width: number,\n    wrap: null,\n\n    // Legacy.\n    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis\n    align: null, // Several. Use CSS `text-align` instead,\n    aLink: null, // `<body>`. Use CSS `a:active {color}` instead\n    archive: spaceSeparated, // `<object>`. List of URIs to archives\n    axis: null, // `<td>` and `<th>`. Use `scope` on `<th>`\n    background: null, // `<body>`. Use CSS `background-image` instead\n    bgColor: null, // `<body>` and table elements. Use CSS `background-color` instead\n    border: number, // `<table>`. Use CSS `border-width` instead,\n    borderColor: null, // `<table>`. Use CSS `border-color` instead,\n    bottomMargin: number, // `<body>`\n    cellPadding: null, // `<table>`\n    cellSpacing: null, // `<table>`\n    char: null, // Several table elements. When `align=char`, sets the character to align on\n    charOff: null, // Several table elements. When `char`, offsets the alignment\n    classId: null, // `<object>`\n    clear: null, // `<br>`. Use CSS `clear` instead\n    code: null, // `<object>`\n    codeBase: null, // `<object>`\n    codeType: null, // `<object>`\n    color: null, // `<font>` and `<hr>`. Use CSS instead\n    compact: boolean, // Lists. Use CSS to reduce space between items instead\n    declare: boolean, // `<object>`\n    event: null, // `<script>`\n    face: null, // `<font>`. Use CSS instead\n    frame: null, // `<table>`\n    frameBorder: null, // `<iframe>`. Use CSS `border` instead\n    hSpace: number, // `<img>` and `<object>`\n    leftMargin: number, // `<body>`\n    link: null, // `<body>`. Use CSS `a:link {color: *}` instead\n    longDesc: null, // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`\n    lowSrc: null, // `<img>`. Use a `<picture>`\n    marginHeight: number, // `<body>`\n    marginWidth: number, // `<body>`\n    noResize: boolean, // `<frame>`\n    noHref: boolean, // `<area>`. Use no href instead of an explicit `nohref`\n    noShade: boolean, // `<hr>`. Use background-color and height instead of borders\n    noWrap: boolean, // `<td>` and `<th>`\n    object: null, // `<applet>`\n    profile: null, // `<head>`\n    prompt: null, // `<isindex>`\n    rev: null, // `<link>`\n    rightMargin: number, // `<body>`\n    rules: null, // `<table>`\n    scheme: null, // `<meta>`\n    scrolling: booleanish, // `<frame>`. Use overflow in the child context\n    standby: null, // `<object>`\n    summary: null, // `<table>`\n    text: null, // `<body>`. Use CSS `color` instead\n    topMargin: number, // `<body>`\n    valueType: null, // `<param>`\n    version: null, // `<html>`. Use a doctype.\n    vAlign: null, // Several. Use CSS `vertical-align` instead\n    vLink: null, // `<body>`. Use CSS `a:visited {color}` instead\n    vSpace: number, // `<img>` and `<object>`\n\n    // Non-standard Properties.\n    allowTransparency: null,\n    autoCorrect: null,\n    autoSave: null,\n    disablePictureInPicture: boolean,\n    disableRemotePlayback: boolean,\n    prefix: null,\n    property: null,\n    results: number,\n    security: null,\n    unselectable: null\n  }\n});\n\nconst svg$1 = create({\n  space: 'svg',\n  attributes: {\n    accentHeight: 'accent-height',\n    alignmentBaseline: 'alignment-baseline',\n    arabicForm: 'arabic-form',\n    baselineShift: 'baseline-shift',\n    capHeight: 'cap-height',\n    className: 'class',\n    clipPath: 'clip-path',\n    clipRule: 'clip-rule',\n    colorInterpolation: 'color-interpolation',\n    colorInterpolationFilters: 'color-interpolation-filters',\n    colorProfile: 'color-profile',\n    colorRendering: 'color-rendering',\n    crossOrigin: 'crossorigin',\n    dataType: 'datatype',\n    dominantBaseline: 'dominant-baseline',\n    enableBackground: 'enable-background',\n    fillOpacity: 'fill-opacity',\n    fillRule: 'fill-rule',\n    floodColor: 'flood-color',\n    floodOpacity: 'flood-opacity',\n    fontFamily: 'font-family',\n    fontSize: 'font-size',\n    fontSizeAdjust: 'font-size-adjust',\n    fontStretch: 'font-stretch',\n    fontStyle: 'font-style',\n    fontVariant: 'font-variant',\n    fontWeight: 'font-weight',\n    glyphName: 'glyph-name',\n    glyphOrientationHorizontal: 'glyph-orientation-horizontal',\n    glyphOrientationVertical: 'glyph-orientation-vertical',\n    hrefLang: 'hreflang',\n    horizAdvX: 'horiz-adv-x',\n    horizOriginX: 'horiz-origin-x',\n    horizOriginY: 'horiz-origin-y',\n    imageRendering: 'image-rendering',\n    letterSpacing: 'letter-spacing',\n    lightingColor: 'lighting-color',\n    markerEnd: 'marker-end',\n    markerMid: 'marker-mid',\n    markerStart: 'marker-start',\n    navDown: 'nav-down',\n    navDownLeft: 'nav-down-left',\n    navDownRight: 'nav-down-right',\n    navLeft: 'nav-left',\n    navNext: 'nav-next',\n    navPrev: 'nav-prev',\n    navRight: 'nav-right',\n    navUp: 'nav-up',\n    navUpLeft: 'nav-up-left',\n    navUpRight: 'nav-up-right',\n    onAbort: 'onabort',\n    onActivate: 'onactivate',\n    onAfterPrint: 'onafterprint',\n    onBeforePrint: 'onbeforeprint',\n    onBegin: 'onbegin',\n    onCancel: 'oncancel',\n    onCanPlay: 'oncanplay',\n    onCanPlayThrough: 'oncanplaythrough',\n    onChange: 'onchange',\n    onClick: 'onclick',\n    onClose: 'onclose',\n    onCopy: 'oncopy',\n    onCueChange: 'oncuechange',\n    onCut: 'oncut',\n    onDblClick: 'ondblclick',\n    onDrag: 'ondrag',\n    onDragEnd: 'ondragend',\n    onDragEnter: 'ondragenter',\n    onDragExit: 'ondragexit',\n    onDragLeave: 'ondragleave',\n    onDragOver: 'ondragover',\n    onDragStart: 'ondragstart',\n    onDrop: 'ondrop',\n    onDurationChange: 'ondurationchange',\n    onEmptied: 'onemptied',\n    onEnd: 'onend',\n    onEnded: 'onended',\n    onError: 'onerror',\n    onFocus: 'onfocus',\n    onFocusIn: 'onfocusin',\n    onFocusOut: 'onfocusout',\n    onHashChange: 'onhashchange',\n    onInput: 'oninput',\n    onInvalid: 'oninvalid',\n    onKeyDown: 'onkeydown',\n    onKeyPress: 'onkeypress',\n    onKeyUp: 'onkeyup',\n    onLoad: 'onload',\n    onLoadedData: 'onloadeddata',\n    onLoadedMetadata: 'onloadedmetadata',\n    onLoadStart: 'onloadstart',\n    onMessage: 'onmessage',\n    onMouseDown: 'onmousedown',\n    onMouseEnter: 'onmouseenter',\n    onMouseLeave: 'onmouseleave',\n    onMouseMove: 'onmousemove',\n    onMouseOut: 'onmouseout',\n    onMouseOver: 'onmouseover',\n    onMouseUp: 'onmouseup',\n    onMouseWheel: 'onmousewheel',\n    onOffline: 'onoffline',\n    onOnline: 'ononline',\n    onPageHide: 'onpagehide',\n    onPageShow: 'onpageshow',\n    onPaste: 'onpaste',\n    onPause: 'onpause',\n    onPlay: 'onplay',\n    onPlaying: 'onplaying',\n    onPopState: 'onpopstate',\n    onProgress: 'onprogress',\n    onRateChange: 'onratechange',\n    onRepeat: 'onrepeat',\n    onReset: 'onreset',\n    onResize: 'onresize',\n    onScroll: 'onscroll',\n    onSeeked: 'onseeked',\n    onSeeking: 'onseeking',\n    onSelect: 'onselect',\n    onShow: 'onshow',\n    onStalled: 'onstalled',\n    onStorage: 'onstorage',\n    onSubmit: 'onsubmit',\n    onSuspend: 'onsuspend',\n    onTimeUpdate: 'ontimeupdate',\n    onToggle: 'ontoggle',\n    onUnload: 'onunload',\n    onVolumeChange: 'onvolumechange',\n    onWaiting: 'onwaiting',\n    onZoom: 'onzoom',\n    overlinePosition: 'overline-position',\n    overlineThickness: 'overline-thickness',\n    paintOrder: 'paint-order',\n    panose1: 'panose-1',\n    pointerEvents: 'pointer-events',\n    referrerPolicy: 'referrerpolicy',\n    renderingIntent: 'rendering-intent',\n    shapeRendering: 'shape-rendering',\n    stopColor: 'stop-color',\n    stopOpacity: 'stop-opacity',\n    strikethroughPosition: 'strikethrough-position',\n    strikethroughThickness: 'strikethrough-thickness',\n    strokeDashArray: 'stroke-dasharray',\n    strokeDashOffset: 'stroke-dashoffset',\n    strokeLineCap: 'stroke-linecap',\n    strokeLineJoin: 'stroke-linejoin',\n    strokeMiterLimit: 'stroke-miterlimit',\n    strokeOpacity: 'stroke-opacity',\n    strokeWidth: 'stroke-width',\n    tabIndex: 'tabindex',\n    textAnchor: 'text-anchor',\n    textDecoration: 'text-decoration',\n    textRendering: 'text-rendering',\n    typeOf: 'typeof',\n    underlinePosition: 'underline-position',\n    underlineThickness: 'underline-thickness',\n    unicodeBidi: 'unicode-bidi',\n    unicodeRange: 'unicode-range',\n    unitsPerEm: 'units-per-em',\n    vAlphabetic: 'v-alphabetic',\n    vHanging: 'v-hanging',\n    vIdeographic: 'v-ideographic',\n    vMathematical: 'v-mathematical',\n    vectorEffect: 'vector-effect',\n    vertAdvY: 'vert-adv-y',\n    vertOriginX: 'vert-origin-x',\n    vertOriginY: 'vert-origin-y',\n    wordSpacing: 'word-spacing',\n    writingMode: 'writing-mode',\n    xHeight: 'x-height',\n    // These were camelcased in Tiny. Now lowercased in SVG 2\n    playbackOrder: 'playbackorder',\n    timelineBegin: 'timelinebegin'\n  },\n  transform: caseSensitiveTransform,\n  properties: {\n    about: commaOrSpaceSeparated,\n    accentHeight: number,\n    accumulate: null,\n    additive: null,\n    alignmentBaseline: null,\n    alphabetic: number,\n    amplitude: number,\n    arabicForm: null,\n    ascent: number,\n    attributeName: null,\n    attributeType: null,\n    azimuth: number,\n    bandwidth: null,\n    baselineShift: null,\n    baseFrequency: null,\n    baseProfile: null,\n    bbox: null,\n    begin: null,\n    bias: number,\n    by: null,\n    calcMode: null,\n    capHeight: number,\n    className: spaceSeparated,\n    clip: null,\n    clipPath: null,\n    clipPathUnits: null,\n    clipRule: null,\n    color: null,\n    colorInterpolation: null,\n    colorInterpolationFilters: null,\n    colorProfile: null,\n    colorRendering: null,\n    content: null,\n    contentScriptType: null,\n    contentStyleType: null,\n    crossOrigin: null,\n    cursor: null,\n    cx: null,\n    cy: null,\n    d: null,\n    dataType: null,\n    defaultAction: null,\n    descent: number,\n    diffuseConstant: number,\n    direction: null,\n    display: null,\n    dur: null,\n    divisor: number,\n    dominantBaseline: null,\n    download: boolean,\n    dx: null,\n    dy: null,\n    edgeMode: null,\n    editable: null,\n    elevation: number,\n    enableBackground: null,\n    end: null,\n    event: null,\n    exponent: number,\n    externalResourcesRequired: null,\n    fill: null,\n    fillOpacity: number,\n    fillRule: null,\n    filter: null,\n    filterRes: null,\n    filterUnits: null,\n    floodColor: null,\n    floodOpacity: null,\n    focusable: null,\n    focusHighlight: null,\n    fontFamily: null,\n    fontSize: null,\n    fontSizeAdjust: null,\n    fontStretch: null,\n    fontStyle: null,\n    fontVariant: null,\n    fontWeight: null,\n    format: null,\n    fr: null,\n    from: null,\n    fx: null,\n    fy: null,\n    g1: commaSeparated,\n    g2: commaSeparated,\n    glyphName: commaSeparated,\n    glyphOrientationHorizontal: null,\n    glyphOrientationVertical: null,\n    glyphRef: null,\n    gradientTransform: null,\n    gradientUnits: null,\n    handler: null,\n    hanging: number,\n    hatchContentUnits: null,\n    hatchUnits: null,\n    height: null,\n    href: null,\n    hrefLang: null,\n    horizAdvX: number,\n    horizOriginX: number,\n    horizOriginY: number,\n    id: null,\n    ideographic: number,\n    imageRendering: null,\n    initialVisibility: null,\n    in: null,\n    in2: null,\n    intercept: number,\n    k: number,\n    k1: number,\n    k2: number,\n    k3: number,\n    k4: number,\n    kernelMatrix: commaOrSpaceSeparated,\n    kernelUnitLength: null,\n    keyPoints: null, // SEMI_COLON_SEPARATED\n    keySplines: null, // SEMI_COLON_SEPARATED\n    keyTimes: null, // SEMI_COLON_SEPARATED\n    kerning: null,\n    lang: null,\n    lengthAdjust: null,\n    letterSpacing: null,\n    lightingColor: null,\n    limitingConeAngle: number,\n    local: null,\n    markerEnd: null,\n    markerMid: null,\n    markerStart: null,\n    markerHeight: null,\n    markerUnits: null,\n    markerWidth: null,\n    mask: null,\n    maskContentUnits: null,\n    maskUnits: null,\n    mathematical: null,\n    max: null,\n    media: null,\n    mediaCharacterEncoding: null,\n    mediaContentEncodings: null,\n    mediaSize: number,\n    mediaTime: null,\n    method: null,\n    min: null,\n    mode: null,\n    name: null,\n    navDown: null,\n    navDownLeft: null,\n    navDownRight: null,\n    navLeft: null,\n    navNext: null,\n    navPrev: null,\n    navRight: null,\n    navUp: null,\n    navUpLeft: null,\n    navUpRight: null,\n    numOctaves: null,\n    observer: null,\n    offset: null,\n    onAbort: null,\n    onActivate: null,\n    onAfterPrint: null,\n    onBeforePrint: null,\n    onBegin: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnd: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFocusIn: null,\n    onFocusOut: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onMouseWheel: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRepeat: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onShow: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onZoom: null,\n    opacity: null,\n    operator: null,\n    order: null,\n    orient: null,\n    orientation: null,\n    origin: null,\n    overflow: null,\n    overlay: null,\n    overlinePosition: number,\n    overlineThickness: number,\n    paintOrder: null,\n    panose1: null,\n    path: null,\n    pathLength: number,\n    patternContentUnits: null,\n    patternTransform: null,\n    patternUnits: null,\n    phase: null,\n    ping: spaceSeparated,\n    pitch: null,\n    playbackOrder: null,\n    pointerEvents: null,\n    points: null,\n    pointsAtX: number,\n    pointsAtY: number,\n    pointsAtZ: number,\n    preserveAlpha: null,\n    preserveAspectRatio: null,\n    primitiveUnits: null,\n    propagate: null,\n    property: commaOrSpaceSeparated,\n    r: null,\n    radius: null,\n    referrerPolicy: null,\n    refX: null,\n    refY: null,\n    rel: commaOrSpaceSeparated,\n    rev: commaOrSpaceSeparated,\n    renderingIntent: null,\n    repeatCount: null,\n    repeatDur: null,\n    requiredExtensions: commaOrSpaceSeparated,\n    requiredFeatures: commaOrSpaceSeparated,\n    requiredFonts: commaOrSpaceSeparated,\n    requiredFormats: commaOrSpaceSeparated,\n    resource: null,\n    restart: null,\n    result: null,\n    rotate: null,\n    rx: null,\n    ry: null,\n    scale: null,\n    seed: null,\n    shapeRendering: null,\n    side: null,\n    slope: null,\n    snapshotTime: null,\n    specularConstant: number,\n    specularExponent: number,\n    spreadMethod: null,\n    spacing: null,\n    startOffset: null,\n    stdDeviation: null,\n    stemh: null,\n    stemv: null,\n    stitchTiles: null,\n    stopColor: null,\n    stopOpacity: null,\n    strikethroughPosition: number,\n    strikethroughThickness: number,\n    string: null,\n    stroke: null,\n    strokeDashArray: commaOrSpaceSeparated,\n    strokeDashOffset: null,\n    strokeLineCap: null,\n    strokeLineJoin: null,\n    strokeMiterLimit: number,\n    strokeOpacity: number,\n    strokeWidth: null,\n    style: null,\n    surfaceScale: number,\n    syncBehavior: null,\n    syncBehaviorDefault: null,\n    syncMaster: null,\n    syncTolerance: null,\n    syncToleranceDefault: null,\n    systemLanguage: commaOrSpaceSeparated,\n    tabIndex: number,\n    tableValues: null,\n    target: null,\n    targetX: number,\n    targetY: number,\n    textAnchor: null,\n    textDecoration: null,\n    textRendering: null,\n    textLength: null,\n    timelineBegin: null,\n    title: null,\n    transformBehavior: null,\n    type: null,\n    typeOf: commaOrSpaceSeparated,\n    to: null,\n    transform: null,\n    u1: null,\n    u2: null,\n    underlinePosition: number,\n    underlineThickness: number,\n    unicode: null,\n    unicodeBidi: null,\n    unicodeRange: null,\n    unitsPerEm: number,\n    values: null,\n    vAlphabetic: number,\n    vMathematical: number,\n    vectorEffect: null,\n    vHanging: number,\n    vIdeographic: number,\n    version: null,\n    vertAdvY: number,\n    vertOriginX: number,\n    vertOriginY: number,\n    viewBox: null,\n    viewTarget: null,\n    visibility: null,\n    width: null,\n    widths: null,\n    wordSpacing: null,\n    writingMode: null,\n    x: null,\n    x1: null,\n    x2: null,\n    xChannelSelector: null,\n    xHeight: number,\n    y: null,\n    y1: null,\n    y2: null,\n    yChannelSelector: null,\n    z: null,\n    zoomAndPan: null\n  }\n});\n\n/**\n * @typedef {import('./util/schema.js').Schema} Schema\n */\n\n\nconst valid = /^data[-\\w.:]+$/i;\nconst dash = /-[a-z]/g;\nconst cap = /[A-Z]/g;\n\n/**\n * @param {Schema} schema\n * @param {string} value\n * @returns {Info}\n */\nfunction find(schema, value) {\n  const normal = normalize(value);\n  let prop = value;\n  let Type = Info;\n\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]]\n  }\n\n  if (normal.length > 4 && normal.slice(0, 4) === 'data' && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      // Turn it into a property.\n      const rest = value.slice(5).replace(dash, camelcase);\n      prop = 'data' + rest.charAt(0).toUpperCase() + rest.slice(1);\n    } else {\n      // Turn it into an attribute.\n      const rest = value.slice(4);\n\n      if (!dash.test(rest)) {\n        let dashes = rest.replace(cap, kebab);\n\n        if (dashes.charAt(0) !== '-') {\n          dashes = '-' + dashes;\n        }\n\n        value = 'data' + dashes;\n      }\n    }\n\n    Type = DefinedInfo;\n  }\n\n  return new Type(prop, value)\n}\n\n/**\n * @param {string} $0\n * @returns {string}\n */\nfunction kebab($0) {\n  return '-' + $0.toLowerCase()\n}\n\n/**\n * @param {string} $0\n * @returns {string}\n */\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase()\n}\n\n/**\n * @typedef {import('./lib/util/info.js').Info} Info\n * @typedef {import('./lib/util/schema.js').Schema} Schema\n */\n\nconst html$2 = merge([xml, xlink, xmlns, aria, html$3], 'html');\nconst svg = merge([xml, xlink, xmlns, aria, svg$1], 'svg');\n\n/**\n * @callback Handler\n *   Handle a value, with a certain ID field set to a certain value.\n *   The ID field is passed to `zwitch`, and it’s value is this function’s\n *   place on the `handlers` record.\n * @param {...any} parameters\n *   Arbitrary parameters passed to the zwitch.\n *   The first will be an object with a certain ID field set to a certain value.\n * @returns {any}\n *   Anything!\n */\n\n/**\n * @callback UnknownHandler\n *   Handle values that do have a certain ID field, but it’s set to a value\n *   that is not listed in the `handlers` record.\n * @param {unknown} value\n *   An object with a certain ID field set to an unknown value.\n * @param {...any} rest\n *   Arbitrary parameters passed to the zwitch.\n * @returns {any}\n *   Anything!\n */\n\n/**\n * @callback InvalidHandler\n *   Handle values that do not have a certain ID field.\n * @param {unknown} value\n *   Any unknown value.\n * @param {...any} rest\n *   Arbitrary parameters passed to the zwitch.\n * @returns {void|null|undefined|never}\n *   This should crash or return nothing.\n */\n\n/**\n * @template {InvalidHandler} [Invalid=InvalidHandler]\n * @template {UnknownHandler} [Unknown=UnknownHandler]\n * @template {Record<string, Handler>} [Handlers=Record<string, Handler>]\n * @typedef Options\n *   Configuration (required).\n * @property {Invalid} [invalid]\n *   Handler to use for invalid values.\n * @property {Unknown} [unknown]\n *   Handler to use for unknown values.\n * @property {Handlers} [handlers]\n *   Handlers to use.\n */\n\nconst own$2 = {}.hasOwnProperty;\n\n/**\n * Handle values based on a field.\n *\n * @template {InvalidHandler} [Invalid=InvalidHandler]\n * @template {UnknownHandler} [Unknown=UnknownHandler]\n * @template {Record<string, Handler>} [Handlers=Record<string, Handler>]\n * @param {string} key\n *   Field to switch on.\n * @param {Options<Invalid, Unknown, Handlers>} [options]\n *   Configuration (required).\n * @returns {{unknown: Unknown, invalid: Invalid, handlers: Handlers, (...parameters: Parameters<Handlers[keyof Handlers]>): ReturnType<Handlers[keyof Handlers]>, (...parameters: Parameters<Unknown>): ReturnType<Unknown>}}\n */\nfunction zwitch(key, options) {\n  const settings = options || {};\n\n  /**\n   * Handle one value.\n   *\n   * Based on the bound `key`, a respective handler will be called.\n   * If `value` is not an object, or doesn’t have a `key` property, the special\n   * “invalid” handler will be called.\n   * If `value` has an unknown `key`, the special “unknown” handler will be\n   * called.\n   *\n   * All arguments, and the context object, are passed through to the handler,\n   * and it’s result is returned.\n   *\n   * @this {unknown}\n   *   Any context object.\n   * @param {unknown} [value]\n   *   Any value.\n   * @param {...unknown} parameters\n   *   Arbitrary parameters passed to the zwitch.\n   * @property {Handler} invalid\n   *   Handle for values that do not have a certain ID field.\n   * @property {Handler} unknown\n   *   Handle values that do have a certain ID field, but it’s set to a value\n   *   that is not listed in the `handlers` record.\n   * @property {Handlers} handlers\n   *   Record of handlers.\n   * @returns {unknown}\n   *   Anything.\n   */\n  function one(value, ...parameters) {\n    /** @type {Handler|undefined} */\n    let fn = one.invalid;\n    const handlers = one.handlers;\n\n    if (value && own$2.call(value, key)) {\n      // @ts-expect-error Indexable.\n      const id = String(value[key]);\n      // @ts-expect-error Indexable.\n      fn = own$2.call(handlers, id) ? handlers[id] : one.unknown;\n    }\n\n    if (fn) {\n      return fn.call(this, value, ...parameters)\n    }\n  }\n\n  one.handlers = settings.handlers || {};\n  one.invalid = settings.invalid;\n  one.unknown = settings.unknown;\n\n  // @ts-expect-error: matches!\n  return one\n}\n\n/**\n * @typedef CoreOptions\n * @property {Array<string>} [subset=[]]\n *   Whether to only escape the given subset of characters.\n * @property {boolean} [escapeOnly=false]\n *   Whether to only escape possibly dangerous characters.\n *   Those characters are `\"`, `&`, `'`, `<`, `>`, and `` ` ``.\n *\n * @typedef FormatOptions\n * @property {(code: number, next: number, options: CoreWithFormatOptions) => string} format\n *   Format strategy.\n *\n * @typedef {CoreOptions & FormatOptions & import('./util/format-smart.js').FormatSmartOptions} CoreWithFormatOptions\n */\n\n/**\n * Encode certain characters in `value`.\n *\n * @param {string} value\n * @param {CoreWithFormatOptions} options\n * @returns {string}\n */\nfunction core(value, options) {\n  value = value.replace(\n    options.subset ? charactersToExpression(options.subset) : /[\"&'<>`]/g,\n    basic\n  );\n\n  if (options.subset || options.escapeOnly) {\n    return value\n  }\n\n  return (\n    value\n      // Surrogate pairs.\n      .replace(/[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g, surrogate)\n      // BMP control characters (C0 except for LF, CR, SP; DEL; and some more\n      // non-ASCII ones).\n      .replace(\n        // eslint-disable-next-line no-control-regex, unicorn/no-hex-escape\n        /[\\x01-\\t\\v\\f\\x0E-\\x1F\\x7F\\x81\\x8D\\x8F\\x90\\x9D\\xA0-\\uFFFF]/g,\n        basic\n      )\n  )\n\n  /**\n   * @param {string} pair\n   * @param {number} index\n   * @param {string} all\n   */\n  function surrogate(pair, index, all) {\n    return options.format(\n      (pair.charCodeAt(0) - 0xd800) * 0x400 +\n        pair.charCodeAt(1) -\n        0xdc00 +\n        0x10000,\n      all.charCodeAt(index + 2),\n      options\n    )\n  }\n\n  /**\n   * @param {string} character\n   * @param {number} index\n   * @param {string} all\n   */\n  function basic(character, index, all) {\n    return options.format(\n      character.charCodeAt(0),\n      all.charCodeAt(index + 1),\n      options\n    )\n  }\n}\n\n/**\n * @param {Array<string>} subset\n * @returns {RegExp}\n */\nfunction charactersToExpression(subset) {\n  /** @type {Array<string>} */\n  const groups = [];\n  let index = -1;\n\n  while (++index < subset.length) {\n    groups.push(subset[index].replace(/[|\\\\{}()[\\]^$+*?.]/g, '\\\\$&'));\n  }\n\n  return new RegExp('(?:' + groups.join('|') + ')', 'g')\n}\n\n/**\n * Configurable ways to encode characters as hexadecimal references.\n *\n * @param {number} code\n * @param {number} next\n * @param {boolean|undefined} omit\n * @returns {string}\n */\nfunction toHexadecimal(code, next, omit) {\n  const value = '&#x' + code.toString(16).toUpperCase();\n  return omit && next && !/[\\dA-Fa-f]/.test(String.fromCharCode(next))\n    ? value\n    : value + ';'\n}\n\n/**\n * Configurable ways to encode characters as decimal references.\n *\n * @param {number} code\n * @param {number} next\n * @param {boolean|undefined} omit\n * @returns {string}\n */\nfunction toDecimal(code, next, omit) {\n  const value = '&#' + String(code);\n  return omit && next && !/\\d/.test(String.fromCharCode(next))\n    ? value\n    : value + ';'\n}\n\n/**\n * List of legacy HTML named character references that don’t need a trailing semicolon.\n *\n * @type {Array<string>}\n */\nconst characterEntitiesLegacy = [\n  'AElig',\n  'AMP',\n  'Aacute',\n  'Acirc',\n  'Agrave',\n  'Aring',\n  'Atilde',\n  'Auml',\n  'COPY',\n  'Ccedil',\n  'ETH',\n  'Eacute',\n  'Ecirc',\n  'Egrave',\n  'Euml',\n  'GT',\n  'Iacute',\n  'Icirc',\n  'Igrave',\n  'Iuml',\n  'LT',\n  'Ntilde',\n  'Oacute',\n  'Ocirc',\n  'Ograve',\n  'Oslash',\n  'Otilde',\n  'Ouml',\n  'QUOT',\n  'REG',\n  'THORN',\n  'Uacute',\n  'Ucirc',\n  'Ugrave',\n  'Uuml',\n  'Yacute',\n  'aacute',\n  'acirc',\n  'acute',\n  'aelig',\n  'agrave',\n  'amp',\n  'aring',\n  'atilde',\n  'auml',\n  'brvbar',\n  'ccedil',\n  'cedil',\n  'cent',\n  'copy',\n  'curren',\n  'deg',\n  'divide',\n  'eacute',\n  'ecirc',\n  'egrave',\n  'eth',\n  'euml',\n  'frac12',\n  'frac14',\n  'frac34',\n  'gt',\n  'iacute',\n  'icirc',\n  'iexcl',\n  'igrave',\n  'iquest',\n  'iuml',\n  'laquo',\n  'lt',\n  'macr',\n  'micro',\n  'middot',\n  'nbsp',\n  'not',\n  'ntilde',\n  'oacute',\n  'ocirc',\n  'ograve',\n  'ordf',\n  'ordm',\n  'oslash',\n  'otilde',\n  'ouml',\n  'para',\n  'plusmn',\n  'pound',\n  'quot',\n  'raquo',\n  'reg',\n  'sect',\n  'shy',\n  'sup1',\n  'sup2',\n  'sup3',\n  'szlig',\n  'thorn',\n  'times',\n  'uacute',\n  'ucirc',\n  'ugrave',\n  'uml',\n  'uuml',\n  'yacute',\n  'yen',\n  'yuml'\n];\n\n/**\n * Map of named character references from HTML 4.\n *\n * @type {Record<string, string>}\n */\nconst characterEntitiesHtml4 = {\n  nbsp: ' ',\n  iexcl: '¡',\n  cent: '¢',\n  pound: '£',\n  curren: '¤',\n  yen: '¥',\n  brvbar: '¦',\n  sect: '§',\n  uml: '¨',\n  copy: '©',\n  ordf: 'ª',\n  laquo: '«',\n  not: '¬',\n  shy: '­',\n  reg: '®',\n  macr: '¯',\n  deg: '°',\n  plusmn: '±',\n  sup2: '²',\n  sup3: '³',\n  acute: '´',\n  micro: 'µ',\n  para: '¶',\n  middot: '·',\n  cedil: '¸',\n  sup1: '¹',\n  ordm: 'º',\n  raquo: '»',\n  frac14: '¼',\n  frac12: '½',\n  frac34: '¾',\n  iquest: '¿',\n  Agrave: 'À',\n  Aacute: 'Á',\n  Acirc: 'Â',\n  Atilde: 'Ã',\n  Auml: 'Ä',\n  Aring: 'Å',\n  AElig: 'Æ',\n  Ccedil: 'Ç',\n  Egrave: 'È',\n  Eacute: 'É',\n  Ecirc: 'Ê',\n  Euml: 'Ë',\n  Igrave: 'Ì',\n  Iacute: 'Í',\n  Icirc: 'Î',\n  Iuml: 'Ï',\n  ETH: 'Ð',\n  Ntilde: 'Ñ',\n  Ograve: 'Ò',\n  Oacute: 'Ó',\n  Ocirc: 'Ô',\n  Otilde: 'Õ',\n  Ouml: 'Ö',\n  times: '×',\n  Oslash: 'Ø',\n  Ugrave: 'Ù',\n  Uacute: 'Ú',\n  Ucirc: 'Û',\n  Uuml: 'Ü',\n  Yacute: 'Ý',\n  THORN: 'Þ',\n  szlig: 'ß',\n  agrave: 'à',\n  aacute: 'á',\n  acirc: 'â',\n  atilde: 'ã',\n  auml: 'ä',\n  aring: 'å',\n  aelig: 'æ',\n  ccedil: 'ç',\n  egrave: 'è',\n  eacute: 'é',\n  ecirc: 'ê',\n  euml: 'ë',\n  igrave: 'ì',\n  iacute: 'í',\n  icirc: 'î',\n  iuml: 'ï',\n  eth: 'ð',\n  ntilde: 'ñ',\n  ograve: 'ò',\n  oacute: 'ó',\n  ocirc: 'ô',\n  otilde: 'õ',\n  ouml: 'ö',\n  divide: '÷',\n  oslash: 'ø',\n  ugrave: 'ù',\n  uacute: 'ú',\n  ucirc: 'û',\n  uuml: 'ü',\n  yacute: 'ý',\n  thorn: 'þ',\n  yuml: 'ÿ',\n  fnof: 'ƒ',\n  Alpha: 'Α',\n  Beta: 'Β',\n  Gamma: 'Γ',\n  Delta: 'Δ',\n  Epsilon: 'Ε',\n  Zeta: 'Ζ',\n  Eta: 'Η',\n  Theta: 'Θ',\n  Iota: 'Ι',\n  Kappa: 'Κ',\n  Lambda: 'Λ',\n  Mu: 'Μ',\n  Nu: 'Ν',\n  Xi: 'Ξ',\n  Omicron: 'Ο',\n  Pi: 'Π',\n  Rho: 'Ρ',\n  Sigma: 'Σ',\n  Tau: 'Τ',\n  Upsilon: 'Υ',\n  Phi: 'Φ',\n  Chi: 'Χ',\n  Psi: 'Ψ',\n  Omega: 'Ω',\n  alpha: 'α',\n  beta: 'β',\n  gamma: 'γ',\n  delta: 'δ',\n  epsilon: 'ε',\n  zeta: 'ζ',\n  eta: 'η',\n  theta: 'θ',\n  iota: 'ι',\n  kappa: 'κ',\n  lambda: 'λ',\n  mu: 'μ',\n  nu: 'ν',\n  xi: 'ξ',\n  omicron: 'ο',\n  pi: 'π',\n  rho: 'ρ',\n  sigmaf: 'ς',\n  sigma: 'σ',\n  tau: 'τ',\n  upsilon: 'υ',\n  phi: 'φ',\n  chi: 'χ',\n  psi: 'ψ',\n  omega: 'ω',\n  thetasym: 'ϑ',\n  upsih: 'ϒ',\n  piv: 'ϖ',\n  bull: '•',\n  hellip: '…',\n  prime: '′',\n  Prime: '″',\n  oline: '‾',\n  frasl: '⁄',\n  weierp: '℘',\n  image: 'ℑ',\n  real: 'ℜ',\n  trade: '™',\n  alefsym: 'ℵ',\n  larr: '←',\n  uarr: '↑',\n  rarr: '→',\n  darr: '↓',\n  harr: '↔',\n  crarr: '↵',\n  lArr: '⇐',\n  uArr: '⇑',\n  rArr: '⇒',\n  dArr: '⇓',\n  hArr: '⇔',\n  forall: '∀',\n  part: '∂',\n  exist: '∃',\n  empty: '∅',\n  nabla: '∇',\n  isin: '∈',\n  notin: '∉',\n  ni: '∋',\n  prod: '∏',\n  sum: '∑',\n  minus: '−',\n  lowast: '∗',\n  radic: '√',\n  prop: '∝',\n  infin: '∞',\n  ang: '∠',\n  and: '∧',\n  or: '∨',\n  cap: '∩',\n  cup: '∪',\n  int: '∫',\n  there4: '∴',\n  sim: '∼',\n  cong: '≅',\n  asymp: '≈',\n  ne: '≠',\n  equiv: '≡',\n  le: '≤',\n  ge: '≥',\n  sub: '⊂',\n  sup: '⊃',\n  nsub: '⊄',\n  sube: '⊆',\n  supe: '⊇',\n  oplus: '⊕',\n  otimes: '⊗',\n  perp: '⊥',\n  sdot: '⋅',\n  lceil: '⌈',\n  rceil: '⌉',\n  lfloor: '⌊',\n  rfloor: '⌋',\n  lang: '〈',\n  rang: '〉',\n  loz: '◊',\n  spades: '♠',\n  clubs: '♣',\n  hearts: '♥',\n  diams: '♦',\n  quot: '\"',\n  amp: '&',\n  lt: '<',\n  gt: '>',\n  OElig: 'Œ',\n  oelig: 'œ',\n  Scaron: 'Š',\n  scaron: 'š',\n  Yuml: 'Ÿ',\n  circ: 'ˆ',\n  tilde: '˜',\n  ensp: ' ',\n  emsp: ' ',\n  thinsp: ' ',\n  zwnj: '‌',\n  zwj: '‍',\n  lrm: '‎',\n  rlm: '‏',\n  ndash: '–',\n  mdash: '—',\n  lsquo: '‘',\n  rsquo: '’',\n  sbquo: '‚',\n  ldquo: '“',\n  rdquo: '”',\n  bdquo: '„',\n  dagger: '†',\n  Dagger: '‡',\n  permil: '‰',\n  lsaquo: '‹',\n  rsaquo: '›',\n  euro: '€'\n};\n\n/**\n * List of legacy (that don’t need a trailing `;`) named references which could,\n * depending on what follows them, turn into a different meaning\n *\n * @type {Array<string>}\n */\nconst dangerous = [\n  'cent',\n  'copy',\n  'divide',\n  'gt',\n  'lt',\n  'not',\n  'para',\n  'times'\n];\n\nconst own$1 = {}.hasOwnProperty;\n\n/**\n * `characterEntitiesHtml4` but inverted.\n *\n * @type {Record<string, string>}\n */\nconst characters = {};\n\n/** @type {string} */\nlet key;\n\nfor (key in characterEntitiesHtml4) {\n  if (own$1.call(characterEntitiesHtml4, key)) {\n    characters[characterEntitiesHtml4[key]] = key;\n  }\n}\n\n/**\n * Configurable ways to encode characters as named references.\n *\n * @param {number} code\n * @param {number} next\n * @param {boolean|undefined} omit\n * @param {boolean|undefined} attribute\n * @returns {string}\n */\nfunction toNamed(code, next, omit, attribute) {\n  const character = String.fromCharCode(code);\n\n  if (own$1.call(characters, character)) {\n    const name = characters[character];\n    const value = '&' + name;\n\n    if (\n      omit &&\n      characterEntitiesLegacy.includes(name) &&\n      !dangerous.includes(name) &&\n      (!attribute ||\n        (next &&\n          next !== 61 /* `=` */ &&\n          /[^\\da-z]/i.test(String.fromCharCode(next))))\n    ) {\n      return value\n    }\n\n    return value + ';'\n  }\n\n  return ''\n}\n\n/**\n * @typedef FormatSmartOptions\n * @property {boolean} [useNamedReferences=false]\n *   Prefer named character references (`&amp;`) where possible.\n * @property {boolean} [useShortestReferences=false]\n *   Prefer the shortest possible reference, if that results in less bytes.\n *   **Note**: `useNamedReferences` can be omitted when using `useShortestReferences`.\n * @property {boolean} [omitOptionalSemicolons=false]\n *   Whether to omit semicolons when possible.\n *   **Note**: This creates what HTML calls “parse errors” but is otherwise still valid HTML — don’t use this except when building a minifier.\n *   Omitting semicolons is possible for certain named and numeric references in some cases.\n * @property {boolean} [attribute=false]\n *   Create character references which don’t fail in attributes.\n *   **Note**: `attribute` only applies when operating dangerously with\n *   `omitOptionalSemicolons: true`.\n */\n\n\n/**\n * Configurable ways to encode a character yielding pretty or small results.\n *\n * @param {number} code\n * @param {number} next\n * @param {FormatSmartOptions} options\n * @returns {string}\n */\nfunction formatSmart(code, next, options) {\n  let numeric = toHexadecimal(code, next, options.omitOptionalSemicolons);\n  /** @type {string|undefined} */\n  let named;\n\n  if (options.useNamedReferences || options.useShortestReferences) {\n    named = toNamed(\n      code,\n      next,\n      options.omitOptionalSemicolons,\n      options.attribute\n    );\n  }\n\n  // Use the shortest numeric reference when requested.\n  // A simple algorithm would use decimal for all code points under 100, as\n  // those are shorter than hexadecimal:\n  //\n  // * `&#99;` vs `&#x63;` (decimal shorter)\n  // * `&#100;` vs `&#x64;` (equal)\n  //\n  // However, because we take `next` into consideration when `omit` is used,\n  // And it would be possible that decimals are shorter on bigger values as\n  // well if `next` is hexadecimal but not decimal, we instead compare both.\n  if (\n    (options.useShortestReferences || !named) &&\n    options.useShortestReferences\n  ) {\n    const decimal = toDecimal(code, next, options.omitOptionalSemicolons);\n\n    if (decimal.length < numeric.length) {\n      numeric = decimal;\n    }\n  }\n\n  return named &&\n    (!options.useShortestReferences || named.length < numeric.length)\n    ? named\n    : numeric\n}\n\n/**\n * @typedef {import('./core.js').CoreOptions & import('./util/format-smart.js').FormatSmartOptions} Options\n * @typedef {import('./core.js').CoreOptions} LightOptions\n */\n\n\n/**\n * Encode special characters in `value`.\n *\n * @param {string} value\n *   Value to encode.\n * @param {Options} [options]\n *   Configuration.\n * @returns {string}\n *   Encoded value.\n */\nfunction stringifyEntities(value, options) {\n  return core(value, Object.assign({format: formatSmart}, options))\n}\n\n/**\n * @typedef {import('hast').Comment} Comment\n * @typedef {import('hast').Parents} Parents\n *\n * @typedef {import('../index.js').State} State\n */\n\n\n/**\n * Serialize a comment.\n *\n * @param {Comment} node\n *   Node to handle.\n * @param {number | undefined} _1\n *   Index of `node` in `parent.\n * @param {Parents | undefined} _2\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction comment(node, _1, _2, state) {\n  // See: <https://html.spec.whatwg.org/multipage/syntax.html#comments>\n  return state.settings.bogusComments\n    ? '<?' +\n        stringifyEntities(\n          node.value,\n          Object.assign({}, state.settings.characterReferences, {subset: ['>']})\n        ) +\n        '>'\n    : '<!--' + node.value.replace(/^>|^->|<!--|-->|--!>|<!-$/g, encode) + '-->'\n\n  /**\n   * @param {string} $0\n   */\n  function encode($0) {\n    return stringifyEntities(\n      $0,\n      Object.assign({}, state.settings.characterReferences, {\n        subset: ['<', '>']\n      })\n    )\n  }\n}\n\n/**\n * @typedef {import('hast').Doctype} Doctype\n * @typedef {import('hast').Parents} Parents\n *\n * @typedef {import('../index.js').State} State\n */\n\n\n/**\n * Serialize a doctype.\n *\n * @param {Doctype} _1\n *   Node to handle.\n * @param {number | undefined} _2\n *   Index of `node` in `parent.\n * @param {Parents | undefined} _3\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction doctype(_1, _2, _3, state) {\n  return (\n    '<!' +\n    (state.settings.upperDoctype ? 'DOCTYPE' : 'doctype') +\n    (state.settings.tightDoctype ? '' : ' ') +\n    'html>'\n  )\n}\n\n/**\n * Count how often a character (or substring) is used in a string.\n *\n * @param {string} value\n *   Value to search in.\n * @param {string} character\n *   Character (or substring) to look for.\n * @return {number}\n *   Number of times `character` occurred in `value`.\n */\nfunction ccount(value, character) {\n  const source = String(value);\n\n  if (typeof character !== 'string') {\n    throw new TypeError('Expected character')\n  }\n\n  let count = 0;\n  let index = source.indexOf(character);\n\n  while (index !== -1) {\n    count++;\n    index = source.indexOf(character, index + character.length);\n  }\n\n  return count\n}\n\n/**\n * @typedef Options\n *   Configuration for `stringify`.\n * @property {boolean} [padLeft=true]\n *   Whether to pad a space before a token.\n * @property {boolean} [padRight=false]\n *   Whether to pad a space after a token.\n */\n\n\n/**\n * Serialize an array of strings or numbers to comma-separated tokens.\n *\n * @param {Array<string|number>} values\n *   List of tokens.\n * @param {Options} [options]\n *   Configuration for `stringify` (optional).\n * @returns {string}\n *   Comma-separated tokens.\n */\nfunction stringify$1(values, options) {\n  const settings = options || {};\n\n  // Ensure the last empty entry is seen.\n  const input = values[values.length - 1] === '' ? [...values, ''] : values;\n\n  return input\n    .join(\n      (settings.padRight ? ' ' : '') +\n        ',' +\n        (settings.padLeft === false ? '' : ' ')\n    )\n    .trim()\n}\n\n/**\n * Parse space-separated tokens to an array of strings.\n *\n * @param {string} value\n *   Space-separated tokens.\n * @returns {Array<string>}\n *   List of tokens.\n */\n\n/**\n * Serialize an array of strings as space separated-tokens.\n *\n * @param {Array<string|number>} values\n *   List of tokens.\n * @returns {string}\n *   Space-separated tokens.\n */\nfunction stringify(values) {\n  return values.join(' ').trim()\n}\n\n/**\n * @typedef {import('hast').Nodes} Nodes\n */\n\n// HTML whitespace expression.\n// See <https://infra.spec.whatwg.org/#ascii-whitespace>.\nconst re = /[ \\t\\n\\f\\r]/g;\n\n/**\n * Check if the given value is *inter-element whitespace*.\n *\n * @param {Nodes | string} thing\n *   Thing to check (`Node` or `string`).\n * @returns {boolean}\n *   Whether the `value` is inter-element whitespace (`boolean`): consisting of\n *   zero or more of space, tab (`\\t`), line feed (`\\n`), carriage return\n *   (`\\r`), or form feed (`\\f`); if a node is passed it must be a `Text` node,\n *   whose `value` field is checked.\n */\nfunction whitespace(thing) {\n  return typeof thing === 'object'\n    ? thing.type === 'text'\n      ? empty(thing.value)\n      : false\n    : empty(thing)\n}\n\n/**\n * @param {string} value\n * @returns {boolean}\n */\nfunction empty(value) {\n  return value.replace(re, '') === ''\n}\n\n/**\n * @typedef {import('hast').Parents} Parents\n * @typedef {import('hast').RootContent} RootContent\n */\n\n\nconst siblingAfter = siblings(1);\nconst siblingBefore = siblings(-1);\n\n/** @type {Array<RootContent>} */\nconst emptyChildren$1 = [];\n\n/**\n * Factory to check siblings in a direction.\n *\n * @param {number} increment\n */\nfunction siblings(increment) {\n  return sibling\n\n  /**\n   * Find applicable siblings in a direction.\n   *\n   * @template {Parents} Parent\n   *   Parent type.\n   * @param {Parent | undefined} parent\n   *   Parent.\n   * @param {number | undefined} index\n   *   Index of child in `parent`.\n   * @param {boolean | undefined} [includeWhitespace=false]\n   *   Whether to include whitespace (default: `false`).\n   * @returns {Parent extends {children: Array<infer Child>} ? Child | undefined : never}\n   *   Child of parent.\n   */\n  function sibling(parent, index, includeWhitespace) {\n    const siblings = parent ? parent.children : emptyChildren$1;\n    let offset = (index || 0) + increment;\n    let next = siblings[offset];\n\n    if (!includeWhitespace) {\n      while (next && whitespace(next)) {\n        offset += increment;\n        next = siblings[offset];\n      }\n    }\n\n    // @ts-expect-error: it’s a correct child.\n    return next\n  }\n}\n\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Parents} Parents\n */\n\n/**\n * @callback OmitHandle\n *   Check if a tag can be omitted.\n * @param {Element} element\n *   Element to check.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether to omit a tag.\n *\n */\n\nconst own = {}.hasOwnProperty;\n\n/**\n * Factory to check if a given node can have a tag omitted.\n *\n * @param {Record<string, OmitHandle>} handlers\n *   Omission handlers, where each key is a tag name, and each value is the\n *   corresponding handler.\n * @returns {OmitHandle}\n *   Whether to omit a tag of an element.\n */\nfunction omission(handlers) {\n  return omit\n\n  /**\n   * Check if a given node can have a tag omitted.\n   *\n   * @type {OmitHandle}\n   */\n  function omit(node, index, parent) {\n    return (\n      own.call(handlers, node.tagName) &&\n      handlers[node.tagName](node, index, parent)\n    )\n  }\n}\n\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Parents} Parents\n */\n\n\nconst closing = omission({\n  body: body$1,\n  caption: headOrColgroupOrCaption,\n  colgroup: headOrColgroupOrCaption,\n  dd,\n  dt,\n  head: headOrColgroupOrCaption,\n  html: html$1,\n  li,\n  optgroup,\n  option,\n  p,\n  rp: rubyElement,\n  rt: rubyElement,\n  tbody: tbody$1,\n  td: cells,\n  tfoot,\n  th: cells,\n  thead,\n  tr\n});\n\n/**\n * Macro for `</head>`, `</colgroup>`, and `</caption>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction headOrColgroupOrCaption(_, index, parent) {\n  const next = siblingAfter(parent, index, true);\n  return (\n    !next ||\n    (next.type !== 'comment' &&\n      !(next.type === 'text' && whitespace(next.value.charAt(0))))\n  )\n}\n\n/**\n * Whether to omit `</html>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction html$1(_, index, parent) {\n  const next = siblingAfter(parent, index);\n  return !next || next.type !== 'comment'\n}\n\n/**\n * Whether to omit `</body>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction body$1(_, index, parent) {\n  const next = siblingAfter(parent, index);\n  return !next || next.type !== 'comment'\n}\n\n/**\n * Whether to omit `</p>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction p(_, index, parent) {\n  const next = siblingAfter(parent, index);\n  return next\n    ? next.type === 'element' &&\n        (next.tagName === 'address' ||\n          next.tagName === 'article' ||\n          next.tagName === 'aside' ||\n          next.tagName === 'blockquote' ||\n          next.tagName === 'details' ||\n          next.tagName === 'div' ||\n          next.tagName === 'dl' ||\n          next.tagName === 'fieldset' ||\n          next.tagName === 'figcaption' ||\n          next.tagName === 'figure' ||\n          next.tagName === 'footer' ||\n          next.tagName === 'form' ||\n          next.tagName === 'h1' ||\n          next.tagName === 'h2' ||\n          next.tagName === 'h3' ||\n          next.tagName === 'h4' ||\n          next.tagName === 'h5' ||\n          next.tagName === 'h6' ||\n          next.tagName === 'header' ||\n          next.tagName === 'hgroup' ||\n          next.tagName === 'hr' ||\n          next.tagName === 'main' ||\n          next.tagName === 'menu' ||\n          next.tagName === 'nav' ||\n          next.tagName === 'ol' ||\n          next.tagName === 'p' ||\n          next.tagName === 'pre' ||\n          next.tagName === 'section' ||\n          next.tagName === 'table' ||\n          next.tagName === 'ul')\n    : !parent ||\n        // Confusing parent.\n        !(\n          parent.type === 'element' &&\n          (parent.tagName === 'a' ||\n            parent.tagName === 'audio' ||\n            parent.tagName === 'del' ||\n            parent.tagName === 'ins' ||\n            parent.tagName === 'map' ||\n            parent.tagName === 'noscript' ||\n            parent.tagName === 'video')\n        )\n}\n\n/**\n * Whether to omit `</li>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction li(_, index, parent) {\n  const next = siblingAfter(parent, index);\n  return !next || (next.type === 'element' && next.tagName === 'li')\n}\n\n/**\n * Whether to omit `</dt>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction dt(_, index, parent) {\n  const next = siblingAfter(parent, index);\n  return Boolean(\n    next &&\n      next.type === 'element' &&\n      (next.tagName === 'dt' || next.tagName === 'dd')\n  )\n}\n\n/**\n * Whether to omit `</dd>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction dd(_, index, parent) {\n  const next = siblingAfter(parent, index);\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'dt' || next.tagName === 'dd'))\n  )\n}\n\n/**\n * Whether to omit `</rt>` or `</rp>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction rubyElement(_, index, parent) {\n  const next = siblingAfter(parent, index);\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'rp' || next.tagName === 'rt'))\n  )\n}\n\n/**\n * Whether to omit `</optgroup>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction optgroup(_, index, parent) {\n  const next = siblingAfter(parent, index);\n  return !next || (next.type === 'element' && next.tagName === 'optgroup')\n}\n\n/**\n * Whether to omit `</option>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction option(_, index, parent) {\n  const next = siblingAfter(parent, index);\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'option' || next.tagName === 'optgroup'))\n  )\n}\n\n/**\n * Whether to omit `</thead>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction thead(_, index, parent) {\n  const next = siblingAfter(parent, index);\n  return Boolean(\n    next &&\n      next.type === 'element' &&\n      (next.tagName === 'tbody' || next.tagName === 'tfoot')\n  )\n}\n\n/**\n * Whether to omit `</tbody>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tbody$1(_, index, parent) {\n  const next = siblingAfter(parent, index);\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'tbody' || next.tagName === 'tfoot'))\n  )\n}\n\n/**\n * Whether to omit `</tfoot>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tfoot(_, index, parent) {\n  return !siblingAfter(parent, index)\n}\n\n/**\n * Whether to omit `</tr>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction tr(_, index, parent) {\n  const next = siblingAfter(parent, index);\n  return !next || (next.type === 'element' && next.tagName === 'tr')\n}\n\n/**\n * Whether to omit `</td>` or `</th>`.\n *\n * @param {Element} _\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the closing tag can be omitted.\n */\nfunction cells(_, index, parent) {\n  const next = siblingAfter(parent, index);\n  return (\n    !next ||\n    (next.type === 'element' &&\n      (next.tagName === 'td' || next.tagName === 'th'))\n  )\n}\n\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Parents} Parents\n */\n\n\nconst opening = omission({\n  body,\n  colgroup,\n  head,\n  html,\n  tbody\n});\n\n/**\n * Whether to omit `<html>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction html(node) {\n  const head = siblingAfter(node, -1);\n  return !head || head.type !== 'comment'\n}\n\n/**\n * Whether to omit `<head>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction head(node) {\n  const children = node.children;\n  /** @type {Array<string>} */\n  const seen = [];\n  let index = -1;\n\n  while (++index < children.length) {\n    const child = children[index];\n    if (\n      child.type === 'element' &&\n      (child.tagName === 'title' || child.tagName === 'base')\n    ) {\n      if (seen.includes(child.tagName)) return false\n      seen.push(child.tagName);\n    }\n  }\n\n  return children.length > 0\n}\n\n/**\n * Whether to omit `<body>`.\n *\n * @param {Element} node\n *   Element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction body(node) {\n  const head = siblingAfter(node, -1, true);\n\n  return (\n    !head ||\n    (head.type !== 'comment' &&\n      !(head.type === 'text' && whitespace(head.value.charAt(0))) &&\n      !(\n        head.type === 'element' &&\n        (head.tagName === 'meta' ||\n          head.tagName === 'link' ||\n          head.tagName === 'script' ||\n          head.tagName === 'style' ||\n          head.tagName === 'template')\n      ))\n  )\n}\n\n/**\n * Whether to omit `<colgroup>`.\n * The spec describes some logic for the opening tag, but it’s easier to\n * implement in the closing tag, to the same effect, so we handle it there\n * instead.\n *\n * @param {Element} node\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction colgroup(node, index, parent) {\n  const previous = siblingBefore(parent, index);\n  const head = siblingAfter(node, -1, true);\n\n  // Previous colgroup was already omitted.\n  if (\n    parent &&\n    previous &&\n    previous.type === 'element' &&\n    previous.tagName === 'colgroup' &&\n    closing(previous, parent.children.indexOf(previous), parent)\n  ) {\n    return false\n  }\n\n  return Boolean(head && head.type === 'element' && head.tagName === 'col')\n}\n\n/**\n * Whether to omit `<tbody>`.\n *\n * @param {Element} node\n *   Element.\n * @param {number | undefined} index\n *   Index of element in parent.\n * @param {Parents | undefined} parent\n *   Parent of element.\n * @returns {boolean}\n *   Whether the opening tag can be omitted.\n */\nfunction tbody(node, index, parent) {\n  const previous = siblingBefore(parent, index);\n  const head = siblingAfter(node, -1);\n\n  // Previous table section was already omitted.\n  if (\n    parent &&\n    previous &&\n    previous.type === 'element' &&\n    (previous.tagName === 'thead' || previous.tagName === 'tbody') &&\n    closing(previous, parent.children.indexOf(previous), parent)\n  ) {\n    return false\n  }\n\n  return Boolean(head && head.type === 'element' && head.tagName === 'tr')\n}\n\n/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Parents} Parents\n * @typedef {import('hast').Properties} Properties\n *\n * @typedef {import('../index.js').State} State\n */\n\n\n/**\n * Maps of subsets.\n *\n * Each value is a matrix of tuples.\n * The value at `0` causes parse errors, the value at `1` is valid.\n * Of both, the value at `0` is unsafe, and the value at `1` is safe.\n *\n * @type {Record<'double' | 'name' | 'single' | 'unquoted', Array<[Array<string>, Array<string>]>>}\n */\nconst constants = {\n  // See: <https://html.spec.whatwg.org/#attribute-name-state>.\n  name: [\n    ['\\t\\n\\f\\r &/=>'.split(''), '\\t\\n\\f\\r \"&\\'/=>`'.split('')],\n    ['\\0\\t\\n\\f\\r \"&\\'/<=>'.split(''), '\\0\\t\\n\\f\\r \"&\\'/<=>`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(unquoted)-state>.\n  unquoted: [\n    ['\\t\\n\\f\\r &>'.split(''), '\\0\\t\\n\\f\\r \"&\\'<=>`'.split('')],\n    ['\\0\\t\\n\\f\\r \"&\\'<=>`'.split(''), '\\0\\t\\n\\f\\r \"&\\'<=>`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(single-quoted)-state>.\n  single: [\n    [\"&'\".split(''), '\"&\\'`'.split('')],\n    [\"\\0&'\".split(''), '\\0\"&\\'`'.split('')]\n  ],\n  // See: <https://html.spec.whatwg.org/#attribute-value-(double-quoted)-state>.\n  double: [\n    ['\"&'.split(''), '\"&\\'`'.split('')],\n    ['\\0\"&'.split(''), '\\0\"&\\'`'.split('')]\n  ]\n};\n\n/**\n * Serialize an element node.\n *\n * @param {Element} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction element(node, index, parent, state) {\n  const schema = state.schema;\n  const omit = schema.space === 'svg' ? false : state.settings.omitOptionalTags;\n  let selfClosing =\n    schema.space === 'svg'\n      ? state.settings.closeEmptyElements\n      : state.settings.voids.includes(node.tagName.toLowerCase());\n  /** @type {Array<string>} */\n  const parts = [];\n  /** @type {string} */\n  let last;\n\n  if (schema.space === 'html' && node.tagName === 'svg') {\n    state.schema = svg;\n  }\n\n  const attrs = serializeAttributes(state, node.properties);\n\n  const content = state.all(\n    schema.space === 'html' && node.tagName === 'template' ? node.content : node\n  );\n\n  state.schema = schema;\n\n  // If the node is categorised as void, but it has children, remove the\n  // categorisation.\n  // This enables for example `menuitem`s, which are void in W3C HTML but not\n  // void in WHATWG HTML, to be stringified properly.\n  // Note: `menuitem` has since been removed from the HTML spec, and so is no\n  // longer void.\n  if (content) selfClosing = false;\n\n  if (attrs || !omit || !opening(node, index, parent)) {\n    parts.push('<', node.tagName, attrs ? ' ' + attrs : '');\n\n    if (\n      selfClosing &&\n      (schema.space === 'svg' || state.settings.closeSelfClosing)\n    ) {\n      last = attrs.charAt(attrs.length - 1);\n      if (\n        !state.settings.tightSelfClosing ||\n        last === '/' ||\n        (last && last !== '\"' && last !== \"'\")\n      ) {\n        parts.push(' ');\n      }\n\n      parts.push('/');\n    }\n\n    parts.push('>');\n  }\n\n  parts.push(content);\n\n  if (!selfClosing && (!omit || !closing(node, index, parent))) {\n    parts.push('</' + node.tagName + '>');\n  }\n\n  return parts.join('')\n}\n\n/**\n * @param {State} state\n * @param {Properties | null | undefined} props\n * @returns {string}\n */\nfunction serializeAttributes(state, props) {\n  /** @type {Array<string>} */\n  const values = [];\n  let index = -1;\n  /** @type {string} */\n  let key;\n\n  if (props) {\n    for (key in props) {\n      if (props[key] !== null && props[key] !== undefined) {\n        const value = serializeAttribute(state, key, props[key]);\n        if (value) values.push(value);\n      }\n    }\n  }\n\n  while (++index < values.length) {\n    const last = state.settings.tightAttributes\n      ? values[index].charAt(values[index].length - 1)\n      : undefined;\n\n    // In tight mode, don’t add a space after quoted attributes.\n    if (index !== values.length - 1 && last !== '\"' && last !== \"'\") {\n      values[index] += ' ';\n    }\n  }\n\n  return values.join('')\n}\n\n/**\n * @param {State} state\n * @param {string} key\n * @param {Properties[keyof Properties]} value\n * @returns {string}\n */\nfunction serializeAttribute(state, key, value) {\n  const info = find(state.schema, key);\n  const x =\n    state.settings.allowParseErrors && state.schema.space === 'html' ? 0 : 1;\n  const y = state.settings.allowDangerousCharacters ? 0 : 1;\n  let quote = state.quote;\n  /** @type {string | undefined} */\n  let result;\n\n  if (info.overloadedBoolean && (value === info.attribute || value === '')) {\n    value = true;\n  } else if (\n    info.boolean ||\n    (info.overloadedBoolean && typeof value !== 'string')\n  ) {\n    value = Boolean(value);\n  }\n\n  if (\n    value === null ||\n    value === undefined ||\n    value === false ||\n    (typeof value === 'number' && Number.isNaN(value))\n  ) {\n    return ''\n  }\n\n  const name = stringifyEntities(\n    info.attribute,\n    Object.assign({}, state.settings.characterReferences, {\n      // Always encode without parse errors in non-HTML.\n      subset: constants.name[x][y]\n    })\n  );\n\n  // No value.\n  // There is currently only one boolean property in SVG: `[download]` on\n  // `<a>`.\n  // This property does not seem to work in browsers (Firefox, Safari, Chrome),\n  // so I can’t test if dropping the value works.\n  // But I assume that it should:\n  //\n  // ```html\n  // <!doctype html>\n  // <svg viewBox=\"0 0 100 100\">\n  //   <a href=https://example.com download>\n  //     <circle cx=50 cy=40 r=35 />\n  //   </a>\n  // </svg>\n  // ```\n  //\n  // See: <https://github.com/wooorm/property-information/blob/main/lib/svg.js>\n  if (value === true) return name\n\n  // `spaces` doesn’t accept a second argument, but it’s given here just to\n  // keep the code cleaner.\n  value = Array.isArray(value)\n    ? (info.commaSeparated ? stringify$1 : stringify)(value, {\n        padLeft: !state.settings.tightCommaSeparatedLists\n      })\n    : String(value);\n\n  if (state.settings.collapseEmptyAttributes && !value) return name\n\n  // Check unquoted value.\n  if (state.settings.preferUnquoted) {\n    result = stringifyEntities(\n      value,\n      Object.assign({}, state.settings.characterReferences, {\n        attribute: true,\n        subset: constants.unquoted[x][y]\n      })\n    );\n  }\n\n  // If we don’t want unquoted, or if `value` contains character references when\n  // unquoted…\n  if (result !== value) {\n    // If the alternative is less common than `quote`, switch.\n    if (\n      state.settings.quoteSmart &&\n      ccount(value, quote) > ccount(value, state.alternative)\n    ) {\n      quote = state.alternative;\n    }\n\n    result =\n      quote +\n      stringifyEntities(\n        value,\n        Object.assign({}, state.settings.characterReferences, {\n          // Always encode without parse errors in non-HTML.\n          subset: (quote === \"'\" ? constants.single : constants.double)[x][y],\n          attribute: true\n        })\n      ) +\n      quote;\n  }\n\n  // Don’t add a `=` for unquoted empties.\n  return name + (result ? '=' + result : result)\n}\n\n/**\n * @typedef {import('hast').Parents} Parents\n * @typedef {import('hast').Text} Text\n *\n * @typedef {import('mdast-util-to-hast').Raw} Raw\n *\n * @typedef {import('../index.js').State} State\n */\n\n\n/**\n * Serialize a text node.\n *\n * @param {Raw | Text} node\n *   Node to handle.\n * @param {number | undefined} _\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction text(node, _, parent, state) {\n  // Check if content of `node` should be escaped.\n  return parent &&\n    parent.type === 'element' &&\n    (parent.tagName === 'script' || parent.tagName === 'style')\n    ? node.value\n    : stringifyEntities(\n        node.value,\n        Object.assign({}, state.settings.characterReferences, {\n          subset: ['<', '&']\n        })\n      )\n}\n\n/**\n * @typedef {import('hast').Parents} Parents\n *\n * @typedef {import('mdast-util-to-hast').Raw} Raw\n *\n * @typedef {import('../index.js').State} State\n */\n\n\n/**\n * Serialize a raw node.\n *\n * @param {Raw} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction raw(node, index, parent, state) {\n  return state.settings.allowDangerousHtml\n    ? node.value\n    : text(node, index, parent, state)\n}\n\n/**\n * @typedef {import('hast').Parents} Parents\n * @typedef {import('hast').Root} Root\n *\n * @typedef {import('../index.js').State} State\n */\n\n\n/**\n * Serialize a root.\n *\n * @param {Root} node\n *   Node to handle.\n * @param {number | undefined} _1\n *   Index of `node` in `parent.\n * @param {Parents | undefined} _2\n *   Parent of `node`.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {string}\n *   Serialized node.\n */\nfunction root(node, _1, _2, state) {\n  return state.all(node)\n}\n\n/**\n * @typedef {import('hast').Nodes} Nodes\n * @typedef {import('hast').Parents} Parents\n *\n * @typedef {import('../index.js').State} State\n */\n\n\n/**\n * @type {(node: Nodes, index: number | undefined, parent: Parents | undefined, state: State) => string}\n */\nconst handle = zwitch('type', {\n  invalid,\n  unknown,\n  handlers: {comment, doctype, element, raw, root, text}\n});\n\n/**\n * Fail when a non-node is found in the tree.\n *\n * @param {unknown} node\n *   Unknown value.\n * @returns {never}\n *   Never.\n */\nfunction invalid(node) {\n  throw new Error('Expected node, not `' + node + '`')\n}\n\n/**\n * Fail when a node with an unknown type is found in the tree.\n *\n * @param {unknown} node_\n *  Unknown node.\n * @returns {never}\n *   Never.\n */\nfunction unknown(node_) {\n  // `type` is guaranteed by runtime JS.\n  const node = /** @type {Nodes} */ (node_);\n  throw new Error('Cannot compile unknown node `' + node.type + '`')\n}\n\n/**\n * @typedef {import('hast').Nodes} Nodes\n * @typedef {import('hast').Parents} Parents\n * @typedef {import('hast').RootContent} RootContent\n *\n * @typedef {import('property-information').Schema} Schema\n *\n * @typedef {import('stringify-entities').Options} StringifyEntitiesOptions\n */\n\n\n/** @type {Options} */\nconst emptyOptions = {};\n\n/** @type {CharacterReferences} */\nconst emptyCharacterReferences = {};\n\n/** @type {Array<never>} */\nconst emptyChildren = [];\n\n/**\n * Serialize hast as HTML.\n *\n * @param {Array<RootContent> | Nodes} tree\n *   Tree to serialize.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized HTML.\n */\nfunction toHtml(tree, options) {\n  const options_ = options || emptyOptions;\n  const quote = options_.quote || '\"';\n  const alternative = quote === '\"' ? \"'\" : '\"';\n\n  if (quote !== '\"' && quote !== \"'\") {\n    throw new Error('Invalid quote `' + quote + '`, expected `\\'` or `\"`')\n  }\n\n  /** @type {State} */\n  const state = {\n    one,\n    all,\n    settings: {\n      omitOptionalTags: options_.omitOptionalTags || false,\n      allowParseErrors: options_.allowParseErrors || false,\n      allowDangerousCharacters: options_.allowDangerousCharacters || false,\n      quoteSmart: options_.quoteSmart || false,\n      preferUnquoted: options_.preferUnquoted || false,\n      tightAttributes: options_.tightAttributes || false,\n      upperDoctype: options_.upperDoctype || false,\n      tightDoctype: options_.tightDoctype || false,\n      bogusComments: options_.bogusComments || false,\n      tightCommaSeparatedLists: options_.tightCommaSeparatedLists || false,\n      tightSelfClosing: options_.tightSelfClosing || false,\n      collapseEmptyAttributes: options_.collapseEmptyAttributes || false,\n      allowDangerousHtml: options_.allowDangerousHtml || false,\n      voids: options_.voids || htmlVoidElements,\n      characterReferences:\n        options_.characterReferences || emptyCharacterReferences,\n      closeSelfClosing: options_.closeSelfClosing || false,\n      closeEmptyElements: options_.closeEmptyElements || false\n    },\n    schema: options_.space === 'svg' ? svg : html$2,\n    quote,\n    alternative\n  };\n\n  return state.one(\n    Array.isArray(tree) ? {type: 'root', children: tree} : tree,\n    undefined,\n    undefined\n  )\n}\n\n/**\n * Serialize a node.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Nodes} node\n *   Node to handle.\n * @param {number | undefined} index\n *   Index of `node` in `parent.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(node, index, parent) {\n  return handle(node, index, parent, this)\n}\n\n/**\n * Serialize all children of `parent`.\n *\n * @this {State}\n *   Info passed around about the current state.\n * @param {Parents | undefined} parent\n *   Parent whose children to serialize.\n * @returns {string}\n */\nfunction all(parent) {\n  /** @type {Array<string>} */\n  const results = [];\n  const children = (parent && parent.children) || emptyChildren;\n  let index = -1;\n\n  while (++index < children.length) {\n    results[index] = this.one(children[index], index, parent);\n  }\n\n  return results.join('')\n}\n\n/**\n * Get highlighted code in HTML.\n */\nfunction codeToHtml(internal, code, options) {\n    const context = {\n        meta: {},\n        options,\n        codeToHast: (_code, _options) => codeToHast(internal, _code, _options),\n    };\n    let result = toHtml(codeToHast(internal, code, options, context));\n    for (const transformer of options.transformers || [])\n        result = transformer.postprocess?.call(context, result, options) || result;\n    return result;\n}\n\nasync function main(init) {\n    let wasmMemory;\n    let buffer;\n    const binding = {};\n    function updateGlobalBufferAndViews(buf) {\n        buffer = buf;\n        binding.HEAPU8 = new Uint8Array(buf);\n        binding.HEAPU32 = new Uint32Array(buf);\n    }\n    function _emscripten_get_now() {\n        return typeof performance !== 'undefined' ? performance.now() : Date.now();\n    }\n    function _emscripten_memcpy_big(dest, src, num) {\n        binding.HEAPU8.copyWithin(dest, src, src + num);\n    }\n    function getHeapMax() {\n        return 2147483648;\n    }\n    function emscripten_realloc_buffer(size) {\n        try {\n            wasmMemory.grow((size - buffer.byteLength + 65535) >>> 16);\n            updateGlobalBufferAndViews(wasmMemory.buffer);\n            return 1;\n        }\n        catch (e) { }\n    }\n    function _emscripten_resize_heap(requestedSize) {\n        const oldSize = binding.HEAPU8.length;\n        requestedSize = requestedSize >>> 0;\n        const maxHeapSize = getHeapMax();\n        if (requestedSize > maxHeapSize)\n            return false;\n        const alignUp = (x, multiple) => x + ((multiple - (x % multiple)) % multiple);\n        for (let cutDown = 1; cutDown <= 4; cutDown *= 2) {\n            let overGrownHeapSize = oldSize * (1 + 0.2 / cutDown);\n            overGrownHeapSize = Math.min(overGrownHeapSize, requestedSize + 100663296);\n            const newSize = Math.min(maxHeapSize, alignUp(Math.max(requestedSize, overGrownHeapSize), 65536));\n            const replacement = emscripten_realloc_buffer(newSize);\n            if (replacement)\n                return true;\n        }\n        return false;\n    }\n    const asmLibraryArg = {\n        emscripten_get_now: _emscripten_get_now,\n        emscripten_memcpy_big: _emscripten_memcpy_big,\n        emscripten_resize_heap: _emscripten_resize_heap,\n        fd_write: () => 0,\n    };\n    async function createWasm() {\n        const info = {\n            env: asmLibraryArg,\n            wasi_snapshot_preview1: asmLibraryArg,\n        };\n        const exports = await init(info);\n        wasmMemory = exports.memory;\n        updateGlobalBufferAndViews(wasmMemory.buffer);\n        Object.assign(binding, exports);\n    }\n    await createWasm();\n    return binding;\n}\n\n/* ---------------------------------------------------------\n * Copyright (C) Microsoft Corporation. All rights reserved.\n *-------------------------------------------------------- */\nlet onigBinding = null;\nlet defaultDebugCall = false;\nfunction throwLastOnigError(onigBinding) {\n    throw new Error(onigBinding.UTF8ToString(onigBinding.getLastOnigError()));\n}\nclass UtfString {\n    static _utf8ByteLength(str) {\n        let result = 0;\n        for (let i = 0, len = str.length; i < len; i++) {\n            const charCode = str.charCodeAt(i);\n            let codepoint = charCode;\n            let wasSurrogatePair = false;\n            if (charCode >= 0xD800 && charCode <= 0xDBFF) {\n                // Hit a high surrogate, try to look for a matching low surrogate\n                if (i + 1 < len) {\n                    const nextCharCode = str.charCodeAt(i + 1);\n                    if (nextCharCode >= 0xDC00 && nextCharCode <= 0xDFFF) {\n                        // Found the matching low surrogate\n                        codepoint = (((charCode - 0xD800) << 10) + 0x10000) | (nextCharCode - 0xDC00);\n                        wasSurrogatePair = true;\n                    }\n                }\n            }\n            if (codepoint <= 0x7F)\n                result += 1;\n            else if (codepoint <= 0x7FF)\n                result += 2;\n            else if (codepoint <= 0xFFFF)\n                result += 3;\n            else\n                result += 4;\n            if (wasSurrogatePair)\n                i++;\n        }\n        return result;\n    }\n    utf16Length;\n    utf8Length;\n    utf16Value;\n    utf8Value;\n    utf16OffsetToUtf8;\n    utf8OffsetToUtf16;\n    constructor(str) {\n        const utf16Length = str.length;\n        const utf8Length = UtfString._utf8ByteLength(str);\n        const computeIndicesMapping = (utf8Length !== utf16Length);\n        const utf16OffsetToUtf8 = computeIndicesMapping ? new Uint32Array(utf16Length + 1) : null;\n        if (computeIndicesMapping)\n            utf16OffsetToUtf8[utf16Length] = utf8Length;\n        const utf8OffsetToUtf16 = computeIndicesMapping ? new Uint32Array(utf8Length + 1) : null;\n        if (computeIndicesMapping)\n            utf8OffsetToUtf16[utf8Length] = utf16Length;\n        const utf8Value = new Uint8Array(utf8Length);\n        let i8 = 0;\n        for (let i16 = 0; i16 < utf16Length; i16++) {\n            const charCode = str.charCodeAt(i16);\n            let codePoint = charCode;\n            let wasSurrogatePair = false;\n            if (charCode >= 0xD800 && charCode <= 0xDBFF) {\n                // Hit a high surrogate, try to look for a matching low surrogate\n                if (i16 + 1 < utf16Length) {\n                    const nextCharCode = str.charCodeAt(i16 + 1);\n                    if (nextCharCode >= 0xDC00 && nextCharCode <= 0xDFFF) {\n                        // Found the matching low surrogate\n                        codePoint = (((charCode - 0xD800) << 10) + 0x10000) | (nextCharCode - 0xDC00);\n                        wasSurrogatePair = true;\n                    }\n                }\n            }\n            if (computeIndicesMapping) {\n                utf16OffsetToUtf8[i16] = i8;\n                if (wasSurrogatePair)\n                    utf16OffsetToUtf8[i16 + 1] = i8;\n                if (codePoint <= 0x7F) {\n                    utf8OffsetToUtf16[i8 + 0] = i16;\n                }\n                else if (codePoint <= 0x7FF) {\n                    utf8OffsetToUtf16[i8 + 0] = i16;\n                    utf8OffsetToUtf16[i8 + 1] = i16;\n                }\n                else if (codePoint <= 0xFFFF) {\n                    utf8OffsetToUtf16[i8 + 0] = i16;\n                    utf8OffsetToUtf16[i8 + 1] = i16;\n                    utf8OffsetToUtf16[i8 + 2] = i16;\n                }\n                else {\n                    utf8OffsetToUtf16[i8 + 0] = i16;\n                    utf8OffsetToUtf16[i8 + 1] = i16;\n                    utf8OffsetToUtf16[i8 + 2] = i16;\n                    utf8OffsetToUtf16[i8 + 3] = i16;\n                }\n            }\n            if (codePoint <= 0x7F) {\n                utf8Value[i8++] = codePoint;\n            }\n            else if (codePoint <= 0x7FF) {\n                utf8Value[i8++] = 0b11000000 | ((codePoint & 0b00000000000000000000011111000000) >>> 6);\n                utf8Value[i8++] = 0b10000000 | ((codePoint & 0b00000000000000000000000000111111) >>> 0);\n            }\n            else if (codePoint <= 0xFFFF) {\n                utf8Value[i8++] = 0b11100000 | ((codePoint & 0b00000000000000001111000000000000) >>> 12);\n                utf8Value[i8++] = 0b10000000 | ((codePoint & 0b00000000000000000000111111000000) >>> 6);\n                utf8Value[i8++] = 0b10000000 | ((codePoint & 0b00000000000000000000000000111111) >>> 0);\n            }\n            else {\n                utf8Value[i8++] = 0b11110000 | ((codePoint & 0b00000000000111000000000000000000) >>> 18);\n                utf8Value[i8++] = 0b10000000 | ((codePoint & 0b00000000000000111111000000000000) >>> 12);\n                utf8Value[i8++] = 0b10000000 | ((codePoint & 0b00000000000000000000111111000000) >>> 6);\n                utf8Value[i8++] = 0b10000000 | ((codePoint & 0b00000000000000000000000000111111) >>> 0);\n            }\n            if (wasSurrogatePair)\n                i16++;\n        }\n        this.utf16Length = utf16Length;\n        this.utf8Length = utf8Length;\n        this.utf16Value = str;\n        this.utf8Value = utf8Value;\n        this.utf16OffsetToUtf8 = utf16OffsetToUtf8;\n        this.utf8OffsetToUtf16 = utf8OffsetToUtf16;\n    }\n    createString(onigBinding) {\n        const result = onigBinding.omalloc(this.utf8Length);\n        onigBinding.HEAPU8.set(this.utf8Value, result);\n        return result;\n    }\n}\nclass OnigString {\n    static LAST_ID = 0;\n    static _sharedPtr = 0; // a pointer to a string of 10000 bytes\n    static _sharedPtrInUse = false;\n    id = (++OnigString.LAST_ID);\n    _onigBinding;\n    content;\n    utf16Length;\n    utf8Length;\n    utf16OffsetToUtf8;\n    utf8OffsetToUtf16;\n    ptr;\n    constructor(str) {\n        if (!onigBinding)\n            throw new Error('Must invoke loadWasm first.');\n        this._onigBinding = onigBinding;\n        this.content = str;\n        const utfString = new UtfString(str);\n        this.utf16Length = utfString.utf16Length;\n        this.utf8Length = utfString.utf8Length;\n        this.utf16OffsetToUtf8 = utfString.utf16OffsetToUtf8;\n        this.utf8OffsetToUtf16 = utfString.utf8OffsetToUtf16;\n        if (this.utf8Length < 10000 && !OnigString._sharedPtrInUse) {\n            if (!OnigString._sharedPtr)\n                OnigString._sharedPtr = onigBinding.omalloc(10000);\n            OnigString._sharedPtrInUse = true;\n            onigBinding.HEAPU8.set(utfString.utf8Value, OnigString._sharedPtr);\n            this.ptr = OnigString._sharedPtr;\n        }\n        else {\n            this.ptr = utfString.createString(onigBinding);\n        }\n    }\n    convertUtf8OffsetToUtf16(utf8Offset) {\n        if (this.utf8OffsetToUtf16) {\n            if (utf8Offset < 0)\n                return 0;\n            if (utf8Offset > this.utf8Length)\n                return this.utf16Length;\n            return this.utf8OffsetToUtf16[utf8Offset];\n        }\n        return utf8Offset;\n    }\n    convertUtf16OffsetToUtf8(utf16Offset) {\n        if (this.utf16OffsetToUtf8) {\n            if (utf16Offset < 0)\n                return 0;\n            if (utf16Offset > this.utf16Length)\n                return this.utf8Length;\n            return this.utf16OffsetToUtf8[utf16Offset];\n        }\n        return utf16Offset;\n    }\n    dispose() {\n        if (this.ptr === OnigString._sharedPtr)\n            OnigString._sharedPtrInUse = false;\n        else\n            this._onigBinding.ofree(this.ptr);\n    }\n}\nclass OnigScanner {\n    _onigBinding;\n    _ptr;\n    constructor(patterns) {\n        if (!onigBinding)\n            throw new Error('Must invoke loadWasm first.');\n        const strPtrsArr = [];\n        const strLenArr = [];\n        for (let i = 0, len = patterns.length; i < len; i++) {\n            const utfString = new UtfString(patterns[i]);\n            strPtrsArr[i] = utfString.createString(onigBinding);\n            strLenArr[i] = utfString.utf8Length;\n        }\n        const strPtrsPtr = onigBinding.omalloc(4 * patterns.length);\n        onigBinding.HEAPU32.set(strPtrsArr, strPtrsPtr / 4);\n        const strLenPtr = onigBinding.omalloc(4 * patterns.length);\n        onigBinding.HEAPU32.set(strLenArr, strLenPtr / 4);\n        const scannerPtr = onigBinding.createOnigScanner(strPtrsPtr, strLenPtr, patterns.length);\n        for (let i = 0, len = patterns.length; i < len; i++)\n            onigBinding.ofree(strPtrsArr[i]);\n        onigBinding.ofree(strLenPtr);\n        onigBinding.ofree(strPtrsPtr);\n        if (scannerPtr === 0)\n            throwLastOnigError(onigBinding);\n        this._onigBinding = onigBinding;\n        this._ptr = scannerPtr;\n    }\n    dispose() {\n        this._onigBinding.freeOnigScanner(this._ptr);\n    }\n    findNextMatchSync(string, startPosition, arg) {\n        let debugCall = defaultDebugCall;\n        let options = 0 /* FindOption.None */;\n        if (typeof arg === 'number') {\n            if (arg & 8 /* FindOption.DebugCall */)\n                debugCall = true;\n            options = arg;\n        }\n        else if (typeof arg === 'boolean') {\n            debugCall = arg;\n        }\n        if (typeof string === 'string') {\n            string = new OnigString(string);\n            const result = this._findNextMatchSync(string, startPosition, debugCall, options);\n            string.dispose();\n            return result;\n        }\n        return this._findNextMatchSync(string, startPosition, debugCall, options);\n    }\n    _findNextMatchSync(string, startPosition, debugCall, options) {\n        const onigBinding = this._onigBinding;\n        let resultPtr;\n        if (debugCall)\n            resultPtr = onigBinding.findNextOnigScannerMatchDbg(this._ptr, string.id, string.ptr, string.utf8Length, string.convertUtf16OffsetToUtf8(startPosition), options);\n        else\n            resultPtr = onigBinding.findNextOnigScannerMatch(this._ptr, string.id, string.ptr, string.utf8Length, string.convertUtf16OffsetToUtf8(startPosition), options);\n        if (resultPtr === 0) {\n            // no match\n            return null;\n        }\n        const HEAPU32 = onigBinding.HEAPU32;\n        let offset = resultPtr / 4; // byte offset -> uint32 offset\n        const index = HEAPU32[offset++];\n        const count = HEAPU32[offset++];\n        const captureIndices = [];\n        for (let i = 0; i < count; i++) {\n            const beg = string.convertUtf8OffsetToUtf16(HEAPU32[offset++]);\n            const end = string.convertUtf8OffsetToUtf16(HEAPU32[offset++]);\n            captureIndices[i] = {\n                start: beg,\n                end,\n                length: end - beg,\n            };\n        }\n        return {\n            index,\n            captureIndices,\n        };\n    }\n}\nfunction isInstantiatorOptionsObject(dataOrOptions) {\n    return (typeof dataOrOptions.instantiator === 'function');\n}\nfunction isInstantiatorModule(dataOrOptions) {\n    return (typeof dataOrOptions.default === 'function');\n}\nfunction isDataOptionsObject(dataOrOptions) {\n    return (typeof dataOrOptions.data !== 'undefined');\n}\nfunction isResponse(dataOrOptions) {\n    return (typeof Response !== 'undefined' && dataOrOptions instanceof Response);\n}\nfunction isArrayBuffer(data) {\n    return (typeof ArrayBuffer !== 'undefined' && (data instanceof ArrayBuffer || ArrayBuffer.isView(data)))\n        // eslint-disable-next-line node/prefer-global/buffer\n        || (typeof Buffer !== 'undefined' && Buffer.isBuffer(data))\n        || (typeof SharedArrayBuffer !== 'undefined' && data instanceof SharedArrayBuffer)\n        || (typeof Uint32Array !== 'undefined' && data instanceof Uint32Array);\n}\nlet initPromise;\nfunction loadWasm(options) {\n    if (initPromise)\n        return initPromise;\n    async function _load() {\n        onigBinding = await main(async (info) => {\n            let instance = options;\n            instance = await instance;\n            if (typeof instance === 'function')\n                instance = await instance(info);\n            if (typeof instance === 'function')\n                instance = await instance(info);\n            if (isInstantiatorOptionsObject(instance)) {\n                instance = await instance.instantiator(info);\n            }\n            else if (isInstantiatorModule(instance)) {\n                instance = await instance.default(info);\n            }\n            else {\n                if (isDataOptionsObject(instance))\n                    instance = instance.data;\n                if (isResponse(instance)) {\n                    if (typeof WebAssembly.instantiateStreaming === 'function')\n                        instance = await _makeResponseStreamingLoader(instance)(info);\n                    else\n                        instance = await _makeResponseNonStreamingLoader(instance)(info);\n                }\n                else if (isArrayBuffer(instance)) {\n                    instance = await _makeArrayBufferLoader(instance)(info);\n                }\n            }\n            if ('instance' in instance)\n                instance = instance.instance;\n            if ('exports' in instance)\n                instance = instance.exports;\n            return instance;\n        });\n    }\n    initPromise = _load();\n    return initPromise;\n}\nfunction _makeArrayBufferLoader(data) {\n    return importObject => WebAssembly.instantiate(data, importObject);\n}\nfunction _makeResponseStreamingLoader(data) {\n    return importObject => WebAssembly.instantiateStreaming(data, importObject);\n}\nfunction _makeResponseNonStreamingLoader(data) {\n    return async (importObject) => {\n        const arrayBuffer = await data.arrayBuffer();\n        return WebAssembly.instantiate(arrayBuffer, importObject);\n    };\n}\nfunction createOnigString(str) {\n    return new OnigString(str);\n}\nfunction createOnigScanner(patterns) {\n    return new OnigScanner(patterns);\n}\n\n/**\n * https://github.com/microsoft/vscode/blob/f7f05dee53fb33fe023db2e06e30a89d3094488f/src/vs/platform/theme/common/colorRegistry.ts#L258-L268\n */\nconst VSCODE_FALLBACK_EDITOR_FG = { light: '#333333', dark: '#bbbbbb' };\nconst VSCODE_FALLBACK_EDITOR_BG = { light: '#fffffe', dark: '#1e1e1e' };\nconst RESOLVED_KEY = '__shiki_resolved';\n/**\n * Normalize a textmate theme to shiki theme\n */\nfunction normalizeTheme(rawTheme) {\n    // @ts-expect-error private field\n    if (rawTheme?.[RESOLVED_KEY])\n        return rawTheme;\n    const theme = {\n        ...rawTheme,\n    };\n    // Fallback settings\n    if (theme.tokenColors && !theme.settings) {\n        theme.settings = theme.tokenColors;\n        delete theme.tokenColors;\n    }\n    theme.type ||= 'dark';\n    theme.colorReplacements = { ...theme.colorReplacements };\n    theme.settings ||= [];\n    // Guess fg/bg colors\n    let { bg, fg } = theme;\n    if (!bg || !fg) {\n        /**\n         * First try:\n         * Theme might contain a global `tokenColor` without `name` or `scope`\n         * Used as default value for foreground/background\n         */\n        const globalSetting = theme.settings\n            ? theme.settings.find((s) => !s.name && !s.scope)\n            : undefined;\n        if (globalSetting?.settings?.foreground)\n            fg = globalSetting.settings.foreground;\n        if (globalSetting?.settings?.background)\n            bg = globalSetting.settings.background;\n        /**\n         * Second try:\n         * If there's no global `tokenColor` without `name` or `scope`\n         * Use `editor.foreground` and `editor.background`\n         */\n        if (!fg && theme?.colors?.['editor.foreground'])\n            fg = theme.colors['editor.foreground'];\n        if (!bg && theme?.colors?.['editor.background'])\n            bg = theme.colors['editor.background'];\n        /**\n         * Last try:\n         * If there's no fg/bg color specified in theme, use default\n         */\n        if (!fg)\n            fg = theme.type === 'light' ? VSCODE_FALLBACK_EDITOR_FG.light : VSCODE_FALLBACK_EDITOR_FG.dark;\n        if (!bg)\n            bg = theme.type === 'light' ? VSCODE_FALLBACK_EDITOR_BG.light : VSCODE_FALLBACK_EDITOR_BG.dark;\n        theme.fg = fg;\n        theme.bg = bg;\n    }\n    // Push a no-scope setting with fallback colors\n    if (!(theme.settings[0] && theme.settings[0].settings && !theme.settings[0].scope)) {\n        theme.settings.unshift({\n            settings: {\n                foreground: theme.fg,\n                background: theme.bg,\n            },\n        });\n    }\n    // Push non-hex colors to color replacements, as `vscode-textmate` doesn't support them\n    let replacementCount = 0;\n    const replacementMap = new Map();\n    function getReplacementColor(value) {\n        if (replacementMap.has(value))\n            return replacementMap.get(value);\n        replacementCount += 1;\n        const hex = `#${replacementCount.toString(16).padStart(8, '0').toLowerCase()}`;\n        if (theme.colorReplacements?.[`#${hex}`]) // already exists\n            return getReplacementColor(value);\n        replacementMap.set(value, hex);\n        return hex;\n    }\n    theme.settings = theme.settings.map((setting) => {\n        const replaceFg = setting.settings?.foreground && !setting.settings.foreground.startsWith('#');\n        const replaceBg = setting.settings?.background && !setting.settings.background.startsWith('#');\n        if (!replaceFg && !replaceBg)\n            return setting;\n        const clone = {\n            ...setting,\n            settings: {\n                ...setting.settings,\n            },\n        };\n        if (replaceFg) {\n            const replacement = getReplacementColor(setting.settings.foreground);\n            theme.colorReplacements[replacement] = setting.settings.foreground;\n            clone.settings.foreground = replacement;\n        }\n        if (replaceBg) {\n            const replacement = getReplacementColor(setting.settings.background);\n            theme.colorReplacements[replacement] = setting.settings.background;\n            clone.settings.background = replacement;\n        }\n        return clone;\n    });\n    for (const key of Object.keys(theme.colors || {})) {\n        // Only patch for known keys\n        if (key === 'editor.foreground' || key === 'editor.background' || key.startsWith('terminal.ansi')) {\n            if (!theme.colors[key]?.startsWith('#')) {\n                const replacement = getReplacementColor(theme.colors[key]);\n                theme.colorReplacements[replacement] = theme.colors[key];\n                theme.colors[key] = replacement;\n            }\n        }\n    }\n    Object.defineProperty(theme, RESOLVED_KEY, {\n        enumerable: false,\n        writable: false,\n        value: true,\n    });\n    return theme;\n}\n\nclass Registry extends Registry$1 {\n    _resolver;\n    _themes;\n    _langs;\n    _resolvedThemes = {};\n    _resolvedGrammars = {};\n    _langMap = {};\n    _langGraph = new Map();\n    alias = {};\n    constructor(_resolver, _themes, _langs) {\n        super(_resolver);\n        this._resolver = _resolver;\n        this._themes = _themes;\n        this._langs = _langs;\n        _themes.forEach(t => this.loadTheme(t));\n        _langs.forEach(l => this.loadLanguage(l));\n    }\n    getTheme(theme) {\n        if (typeof theme === 'string')\n            return this._resolvedThemes[theme];\n        else\n            return this.loadTheme(theme);\n    }\n    loadTheme(theme) {\n        const _theme = normalizeTheme(theme);\n        if (_theme.name)\n            this._resolvedThemes[_theme.name] = _theme;\n        return _theme;\n    }\n    getLoadedThemes() {\n        return Object.keys(this._resolvedThemes);\n    }\n    getGrammar(name) {\n        if (this.alias[name]) {\n            const resolved = new Set([name]);\n            while (this.alias[name]) {\n                name = this.alias[name];\n                if (resolved.has(name))\n                    throw new Error(`[shikiji] Circular alias \\`${Array.from(resolved).join(' -> ')} -> ${name}\\``);\n                resolved.add(name);\n            }\n        }\n        return this._resolvedGrammars[name];\n    }\n    async loadLanguage(lang) {\n        if (this.getGrammar(lang.name))\n            return;\n        const embeddedLazilyBy = new Set(Object.values(this._langMap).filter(i => i.embeddedLangsLazy?.includes(lang.name)));\n        this._resolver.addLanguage(lang);\n        const grammarConfig = {\n            balancedBracketSelectors: lang.balancedBracketSelectors || ['*'],\n            unbalancedBracketSelectors: lang.unbalancedBracketSelectors || [],\n        };\n        // @ts-expect-error Private members, set this to override the previous grammar (that can be a stub)\n        this._syncRegistry._rawGrammars.set(lang.scopeName, lang);\n        const g = await this.loadGrammarWithConfiguration(lang.scopeName, 1, grammarConfig);\n        this._resolvedGrammars[lang.name] = g;\n        if (lang.aliases) {\n            lang.aliases.forEach((alias) => {\n                this.alias[alias] = lang.name;\n            });\n        }\n        // If there is a language that embeds this language lazily, we need to reload it\n        if (embeddedLazilyBy.size) {\n            for (const e of embeddedLazilyBy) {\n                delete this._resolvedGrammars[e.name];\n                // @ts-expect-error clear cache\n                this._syncRegistry?._injectionGrammars?.delete(e.scopeName);\n                // @ts-expect-error clear cache\n                this._syncRegistry?._grammars?.delete(e.scopeName);\n                await this.loadLanguage(this._langMap[e.name]);\n            }\n        }\n    }\n    async init() {\n        this._themes.map(t => this.loadTheme(t));\n        await this.loadLanguages(this._langs);\n    }\n    async loadLanguages(langs) {\n        for (const lang of langs)\n            this.resolveEmbeddedLanguages(lang);\n        const langsGraphArray = Array.from(this._langGraph.entries());\n        const missingLangs = langsGraphArray.filter(([_, lang]) => !lang);\n        if (missingLangs.length) {\n            const dependents = langsGraphArray\n                .filter(([_, lang]) => lang && lang.embeddedLangs?.some(l => missingLangs.map(([name]) => name).includes(l)))\n                .filter(lang => !missingLangs.includes(lang));\n            throw new Error(`[shikiji] Missing languages ${missingLangs.map(([name]) => `\\`${name}\\``).join(', ')}, required by ${dependents.map(([name]) => `\\`${name}\\``).join(', ')}`);\n        }\n        for (const [_, lang] of langsGraphArray)\n            this._resolver.addLanguage(lang);\n        for (const [_, lang] of langsGraphArray)\n            await this.loadLanguage(lang);\n    }\n    getLoadedLanguages() {\n        return Object.keys({ ...this._resolvedGrammars, ...this.alias });\n    }\n    resolveEmbeddedLanguages(lang) {\n        this._langMap[lang.name] = lang;\n        this._langGraph.set(lang.name, lang);\n        if (lang.embeddedLangs) {\n            for (const embeddedLang of lang.embeddedLangs)\n                this._langGraph.set(embeddedLang, this._langMap[embeddedLang]);\n        }\n    }\n}\n\nclass Resolver {\n    _langs = new Map();\n    _scopeToLang = new Map();\n    _injections = new Map();\n    _onigLibPromise;\n    constructor(onigLibPromise, langs) {\n        this._onigLibPromise = onigLibPromise;\n        langs.forEach(i => this.addLanguage(i));\n    }\n    get onigLib() {\n        return this._onigLibPromise;\n    }\n    getLangRegistration(langIdOrAlias) {\n        return this._langs.get(langIdOrAlias);\n    }\n    async loadGrammar(scopeName) {\n        return this._scopeToLang.get(scopeName);\n    }\n    addLanguage(l) {\n        this._langs.set(l.name, l);\n        if (l.aliases) {\n            l.aliases.forEach((a) => {\n                this._langs.set(a, l);\n            });\n        }\n        this._scopeToLang.set(l.scopeName, l);\n        if (l.injectTo) {\n            l.injectTo.forEach((i) => {\n                if (!this._injections.get(i))\n                    this._injections.set(i, []);\n                this._injections.get(i).push(l.scopeName);\n            });\n        }\n    }\n    getInjections(scopeName) {\n        const scopeParts = scopeName.split('.');\n        let injections = [];\n        for (let i = 1; i <= scopeParts.length; i++) {\n            const subScopeName = scopeParts.slice(0, i).join('.');\n            injections = [...injections, ...(this._injections.get(subScopeName) || [])];\n        }\n        return injections;\n    }\n}\n\nlet _defaultWasmLoader;\n/**\n * Set the default wasm loader for `loadWasm`.\n * @internal\n */\nfunction setDefaultWasmLoader(_loader) {\n    _defaultWasmLoader = _loader;\n}\n/**\n * Get the minimal shiki context for rendering.\n */\nasync function getShikiInternal(options = {}) {\n    async function normalizeGetter(p) {\n        return Promise.resolve(typeof p === 'function' ? p() : p).then(r => r.default || r);\n    }\n    async function resolveLangs(langs) {\n        return Array.from(new Set((await Promise.all(langs.map(async (lang) => await normalizeGetter(lang).then(r => Array.isArray(r) ? r : [r])))).flat()));\n    }\n    const wasmLoader = options.loadWasm || _defaultWasmLoader;\n    const [themes, langs,] = await Promise.all([\n        Promise.all((options.themes || []).map(normalizeGetter)).then(r => r.map(normalizeTheme)),\n        resolveLangs(options.langs || []),\n        wasmLoader ? loadWasm(wasmLoader) : undefined,\n    ]);\n    const resolver = new Resolver(Promise.resolve({\n        createOnigScanner(patterns) {\n            return createOnigScanner(patterns);\n        },\n        createOnigString(s) {\n            return createOnigString(s);\n        },\n    }), langs);\n    const _registry = new Registry(resolver, themes, langs);\n    Object.assign(_registry.alias, options.langAlias);\n    await _registry.init();\n    let _lastTheme;\n    function getLangGrammar(name) {\n        const _lang = _registry.getGrammar(name);\n        if (!_lang)\n            throw new Error(`[shikiji] Language \\`${name}\\` not found, you may need to load it first`);\n        return _lang;\n    }\n    function getTheme(name) {\n        if (name === 'none')\n            return { bg: '', fg: '', name: 'none', settings: [], type: 'dark' };\n        const _theme = _registry.getTheme(name);\n        if (!_theme)\n            throw new Error(`[shikiji] Theme \\`${name}\\` not found, you may need to load it first`);\n        return _theme;\n    }\n    function setTheme(name) {\n        const theme = getTheme(name);\n        if (_lastTheme !== name) {\n            _registry.setTheme(theme);\n            _lastTheme = name;\n        }\n        const colorMap = _registry.getColorMap();\n        return {\n            theme,\n            colorMap,\n        };\n    }\n    function getLoadedThemes() {\n        return _registry.getLoadedThemes();\n    }\n    function getLoadedLanguages() {\n        return _registry.getLoadedLanguages();\n    }\n    async function loadLanguage(...langs) {\n        await _registry.loadLanguages(await resolveLangs(langs));\n    }\n    async function loadTheme(...themes) {\n        await Promise.all(themes.map(async (theme) => isSpecialTheme(theme)\n            ? null\n            : _registry.loadTheme(await normalizeGetter(theme))));\n    }\n    function updateAlias(alias) {\n        Object.assign(_registry.alias, alias);\n    }\n    function getAlias() {\n        return _registry.alias;\n    }\n    return {\n        setTheme,\n        getTheme,\n        getLangGrammar,\n        getLoadedThemes,\n        getLoadedLanguages,\n        getAlias,\n        updateAlias,\n        loadLanguage,\n        loadTheme,\n    };\n}\n\n/**\n * Create a Shikiji core highlighter instance, with no languages or themes bundled.\n * Wasm and each language and theme must be loaded manually.\n *\n * @see http://shikiji.netlify.app/guide/install#fine-grained-bundle\n */\nasync function getHighlighterCore(options = {}) {\n    const internal = await getShikiInternal(options);\n    return {\n        codeToThemedTokens: (code, options) => codeToThemedTokens(internal, code, options),\n        codeToTokensWithThemes: (code, options) => codeToTokensWithThemes(internal, code, options),\n        codeToHast: (code, options) => codeToHast(internal, code, options),\n        codeToHtml: (code, options) => codeToHtml(internal, code, options),\n        loadLanguage: internal.loadLanguage,\n        loadTheme: internal.loadTheme,\n        getTheme: internal.getTheme,\n        getLangGrammar: internal.getLangGrammar,\n        setTheme: internal.setTheme,\n        getLoadedThemes: internal.getLoadedThemes,\n        getLoadedLanguages: internal.getLoadedLanguages,\n        getInternalContext: () => internal,\n    };\n}\n\n/**\n * Create a `getHighlighter` function with bundled themes and languages.\n *\n * @param bundledLanguages\n * @param bundledThemes\n * @param loadWasm\n */\nfunction createdBundledHighlighter(bundledLanguages, bundledThemes, loadWasm) {\n    async function getHighlighter(options = {}) {\n        function resolveLang(lang) {\n            if (typeof lang === 'string') {\n                if (isSpecialLang(lang))\n                    return [];\n                const bundle = bundledLanguages[lang];\n                if (!bundle)\n                    throw new Error(`[shikiji] Language \\`${lang}\\` is not built-in.`);\n                return bundle;\n            }\n            return lang;\n        }\n        function resolveTheme(theme) {\n            if (isSpecialTheme(theme))\n                return 'none';\n            if (typeof theme === 'string') {\n                const bundle = bundledThemes[theme];\n                if (!bundle)\n                    throw new Error(`[shikiji] Theme \\`${theme}\\` is not built-in.`);\n                return bundle;\n            }\n            return theme;\n        }\n        const _themes = (options.themes ?? []).map(i => resolveTheme(i));\n        const langs = (options.langs ?? [])\n            .map(i => resolveLang(i));\n        const core = await getHighlighterCore({\n            ...options,\n            themes: _themes,\n            langs,\n            loadWasm,\n        });\n        return {\n            ...core,\n            loadLanguage(...langs) {\n                return core.loadLanguage(...langs.map(resolveLang));\n            },\n            loadTheme(...themes) {\n                return core.loadTheme(...themes.map(resolveTheme));\n            },\n        };\n    }\n    return getHighlighter;\n}\nfunction createSingletonShorthands(getHighlighter) {\n    let _shiki;\n    async function _getHighlighter(options = {}) {\n        if (!_shiki) {\n            _shiki = getHighlighter({\n                themes: toArray(options.theme || []),\n                langs: toArray(options.lang || []),\n            });\n            return _shiki;\n        }\n        else {\n            const s = await _shiki;\n            await Promise.all([\n                s.loadTheme(...toArray(options.theme || [])),\n                s.loadLanguage(...toArray(options.lang || [])),\n            ]);\n            return s;\n        }\n    }\n    return {\n        getSingletonHighlighter: () => _getHighlighter(),\n        async codeToHtml(code, options) {\n            const shiki = await _getHighlighter({\n                lang: options.lang,\n                theme: ('theme' in options ? [options.theme] : Object.values(options.themes)),\n            });\n            return shiki.codeToHtml(code, options);\n        },\n        async codeToHast(code, options) {\n            const shiki = await _getHighlighter({\n                lang: options.lang,\n                theme: ('theme' in options ? [options.theme] : Object.values(options.themes)),\n            });\n            return shiki.codeToHast(code, options);\n        },\n        async codeToThemedTokens(code, options) {\n            const shiki = await _getHighlighter(options);\n            return shiki.codeToThemedTokens(code, options);\n        },\n        async codeToTokensWithThemes(code, options) {\n            const shiki = await _getHighlighter({\n                lang: options.lang,\n                theme: Object.values(options.themes).filter(Boolean),\n            });\n            return shiki.codeToTokensWithThemes(code, options);\n        },\n    };\n}\n\nexport { FontStyle, addClassToHast, applyColorReplacements, codeToHast, codeToHtml, codeToThemedTokens, codeToTokensWithThemes, createSingletonShorthands, createdBundledHighlighter, getHighlighterCore, getShikiInternal, toHtml as hastToHtml, isNoneTheme, isPlainLang, isPlaintext, isSpecialLang, isSpecialTheme, loadWasm, normalizeTheme, setDefaultWasmLoader, splitLines, splitToken, toArray, tokenizeAnsiWithTheme, tokenizeWithTheme };\n", "import getWasm from 'shikiji/wasm';\nexport { default as getWasmInlined } from 'shikiji/wasm';\nimport { bundledLanguages } from './langs.mjs';\nexport { bundledLanguagesAlias, bundledLanguagesBase, bundledLanguagesInfo } from './langs.mjs';\nimport { bundledThemes } from './themes.mjs';\nexport { bundledThemesInfo } from './themes.mjs';\nimport { createdBundledHighlighter, createSingletonShorthands } from 'shikiji-core';\nexport * from 'shikiji-core';\n\nconst getHighlighter = /* @__PURE__ */ createdBundledHighlighter(\n  bundledLanguages,\n  bundledThemes,\n  getWasm\n);\nconst {\n  codeToHtml,\n  codeToHast,\n  codeToThemedTokens,\n  codeToTokensWithThemes,\n  getSingletonHighlighter\n} = /* @__PURE__ */ createSingletonShorthands(\n  getHighlighter\n);\n\nexport { bundledLanguages, bundledThemes, codeToHast, codeToHtml, codeToThemedTokens, codeToTokensWithThemes, getHighlighter, getSingletonHighlighter };\n", "function createCssVariablesTheme(options = {}) {\n  const {\n    name = \"css-variables\",\n    variablePrefix = \"--shiki-\",\n    fontStyle = true\n  } = options;\n  const variable = (name2) => {\n    if (options.variableDefaults?.[name2])\n      return `var(${variablePrefix}${name2}, ${options.variableDefaults[name2]})`;\n    return `var(${variablePrefix}${name2})`;\n  };\n  const theme = {\n    name,\n    type: \"dark\",\n    colors: {\n      \"editor.foreground\": variable(\"foreground\"),\n      \"editor.background\": variable(\"background\"),\n      \"terminal.ansiBlack\": variable(\"ansi-black\"),\n      \"terminal.ansiRed\": variable(\"ansi-red\"),\n      \"terminal.ansiGreen\": variable(\"ansi-green\"),\n      \"terminal.ansiYellow\": variable(\"ansi-yellow\"),\n      \"terminal.ansiBlue\": variable(\"ansi-blue\"),\n      \"terminal.ansiMagenta\": variable(\"ansi-magenta\"),\n      \"terminal.ansiCyan\": variable(\"ansi-cyan\"),\n      \"terminal.ansiWhite\": variable(\"ansi-white\"),\n      \"terminal.ansiBrightBlack\": variable(\"ansi-bright-black\"),\n      \"terminal.ansiBrightRed\": variable(\"ansi-bright-red\"),\n      \"terminal.ansiBrightGreen\": variable(\"ansi-bright-green\"),\n      \"terminal.ansiBrightYellow\": variable(\"ansi-bright-yellow\"),\n      \"terminal.ansiBrightBlue\": variable(\"ansi-bright-blue\"),\n      \"terminal.ansiBrightMagenta\": variable(\"ansi-bright-magenta\"),\n      \"terminal.ansiBrightCyan\": variable(\"ansi-bright-cyan\"),\n      \"terminal.ansiBrightWhite\": variable(\"ansi-bright-white\")\n    },\n    tokenColors: [\n      {\n        scope: [\n          \"keyword.operator.accessor\",\n          \"meta.group.braces.round.function.arguments\",\n          \"meta.template.expression\",\n          \"markup.fenced_code meta.embedded.block\"\n        ],\n        settings: {\n          foreground: variable(\"foreground\")\n        }\n      },\n      {\n        scope: \"emphasis\",\n        settings: {\n          fontStyle: \"italic\"\n        }\n      },\n      {\n        scope: [\"strong\", \"markup.heading.markdown\", \"markup.bold.markdown\"],\n        settings: {\n          fontStyle: \"bold\"\n        }\n      },\n      {\n        scope: [\"markup.italic.markdown\"],\n        settings: {\n          fontStyle: \"italic\"\n        }\n      },\n      {\n        scope: \"meta.link.inline.markdown\",\n        settings: {\n          fontStyle: \"underline\",\n          foreground: variable(\"token-link\")\n        }\n      },\n      {\n        scope: [\"string\", \"markup.fenced_code\", \"markup.inline\"],\n        settings: {\n          foreground: variable(\"token-string\")\n        }\n      },\n      {\n        scope: [\"comment\", \"string.quoted.docstring.multi\"],\n        settings: {\n          foreground: variable(\"token-comment\")\n        }\n      },\n      {\n        scope: [\n          \"constant.numeric\",\n          \"constant.language\",\n          \"constant.other.placeholder\",\n          \"constant.character.format.placeholder\",\n          \"variable.language.this\",\n          \"variable.other.object\",\n          \"variable.other.class\",\n          \"variable.other.constant\",\n          \"meta.property-name\",\n          \"meta.property-value\",\n          \"support\"\n        ],\n        settings: {\n          foreground: variable(\"token-constant\")\n        }\n      },\n      {\n        scope: [\n          \"keyword\",\n          \"storage.modifier\",\n          \"storage.type\",\n          \"storage.control.clojure\",\n          \"entity.name.function.clojure\",\n          \"entity.name.tag.yaml\",\n          \"support.function.node\",\n          \"support.type.property-name.json\",\n          \"punctuation.separator.key-value\",\n          \"punctuation.definition.template-expression\"\n        ],\n        settings: {\n          foreground: variable(\"token-keyword\")\n        }\n      },\n      {\n        scope: \"variable.parameter.function\",\n        settings: {\n          foreground: variable(\"token-parameter\")\n        }\n      },\n      {\n        scope: [\n          \"support.function\",\n          \"entity.name.type\",\n          \"entity.other.inherited-class\",\n          \"meta.function-call\",\n          \"meta.instance.constructor\",\n          \"entity.other.attribute-name\",\n          \"entity.name.function\",\n          \"constant.keyword.clojure\"\n        ],\n        settings: {\n          foreground: variable(\"token-function\")\n        }\n      },\n      {\n        scope: [\n          \"entity.name.tag\",\n          \"string.quoted\",\n          \"string.regexp\",\n          \"string.interpolated\",\n          \"string.template\",\n          \"string.unquoted.plain.out.yaml\",\n          \"keyword.other.template\"\n        ],\n        settings: {\n          foreground: variable(\"token-string-expression\")\n        }\n      },\n      {\n        scope: [\n          \"punctuation.definition.arguments\",\n          \"punctuation.definition.dict\",\n          \"punctuation.separator\",\n          \"meta.function-call.arguments\"\n        ],\n        settings: {\n          foreground: variable(\"token-punctuation\")\n        }\n      },\n      {\n        // [Custom] Markdown links\n        scope: [\n          \"markup.underline.link\",\n          \"punctuation.definition.metadata.markdown\"\n        ],\n        settings: {\n          foreground: variable(\"token-link\")\n        }\n      },\n      {\n        // [Custom] Markdown list\n        scope: [\"beginning.punctuation.definition.list.markdown\"],\n        settings: {\n          foreground: variable(\"token-string\")\n        }\n      },\n      {\n        // [Custom] Markdown punctuation definition brackets\n        scope: [\n          \"punctuation.definition.string.begin.markdown\",\n          \"punctuation.definition.string.end.markdown\",\n          \"string.other.link.title.markdown\",\n          \"string.other.link.description.markdown\"\n        ],\n        settings: {\n          foreground: variable(\"token-keyword\")\n        }\n      }\n    ]\n  };\n  if (!fontStyle) {\n    theme.tokenColors = theme.tokenColors?.map((tokenColor) => {\n      if (tokenColor.settings?.fontStyle)\n        delete tokenColor.settings.fontStyle;\n      return tokenColor;\n    });\n  }\n  return theme;\n}\n\nexport { createCssVariablesTheme };\n"], "mappings": ";;;;;AAAA,IAAM,UAAU,OAAO,SAAS;AAE5B,QAAM,SAAS,MAAM,OAAO,oBAAY,EAAE,KAAK,OAAK,EAAE,OAAO;AAC7D,SAAO,YAAY,YAAY,QAAQ,IAAI,EAAE,KAAK,UAAQ,KAAK,SAAS,OAAO;AACnF;;;ACJA,IAAM,uBAAuB;AAAA,EAC3B;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,8BAA4B;AAAA,EACrD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,4BAA0B,EAAE,KAAK,SAAU,GAAG;AAAE,aAAO,EAAE;AAAA,IAAG,CAAC;AAAA,EACtF;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,0BAAwB;AAAA,EACjD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,2BAAyB;AAAA,EAClD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,yBAAuB;AAAA,EAChD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,yBAAuB;AAAA,EAChD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,iBAAe;AAAA,EACxC;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,uBAAqB;AAAA,EAC9C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,uBAAqB;AAAA,EAC9C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,uBAAqB;AAAA,EAC9C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,uBAAqB;AAAA,EAC9C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,iBAAe;AAAA,EACxC;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,2BAAyB;AAAA,EAClD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,0BAAwB;AAAA,EACjD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,wBAAsB;AAAA,EAC/C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,wBAAsB;AAAA,EAC/C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,uBAAqB;AAAA,EAC9C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,0BAAwB;AAAA,EACjD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,0BAAwB;AAAA,EACjD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,0BAAwB;AAAA,EACjD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,0BAAwB;AAAA,EACjD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,uBAAqB;AAAA,EAC9C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,kBAAgB;AAAA,EACzC;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,uBAAqB;AAAA,EAC9C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,0BAAwB;AAAA,EACjD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,uBAAqB;AAAA,EAC9C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,0BAAwB;AAAA,EACjD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,uBAAqB;AAAA,EAC9C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,wBAAsB;AAAA,EAC/C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,uBAAqB;AAAA,EAC9C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,wBAAsB;AAAA,EAC/C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,uBAAqB;AAAA,EAC9C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,2BAAyB;AAAA,EAClD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,6BAA2B;AAAA,EACpD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,uBAAqB;AAAA,EAC9C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,0BAAwB;AAAA,EACjD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,0BAAwB;AAAA,EACjD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,0BAAwB;AAAA,EACjD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,iBAAe;AAAA,EACxC;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,yBAAuB;AAAA,EAChD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,2BAAyB;AAAA,EAClD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,4BAA0B;AAAA,EACnD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,yBAAuB;AAAA,EAChD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,wBAAsB;AAAA,EAC/C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,0BAAwB;AAAA,EACjD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,8BAA4B;AAAA,EACrD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,0BAAwB;AAAA,EACjD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,iBAAe;AAAA,EACxC;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,kBAAgB;AAAA,EACzC;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,uBAAqB;AAAA,EAC9C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,MACA;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,wBAAsB;AAAA,EAC/C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,qBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,sBAAoB;AAAA,EAC7C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,uBAAqB;AAAA,EAC9C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,MACT;AAAA,IACF;AAAA,IACA,UAAU,MAAM,OAAO,oBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,yBAAuB;AAAA,EAChD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAiB;AAAA,EAC1C;AACF;AACA,IAAM,uBAAuB,OAAO,YAAY,qBAAqB,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AACjG,IAAM,wBAAwB,OAAO,YAAY,qBAAqB,QAAQ,CAAC,MAAG;AAhgClF;AAggCqF,kBAAE,YAAF,mBAAW,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,OAAM,CAAC;AAAA,CAAC,CAAC;AAChI,IAAM,mBAAmB;AAAA,EACvB,GAAG;AAAA,EACH,GAAG;AACL;;;ACpgCA,IAAM,oBAAoB;AAAA,EACxB;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,0BAAyB;AAAA,EAClD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,wBAAuB;AAAA,EAChD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,wBAAuB;AAAA,EAChD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,iCAAgC;AAAA,EACzD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,gCAA+B;AAAA,EACxD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oCAAmC;AAAA,EAC5D;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,gCAA+B;AAAA,EACxD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,yBAAwB;AAAA,EACjD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,uBAAsB;AAAA,EAC/C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,4BAA2B;AAAA,EACpD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,2BAA0B;AAAA,EACnD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,kCAAiC;AAAA,EAC1D;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,4BAA2B;AAAA,EACpD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,0BAAyB;AAAA,EAClD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,8BAA6B;AAAA,EACtD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,qCAAoC;AAAA,EAC7D;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,sCAAqC;AAAA,EAC9D;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oCAAmC;AAAA,EAC5D;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,wCAAuC;AAAA,EAChE;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,wBAAuB;AAAA,EAChD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,yBAAwB;AAAA,EACjD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,uBAAsB;AAAA,EAC/C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,yBAAwB;AAAA,EACjD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,oBAAmB;AAAA,EAC5C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,4BAA2B;AAAA,EACpD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,0BAAyB;AAAA,EAClD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,mBAAkB;AAAA,EAC3C;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,yBAAwB;AAAA,EACjD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,8BAA6B;AAAA,EACtD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,8BAA6B;AAAA,EACtD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,0BAAyB;AAAA,EAClD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,2BAA0B;AAAA,EACnD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,8BAA6B;AAAA,EACtD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,+BAA8B;AAAA,EACvD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,4BAA2B;AAAA,EACpD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,2BAA0B;AAAA,EACnD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,6BAA4B;AAAA,EACrD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,4BAA2B;AAAA,EACpD;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,UAAU,MAAM,OAAO,6BAA4B;AAAA,EACrD;AACF;AACA,IAAM,gBAAgB,OAAO,YAAY,kBAAkB,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;;;AC5OvF,IAAI;AAAA,CACH,SAAUA,YAAW;AAClB,EAAAA,WAAUA,WAAU,QAAQ,IAAI,EAAE,IAAI;AACtC,EAAAA,WAAUA,WAAU,MAAM,IAAI,CAAC,IAAI;AACnC,EAAAA,WAAUA,WAAU,QAAQ,IAAI,CAAC,IAAI;AACrC,EAAAA,WAAUA,WAAU,MAAM,IAAI,CAAC,IAAI;AACnC,EAAAA,WAAUA,WAAU,WAAW,IAAI,CAAC,IAAI;AAC5C,GAAG,cAAc,YAAY,CAAC,EAAE;;;CCF/B;AAAA,EACG,aAAc,OAAO,YAAY,eAAe,CAAC,CAAC,QAAQ,IAAI,uBAAuB;AACzF;AAKA,IAAI;AAAA,CACH,SAAUC,yBAAwB;AAC/B,WAAS,YAAY,wBAAwB;AACzC,WAAO,uBAAuB,SAAS,CAAC,EAAE,SAAS,IAAI,GAAG;AAAA,EAC9D;AACA,EAAAA,wBAAuB,cAAc;AACrC,WAAS,MAAM,wBAAwB;AACnC,UAAM,aAAaA,wBAAuB,cAAc,sBAAsB;AAC9E,UAAM,YAAYA,wBAAuB,aAAa,sBAAsB;AAC5E,UAAM,YAAYA,wBAAuB,aAAa,sBAAsB;AAC5E,UAAM,aAAaA,wBAAuB,cAAc,sBAAsB;AAC9E,UAAM,aAAaA,wBAAuB,cAAc,sBAAsB;AAC9E,YAAQ,IAAI;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL;AACA,EAAAA,wBAAuB,QAAQ;AAC/B,WAAS,cAAc,wBAAwB;AAC3C,YAAS,yBAAyB,SAC9B;AAAA,EACR;AACA,EAAAA,wBAAuB,gBAAgB;AACvC,WAAS,aAAa,wBAAwB;AAC1C,YAAS,yBAAyB,SAC9B;AAAA,EACR;AACA,EAAAA,wBAAuB,eAAe;AACtC,WAAS,yBAAyB,wBAAwB;AACtD,YAAQ,yBAAyB,UAA8D;AAAA,EACnG;AACA,EAAAA,wBAAuB,2BAA2B;AAClD,WAAS,aAAa,wBAAwB;AAC1C,YAAS,yBAAyB,WAC9B;AAAA,EACR;AACA,EAAAA,wBAAuB,eAAe;AACtC,WAAS,cAAc,wBAAwB;AAC3C,YAAS,yBAAyB,cAC9B;AAAA,EACR;AACA,EAAAA,wBAAuB,gBAAgB;AACvC,WAAS,cAAc,wBAAwB;AAC3C,YAAS,yBAAyB,gBAC9B;AAAA,EACR;AACA,EAAAA,wBAAuB,gBAAgB;AAKvC,WAAS,IAAI,wBAAwB,YAAY,WAAWC,2BAA0B,WAAW,YAAY,YAAY;AACrH,QAAI,cAAcD,wBAAuB,cAAc,sBAAsB;AAC7E,QAAI,aAAaA,wBAAuB,aAAa,sBAAsB;AAC3E,QAAI,+BAA+BA,wBAAuB,yBAAyB,sBAAsB,IAAI,IAAI;AACjH,QAAI,aAAaA,wBAAuB,aAAa,sBAAsB;AAC3E,QAAI,cAAcA,wBAAuB,cAAc,sBAAsB;AAC7E,QAAI,cAAcA,wBAAuB,cAAc,sBAAsB;AAC7E,QAAI,eAAe,GAAG;AAClB,oBAAc;AAAA,IAClB;AACA,QAAI,cAAc,GAA0C;AACxD,mBAAa,sBAAsB,SAAS;AAAA,IAChD;AACA,QAAIC,8BAA6B,MAAM;AACnC,qCAA+BA,4BAA2B,IAAI;AAAA,IAClE;AACA,QAAI,cAAc,IAA2B;AACzC,mBAAa;AAAA,IACjB;AACA,QAAI,eAAe,GAAG;AAClB,oBAAc;AAAA,IAClB;AACA,QAAI,eAAe,GAAG;AAClB,oBAAc;AAAA,IAClB;AACA,YAAU,eAAe,IACpB,cAAc,IACd,gCACG,KACH,cAAc,KACd,eAAe,KACf,eAAe,QAChB;AAAA,EACR;AACA,EAAAD,wBAAuB,MAAM;AACjC,GAAG,2BAA2B,yBAAyB,CAAC,EAAE;AAC1D,SAAS,oBAAoB,cAAc;AACvC,SAAO;AACX;AACA,SAAS,sBAAsB,cAAc;AACzC,SAAO;AACX;AAKA,SAAS,eAAe,UAAU,aAAa;AAC3C,QAAM,UAAU,CAAC;AACjB,QAAM,YAAY,aAAa,QAAQ;AACvC,MAAI,QAAQ,UAAU,KAAK;AAC3B,SAAO,UAAU,MAAM;AACnB,QAAI,WAAW;AACf,QAAI,MAAM,WAAW,KAAK,MAAM,OAAO,CAAC,MAAM,KAAK;AAC/C,cAAQ,MAAM,OAAO,CAAC,GAAG;AAAA,QACrB,KAAK;AACD,qBAAW;AACX;AAAA,QACJ,KAAK;AACD,qBAAW;AACX;AAAA,QACJ;AACI,kBAAQ,IAAI,oBAAoB,KAAK,oBAAoB;AAAA,MACjE;AACA,cAAQ,UAAU,KAAK;AAAA,IAC3B;AACA,QAAI,UAAU,iBAAiB;AAC/B,YAAQ,KAAK,EAAE,SAAS,SAAS,CAAC;AAClC,QAAI,UAAU,KAAK;AACf;AAAA,IACJ;AACA,YAAQ,UAAU,KAAK;AAAA,EAC3B;AACA,SAAO;AACP,WAAS,eAAe;AACpB,QAAI,UAAU,KAAK;AACf,cAAQ,UAAU,KAAK;AACvB,YAAM,qBAAqB,aAAa;AACxC,aAAO,kBAAgB,CAAC,CAAC,sBAAsB,CAAC,mBAAmB,YAAY;AAAA,IACnF;AACA,QAAI,UAAU,KAAK;AACf,cAAQ,UAAU,KAAK;AACvB,YAAM,sBAAsB,qBAAqB;AACjD,UAAI,UAAU,KAAK;AACf,gBAAQ,UAAU,KAAK;AAAA,MAC3B;AACA,aAAO;AAAA,IACX;AACA,QAAI,aAAa,KAAK,GAAG;AACrB,YAAM,cAAc,CAAC;AACrB,SAAG;AACC,oBAAY,KAAK,KAAK;AACtB,gBAAQ,UAAU,KAAK;AAAA,MAC3B,SAAS,aAAa,KAAK;AAC3B,aAAO,kBAAgB,YAAY,aAAa,YAAY;AAAA,IAChE;AACA,WAAO;AAAA,EACX;AACA,WAAS,mBAAmB;AACxB,UAAM,WAAW,CAAC;AAClB,QAAI,UAAU,aAAa;AAC3B,WAAO,SAAS;AACZ,eAAS,KAAK,OAAO;AACrB,gBAAU,aAAa;AAAA,IAC3B;AACA,WAAO,kBAAgB,SAAS,MAAM,CAAAE,aAAWA,SAAQ,YAAY,CAAC;AAAA,EAC1E;AACA,WAAS,uBAAuB;AAC5B,UAAM,WAAW,CAAC;AAClB,QAAI,UAAU,iBAAiB;AAC/B,WAAO,SAAS;AACZ,eAAS,KAAK,OAAO;AACrB,UAAI,UAAU,OAAO,UAAU,KAAK;AAChC,WAAG;AACC,kBAAQ,UAAU,KAAK;AAAA,QAC3B,SAAS,UAAU,OAAO,UAAU;AAAA,MACxC,OACK;AACD;AAAA,MACJ;AACA,gBAAU,iBAAiB;AAAA,IAC/B;AACA,WAAO,kBAAgB,SAAS,KAAK,CAAAA,aAAWA,SAAQ,YAAY,CAAC;AAAA,EACzE;AACJ;AACA,SAAS,aAAa,OAAO;AACzB,SAAO,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,MAAM,UAAU;AAC9C;AACA,SAAS,aAAa,OAAO;AACzB,MAAI,QAAQ;AACZ,MAAI,QAAQ,MAAM,KAAK,KAAK;AAC5B,SAAO;AAAA,IACH,MAAM,MAAM;AACR,UAAI,CAAC,OAAO;AACR,eAAO;AAAA,MACX;AACA,YAAM,MAAM,MAAM,CAAC;AACnB,cAAQ,MAAM,KAAK,KAAK;AACxB,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAKA,SAAS,kBAAkB,KAAK;AAC5B,MAAI,OAAO,IAAI,YAAY,YAAY;AACnC,QAAI,QAAQ;AAAA,EAChB;AACJ;AAKA,SAAS,MAAM,WAAW;AACtB,SAAO,QAAQ,SAAS;AAC5B;AACA,SAAS,QAAQ,WAAW;AACxB,MAAI,MAAM,QAAQ,SAAS,GAAG;AAC1B,WAAO,WAAW,SAAS;AAAA,EAC/B;AACA,MAAI,OAAO,cAAc,UAAU;AAC/B,WAAO,SAAS,SAAS;AAAA,EAC7B;AACA,SAAO;AACX;AACA,SAAS,WAAW,KAAK;AACrB,MAAI,IAAI,CAAC;AACT,WAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC5C,MAAE,CAAC,IAAI,QAAQ,IAAI,CAAC,CAAC;AAAA,EACzB;AACA,SAAO;AACX;AACA,SAAS,SAAS,KAAK;AACnB,MAAI,IAAI,CAAC;AACT,WAASC,QAAO,KAAK;AACjB,MAAEA,IAAG,IAAI,QAAQ,IAAIA,IAAG,CAAC;AAAA,EAC7B;AACA,SAAO;AACX;AACA,SAAS,aAAa,WAAW,SAAS;AACtC,UAAQ,QAAQ,YAAU;AACtB,aAASA,QAAO,QAAQ;AACpB,aAAOA,IAAG,IAAI,OAAOA,IAAG;AAAA,IAC5B;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AACA,SAAS,SAAS,MAAM;AACpB,QAAM,MAAM,CAAC,KAAK,YAAY,GAAG,KAAK,CAAC,KAAK,YAAY,IAAI;AAC5D,MAAI,QAAQ,GAAG;AACX,WAAO;AAAA,EACX,WACS,CAAC,QAAQ,KAAK,SAAS,GAAG;AAC/B,WAAO,SAAS,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC,CAAC;AAAA,EACtD,OACK;AACD,WAAO,KAAK,OAAO,CAAC,MAAM,CAAC;AAAA,EAC/B;AACJ;AACA,IAAI,yBAAyB;AAC7B,IAAM,cAAN,MAAkB;AAAA,EACd,OAAO,YAAY,aAAa;AAC5B,QAAI,gBAAgB,MAAM;AACtB,aAAO;AAAA,IACX;AACA,2BAAuB,YAAY;AACnC,WAAO,uBAAuB,KAAK,WAAW;AAAA,EAClD;AAAA,EACA,OAAO,gBAAgB,aAAa,eAAe,gBAAgB;AAC/D,WAAO,YAAY,QAAQ,wBAAwB,CAAC,OAAO,OAAO,cAAc,YAAY;AACxF,UAAI,UAAU,eAAe,SAAS,SAAS,cAAc,EAAE,CAAC;AAChE,UAAI,SAAS;AACT,YAAI,SAAS,cAAc,UAAU,QAAQ,OAAO,QAAQ,GAAG;AAE/D,eAAO,OAAO,CAAC,MAAM,KAAK;AACtB,mBAAS,OAAO,UAAU,CAAC;AAAA,QAC/B;AACA,gBAAQ,SAAS;AAAA,UACb,KAAK;AACD,mBAAO,OAAO,YAAY;AAAA,UAC9B,KAAK;AACD,mBAAO,OAAO,YAAY;AAAA,UAC9B;AACI,mBAAO;AAAA,QACf;AAAA,MACJ,OACK;AACD,eAAO;AAAA,MACX;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AACA,SAAS,OAAO,GAAG,GAAG;AAClB,MAAI,IAAI,GAAG;AACP,WAAO;AAAA,EACX;AACA,MAAI,IAAI,GAAG;AACP,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,UAAU,GAAG,GAAG;AACrB,MAAI,MAAM,QAAQ,MAAM,MAAM;AAC1B,WAAO;AAAA,EACX;AACA,MAAI,CAAC,GAAG;AACJ,WAAO;AAAA,EACX;AACA,MAAI,CAAC,GAAG;AACJ,WAAO;AAAA,EACX;AACA,MAAI,OAAO,EAAE;AACb,MAAI,OAAO,EAAE;AACb,MAAI,SAAS,MAAM;AACf,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC3B,UAAI,MAAM,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC3B,UAAI,QAAQ,GAAG;AACX,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,SAAO,OAAO;AAClB;AACA,SAAS,gBAAgB,KAAK;AAC1B,MAAI,kBAAkB,KAAK,GAAG,GAAG;AAE7B,WAAO;AAAA,EACX;AACA,MAAI,kBAAkB,KAAK,GAAG,GAAG;AAE7B,WAAO;AAAA,EACX;AACA,MAAI,kBAAkB,KAAK,GAAG,GAAG;AAE7B,WAAO;AAAA,EACX;AACA,MAAI,kBAAkB,KAAK,GAAG,GAAG;AAE7B,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAIA,SAAS,uBAAuB,OAAO;AACnC,SAAO,MAAM,QAAQ,2CAA2C,MAAM;AAC1E;AACA,IAAM,WAAN,MAAe;AAAA,EAGX,YAAY,IAAI;AAFhB;AACA,iCAAQ,oBAAI,IAAI;AAEZ,SAAK,KAAK;AAAA,EACd;AAAA,EACA,IAAIA,MAAK;AACL,QAAI,KAAK,MAAM,IAAIA,IAAG,GAAG;AACrB,aAAO,KAAK,MAAM,IAAIA,IAAG;AAAA,IAC7B;AACA,UAAM,QAAQ,KAAK,GAAGA,IAAG;AACzB,SAAK,MAAM,IAAIA,MAAK,KAAK;AACzB,WAAO;AAAA,EACX;AACJ;AAQA,IAAM,wBAAN,MAA4B;AAAA,EAExB,YAAY,WAAW;AADvB;AAEI,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,QAAQ;AACJ,WAAO,KAAK;AAAA,EAChB;AACJ;AAIA,IAAM,kCAAN,MAAsC;AAAA,EAGlC,YAAY,WAAW,UAAU;AAFjC;AACA;AAEI,SAAK,YAAY;AACjB,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,QAAQ;AACJ,WAAO,GAAG,KAAK,SAAS,IAAI,KAAK,QAAQ;AAAA,EAC7C;AACJ;AACA,IAAM,6BAAN,MAAiC;AAAA,EAAjC;AACI,uCAAc,CAAC;AACf,8CAAqB,oBAAI,IAAI;AAI7B,uCAAc,oBAAI,IAAI;AAAA;AAAA,EAHtB,IAAI,aAAa;AACb,WAAO,KAAK;AAAA,EAChB;AAAA,EAEA,IAAI,WAAW;AACX,UAAMA,OAAM,UAAU,MAAM;AAC5B,QAAI,KAAK,mBAAmB,IAAIA,IAAG,GAAG;AAClC;AAAA,IACJ;AACA,SAAK,mBAAmB,IAAIA,IAAG;AAC/B,SAAK,YAAY,KAAK,SAAS;AAAA,EACnC;AACJ;AACA,IAAM,2BAAN,MAA+B;AAAA,EAM3B,YAAY,MAAM,kBAAkB;AALpC;AACA;AACA,iDAAwB,oBAAI,IAAI;AAChC,oDAA2B,oBAAI,IAAI;AACnC;AAEI,SAAK,OAAO;AACZ,SAAK,mBAAmB;AACxB,SAAK,sBAAsB,IAAI,KAAK,gBAAgB;AACpD,SAAK,IAAI,CAAC,IAAI,sBAAsB,KAAK,gBAAgB,CAAC;AAAA,EAC9D;AAAA,EACA,eAAe;AACX,UAAM,IAAI,KAAK;AACf,SAAK,IAAI,CAAC;AACV,UAAM,OAAO,IAAI,2BAA2B;AAC5C,eAAW,OAAO,GAAG;AACjB,mCAA6B,KAAK,KAAK,kBAAkB,KAAK,MAAM,IAAI;AAAA,IAC5E;AACA,eAAW,OAAO,KAAK,YAAY;AAC/B,UAAI,eAAe,uBAAuB;AACtC,YAAI,KAAK,sBAAsB,IAAI,IAAI,SAAS,GAAG;AAE/C;AAAA,QACJ;AACA,aAAK,sBAAsB,IAAI,IAAI,SAAS;AAC5C,aAAK,EAAE,KAAK,GAAG;AAAA,MACnB,OACK;AACD,YAAI,KAAK,sBAAsB,IAAI,IAAI,SAAS,GAAG;AAE/C;AAAA,QACJ;AACA,YAAI,KAAK,yBAAyB,IAAI,IAAI,MAAM,CAAC,GAAG;AAEhD;AAAA,QACJ;AACA,aAAK,yBAAyB,IAAI,IAAI,MAAM,CAAC;AAC7C,aAAK,EAAE,KAAK,GAAG;AAAA,MACnB;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,6BAA6B,WAAW,sBAAsB,MAAM,QAAQ;AACjF,QAAM,cAAc,KAAK,OAAO,UAAU,SAAS;AACnD,MAAI,CAAC,aAAa;AACd,QAAI,UAAU,cAAc,sBAAsB;AAC9C,YAAM,IAAI,MAAM,4BAA4B,oBAAoB,GAAG;AAAA,IACvE;AACA;AAAA,EACJ;AACA,QAAM,cAAc,KAAK,OAAO,oBAAoB;AACpD,MAAI,qBAAqB,uBAAuB;AAC5C,4CAAwC,EAAE,aAAa,YAAY,GAAG,MAAM;AAAA,EAChF,OACK;AACD,sDAAkD,UAAU,UAAU,EAAE,aAAa,aAAa,YAAY,YAAY,WAAW,GAAG,MAAM;AAAA,EAClJ;AACA,QAAM,aAAa,KAAK,WAAW,UAAU,SAAS;AACtD,MAAI,YAAY;AACZ,eAAW,aAAa,YAAY;AAChC,aAAO,IAAI,IAAI,sBAAsB,SAAS,CAAC;AAAA,IACnD;AAAA,EACJ;AACJ;AACA,SAAS,kDAAkD,UAAU,SAAS,QAAQ;AAClF,MAAI,QAAQ,cAAc,QAAQ,WAAW,QAAQ,GAAG;AACpD,UAAM,OAAO,QAAQ,WAAW,QAAQ;AACxC,qCAAiC,CAAC,IAAI,GAAG,SAAS,MAAM;AAAA,EAC5D;AACJ;AACA,SAAS,wCAAwC,SAAS,QAAQ;AAC9D,MAAI,QAAQ,YAAY,YAAY,MAAM,QAAQ,QAAQ,YAAY,QAAQ,GAAG;AAC7E,qCAAiC,QAAQ,YAAY,UAAU,EAAE,GAAG,SAAS,YAAY,QAAQ,YAAY,WAAW,GAAG,MAAM;AAAA,EACrI;AACA,MAAI,QAAQ,YAAY,YAAY;AAChC,qCAAiC,OAAO,OAAO,QAAQ,YAAY,UAAU,GAAG,EAAE,GAAG,SAAS,YAAY,QAAQ,YAAY,WAAW,GAAG,MAAM;AAAA,EACtJ;AACJ;AACA,SAAS,iCAAiC,OAAO,SAAS,QAAQ;AAC9D,aAAW,QAAQ,OAAO;AACtB,QAAI,OAAO,YAAY,IAAI,IAAI,GAAG;AAC9B;AAAA,IACJ;AACA,WAAO,YAAY,IAAI,IAAI;AAC3B,UAAM,oBAAoB,KAAK,aAAa,aAAa,CAAC,GAAG,QAAQ,YAAY,KAAK,UAAU,IAAI,QAAQ;AAC5G,QAAI,MAAM,QAAQ,KAAK,QAAQ,GAAG;AAC9B,uCAAiC,KAAK,UAAU,EAAE,GAAG,SAAS,YAAY,kBAAkB,GAAG,MAAM;AAAA,IACzG;AACA,UAAM,UAAU,KAAK;AACrB,QAAI,CAAC,SAAS;AACV;AAAA,IACJ;AACA,UAAM,YAAY,aAAa,OAAO;AACtC,YAAQ,UAAU,MAAM;AAAA,MACpB,KAAK;AACD,gDAAwC,EAAE,GAAG,SAAS,aAAa,QAAQ,YAAY,GAAG,MAAM;AAChG;AAAA,MACJ,KAAK;AACD,gDAAwC,SAAS,MAAM;AACvD;AAAA,MACJ,KAAK;AACD,0DAAkD,UAAU,UAAU,EAAE,GAAG,SAAS,YAAY,kBAAkB,GAAG,MAAM;AAC3H;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,cAAM,cAAc,UAAU,cAAc,QAAQ,YAAY,YAC1D,QAAQ,cACR,UAAU,cAAc,QAAQ,YAAY,YACxC,QAAQ,cACR;AACV,YAAI,aAAa;AACb,gBAAM,aAAa,EAAE,aAAa,QAAQ,aAAa,aAAa,YAAY,kBAAkB;AAClG,cAAI,UAAU,SAAS,GAA0D;AAC7E,8DAAkD,UAAU,UAAU,YAAY,MAAM;AAAA,UAC5F,OACK;AACD,oDAAwC,YAAY,MAAM;AAAA,UAC9D;AAAA,QACJ,OACK;AACD,cAAI,UAAU,SAAS,GAA0D;AAC7E,mBAAO,IAAI,IAAI,gCAAgC,UAAU,WAAW,UAAU,QAAQ,CAAC;AAAA,UAC3F,OACK;AACD,mBAAO,IAAI,IAAI,sBAAsB,UAAU,SAAS,CAAC;AAAA,UAC7D;AAAA,QACJ;AACA;AAAA,IACR;AAAA,EACJ;AACJ;AACA,IAAM,gBAAN,MAAoB;AAAA,EAApB;AACI,gCAAO;AAAA;AACX;AACA,IAAM,gBAAN,MAAoB;AAAA,EAApB;AACI,gCAAO;AAAA;AACX;AACA,IAAM,oBAAN,MAAwB;AAAA,EAGpB,YAAY,UAAU;AAFtB;AACA,gCAAO;AAEH,SAAK,WAAW;AAAA,EACpB;AACJ;AACA,IAAM,oBAAN,MAAwB;AAAA,EAGpB,YAAY,WAAW;AAFvB;AACA,gCAAO;AAEH,SAAK,YAAY;AAAA,EACrB;AACJ;AACA,IAAM,8BAAN,MAAkC;AAAA,EAI9B,YAAY,WAAW,UAAU;AAHjC;AACA;AACA,gCAAO;AAEH,SAAK,YAAY;AACjB,SAAK,WAAW;AAAA,EACpB;AACJ;AACA,SAAS,aAAa,SAAS;AAC3B,MAAI,YAAY,SAAS;AACrB,WAAO,IAAI,cAAc;AAAA,EAC7B,WACS,YAAY,SAAS;AAC1B,WAAO,IAAI,cAAc;AAAA,EAC7B;AACA,QAAM,eAAe,QAAQ,QAAQ,GAAG;AACxC,MAAI,iBAAiB,IAAI;AACrB,WAAO,IAAI,kBAAkB,OAAO;AAAA,EACxC,WACS,iBAAiB,GAAG;AACzB,WAAO,IAAI,kBAAkB,QAAQ,UAAU,CAAC,CAAC;AAAA,EACrD,OACK;AACD,UAAM,YAAY,QAAQ,UAAU,GAAG,YAAY;AACnD,UAAM,WAAW,QAAQ,UAAU,eAAe,CAAC;AACnD,WAAO,IAAI,4BAA4B,WAAW,QAAQ;AAAA,EAC9D;AACJ;AAKA,IAAM,sBAAsB;AAC5B,IAAM,uBAAuB;AAE7B,IAAM,YAAY;AAElB,IAAM,cAAc;AACpB,SAAS,iBAAiB,IAAI;AAC1B,SAAO;AACX;AACA,SAAS,eAAe,IAAI;AACxB,SAAO;AACX;AACA,IAAM,OAAN,MAAW;AAAA,EAOP,YAAY,WAAW,IAAI,MAAM,aAAa;AAN9C;AACA;AACA;AACA;AACA;AACA;AAEI,SAAK,YAAY;AACjB,SAAK,KAAK;AACV,SAAK,QAAQ,QAAQ;AACrB,SAAK,mBAAmB,YAAY,YAAY,KAAK,KAAK;AAC1D,SAAK,eAAe,eAAe;AACnC,SAAK,0BAA0B,YAAY,YAAY,KAAK,YAAY;AAAA,EAC5E;AAAA,EACA,IAAI,YAAY;AACZ,UAAM,WAAW,KAAK,YAAY,GAAG,SAAS,KAAK,UAAU,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,KAAK;AAClG,WAAO,GAAG,KAAK,YAAY,IAAI,IAAI,KAAK,EAAE,MAAM,QAAQ;AAAA,EAC5D;AAAA,EACA,QAAQ,UAAU,gBAAgB;AAC9B,QAAI,CAAC,KAAK,oBAAoB,KAAK,UAAU,QAAQ,aAAa,QAAQ,mBAAmB,MAAM;AAC/F,aAAO,KAAK;AAAA,IAChB;AACA,WAAO,YAAY,gBAAgB,KAAK,OAAO,UAAU,cAAc;AAAA,EAC3E;AAAA,EACA,eAAe,UAAU,gBAAgB;AACrC,QAAI,CAAC,KAAK,2BAA2B,KAAK,iBAAiB,MAAM;AAC7D,aAAO,KAAK;AAAA,IAChB;AACA,WAAO,YAAY,gBAAgB,KAAK,cAAc,UAAU,cAAc;AAAA,EAClF;AACJ;AACA,IAAM,cAAN,cAA0B,KAAK;AAAA,EAE3B,YAAY,WAAW,IAAI,MAAM,aAAa,8BAA8B;AACxE,UAAM,WAAW,IAAI,MAAM,WAAW;AAF1C;AAGI,SAAK,+BAA+B;AAAA,EACxC;AAAA,EACA,UAAU;AAAA,EAEV;AAAA,EACA,gBAAgB,SAAS,KAAK;AAC1B,UAAM,IAAI,MAAM,gBAAgB;AAAA,EACpC;AAAA,EACA,QAAQ,SAAS,gBAAgB;AAC7B,UAAM,IAAI,MAAM,gBAAgB;AAAA,EACpC;AAAA,EACA,UAAU,SAAS,gBAAgB,QAAQ,QAAQ;AAC/C,UAAM,IAAI,MAAM,gBAAgB;AAAA,EACpC;AACJ;AACA,IAAM,YAAN,cAAwB,KAAK;AAAA,EAIzB,YAAY,WAAW,IAAI,MAAM,OAAO,UAAU;AAC9C,UAAM,WAAW,IAAI,MAAM,IAAI;AAJnC;AACA;AACA;AAGI,SAAK,SAAS,IAAI,aAAa,OAAO,KAAK,EAAE;AAC7C,SAAK,WAAW;AAChB,SAAK,0BAA0B;AAAA,EACnC;AAAA,EACA,UAAU;AACN,QAAI,KAAK,yBAAyB;AAC9B,WAAK,wBAAwB,QAAQ;AACrC,WAAK,0BAA0B;AAAA,IACnC;AAAA,EACJ;AAAA,EACA,IAAI,mBAAmB;AACnB,WAAO,GAAG,KAAK,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,gBAAgB,SAAS,KAAK;AAC1B,QAAI,KAAK,KAAK,MAAM;AAAA,EACxB;AAAA,EACA,QAAQ,SAAS,gBAAgB;AAC7B,WAAO,KAAK,2BAA2B,OAAO,EAAE,QAAQ,OAAO;AAAA,EACnE;AAAA,EACA,UAAU,SAAS,gBAAgB,QAAQ,QAAQ;AAC/C,WAAO,KAAK,2BAA2B,OAAO,EAAE,UAAU,SAAS,QAAQ,MAAM;AAAA,EACrF;AAAA,EACA,2BAA2B,SAAS;AAChC,QAAI,CAAC,KAAK,yBAAyB;AAC/B,WAAK,0BAA0B,IAAI,iBAAiB;AACpD,WAAK,gBAAgB,SAAS,KAAK,uBAAuB;AAAA,IAC9D;AACA,WAAO,KAAK;AAAA,EAChB;AACJ;AACA,IAAM,kBAAN,cAA8B,KAAK;AAAA,EAI/B,YAAY,WAAW,IAAI,MAAM,aAAa,UAAU;AACpD,UAAM,WAAW,IAAI,MAAM,WAAW;AAJ1C;AACA;AACA;AAGI,SAAK,WAAW,SAAS;AACzB,SAAK,qBAAqB,SAAS;AACnC,SAAK,0BAA0B;AAAA,EACnC;AAAA,EACA,UAAU;AACN,QAAI,KAAK,yBAAyB;AAC9B,WAAK,wBAAwB,QAAQ;AACrC,WAAK,0BAA0B;AAAA,IACnC;AAAA,EACJ;AAAA,EACA,gBAAgB,SAAS,KAAK;AAC1B,eAAW,WAAW,KAAK,UAAU;AACjC,YAAM,OAAO,QAAQ,QAAQ,OAAO;AACpC,WAAK,gBAAgB,SAAS,GAAG;AAAA,IACrC;AAAA,EACJ;AAAA,EACA,QAAQ,SAAS,gBAAgB;AAC7B,WAAO,KAAK,2BAA2B,OAAO,EAAE,QAAQ,OAAO;AAAA,EACnE;AAAA,EACA,UAAU,SAAS,gBAAgB,QAAQ,QAAQ;AAC/C,WAAO,KAAK,2BAA2B,OAAO,EAAE,UAAU,SAAS,QAAQ,MAAM;AAAA,EACrF;AAAA,EACA,2BAA2B,SAAS;AAChC,QAAI,CAAC,KAAK,yBAAyB;AAC/B,WAAK,0BAA0B,IAAI,iBAAiB;AACpD,WAAK,gBAAgB,SAAS,KAAK,uBAAuB;AAAA,IAC9D;AACA,WAAO,KAAK;AAAA,EAChB;AACJ;AACA,IAAM,eAAN,cAA2B,KAAK;AAAA,EAU5B,YAAY,WAAW,IAAI,MAAM,aAAa,OAAO,eAAe,KAAK,aAAa,qBAAqB,UAAU;AACjH,UAAM,WAAW,IAAI,MAAM,WAAW;AAV1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGI,SAAK,SAAS,IAAI,aAAa,OAAO,KAAK,EAAE;AAC7C,SAAK,gBAAgB;AACrB,SAAK,OAAO,IAAI,aAAa,MAAM,MAAM,KAAU,EAAE;AACrD,SAAK,uBAAuB,KAAK,KAAK;AACtC,SAAK,cAAc;AACnB,SAAK,sBAAsB,uBAAuB;AAClD,SAAK,WAAW,SAAS;AACzB,SAAK,qBAAqB,SAAS;AACnC,SAAK,0BAA0B;AAAA,EACnC;AAAA,EACA,UAAU;AACN,QAAI,KAAK,yBAAyB;AAC9B,WAAK,wBAAwB,QAAQ;AACrC,WAAK,0BAA0B;AAAA,IACnC;AAAA,EACJ;AAAA,EACA,IAAI,mBAAmB;AACnB,WAAO,GAAG,KAAK,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,IAAI,iBAAiB;AACjB,WAAO,GAAG,KAAK,KAAK,MAAM;AAAA,EAC9B;AAAA,EACA,iCAAiC,UAAU,gBAAgB;AACvD,WAAO,KAAK,KAAK,sBAAsB,UAAU,cAAc;AAAA,EACnE;AAAA,EACA,gBAAgB,SAAS,KAAK;AAC1B,QAAI,KAAK,KAAK,MAAM;AAAA,EACxB;AAAA,EACA,QAAQ,SAAS,gBAAgB;AAC7B,WAAO,KAAK,2BAA2B,SAAS,cAAc,EAAE,QAAQ,OAAO;AAAA,EACnF;AAAA,EACA,UAAU,SAAS,gBAAgB,QAAQ,QAAQ;AAC/C,WAAO,KAAK,2BAA2B,SAAS,cAAc,EAAE,UAAU,SAAS,QAAQ,MAAM;AAAA,EACrG;AAAA,EACA,2BAA2B,SAAS,gBAAgB;AAChD,QAAI,CAAC,KAAK,yBAAyB;AAC/B,WAAK,0BAA0B,IAAI,iBAAiB;AACpD,iBAAW,WAAW,KAAK,UAAU;AACjC,cAAM,OAAO,QAAQ,QAAQ,OAAO;AACpC,aAAK,gBAAgB,SAAS,KAAK,uBAAuB;AAAA,MAC9D;AACA,UAAI,KAAK,qBAAqB;AAC1B,aAAK,wBAAwB,KAAK,KAAK,KAAK,oBAAoB,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI;AAAA,MACjG,OACK;AACD,aAAK,wBAAwB,QAAQ,KAAK,KAAK,oBAAoB,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI;AAAA,MACpG;AAAA,IACJ;AACA,QAAI,KAAK,KAAK,mBAAmB;AAC7B,UAAI,KAAK,qBAAqB;AAC1B,aAAK,wBAAwB,UAAU,KAAK,wBAAwB,OAAO,IAAI,GAAG,cAAc;AAAA,MACpG,OACK;AACD,aAAK,wBAAwB,UAAU,GAAG,cAAc;AAAA,MAC5D;AAAA,IACJ;AACA,WAAO,KAAK;AAAA,EAChB;AACJ;AACA,IAAM,iBAAN,cAA6B,KAAK;AAAA,EAU9B,YAAY,WAAW,IAAI,MAAM,aAAa,OAAO,eAAe,QAAQ,eAAe,UAAU;AACjG,UAAM,WAAW,IAAI,MAAM,WAAW;AAV1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGI,SAAK,SAAS,IAAI,aAAa,OAAO,KAAK,EAAE;AAC7C,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,SAAS,IAAI,aAAa,QAAQ,WAAW;AAClD,SAAK,yBAAyB,KAAK,OAAO;AAC1C,SAAK,WAAW,SAAS;AACzB,SAAK,qBAAqB,SAAS;AACnC,SAAK,0BAA0B;AAC/B,SAAK,+BAA+B;AAAA,EACxC;AAAA,EACA,UAAU;AACN,QAAI,KAAK,yBAAyB;AAC9B,WAAK,wBAAwB,QAAQ;AACrC,WAAK,0BAA0B;AAAA,IACnC;AACA,QAAI,KAAK,8BAA8B;AACnC,WAAK,6BAA6B,QAAQ;AAC1C,WAAK,+BAA+B;AAAA,IACxC;AAAA,EACJ;AAAA,EACA,IAAI,mBAAmB;AACnB,WAAO,GAAG,KAAK,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,IAAI,mBAAmB;AACnB,WAAO,GAAG,KAAK,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,mCAAmC,UAAU,gBAAgB;AACzD,WAAO,KAAK,OAAO,sBAAsB,UAAU,cAAc;AAAA,EACrE;AAAA,EACA,gBAAgB,SAAS,KAAK;AAC1B,QAAI,KAAK,KAAK,MAAM;AAAA,EACxB;AAAA,EACA,QAAQ,SAAS,gBAAgB;AAC7B,WAAO,KAAK,2BAA2B,OAAO,EAAE,QAAQ,OAAO;AAAA,EACnE;AAAA,EACA,UAAU,SAAS,gBAAgB,QAAQ,QAAQ;AAC/C,WAAO,KAAK,2BAA2B,OAAO,EAAE,UAAU,SAAS,QAAQ,MAAM;AAAA,EACrF;AAAA,EACA,2BAA2B,SAAS;AAChC,QAAI,CAAC,KAAK,yBAAyB;AAC/B,WAAK,0BAA0B,IAAI,iBAAiB;AACpD,iBAAW,WAAW,KAAK,UAAU;AACjC,cAAM,OAAO,QAAQ,QAAQ,OAAO;AACpC,aAAK,gBAAgB,SAAS,KAAK,uBAAuB;AAAA,MAC9D;AAAA,IACJ;AACA,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,aAAa,SAAS,gBAAgB;AAClC,WAAO,KAAK,gCAAgC,SAAS,cAAc,EAAE,QAAQ,OAAO;AAAA,EACxF;AAAA,EACA,eAAe,SAAS,gBAAgB,QAAQ,QAAQ;AACpD,WAAO,KAAK,gCAAgC,SAAS,cAAc,EAAE,UAAU,SAAS,QAAQ,MAAM;AAAA,EAC1G;AAAA,EACA,gCAAgC,SAAS,gBAAgB;AACrD,QAAI,CAAC,KAAK,8BAA8B;AACpC,WAAK,+BAA+B,IAAI,iBAAiB;AACzD,WAAK,6BAA6B,KAAK,KAAK,OAAO,oBAAoB,KAAK,OAAO,MAAM,IAAI,KAAK,MAAM;AAAA,IAC5G;AACA,QAAI,KAAK,OAAO,mBAAmB;AAC/B,WAAK,6BAA6B,UAAU,GAAG,iBAAiB,iBAAiB,GAAQ;AAAA,IAC7F;AACA,WAAO,KAAK;AAAA,EAChB;AACJ;AACA,IAAM,cAAN,MAAM,aAAY;AAAA,EACd,OAAO,kBAAkB,QAAQ,WAAW,MAAM,aAAa,8BAA8B;AACzF,WAAO,OAAO,aAAa,CAAC,OAAO;AAC/B,aAAO,IAAI,YAAY,WAAW,IAAI,MAAM,aAAa,4BAA4B;AAAA,IACzF,CAAC;AAAA,EACL;AAAA,EACA,OAAO,kBAAkB,MAAM,QAAQ,YAAY;AAC/C,QAAI,CAAC,KAAK,IAAI;AACV,aAAO,aAAa,CAAC,OAAO;AACxB,aAAK,KAAK;AACV,YAAI,KAAK,OAAO;AACZ,iBAAO,IAAI,UAAU,KAAK,yBAAyB,KAAK,IAAI,KAAK,MAAM,KAAK,OAAO,aAAY,iBAAiB,KAAK,UAAU,QAAQ,UAAU,CAAC;AAAA,QACtJ;AACA,YAAI,OAAO,KAAK,UAAU,aAAa;AACnC,cAAI,KAAK,YAAY;AACjB,yBAAa,aAAa,CAAC,GAAG,YAAY,KAAK,UAAU;AAAA,UAC7D;AACA,cAAI,WAAW,KAAK;AACpB,cAAI,OAAO,aAAa,eAAe,KAAK,SAAS;AACjD,uBAAW,CAAC,EAAE,SAAS,KAAK,QAAQ,CAAC;AAAA,UACzC;AACA,iBAAO,IAAI,gBAAgB,KAAK,yBAAyB,KAAK,IAAI,KAAK,MAAM,KAAK,aAAa,aAAY,iBAAiB,UAAU,QAAQ,UAAU,CAAC;AAAA,QAC7J;AACA,YAAI,KAAK,OAAO;AACZ,iBAAO,IAAI,eAAe,KAAK,yBAAyB,KAAK,IAAI,KAAK,MAAM,KAAK,aAAa,KAAK,OAAO,aAAY,iBAAiB,KAAK,iBAAiB,KAAK,UAAU,QAAQ,UAAU,GAAG,KAAK,OAAO,aAAY,iBAAiB,KAAK,iBAAiB,KAAK,UAAU,QAAQ,UAAU,GAAG,aAAY,iBAAiB,KAAK,UAAU,QAAQ,UAAU,CAAC;AAAA,QACvW;AACA,eAAO,IAAI,aAAa,KAAK,yBAAyB,KAAK,IAAI,KAAK,MAAM,KAAK,aAAa,KAAK,OAAO,aAAY,iBAAiB,KAAK,iBAAiB,KAAK,UAAU,QAAQ,UAAU,GAAG,KAAK,KAAK,aAAY,iBAAiB,KAAK,eAAe,KAAK,UAAU,QAAQ,UAAU,GAAG,KAAK,qBAAqB,aAAY,iBAAiB,KAAK,UAAU,QAAQ,UAAU,CAAC;AAAA,MAC3X,CAAC;AAAA,IACL;AACA,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,OAAO,iBAAiB,UAAU,QAAQ,YAAY;AAClD,QAAI,IAAI,CAAC;AACT,QAAI,UAAU;AAEV,UAAI,mBAAmB;AACvB,iBAAW,aAAa,UAAU;AAC9B,YAAI,cAAc,2BAA2B;AACzC;AAAA,QACJ;AACA,cAAM,mBAAmB,SAAS,WAAW,EAAE;AAC/C,YAAI,mBAAmB,kBAAkB;AACrC,6BAAmB;AAAA,QACvB;AAAA,MACJ;AAEA,eAAS,IAAI,GAAG,KAAK,kBAAkB,KAAK;AACxC,UAAE,CAAC,IAAI;AAAA,MACX;AAEA,iBAAW,aAAa,UAAU;AAC9B,YAAI,cAAc,2BAA2B;AACzC;AAAA,QACJ;AACA,cAAM,mBAAmB,SAAS,WAAW,EAAE;AAC/C,YAAI,+BAA+B;AACnC,YAAI,SAAS,SAAS,EAAE,UAAU;AAC9B,yCAA+B,aAAY,kBAAkB,SAAS,SAAS,GAAG,QAAQ,UAAU;AAAA,QACxG;AACA,UAAE,gBAAgB,IAAI,aAAY,kBAAkB,QAAQ,SAAS,SAAS,EAAE,yBAAyB,SAAS,SAAS,EAAE,MAAM,SAAS,SAAS,EAAE,aAAa,4BAA4B;AAAA,MACpM;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,iBAAiB,UAAU,QAAQ,YAAY;AAClD,QAAI,IAAI,CAAC;AACT,QAAI,UAAU;AACV,eAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,IAAI,KAAK,KAAK;AACjD,cAAM,UAAU,SAAS,CAAC;AAC1B,YAAI,SAAS;AACb,YAAI,QAAQ,SAAS;AACjB,gBAAM,YAAY,aAAa,QAAQ,OAAO;AAC9C,kBAAQ,UAAU,MAAM;AAAA,YACpB,KAAK;AAAA,YACL,KAAK;AACD,uBAAS,aAAY,kBAAkB,WAAW,QAAQ,OAAO,GAAG,QAAQ,UAAU;AACtF;AAAA,YACJ,KAAK;AAED,kBAAI,oBAAoB,WAAW,UAAU,QAAQ;AACrD,kBAAI,mBAAmB;AACnB,yBAAS,aAAY,kBAAkB,mBAAmB,QAAQ,UAAU;AAAA,cAChF;AACA;AAAA,YACJ,KAAK;AAAA,YACL,KAAK;AACD,oBAAM,sBAAsB,UAAU;AACtC,oBAAM,yBAAyB,UAAU,SAAS,IAC5C,UAAU,WACV;AAEN,oBAAM,kBAAkB,OAAO,mBAAmB,qBAAqB,UAAU;AACjF,kBAAI,iBAAiB;AACjB,oBAAI,wBAAwB;AACxB,sBAAI,uBAAuB,gBAAgB,WAAW,sBAAsB;AAC5E,sBAAI,sBAAsB;AACtB,6BAAS,aAAY,kBAAkB,sBAAsB,QAAQ,gBAAgB,UAAU;AAAA,kBACnG;AAAA,gBACJ,OACK;AACD,2BAAS,aAAY,kBAAkB,gBAAgB,WAAW,OAAO,QAAQ,gBAAgB,UAAU;AAAA,gBAC/G;AAAA,cACJ;AACA;AAAA,UACR;AAAA,QACJ,OACK;AACD,mBAAS,aAAY,kBAAkB,SAAS,QAAQ,UAAU;AAAA,QACtE;AACA,YAAI,WAAW,IAAI;AACf,gBAAM,OAAO,OAAO,QAAQ,MAAM;AAClC,cAAI,WAAW;AACf,cAAI,gBAAgB,mBAAmB,gBAAgB,gBAAgB,gBAAgB,gBAAgB;AACnG,gBAAI,KAAK,sBAAsB,KAAK,SAAS,WAAW,GAAG;AACvD,yBAAW;AAAA,YACf;AAAA,UACJ;AACA,cAAI,UAAU;AAEV;AAAA,UACJ;AACA,YAAE,KAAK,MAAM;AAAA,QACjB;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,MACH,UAAU;AAAA,MACV,qBAAsB,WAAW,SAAS,SAAS,OAAO,EAAE;AAAA,IAChE;AAAA,EACJ;AACJ;AACA,IAAM,eAAN,MAAM,cAAa;AAAA,EAMf,YAAY,cAAc,QAAQ;AALlC;AACA;AACA;AACA;AACA;AAEI,QAAI,cAAc;AACd,YAAM,MAAM,aAAa;AACzB,UAAI,gBAAgB;AACpB,UAAI,SAAS,CAAC;AACd,UAAI,YAAY;AAChB,eAAS,MAAM,GAAG,MAAM,KAAK,OAAO;AAChC,cAAM,KAAK,aAAa,OAAO,GAAG;AAClC,YAAI,OAAO,MAAM;AACb,cAAI,MAAM,IAAI,KAAK;AACf,kBAAM,SAAS,aAAa,OAAO,MAAM,CAAC;AAC1C,gBAAI,WAAW,KAAK;AAChB,qBAAO,KAAK,aAAa,UAAU,eAAe,GAAG,CAAC;AACtD,qBAAO,KAAK,kBAAkB;AAC9B,8BAAgB,MAAM;AAAA,YAC1B,WACS,WAAW,OAAO,WAAW,KAAK;AACvC,0BAAY;AAAA,YAChB;AACA;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,WAAK,YAAY;AACjB,UAAI,kBAAkB,GAAG;AAErB,aAAK,SAAS;AAAA,MAClB,OACK;AACD,eAAO,KAAK,aAAa,UAAU,eAAe,GAAG,CAAC;AACtD,aAAK,SAAS,OAAO,KAAK,EAAE;AAAA,MAChC;AAAA,IACJ,OACK;AACD,WAAK,YAAY;AACjB,WAAK,SAAS;AAAA,IAClB;AACA,QAAI,KAAK,WAAW;AAChB,WAAK,eAAe,KAAK,kBAAkB;AAAA,IAC/C,OACK;AACD,WAAK,eAAe;AAAA,IACxB;AACA,SAAK,SAAS;AACd,SAAK,oBAAoB,oBAAoB,KAAK,KAAK,MAAM;AAAA,EAEjE;AAAA,EACA,QAAQ;AACJ,WAAO,IAAI,cAAa,KAAK,QAAQ,KAAK,MAAM;AAAA,EACpD;AAAA,EACA,UAAU,WAAW;AACjB,QAAI,KAAK,WAAW,WAAW;AAC3B;AAAA,IACJ;AACA,SAAK,SAAS;AACd,QAAI,KAAK,WAAW;AAChB,WAAK,eAAe,KAAK,kBAAkB;AAAA,IAC/C;AAAA,EACJ;AAAA,EACA,sBAAsB,UAAU,gBAAgB;AAC5C,QAAI,iBAAiB,eAAe,IAAI,CAAC,YAAY;AACjD,aAAO,SAAS,UAAU,QAAQ,OAAO,QAAQ,GAAG;AAAA,IACxD,CAAC;AACD,yBAAqB,YAAY;AACjC,WAAO,KAAK,OAAO,QAAQ,sBAAsB,CAAC,OAAO,OAAO;AAC5D,aAAO,uBAAuB,eAAe,SAAS,IAAI,EAAE,CAAC,KAAK,EAAE;AAAA,IACxE,CAAC;AAAA,EACL;AAAA,EACA,oBAAoB;AAChB,QAAI,eAAe,CAAC;AACpB,QAAI,eAAe,CAAC;AACpB,QAAI,eAAe,CAAC;AACpB,QAAI,eAAe,CAAC;AACpB,QAAI,KAAK,KAAK,IAAI;AAClB,SAAK,MAAM,GAAG,MAAM,KAAK,OAAO,QAAQ,MAAM,KAAK,OAAO;AACtD,WAAK,KAAK,OAAO,OAAO,GAAG;AAC3B,mBAAa,GAAG,IAAI;AACpB,mBAAa,GAAG,IAAI;AACpB,mBAAa,GAAG,IAAI;AACpB,mBAAa,GAAG,IAAI;AACpB,UAAI,OAAO,MAAM;AACb,YAAI,MAAM,IAAI,KAAK;AACf,mBAAS,KAAK,OAAO,OAAO,MAAM,CAAC;AACnC,cAAI,WAAW,KAAK;AAChB,yBAAa,MAAM,CAAC,IAAI;AACxB,yBAAa,MAAM,CAAC,IAAI;AACxB,yBAAa,MAAM,CAAC,IAAI;AACxB,yBAAa,MAAM,CAAC,IAAI;AAAA,UAC5B,WACS,WAAW,KAAK;AACrB,yBAAa,MAAM,CAAC,IAAI;AACxB,yBAAa,MAAM,CAAC,IAAI;AACxB,yBAAa,MAAM,CAAC,IAAI;AACxB,yBAAa,MAAM,CAAC,IAAI;AAAA,UAC5B,OACK;AACD,yBAAa,MAAM,CAAC,IAAI;AACxB,yBAAa,MAAM,CAAC,IAAI;AACxB,yBAAa,MAAM,CAAC,IAAI;AACxB,yBAAa,MAAM,CAAC,IAAI;AAAA,UAC5B;AACA;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,MACH,OAAO,aAAa,KAAK,EAAE;AAAA,MAC3B,OAAO,aAAa,KAAK,EAAE;AAAA,MAC3B,OAAO,aAAa,KAAK,EAAE;AAAA,MAC3B,OAAO,aAAa,KAAK,EAAE;AAAA,IAC/B;AAAA,EACJ;AAAA,EACA,eAAe,QAAQ,QAAQ;AAC3B,QAAI,CAAC,KAAK,aAAa,CAAC,KAAK,cAAc;AACvC,aAAO,KAAK;AAAA,IAChB;AACA,QAAI,QAAQ;AACR,UAAI,QAAQ;AACR,eAAO,KAAK,aAAa;AAAA,MAC7B,OACK;AACD,eAAO,KAAK,aAAa;AAAA,MAC7B;AAAA,IACJ,OACK;AACD,UAAI,QAAQ;AACR,eAAO,KAAK,aAAa;AAAA,MAC7B,OACK;AACD,eAAO,KAAK,aAAa;AAAA,MAC7B;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,IAAM,mBAAN,MAAuB;AAAA,EAKnB,cAAc;AAJd;AACA;AACA;AACA;AAEI,SAAK,SAAS,CAAC;AACf,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,eAAe;AAAA,MAChB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,UAAU;AACN,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,iBAAiB;AACb,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,QAAQ;AACrB,WAAK,UAAU;AAAA,IACnB;AACA,QAAI,KAAK,aAAa,OAAO;AACzB,WAAK,aAAa,MAAM,QAAQ;AAChC,WAAK,aAAa,QAAQ;AAAA,IAC9B;AACA,QAAI,KAAK,aAAa,OAAO;AACzB,WAAK,aAAa,MAAM,QAAQ;AAChC,WAAK,aAAa,QAAQ;AAAA,IAC9B;AACA,QAAI,KAAK,aAAa,OAAO;AACzB,WAAK,aAAa,MAAM,QAAQ;AAChC,WAAK,aAAa,QAAQ;AAAA,IAC9B;AACA,QAAI,KAAK,aAAa,OAAO;AACzB,WAAK,aAAa,MAAM,QAAQ;AAChC,WAAK,aAAa,QAAQ;AAAA,IAC9B;AAAA,EACJ;AAAA,EACA,KAAK,MAAM;AACP,SAAK,OAAO,KAAK,IAAI;AACrB,SAAK,cAAc,KAAK,eAAe,KAAK;AAAA,EAChD;AAAA,EACA,QAAQ,MAAM;AACV,SAAK,OAAO,QAAQ,IAAI;AACxB,SAAK,cAAc,KAAK,eAAe,KAAK;AAAA,EAChD;AAAA,EACA,SAAS;AACL,WAAO,KAAK,OAAO;AAAA,EACvB;AAAA,EACA,UAAU,OAAO,WAAW;AACxB,QAAI,KAAK,OAAO,KAAK,EAAE,WAAW,WAAW;AAEzC,WAAK,eAAe;AACpB,WAAK,OAAO,KAAK,EAAE,UAAU,SAAS;AAAA,IAC1C;AAAA,EACJ;AAAA,EACA,QAAQ,SAAS;AACb,QAAI,CAAC,KAAK,SAAS;AACf,UAAI,UAAU,KAAK,OAAO,IAAI,OAAK,EAAE,MAAM;AAC3C,WAAK,UAAU,IAAI,aAAa,SAAS,SAAS,KAAK,OAAO,IAAI,OAAK,EAAE,MAAM,CAAC;AAAA,IACpF;AACA,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,UAAU,SAAS,QAAQ,QAAQ;AAC/B,QAAI,CAAC,KAAK,aAAa;AACnB,aAAO,KAAK,QAAQ,OAAO;AAAA,IAC/B,OACK;AACD,UAAI,QAAQ;AACR,YAAI,QAAQ;AACR,cAAI,CAAC,KAAK,aAAa,OAAO;AAC1B,iBAAK,aAAa,QAAQ,KAAK,gBAAgB,SAAS,QAAQ,MAAM;AAAA,UAC1E;AACA,iBAAO,KAAK,aAAa;AAAA,QAC7B,OACK;AACD,cAAI,CAAC,KAAK,aAAa,OAAO;AAC1B,iBAAK,aAAa,QAAQ,KAAK,gBAAgB,SAAS,QAAQ,MAAM;AAAA,UAC1E;AACA,iBAAO,KAAK,aAAa;AAAA,QAC7B;AAAA,MACJ,OACK;AACD,YAAI,QAAQ;AACR,cAAI,CAAC,KAAK,aAAa,OAAO;AAC1B,iBAAK,aAAa,QAAQ,KAAK,gBAAgB,SAAS,QAAQ,MAAM;AAAA,UAC1E;AACA,iBAAO,KAAK,aAAa;AAAA,QAC7B,OACK;AACD,cAAI,CAAC,KAAK,aAAa,OAAO;AAC1B,iBAAK,aAAa,QAAQ,KAAK,gBAAgB,SAAS,QAAQ,MAAM;AAAA,UAC1E;AACA,iBAAO,KAAK,aAAa;AAAA,QAC7B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,gBAAgB,SAAS,QAAQ,QAAQ;AACrC,QAAI,UAAU,KAAK,OAAO,IAAI,OAAK,EAAE,eAAe,QAAQ,MAAM,CAAC;AACnE,WAAO,IAAI,aAAa,SAAS,SAAS,KAAK,OAAO,IAAI,OAAK,EAAE,MAAM,CAAC;AAAA,EAC5E;AACJ;AACA,IAAM,eAAN,MAAmB;AAAA,EAIf,YAAY,SAAS,SAAS,OAAO;AAHrC;AACA;AACA;AAEI,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,UAAU,QAAQ,kBAAkB,OAAO;AAAA,EACpD;AAAA,EACA,UAAU;AACN,QAAI,OAAO,KAAK,QAAQ,YAAY,YAAY;AAC5C,WAAK,QAAQ,QAAQ;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,WAAW;AACP,UAAM,IAAI,CAAC;AACX,aAAS,IAAI,GAAG,MAAM,KAAK,MAAM,QAAQ,IAAI,KAAK,KAAK;AACnD,QAAE,KAAK,UAAU,KAAK,MAAM,CAAC,IAAI,OAAO,KAAK,QAAQ,CAAC,CAAC;AAAA,IAC3D;AACA,WAAO,EAAE,KAAK,IAAI;AAAA,EACtB;AAAA,EACA,kBAAkB,QAAQ,eAAe,SAAS;AAC9C,UAAM,SAAS,KAAK,QAAQ,kBAAkB,QAAQ,eAAe,OAAO;AAC5E,QAAI,CAAC,QAAQ;AACT,aAAO;AAAA,IACX;AACA,WAAO;AAAA,MACH,QAAQ,KAAK,MAAM,OAAO,KAAK;AAAA,MAC/B,gBAAgB,OAAO;AAAA,IAC3B;AAAA,EACJ;AACJ;AAKA,IAAM,QAAN,MAAY;AAAA,EAWR,YAAY,WAAW,WAAW,OAAO;AAVzC;AACA;AACA;AAOA,4CAAmB,IAAI,SAAS,CAAC,cAAc,KAAK,MAAM,MAAM,SAAS,CAAC;AAEtE,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,QAAQ;AAAA,EACjB;AAAA,EAXA,OAAO,mBAAmB,QAAQ,UAAU;AACxC,WAAO,KAAK,sBAAsB,WAAW,MAAM,GAAG,QAAQ;AAAA,EAClE;AAAA,EACA,OAAO,sBAAsB,QAAQ,UAAU;AAC3C,WAAO,wBAAwB,QAAQ,QAAQ;AAAA,EACnD;AAAA,EAOA,cAAc;AACV,WAAO,KAAK,UAAU,YAAY;AAAA,EACtC;AAAA,EACA,cAAc;AACV,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,MAAM,WAAW;AACb,QAAI,cAAc,MAAM;AACpB,aAAO,KAAK;AAAA,IAChB;AACA,UAAM,YAAY,UAAU;AAC5B,UAAM,uBAAuB,KAAK,iBAAiB,IAAI,SAAS;AAChE,UAAM,gBAAgB,qBAAqB,KAAK,CAAC,MAAM,8BAA8B,UAAU,QAAQ,EAAE,YAAY,CAAC;AACtH,QAAI,CAAC,eAAe;AAChB,aAAO;AAAA,IACX;AACA,WAAO,IAAI,gBAAgB,cAAc,WAAW,cAAc,YAAY,cAAc,UAAU;AAAA,EAC1G;AACJ;AACA,IAAM,aAAN,MAAM,YAAW;AAAA,EAgBb,YAAY,QAAQ,WAAW;AAf/B;AACA;AAeI,SAAK,SAAS;AACd,SAAK,YAAY;AAAA,EACrB;AAAA,EAhBA,OAAO,KAAK,MAAM,YAAY;AAC1B,eAAW,QAAQ,YAAY;AAC3B,aAAO,IAAI,YAAW,MAAM,IAAI;AAAA,IACpC;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,QAAQ,UAAU;AACrB,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,eAAS,IAAI,YAAW,QAAQ,SAAS,CAAC,CAAC;AAAA,IAC/C;AACA,WAAO;AAAA,EACX;AAAA,EAKA,KAAK,WAAW;AACZ,WAAO,IAAI,YAAW,MAAM,SAAS;AAAA,EACzC;AAAA,EACA,cAAc;AACV,QAAI,OAAO;AACX,UAAM,SAAS,CAAC;AAChB,WAAO,MAAM;AACT,aAAO,KAAK,KAAK,SAAS;AAC1B,aAAO,KAAK;AAAA,IAChB;AACA,WAAO,QAAQ;AACf,WAAO;AAAA,EACX;AAAA,EACA,WAAW;AACP,WAAO,KAAK,YAAY,EAAE,KAAK,GAAG;AAAA,EACtC;AAAA,EACA,QAAQ,OAAO;AACX,QAAI,SAAS,OAAO;AAChB,aAAO;AAAA,IACX;AACA,QAAI,KAAK,WAAW,MAAM;AACtB,aAAO;AAAA,IACX;AACA,WAAO,KAAK,OAAO,QAAQ,KAAK;AAAA,EACpC;AAAA,EACA,sBAAsB,MAAM;AACxB,UAAM,SAAS,CAAC;AAChB,QAAI,OAAO;AACX,WAAO,QAAQ,SAAS,MAAM;AAC1B,aAAO,KAAK,KAAK,SAAS;AAC1B,aAAO,KAAK;AAAA,IAChB;AACA,WAAO,SAAS,OAAO,OAAO,QAAQ,IAAI;AAAA,EAC9C;AACJ;AACA,SAAS,8BAA8B,WAAW,cAAc;AAC5D,MAAI,iBAAiB,MAAM;AACvB,WAAO;AAAA,EACX;AACA,MAAI,QAAQ;AACZ,MAAI,eAAe,aAAa,KAAK;AACrC,SAAO,WAAW;AACd,QAAI,cAAc,UAAU,WAAW,YAAY,GAAG;AAClD;AACA,UAAI,UAAU,aAAa,QAAQ;AAC/B,eAAO;AAAA,MACX;AACA,qBAAe,aAAa,KAAK;AAAA,IACrC;AACA,gBAAY,UAAU;AAAA,EAC1B;AACA,SAAO;AACX;AACA,SAAS,cAAc,WAAW,cAAc;AAC5C,SAAO,iBAAiB,aAAc,UAAU,WAAW,YAAY,KAAK,UAAU,aAAa,MAAM,MAAM;AACnH;AACA,IAAM,kBAAN,MAAsB;AAAA,EAIlB,YAAY,WAAW,cAAc,cAAc;AAHnD;AACA;AACA;AAEI,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,eAAe;AAAA,EACxB;AACJ;AAIA,SAAS,WAAW,QAAQ;AACxB,MAAI,CAAC,QAAQ;AACT,WAAO,CAAC;AAAA,EACZ;AACA,MAAI,CAAC,OAAO,YAAY,CAAC,MAAM,QAAQ,OAAO,QAAQ,GAAG;AACrD,WAAO,CAAC;AAAA,EACZ;AACA,MAAI,WAAW,OAAO;AACtB,MAAI,SAAS,CAAC,GAAG,YAAY;AAC7B,WAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,IAAI,KAAK,KAAK;AACjD,QAAI,QAAQ,SAAS,CAAC;AACtB,QAAI,CAAC,MAAM,UAAU;AACjB;AAAA,IACJ;AACA,QAAI;AACJ,QAAI,OAAO,MAAM,UAAU,UAAU;AACjC,UAAI,SAAS,MAAM;AAEnB,eAAS,OAAO,QAAQ,SAAS,EAAE;AAEnC,eAAS,OAAO,QAAQ,SAAS,EAAE;AACnC,eAAS,OAAO,MAAM,GAAG;AAAA,IAC7B,WACS,MAAM,QAAQ,MAAM,KAAK,GAAG;AACjC,eAAS,MAAM;AAAA,IACnB,OACK;AACD,eAAS,CAAC,EAAE;AAAA,IAChB;AACA,QAAI,YAAY;AAChB,QAAI,OAAO,MAAM,SAAS,cAAc,UAAU;AAC9C,kBAAY;AACZ,UAAI,WAAW,MAAM,SAAS,UAAU,MAAM,GAAG;AACjD,eAAS,IAAI,GAAG,OAAO,SAAS,QAAQ,IAAI,MAAM,KAAK;AACnD,YAAI,UAAU,SAAS,CAAC;AACxB,gBAAQ,SAAS;AAAA,UACb,KAAK;AACD,wBAAY,YAAY;AACxB;AAAA,UACJ,KAAK;AACD,wBAAY,YAAY;AACxB;AAAA,UACJ,KAAK;AACD,wBAAY,YAAY;AACxB;AAAA,UACJ,KAAK;AACD,wBAAY,YAAY;AACxB;AAAA,QACR;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,aAAa;AACjB,QAAI,OAAO,MAAM,SAAS,eAAe,YAAY,gBAAgB,MAAM,SAAS,UAAU,GAAG;AAC7F,mBAAa,MAAM,SAAS;AAAA,IAChC;AACA,QAAI,aAAa;AACjB,QAAI,OAAO,MAAM,SAAS,eAAe,YAAY,gBAAgB,MAAM,SAAS,UAAU,GAAG;AAC7F,mBAAa,MAAM,SAAS;AAAA,IAChC;AACA,aAAS,IAAI,GAAG,OAAO,OAAO,QAAQ,IAAI,MAAM,KAAK;AACjD,UAAI,SAAS,OAAO,CAAC,EAAE,KAAK;AAC5B,UAAI,WAAW,OAAO,MAAM,GAAG;AAC/B,UAAI,QAAQ,SAAS,SAAS,SAAS,CAAC;AACxC,UAAI,eAAe;AACnB,UAAI,SAAS,SAAS,GAAG;AACrB,uBAAe,SAAS,MAAM,GAAG,SAAS,SAAS,CAAC;AACpD,qBAAa,QAAQ;AAAA,MACzB;AACA,aAAO,WAAW,IAAI,IAAI,gBAAgB,OAAO,cAAc,GAAG,WAAW,YAAY,UAAU;AAAA,IACvG;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,kBAAN,MAAsB;AAAA,EAOlB,YAAY,OAAO,cAAc,OAAO,WAAW,YAAY,YAAY;AAN3E;AACA;AACA;AACA;AACA;AACA;AAEI,SAAK,QAAQ;AACb,SAAK,eAAe;AACpB,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,aAAa;AAAA,EACtB;AACJ;AAIA,SAAS,wBAAwB,kBAAkB,WAAW;AAE1D,mBAAiB,KAAK,CAAC,GAAG,MAAM;AAC5B,QAAI,IAAI,OAAO,EAAE,OAAO,EAAE,KAAK;AAC/B,QAAI,MAAM,GAAG;AACT,aAAO;AAAA,IACX;AACA,QAAI,UAAU,EAAE,cAAc,EAAE,YAAY;AAC5C,QAAI,MAAM,GAAG;AACT,aAAO;AAAA,IACX;AACA,WAAO,EAAE,QAAQ,EAAE;AAAA,EACvB,CAAC;AAED,MAAI,mBAAmB;AACvB,MAAI,oBAAoB;AACxB,MAAI,oBAAoB;AACxB,SAAO,iBAAiB,UAAU,KAAK,iBAAiB,CAAC,EAAE,UAAU,IAAI;AACrE,QAAI,mBAAmB,iBAAiB,MAAM;AAC9C,QAAI,iBAAiB,cAAc,IAA2B;AAC1D,yBAAmB,iBAAiB;AAAA,IACxC;AACA,QAAI,iBAAiB,eAAe,MAAM;AACtC,0BAAoB,iBAAiB;AAAA,IACzC;AACA,QAAI,iBAAiB,eAAe,MAAM;AACtC,0BAAoB,iBAAiB;AAAA,IACzC;AAAA,EACJ;AACA,MAAI,WAAW,IAAI,SAAS,SAAS;AACrC,MAAI,WAAW,IAAI,gBAAgB,kBAAkB,SAAS,MAAM,iBAAiB,GAAG,SAAS,MAAM,iBAAiB,CAAC;AACzH,MAAIC,QAAO,IAAI,iBAAiB,IAAI,qBAAqB,GAAG,MAAM,IAA2B,GAAG,CAAC,GAAG,CAAC,CAAC;AACtG,WAAS,IAAI,GAAG,MAAM,iBAAiB,QAAQ,IAAI,KAAK,KAAK;AACzD,QAAI,OAAO,iBAAiB,CAAC;AAC7B,IAAAA,MAAK,OAAO,GAAG,KAAK,OAAO,KAAK,cAAc,KAAK,WAAW,SAAS,MAAM,KAAK,UAAU,GAAG,SAAS,MAAM,KAAK,UAAU,CAAC;AAAA,EAClI;AACA,SAAO,IAAI,MAAM,UAAU,UAAUA,KAAI;AAC7C;AACA,IAAM,WAAN,MAAe;AAAA,EAKX,YAAY,WAAW;AAJvB;AACA;AACA;AACA;AAEI,SAAK,eAAe;AACpB,SAAK,YAAY,CAAC;AAClB,SAAK,YAAY,uBAAO,OAAO,IAAI;AACnC,QAAI,MAAM,QAAQ,SAAS,GAAG;AAC1B,WAAK,YAAY;AACjB,eAAS,IAAI,GAAG,MAAM,UAAU,QAAQ,IAAI,KAAK,KAAK;AAClD,aAAK,UAAU,UAAU,CAAC,CAAC,IAAI;AAC/B,aAAK,UAAU,CAAC,IAAI,UAAU,CAAC;AAAA,MACnC;AAAA,IACJ,OACK;AACD,WAAK,YAAY;AAAA,IACrB;AAAA,EACJ;AAAA,EACA,MAAM,OAAO;AACT,QAAI,UAAU,MAAM;AAChB,aAAO;AAAA,IACX;AACA,YAAQ,MAAM,YAAY;AAC1B,QAAI,QAAQ,KAAK,UAAU,KAAK;AAChC,QAAI,OAAO;AACP,aAAO;AAAA,IACX;AACA,QAAI,KAAK,WAAW;AAChB,YAAM,IAAI,MAAM,gCAAgC,KAAK,EAAE;AAAA,IAC3D;AACA,YAAQ,EAAE,KAAK;AACf,SAAK,UAAU,KAAK,IAAI;AACxB,SAAK,UAAU,KAAK,IAAI;AACxB,WAAO;AAAA,EACX;AAAA,EACA,cAAc;AACV,WAAO,KAAK,UAAU,MAAM,CAAC;AAAA,EACjC;AACJ;AACA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EAMvB,YAAY,YAAY,cAAc,WAAW,YAAY,YAAY;AALzE;AACA;AACA;AACA;AACA;AAEI,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,QAAQ;AACJ,WAAO,IAAI,sBAAqB,KAAK,YAAY,KAAK,cAAc,KAAK,WAAW,KAAK,YAAY,KAAK,UAAU;AAAA,EACxH;AAAA,EACA,OAAO,SAAS,KAAK;AACjB,QAAI,IAAI,CAAC;AACT,aAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC5C,QAAE,CAAC,IAAI,IAAI,CAAC,EAAE,MAAM;AAAA,IACxB;AACA,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB,YAAY,WAAW,YAAY,YAAY;AAC3D,QAAI,KAAK,aAAa,YAAY;AAC9B,cAAQ,IAAI,sBAAsB;AAAA,IACtC,OACK;AACD,WAAK,aAAa;AAAA,IACtB;AAEA,QAAI,cAAc,IAA2B;AACzC,WAAK,YAAY;AAAA,IACrB;AACA,QAAI,eAAe,GAAG;AAClB,WAAK,aAAa;AAAA,IACtB;AACA,QAAI,eAAe,GAAG;AAClB,WAAK,aAAa;AAAA,IACtB;AAAA,EACJ;AACJ;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EAInB,YAAY,WAAW,wBAAwB,CAAC,GAAG,YAAY,CAAC,GAAG;AAHnE;AACA;AACA;AAEI,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,yBAAyB;AAAA,EAClC;AAAA,EACA,OAAO,mBAAmB,KAAK;AAC3B,QAAI,IAAI,WAAW,GAAG;AAClB,aAAO;AAAA,IACX;AACA,QAAI,KAAK,KAAK,iBAAiB;AAC/B,WAAO;AAAA,EACX;AAAA,EACA,OAAO,kBAAkB,GAAG,GAAG;AAC3B,QAAI,EAAE,eAAe,EAAE,YAAY;AAC/B,YAAM,gBAAgB,EAAE;AACxB,YAAM,gBAAgB,EAAE;AACxB,UAAI,mBAAmB,kBAAkB,OAAO,IAAI,cAAc;AAClE,UAAI,mBAAmB,kBAAkB,OAAO,IAAI,cAAc;AAClE,UAAI,qBAAqB,kBAAkB;AACvC,iBAAS,IAAI,GAAG,IAAI,kBAAkB,KAAK;AACvC,gBAAM,OAAO,cAAc,CAAC,EAAE;AAC9B,gBAAM,OAAO,cAAc,CAAC,EAAE;AAC9B,cAAI,SAAS,MAAM;AACf,mBAAO,OAAO;AAAA,UAClB;AAAA,QACJ;AAAA,MACJ;AACA,aAAO,mBAAmB;AAAA,IAC9B;AACA,WAAO,EAAE,aAAa,EAAE;AAAA,EAC5B;AAAA,EACA,MAAM,OAAO;AACT,QAAI,UAAU,IAAI;AACd,aAAO,kBAAiB,mBAAmB,CAAC,EAAE,OAAO,KAAK,SAAS,EAAE,OAAO,KAAK,sBAAsB,CAAC;AAAA,IAC5G;AACA,QAAI,WAAW,MAAM,QAAQ,GAAG;AAChC,QAAIC;AACJ,QAAI;AACJ,QAAI,aAAa,IAAI;AACjB,MAAAA,QAAO;AACP,aAAO;AAAA,IACX,OACK;AACD,MAAAA,QAAO,MAAM,UAAU,GAAG,QAAQ;AAClC,aAAO,MAAM,UAAU,WAAW,CAAC;AAAA,IACvC;AACA,QAAI,KAAK,UAAU,eAAeA,KAAI,GAAG;AACrC,aAAO,KAAK,UAAUA,KAAI,EAAE,MAAM,IAAI;AAAA,IAC1C;AACA,WAAO,kBAAiB,mBAAmB,CAAC,EAAE,OAAO,KAAK,SAAS,EAAE,OAAO,KAAK,sBAAsB,CAAC;AAAA,EAC5G;AAAA,EACA,OAAO,YAAY,OAAO,cAAc,WAAW,YAAY,YAAY;AACvE,QAAI,UAAU,IAAI;AACd,WAAK,cAAc,YAAY,cAAc,WAAW,YAAY,UAAU;AAC9E;AAAA,IACJ;AACA,QAAI,WAAW,MAAM,QAAQ,GAAG;AAChC,QAAIA;AACJ,QAAI;AACJ,QAAI,aAAa,IAAI;AACjB,MAAAA,QAAO;AACP,aAAO;AAAA,IACX,OACK;AACD,MAAAA,QAAO,MAAM,UAAU,GAAG,QAAQ;AAClC,aAAO,MAAM,UAAU,WAAW,CAAC;AAAA,IACvC;AACA,QAAI;AACJ,QAAI,KAAK,UAAU,eAAeA,KAAI,GAAG;AACrC,cAAQ,KAAK,UAAUA,KAAI;AAAA,IAC/B,OACK;AACD,cAAQ,IAAI,kBAAiB,KAAK,UAAU,MAAM,GAAG,qBAAqB,SAAS,KAAK,sBAAsB,CAAC;AAC/G,WAAK,UAAUA,KAAI,IAAI;AAAA,IAC3B;AACA,UAAM,OAAO,aAAa,GAAG,MAAM,cAAc,WAAW,YAAY,UAAU;AAAA,EACtF;AAAA,EACA,cAAc,YAAY,cAAc,WAAW,YAAY,YAAY;AACvE,QAAI,iBAAiB,MAAM;AAEvB,WAAK,UAAU,gBAAgB,YAAY,WAAW,YAAY,UAAU;AAC5E;AAAA,IACJ;AAEA,aAAS,IAAI,GAAG,MAAM,KAAK,uBAAuB,QAAQ,IAAI,KAAK,KAAK;AACpE,UAAI,OAAO,KAAK,uBAAuB,CAAC;AACxC,UAAI,UAAU,KAAK,cAAc,YAAY,MAAM,GAAG;AAElD,aAAK,gBAAgB,YAAY,WAAW,YAAY,UAAU;AAClE;AAAA,MACJ;AAAA,IACJ;AAGA,QAAI,cAAc,IAA2B;AACzC,kBAAY,KAAK,UAAU;AAAA,IAC/B;AACA,QAAI,eAAe,GAAG;AAClB,mBAAa,KAAK,UAAU;AAAA,IAChC;AACA,QAAI,eAAe,GAAG;AAClB,mBAAa,KAAK,UAAU;AAAA,IAChC;AACA,SAAK,uBAAuB,KAAK,IAAI,qBAAqB,YAAY,cAAc,WAAW,YAAY,UAAU,CAAC;AAAA,EAC1H;AACJ;AAKA,IAAM,uBAAN,MAA2B;AAAA,EAGvB,YAAY,YAAY,WAAW;AAFnC;AACA;AAEI,SAAK,aAAa;AAClB,SAAK,YAAY;AAAA,EACrB;AACJ;AACA,IAAM,gCAAN,MAAM,8BAA6B;AAAA,EAG/B,YAAY,mBAAmB,mBAAmB;AAFlD;AACA;AAeA,oDAA2B,IAAI,SAAS,CAAC,cAAc;AACnD,YAAM,aAAa,KAAK,iBAAiB,SAAS;AAClD,YAAM,oBAAoB,KAAK,qBAAqB,SAAS;AAC7D,aAAO,IAAI,qBAAqB,YAAY,iBAAiB;AAAA,IACjE,CAAC;AAjBG,SAAK,qBAAqB,IAAI;AAAA,MAAqB;AAAA,MAAmB;AAAA;AAAA,IAAwC;AAC9G,SAAK,4BAA4B,IAAI,aAAa,OAAO,QAAQ,qBAAqB,CAAC,CAAC,CAAC;AAAA,EAC7F;AAAA,EACA,uBAAuB;AACnB,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,wBAAwB,WAAW;AAC/B,QAAI,cAAc,MAAM;AACpB,aAAO,8BAA6B;AAAA,IACxC;AACA,WAAO,KAAK,yBAAyB,IAAI,SAAS;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,iBAAiB,OAAO;AACpB,WAAO,KAAK,0BAA0B,MAAM,KAAK,KAAK;AAAA,EAC1D;AAAA,EACA,qBAAqB,WAAW;AAC5B,UAAM,IAAI,UAAU,MAAM,8BAA6B,0BAA0B;AACjF,QAAI,CAAC,GAAG;AACJ,aAAO;AAAA,IACX;AACA,YAAQ,EAAE,CAAC,GAAG;AAAA,MACV,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,IACf;AACA,UAAM,IAAI,MAAM,2CAA2C;AAAA,EAC/D;AAEJ;AA/BI,cAhBE,+BAgBK,wBAAuB,IAAI,qBAAqB,GAAG,CAAC;AA8B3D,cA9CE,+BA8CK,8BAA6B;AA9CxC,IAAM,+BAAN;AAgDA,IAAM,eAAN,MAAmB;AAAA,EAGf,YAAY,QAAQ;AAFpB;AACA;AAEI,QAAI,OAAO,WAAW,GAAG;AACrB,WAAK,SAAS;AACd,WAAK,eAAe;AAAA,IACxB,OACK;AACD,WAAK,SAAS,IAAI,IAAI,MAAM;AAE5B,YAAM,gBAAgB,OAAO,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM,uBAAuB,SAAS,CAAC;AAC1F,oBAAc,KAAK;AACnB,oBAAc,QAAQ;AACtB,WAAK,eAAe,IAAI,OAAO,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa,EAAE;AAAA,IACjF;AAAA,EACJ;AAAA,EACA,MAAM,OAAO;AACT,QAAI,CAAC,KAAK,cAAc;AACpB,aAAO;AAAA,IACX;AACA,UAAM,IAAI,MAAM,MAAM,KAAK,YAAY;AACvC,QAAI,CAAC,GAAG;AAEJ,aAAO;AAAA,IACX;AACA,WAAO,KAAK,OAAO,IAAI,EAAE,CAAC,CAAC;AAAA,EAC/B;AACJ;AAKA,IAAM,uBAAN,MAA2B;AAAA,EAGvB,YAAY,OAAO,cAAc;AAFjC;AACA;AAEI,SAAK,QAAQ;AACb,SAAK,eAAe;AAAA,EACxB;AACJ;AAaA,SAAS,gBAAgB,SAAS,UAAU,aAAa,SAAS,OAAO,YAAY,sBAAsB,WAAW;AAClH,QAAM,aAAa,SAAS,QAAQ;AACpC,MAAI,OAAO;AACX,MAAI,iBAAiB;AACrB,MAAI,sBAAsB;AACtB,UAAM,mBAAmB,sBAAsB,SAAS,UAAU,aAAa,SAAS,OAAO,UAAU;AACzG,YAAQ,iBAAiB;AACzB,cAAU,iBAAiB;AAC3B,kBAAc,iBAAiB;AAC/B,qBAAiB,iBAAiB;AAAA,EACtC;AACA,QAAM,YAAY,KAAK,IAAI;AAC3B,SAAO,CAAC,MAAM;AACV,QAAI,cAAc,GAAG;AACjB,YAAM,cAAc,KAAK,IAAI,IAAI;AACjC,UAAI,cAAc,WAAW;AACzB,eAAO,IAAI,qBAAqB,OAAO,IAAI;AAAA,MAC/C;AAAA,IACJ;AACA,aAAS;AAAA,EACb;AACA,SAAO,IAAI,qBAAqB,OAAO,KAAK;AAC5C,WAAS,WAAW;AAChB,UAAM,IAAI,sBAAsB,SAAS,UAAU,aAAa,SAAS,OAAO,cAAc;AAC9F,QAAI,CAAC,GAAG;AAEJ,iBAAW,QAAQ,OAAO,UAAU;AACpC,aAAO;AACP;AAAA,IACJ;AACA,UAAM,iBAAiB,EAAE;AACzB,UAAM,gBAAgB,EAAE;AACxB,UAAM,cAAc,kBAAkB,eAAe,SAAS,IACxD,eAAe,CAAC,EAAE,MAAM,UACxB;AACN,QAAI,kBAAkB,WAAW;AAE7B,YAAM,aAAa,MAAM,QAAQ,OAAO;AACxC,iBAAW,QAAQ,OAAO,eAAe,CAAC,EAAE,KAAK;AACjD,cAAQ,MAAM,0BAA0B,MAAM,cAAc;AAC5D,qBAAe,SAAS,UAAU,aAAa,OAAO,YAAY,WAAW,aAAa,cAAc;AACxG,iBAAW,QAAQ,OAAO,eAAe,CAAC,EAAE,GAAG;AAE/C,YAAM,SAAS;AACf,cAAQ,MAAM;AACd,uBAAiB,OAAO,aAAa;AACrC,UAAI,CAAC,eAAe,OAAO,YAAY,MAAM,SAAS;AAGlD,gBAAQ;AACR,mBAAW,QAAQ,OAAO,UAAU;AACpC,eAAO;AACP;AAAA,MACJ;AAAA,IACJ,OACK;AAED,YAAM,QAAQ,QAAQ,QAAQ,aAAa;AAC3C,iBAAW,QAAQ,OAAO,eAAe,CAAC,EAAE,KAAK;AACjD,YAAM,aAAa;AAEnB,YAAM,YAAY,MAAM,QAAQ,SAAS,SAAS,cAAc;AAChE,YAAM,iBAAiB,MAAM,sBAAsB,eAAe,WAAW,OAAO;AACpF,cAAQ,MAAM,KAAK,eAAe,SAAS,gBAAgB,eAAe,CAAC,EAAE,QAAQ,YAAY,MAAM,gBAAgB,cAAc;AACrI,UAAI,iBAAiB,cAAc;AAC/B,cAAM,aAAa;AACnB,uBAAe,SAAS,UAAU,aAAa,OAAO,YAAY,WAAW,eAAe,cAAc;AAC1G,mBAAW,QAAQ,OAAO,eAAe,CAAC,EAAE,GAAG;AAC/C,yBAAiB,eAAe,CAAC,EAAE;AACnC,cAAM,cAAc,WAAW,eAAe,SAAS,SAAS,cAAc;AAC9E,cAAM,wBAAwB,eAAe,eAAe,aAAa,OAAO;AAChF,gBAAQ,MAAM,0BAA0B,qBAAqB;AAC7D,YAAI,WAAW,sBAAsB;AACjC,kBAAQ,MAAM,YAAY,WAAW,iCAAiC,SAAS,SAAS,cAAc,CAAC;AAAA,QAC3G;AACA,YAAI,CAAC,eAAe,WAAW,cAAc,KAAK,GAAG;AACjD,kBAAQ,MAAM,IAAI;AAClB,qBAAW,QAAQ,OAAO,UAAU;AACpC,iBAAO;AACP;AAAA,QACJ;AAAA,MACJ,WACS,iBAAiB,gBAAgB;AACtC,cAAM,aAAa;AACnB,uBAAe,SAAS,UAAU,aAAa,OAAO,YAAY,WAAW,eAAe,cAAc;AAC1G,mBAAW,QAAQ,OAAO,eAAe,CAAC,EAAE,GAAG;AAC/C,yBAAiB,eAAe,CAAC,EAAE;AACnC,cAAM,cAAc,WAAW,eAAe,SAAS,SAAS,cAAc;AAC9E,cAAM,wBAAwB,eAAe,eAAe,aAAa,OAAO;AAChF,gBAAQ,MAAM,0BAA0B,qBAAqB;AAC7D,YAAI,WAAW,wBAAwB;AACnC,kBAAQ,MAAM,YAAY,WAAW,mCAAmC,SAAS,SAAS,cAAc,CAAC;AAAA,QAC7G;AACA,YAAI,CAAC,eAAe,WAAW,cAAc,KAAK,GAAG;AACjD,kBAAQ,MAAM,IAAI;AAClB,qBAAW,QAAQ,OAAO,UAAU;AACpC,iBAAO;AACP;AAAA,QACJ;AAAA,MACJ,OACK;AACD,cAAM,eAAe;AACrB,uBAAe,SAAS,UAAU,aAAa,OAAO,YAAY,aAAa,UAAU,cAAc;AACvG,mBAAW,QAAQ,OAAO,eAAe,CAAC,EAAE,GAAG;AAE/C,gBAAQ,MAAM,IAAI;AAClB,YAAI,CAAC,aAAa;AACd,kBAAQ,MAAM,QAAQ;AACtB,qBAAW,QAAQ,OAAO,UAAU;AACpC,iBAAO;AACP;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,eAAe,CAAC,EAAE,MAAM,SAAS;AAEjC,gBAAU,eAAe,CAAC,EAAE;AAC5B,oBAAc;AAAA,IAClB;AAAA,EACJ;AACJ;AAMA,SAAS,sBAAsB,SAAS,UAAU,aAAa,SAAS,OAAO,YAAY;AACvF,MAAI,iBAAkB,MAAM,uBAAuB,IAAI;AACvD,QAAM,aAAa,CAAC;AACpB,WAAS,OAAO,OAAO,MAAM,OAAO,KAAK,IAAI,GAAG;AAC5C,UAAM,WAAW,KAAK,QAAQ,OAAO;AACrC,QAAI,oBAAoB,gBAAgB;AACpC,iBAAW,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ;AACA,WAAS,YAAY,WAAW,IAAI,GAAG,WAAW,YAAY,WAAW,IAAI,GAAG;AAC5E,UAAM,EAAE,aAAa,YAAY,IAAI,uBAAuB,UAAU,MAAM,SAAS,UAAU,MAAM,SAAS,aAAa,YAAY,cAAc;AACrJ,UAAM,IAAI,YAAY,kBAAkB,UAAU,SAAS,WAAW;AACtE,QAAI,GAAG;AACH,YAAM,gBAAgB,EAAE;AACxB,UAAI,kBAAkB,aAAa;AAE/B,gBAAQ,UAAU,MAAM,IAAI;AAC5B;AAAA,MACJ;AACA,UAAI,EAAE,kBAAkB,EAAE,eAAe,QAAQ;AAC7C,mBAAW,QAAQ,UAAU,OAAO,EAAE,eAAe,CAAC,EAAE,KAAK;AAC7D,uBAAe,SAAS,UAAU,aAAa,UAAU,OAAO,YAAY,UAAU,KAAK,eAAe,EAAE,cAAc;AAC1H,mBAAW,QAAQ,UAAU,OAAO,EAAE,eAAe,CAAC,EAAE,GAAG;AAC3D,yBAAiB,EAAE,eAAe,CAAC,EAAE;AACrC,YAAI,EAAE,eAAe,CAAC,EAAE,MAAM,SAAS;AACnC,oBAAU,EAAE,eAAe,CAAC,EAAE;AAC9B,wBAAc;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ,OACK;AACD,cAAQ,UAAU,MAAM,IAAI;AAC5B;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,EAAE,OAAc,SAAkB,gBAAgC,YAAyB;AACtG;AACA,SAAS,sBAAsB,SAAS,UAAU,aAAa,SAAS,OAAO,gBAAgB;AAE3F,QAAM,cAAc,UAAU,SAAS,UAAU,aAAa,SAAS,OAAO,cAAc;AAE5F,QAAM,aAAa,QAAQ,cAAc;AACzC,MAAI,WAAW,WAAW,GAAG;AAEzB,WAAO;AAAA,EACX;AACA,QAAM,kBAAkB,gBAAgB,YAAY,SAAS,UAAU,aAAa,SAAS,OAAO,cAAc;AAClH,MAAI,CAAC,iBAAiB;AAElB,WAAO;AAAA,EACX;AACA,MAAI,CAAC,aAAa;AAEd,WAAO;AAAA,EACX;AAEA,QAAM,mBAAmB,YAAY,eAAe,CAAC,EAAE;AACvD,QAAM,uBAAuB,gBAAgB,eAAe,CAAC,EAAE;AAC/D,MAAI,uBAAuB,oBAAqB,gBAAgB,iBAAiB,yBAAyB,kBAAmB;AAEzH,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,UAAU,SAAS,UAAU,aAAa,SAAS,OAAO,gBAAgB;AAC/E,QAAM,OAAO,MAAM,QAAQ,OAAO;AAClC,QAAM,EAAE,aAAa,YAAY,IAAI,kBAAkB,MAAM,SAAS,MAAM,SAAS,aAAa,YAAY,cAAc;AAC5H,QAAM,IAAI,YAAY,kBAAkB,UAAU,SAAS,WAAW;AACtE,MAAI,GAAG;AACH,WAAO;AAAA,MACH,gBAAgB,EAAE;AAAA,MAClB,eAAe,EAAE;AAAA,IACrB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,YAAY,SAAS,UAAU,aAAa,SAAS,OAAO,gBAAgB;AAEjG,MAAI,kBAAkB,OAAO;AAC7B,MAAI,0BAA0B;AAC9B,MAAI;AACJ,MAAI,0BAA0B;AAC9B,QAAM,SAAS,MAAM,sBAAsB,cAAc;AACzD,WAAS,IAAI,GAAG,MAAM,WAAW,QAAQ,IAAI,KAAK,KAAK;AACnD,UAAM,YAAY,WAAW,CAAC;AAC9B,QAAI,CAAC,UAAU,QAAQ,MAAM,GAAG;AAE5B;AAAA,IACJ;AACA,UAAM,OAAO,QAAQ,QAAQ,UAAU,MAAM;AAC7C,UAAM,EAAE,aAAa,YAAY,IAAI,kBAAkB,MAAM,SAAS,MAAM,aAAa,YAAY,cAAc;AACnH,UAAM,cAAc,YAAY,kBAAkB,UAAU,SAAS,WAAW;AAChF,QAAI,CAAC,aAAa;AACd;AAAA,IACJ;AACA,UAAM,cAAc,YAAY,eAAe,CAAC,EAAE;AAClD,QAAI,eAAe,iBAAiB;AAEhC;AAAA,IACJ;AACA,sBAAkB;AAClB,8BAA0B,YAAY;AACtC,sBAAkB,YAAY;AAC9B,8BAA0B,UAAU;AACpC,QAAI,oBAAoB,SAAS;AAE7B;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,yBAAyB;AACzB,WAAO;AAAA,MACH,eAAe,4BAA4B;AAAA,MAC3C,gBAAgB;AAAA,MAChB,eAAe;AAAA,IACnB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,kBAAkB,MAAM,SAAS,gBAAgB,QAAQ,QAAQ;AACtE,QAAM,cAAc,KAAK,UAAU,SAAS,gBAAgB,QAAQ,MAAM;AAC1E,SAAO;AAAA,IAAE;AAAA,IAAa,aAAa;AAAA;AAAA,EAAwB;AAC/D;AACA,SAAS,uBAAuB,MAAM,SAAS,gBAAgB,QAAQ,QAAQ;AAC3E,QAAM,cAAc,KAAK,eAAe,SAAS,gBAAgB,QAAQ,MAAM;AAC/E,SAAO;AAAA,IAAE;AAAA,IAAa,aAAa;AAAA;AAAA,EAAwB;AAC/D;AACA,SAAS,eAAe,SAAS,UAAU,aAAa,OAAO,YAAY,UAAU,gBAAgB;AACjG,MAAI,SAAS,WAAW,GAAG;AACvB;AAAA,EACJ;AACA,QAAM,kBAAkB,SAAS;AACjC,QAAM,MAAM,KAAK,IAAI,SAAS,QAAQ,eAAe,MAAM;AAC3D,QAAM,aAAa,CAAC;AACpB,QAAM,SAAS,eAAe,CAAC,EAAE;AACjC,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,UAAM,cAAc,SAAS,CAAC;AAC9B,QAAI,gBAAgB,MAAM;AAEtB;AAAA,IACJ;AACA,UAAM,eAAe,eAAe,CAAC;AACrC,QAAI,aAAa,WAAW,GAAG;AAE3B;AAAA,IACJ;AACA,QAAI,aAAa,QAAQ,QAAQ;AAE7B;AAAA,IACJ;AAEA,WAAO,WAAW,SAAS,KAAK,WAAW,WAAW,SAAS,CAAC,EAAE,UAAU,aAAa,OAAO;AAE5F,iBAAW,kBAAkB,WAAW,WAAW,SAAS,CAAC,EAAE,QAAQ,WAAW,WAAW,SAAS,CAAC,EAAE,MAAM;AAC/G,iBAAW,IAAI;AAAA,IACnB;AACA,QAAI,WAAW,SAAS,GAAG;AACvB,iBAAW,kBAAkB,WAAW,WAAW,SAAS,CAAC,EAAE,QAAQ,aAAa,KAAK;AAAA,IAC7F,OACK;AACD,iBAAW,QAAQ,OAAO,aAAa,KAAK;AAAA,IAChD;AACA,QAAI,YAAY,8BAA8B;AAE1C,YAAM,YAAY,YAAY,QAAQ,iBAAiB,cAAc;AACrE,YAAM,iBAAiB,MAAM,sBAAsB,eAAe,WAAW,OAAO;AACpF,YAAM,cAAc,YAAY,eAAe,iBAAiB,cAAc;AAC9E,YAAM,wBAAwB,eAAe,eAAe,aAAa,OAAO;AAChF,YAAM,aAAa,MAAM,KAAK,YAAY,8BAA8B,aAAa,OAAO,IAAI,OAAO,MAAM,gBAAgB,qBAAqB;AAClJ,YAAM,aAAa,QAAQ,iBAAiB,gBAAgB,UAAU,GAAG,aAAa,GAAG,CAAC;AAC1F;AAAA,QAAgB;AAAA,QAAS;AAAA,QAAa,eAAe,aAAa,UAAU;AAAA,QAAI,aAAa;AAAA,QAAO;AAAA,QAAY;AAAA,QAAY;AAAA;AAAA,QAA2B;AAAA,MAAC;AACxJ,wBAAkB,UAAU;AAC5B;AAAA,IACJ;AACA,UAAM,uBAAuB,YAAY,QAAQ,iBAAiB,cAAc;AAChF,QAAI,yBAAyB,MAAM;AAE/B,YAAM,OAAO,WAAW,SAAS,IAAI,WAAW,WAAW,SAAS,CAAC,EAAE,SAAS,MAAM;AACtF,YAAM,wBAAwB,KAAK,eAAe,sBAAsB,OAAO;AAC/E,iBAAW,KAAK,IAAI,kBAAkB,uBAAuB,aAAa,GAAG,CAAC;AAAA,IAClF;AAAA,EACJ;AACA,SAAO,WAAW,SAAS,GAAG;AAE1B,eAAW,kBAAkB,WAAW,WAAW,SAAS,CAAC,EAAE,QAAQ,WAAW,WAAW,SAAS,CAAC,EAAE,MAAM;AAC/G,eAAW,IAAI;AAAA,EACnB;AACJ;AACA,IAAM,oBAAN,MAAwB;AAAA,EAGpB,YAAY,QAAQ,QAAQ;AAF5B;AACA;AAEI,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,EAClB;AACJ;AAKA,SAAS,cAAc,WAAW,SAAS,iBAAiB,mBAAmB,YAAY,0BAA0B,mBAAmB,SAAS;AAC7I,SAAO,IAAI,QAAQ,WAAW,SAAS,iBAAiB,mBAAmB,YAAY,0BAA0B,mBAAmB,OAAO;AAC/I;AACA,SAAS,kBAAkB,QAAQ,UAAU,MAAM,mBAAmB,SAAS;AAC3E,QAAM,WAAW,eAAe,UAAU,WAAW;AACrD,QAAM,SAAS,YAAY,kBAAkB,MAAM,mBAAmB,QAAQ,UAAU;AACxF,aAAW,WAAW,UAAU;AAC5B,WAAO,KAAK;AAAA,MACR,eAAe;AAAA,MACf,SAAS,QAAQ;AAAA,MACjB;AAAA,MACA;AAAA,MACA,UAAU,QAAQ;AAAA,IACtB,CAAC;AAAA,EACL;AACJ;AACA,SAAS,YAAY,YAAY,QAAQ;AACrC,MAAI,OAAO,SAAS,WAAW,QAAQ;AACnC,WAAO;AAAA,EACX;AACA,MAAI,YAAY;AAChB,SAAO,WAAW,MAAM,gBAAc;AAClC,aAAS,IAAI,WAAW,IAAI,OAAO,QAAQ,KAAK;AAC5C,UAAI,kBAAkB,OAAO,CAAC,GAAG,UAAU,GAAG;AAC1C,oBAAY,IAAI;AAChB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX,CAAC;AACL;AACA,SAAS,kBAAkB,eAAe,WAAW;AACjD,MAAI,CAAC,eAAe;AAChB,WAAO;AAAA,EACX;AACA,MAAI,kBAAkB,WAAW;AAC7B,WAAO;AAAA,EACX;AACA,QAAM,MAAM,UAAU;AACtB,SAAO,cAAc,SAAS,OAAO,cAAc,OAAO,GAAG,GAAG,MAAM,aAAa,cAAc,GAAG,MAAM;AAC9G;AACA,IAAM,UAAN,MAAc;AAAA,EAcV,YAAY,gBAAgB,SAAS,iBAAiB,mBAAmB,YAAY,0BAA0B,mBAAmB,UAAU;AAb5I;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGI,SAAK,iBAAiB;AACtB,SAAK,2BAA2B;AAChC,SAAK,WAAW;AAChB,SAAK,gCAAgC,IAAI,6BAA6B,iBAAiB,iBAAiB;AACxG,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,eAAe,CAAC,IAAI;AACzB,SAAK,oBAAoB,CAAC;AAC1B,SAAK,qBAAqB;AAC1B,SAAK,WAAW,YAAY,SAAS,IAAI;AACzC,SAAK,cAAc;AACnB,SAAK,qBAAqB,CAAC;AAC3B,QAAI,YAAY;AACZ,iBAAW,YAAY,OAAO,KAAK,UAAU,GAAG;AAC5C,cAAM,WAAW,eAAe,UAAU,WAAW;AACrD,mBAAW,WAAW,UAAU;AAC5B,eAAK,mBAAmB,KAAK;AAAA,YACzB,SAAS,QAAQ;AAAA,YACjB,MAAM,WAAW,QAAQ;AAAA,UAC7B,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EAzBA,IAAI,gBAAgB;AAAE,WAAO,KAAK;AAAA,EAAoB;AAAA,EA0BtD,UAAU;AACN,eAAW,QAAQ,KAAK,cAAc;AAClC,UAAI,MAAM;AACN,aAAK,QAAQ;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,kBAAkB,SAAS;AACvB,WAAO,KAAK,SAAS,kBAAkB,OAAO;AAAA,EAClD;AAAA,EACA,iBAAiB,SAAS;AACtB,WAAO,KAAK,SAAS,iBAAiB,OAAO;AAAA,EACjD;AAAA,EACA,oBAAoB,OAAO;AACvB,WAAO,KAAK,8BAA8B,wBAAwB,KAAK;AAAA,EAC3E;AAAA,EACA,qBAAqB;AACjB,UAAM,oBAAoB;AAAA,MACtB,QAAQ,CAACC,eAAc;AACnB,YAAIA,eAAc,KAAK,gBAAgB;AACnC,iBAAO,KAAK;AAAA,QAChB;AACA,eAAO,KAAK,mBAAmBA,UAAS;AAAA,MAC5C;AAAA,MACA,YAAY,CAACA,eAAc;AACvB,eAAO,KAAK,mBAAmB,WAAWA,UAAS;AAAA,MACvD;AAAA,IACJ;AACA,UAAM,SAAS,CAAC;AAChB,UAAM,YAAY,KAAK;AACvB,UAAM,UAAU,kBAAkB,OAAO,SAAS;AAClD,QAAI,SAAS;AAET,YAAM,gBAAgB,QAAQ;AAC9B,UAAI,eAAe;AACf,iBAAS,cAAc,eAAe;AAClC,4BAAkB,QAAQ,YAAY,cAAc,UAAU,GAAG,MAAM,OAAO;AAAA,QAClF;AAAA,MACJ;AAEA,YAAM,sBAAsB,KAAK,mBAAmB,WAAW,SAAS;AACxE,UAAI,qBAAqB;AACrB,4BAAoB,QAAQ,CAAC,uBAAuB;AAChD,gBAAM,mBAAmB,KAAK,mBAAmB,kBAAkB;AACnE,cAAI,kBAAkB;AAClB,kBAAM,WAAW,iBAAiB;AAClC,gBAAI,UAAU;AACV,gCAAkB,QAAQ,UAAU,kBAAkB,MAAM,gBAAgB;AAAA,YAChF;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,WAAO,KAAK,CAAC,IAAI,OAAO,GAAG,WAAW,GAAG,QAAQ;AACjD,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB;AACZ,QAAI,KAAK,gBAAgB,MAAM;AAC3B,WAAK,cAAc,KAAK,mBAAmB;AAAA,IAC/C;AACA,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,aAAa,SAAS;AAClB,UAAM,KAAK,EAAE,KAAK;AAClB,UAAM,SAAS,QAAQ,iBAAiB,EAAE,CAAC;AAC3C,SAAK,aAAa,EAAE,IAAI;AACxB,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,QAAQ;AACZ,WAAO,KAAK,aAAa,eAAe,MAAM,CAAC;AAAA,EACnD;AAAA,EACA,mBAAmB,WAAW,YAAY;AACtC,QAAI,KAAK,kBAAkB,SAAS,GAAG;AACnC,aAAO,KAAK,kBAAkB,SAAS;AAAA,IAC3C,WACS,KAAK,oBAAoB;AAC9B,YAAM,qBAAqB,KAAK,mBAAmB,OAAO,SAAS;AACnE,UAAI,oBAAoB;AAEpB,aAAK,kBAAkB,SAAS,IAAI,YAAY,oBAAoB,cAAc,WAAW,KAAK;AAClG,eAAO,KAAK,kBAAkB,SAAS;AAAA,MAC3C;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,aAAa,UAAU,WAAW,YAAY,GAAG;AAC7C,UAAM,IAAI,KAAK,UAAU,UAAU,WAAW,OAAO,SAAS;AAC9D,WAAO;AAAA,MACH,QAAQ,EAAE,WAAW,UAAU,EAAE,WAAW,EAAE,UAAU;AAAA,MACxD,WAAW,EAAE;AAAA,MACb,cAAc,EAAE;AAAA,IACpB;AAAA,EACJ;AAAA,EACA,cAAc,UAAU,WAAW,YAAY,GAAG;AAC9C,UAAM,IAAI,KAAK,UAAU,UAAU,WAAW,MAAM,SAAS;AAC7D,WAAO;AAAA,MACH,QAAQ,EAAE,WAAW,gBAAgB,EAAE,WAAW,EAAE,UAAU;AAAA,MAC9D,WAAW,EAAE;AAAA,MACb,cAAc,EAAE;AAAA,IACpB;AAAA,EACJ;AAAA,EACA,UAAU,UAAU,WAAW,kBAAkB,WAAW;AACxD,QAAI,KAAK,YAAY,IAAI;AACrB,WAAK,UAAU,YAAY,kBAAkB,KAAK,SAAS,WAAW,OAAO,MAAM,KAAK,SAAS,UAAU;AAE3G,WAAK,cAAc;AAAA,IACvB;AACA,QAAI;AACJ,QAAI,CAAC,aAAa,cAAc,eAAe,MAAM;AACjD,oBAAc;AACd,YAAM,qBAAqB,KAAK,8BAA8B,qBAAqB;AACnF,YAAM,eAAe,KAAK,cAAc,YAAY;AACpD,YAAM,kBAAkB,uBAAuB,IAAI,GAAG,mBAAmB,YAAY,mBAAmB,WAAW,MAAM,aAAa,WAAW,aAAa,cAAc,aAAa,YAAY;AACrM,YAAM,gBAAgB,KAAK,QAAQ,KAAK,OAAO,EAAE,QAAQ,MAAM,IAAI;AACnE,UAAI;AACJ,UAAI,eAAe;AACf,oBAAY,qBAAqB,6BAA6B,eAAe,iBAAiB,IAAI;AAAA,MACtG,OACK;AACD,oBAAY,qBAAqB,WAAW,WAAW,eAAe;AAAA,MAC1E;AACA,kBAAY,IAAI,eAAe,MAAM,KAAK,SAAS,IAAI,IAAI,OAAO,MAAM,WAAW,SAAS;AAAA,IAChG,OACK;AACD,oBAAc;AACd,gBAAU,MAAM;AAAA,IACpB;AACA,eAAW,WAAW;AACtB,UAAM,eAAe,KAAK,iBAAiB,QAAQ;AACnD,UAAM,aAAa,aAAa,QAAQ;AACxC,UAAM,aAAa,IAAI,WAAW,kBAAkB,UAAU,KAAK,oBAAoB,KAAK,wBAAwB;AACpH,UAAM,IAAI,gBAAgB,MAAM,cAAc,aAAa,GAAG,WAAW,YAAY,MAAM,SAAS;AACpG,sBAAkB,YAAY;AAC9B,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA,WAAW,EAAE;AAAA,MACb,cAAc,EAAE;AAAA,IACpB;AAAA,EACJ;AACJ;AACA,SAAS,YAAY,SAAS,MAAM;AAChC,YAAU,MAAM,OAAO;AACvB,UAAQ,aAAa,QAAQ,cAAc,CAAC;AAC5C,UAAQ,WAAW,QAAQ;AAAA,IACvB,yBAAyB,QAAQ;AAAA,IACjC,UAAU,QAAQ;AAAA,IAClB,MAAM,QAAQ;AAAA,EAClB;AACA,UAAQ,WAAW,QAAQ,QAAQ,QAAQ,WAAW;AACtD,SAAO;AACX;AACA,IAAM,uBAAN,MAAM,sBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgCvB,YAAY,QAAQ,WAAW,iBAAiB;AA/BhD;AACA;AACA;AA8BI,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,kBAAkB;AAAA,EAC3B;AAAA,EAhCA,OAAO,cAAc,gBAAgB,uBAAuB;AACxD,QAAI,UAAU;AACd,QAAI,cAAa,iDAAgB,cAAa;AAC9C,eAAW,SAAS,uBAAuB;AACvC,mBAAa,WAAW,KAAK,YAAY,MAAM,UAAU;AACzD,gBAAU,IAAI,sBAAqB,SAAS,YAAY,MAAM,sBAAsB;AAAA,IACxF;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,WAAW,WAAW,iBAAiB;AAC1C,WAAO,IAAI,sBAAqB,MAAM,IAAI,WAAW,MAAM,SAAS,GAAG,eAAe;AAAA,EAC1F;AAAA,EACA,OAAO,6BAA6B,WAAW,iBAAiB,SAAS;AACrE,UAAM,kBAAkB,QAAQ,oBAAoB,SAAS;AAC7D,UAAM,YAAY,IAAI,WAAW,MAAM,SAAS;AAChD,UAAM,YAAY,QAAQ,cAAc,WAAW,SAAS;AAC5D,UAAM,0BAA0B,sBAAqB,gBAAgB,iBAAiB,iBAAiB,SAAS;AAChH,WAAO,IAAI,sBAAqB,MAAM,WAAW,uBAAuB;AAAA,EAC5E;AAAA,EACA,IAAI,YAAY;AAAE,WAAO,KAAK,UAAU;AAAA,EAAW;AAAA,EAcnD,WAAW;AACP,WAAO,KAAK,cAAc,EAAE,KAAK,GAAG;AAAA,EACxC;AAAA,EACA,OAAO,OAAO;AACV,WAAO,sBAAqB,OAAO,MAAM,KAAK;AAAA,EAClD;AAAA,EACA,OAAO,OAAO,GAAG,GAAG;AAChB,OAAG;AACC,UAAI,MAAM,GAAG;AACT,eAAO;AAAA,MACX;AACA,UAAI,CAAC,KAAK,CAAC,GAAG;AAEV,eAAO;AAAA,MACX;AACA,UAAI,CAAC,KAAK,CAAC,GAAG;AAEV,eAAO;AAAA,MACX;AACA,UAAI,EAAE,cAAc,EAAE,aAAa,EAAE,oBAAoB,EAAE,iBAAiB;AACxE,eAAO;AAAA,MACX;AAEA,UAAI,EAAE;AACN,UAAI,EAAE;AAAA,IACV,SAAS;AAAA,EACb;AAAA,EACA,OAAO,gBAAgB,yBAAyB,sBAAsB,iBAAiB;AACnF,QAAI,YAAY;AAChB,QAAI,aAAa;AACjB,QAAI,aAAa;AACjB,QAAI,oBAAoB,MAAM;AAC1B,kBAAY,gBAAgB;AAC5B,mBAAa,gBAAgB;AAC7B,mBAAa,gBAAgB;AAAA,IACjC;AACA,WAAO,uBAAuB,IAAI,yBAAyB,qBAAqB,YAAY,qBAAqB,WAAW,MAAM,WAAW,YAAY,UAAU;AAAA,EACvK;AAAA,EACA,eAAe,WAAW,SAAS;AAC/B,QAAI,cAAc,MAAM;AACpB,aAAO;AAAA,IACX;AACA,QAAI,UAAU,QAAQ,GAAG,MAAM,IAAI;AAE/B,aAAO,sBAAqB,gBAAgB,MAAM,WAAW,OAAO;AAAA,IACxE;AACA,UAAM,SAAS,UAAU,MAAM,IAAI;AACnC,QAAI,SAAS;AACb,eAAW,SAAS,QAAQ;AACxB,eAAS,sBAAqB,gBAAgB,QAAQ,OAAO,OAAO;AAAA,IACxE;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,gBAAgB,QAAQ,WAAW,SAAS;AAC/C,UAAM,cAAc,QAAQ,oBAAoB,SAAS;AACzD,UAAM,UAAU,OAAO,UAAU,KAAK,SAAS;AAC/C,UAAM,wBAAwB,QAAQ,cAAc,WAAW,OAAO;AACtE,UAAM,WAAW,sBAAqB,gBAAgB,OAAO,iBAAiB,aAAa,qBAAqB;AAChH,WAAO,IAAI,sBAAqB,QAAQ,SAAS,QAAQ;AAAA,EAC7D;AAAA,EACA,gBAAgB;AACZ,WAAO,KAAK,UAAU,YAAY;AAAA,EACtC;AAAA,EACA,sBAAsB,MAAM;AAt9EhC;AAu9EQ,UAAM,SAAS,CAAC;AAChB,QAAI,OAAO;AACX,WAAO,QAAQ,SAAS,MAAM;AAC1B,aAAO,KAAK;AAAA,QACR,wBAAwB,KAAK;AAAA,QAC7B,YAAY,KAAK,UAAU,wBAAsB,UAAK,WAAL,mBAAa,cAAa,IAAI;AAAA,MACnF,CAAC;AACD,aAAO,KAAK;AAAA,IAChB;AACA,WAAO,SAAS,OAAO,OAAO,QAAQ,IAAI;AAAA,EAC9C;AACJ;AAIA,IAAM,kBAAN,MAAM,gBAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqCjB,YAIA,QAIA,QAAQ,UAAU,WAIlB,sBAIA,SAIA,gBAKA,uBAAuB;AA7DvB;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA;AAsCI,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,uBAAuB;AAC5B,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,wBAAwB;AAC7B,SAAK,QAAQ,KAAK,SAAS,KAAK,OAAO,QAAQ,IAAI;AACnD,SAAK,YAAY;AACjB,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,OAAO,OAAO;AACV,QAAI,UAAU,MAAM;AAChB,aAAO;AAAA,IACX;AACA,WAAO,gBAAe,QAAQ,MAAM,KAAK;AAAA,EAC7C;AAAA,EACA,OAAO,QAAQ,GAAG,GAAG;AACjB,QAAI,MAAM,GAAG;AACT,aAAO;AAAA,IACX;AACA,QAAI,CAAC,KAAK,kBAAkB,GAAG,CAAC,GAAG;AAC/B,aAAO;AAAA,IACX;AACA,WAAO,qBAAqB,OAAO,EAAE,uBAAuB,EAAE,qBAAqB;AAAA,EACvF;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,kBAAkB,GAAG,GAAG;AAC3B,OAAG;AACC,UAAI,MAAM,GAAG;AACT,eAAO;AAAA,MACX;AACA,UAAI,CAAC,KAAK,CAAC,GAAG;AAEV,eAAO;AAAA,MACX;AACA,UAAI,CAAC,KAAK,CAAC,GAAG;AAEV,eAAO;AAAA,MACX;AACA,UAAI,EAAE,UAAU,EAAE,SACd,EAAE,WAAW,EAAE,UACf,EAAE,YAAY,EAAE,SAAS;AACzB,eAAO;AAAA,MACX;AAEA,UAAI,EAAE;AACN,UAAI,EAAE;AAAA,IACV,SAAS;AAAA,EACb;AAAA,EACA,QAAQ;AACJ,WAAO;AAAA,EACX;AAAA,EACA,OAAO,OAAO,IAAI;AACd,WAAO,IAAI;AACP,SAAG,YAAY;AACf,SAAG,aAAa;AAChB,WAAK,GAAG;AAAA,IACZ;AAAA,EACJ;AAAA,EACA,QAAQ;AACJ,oBAAe,OAAO,IAAI;AAAA,EAC9B;AAAA,EACA,MAAM;AACF,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,UAAU;AACN,QAAI,KAAK,QAAQ;AACb,aAAO,KAAK;AAAA,IAChB;AACA,WAAO;AAAA,EACX;AAAA,EACA,KAAK,QAAQ,UAAU,WAAW,sBAAsB,SAAS,gBAAgB,uBAAuB;AACpG,WAAO,IAAI,gBAAe,MAAM,QAAQ,UAAU,WAAW,sBAAsB,SAAS,gBAAgB,qBAAqB;AAAA,EACrI;AAAA,EACA,cAAc;AACV,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,eAAe;AACX,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,QAAQ,SAAS;AACb,WAAO,QAAQ,QAAQ,KAAK,MAAM;AAAA,EACtC;AAAA,EACA,WAAW;AACP,UAAM,IAAI,CAAC;AACX,SAAK,aAAa,GAAG,CAAC;AACtB,WAAO,MAAM,EAAE,KAAK,GAAG,IAAI;AAAA,EAC/B;AAAA,EACA,aAAa,KAAK,UAAU;AA/nFhC;AAgoFQ,QAAI,KAAK,QAAQ;AACb,iBAAW,KAAK,OAAO,aAAa,KAAK,QAAQ;AAAA,IACrD;AACA,QAAI,UAAU,IAAI,IAAI,KAAK,MAAM,MAAK,UAAK,mBAAL,mBAAqB,UAAU,MAAK,UAAK,0BAAL,mBAA4B,UAAU;AAChH,WAAO;AAAA,EACX;AAAA,EACA,0BAA0B,uBAAuB;AAC7C,QAAI,KAAK,0BAA0B,uBAAuB;AACtD,aAAO;AAAA,IACX;AACA,WAAO,KAAK,OAAO,KAAK,KAAK,QAAQ,KAAK,WAAW,KAAK,YAAY,KAAK,sBAAsB,KAAK,SAAS,KAAK,gBAAgB,qBAAqB;AAAA,EAC7J;AAAA,EACA,YAAY,SAAS;AACjB,QAAI,KAAK,YAAY,SAAS;AAC1B,aAAO;AAAA,IACX;AACA,WAAO,IAAI,gBAAe,KAAK,QAAQ,KAAK,QAAQ,KAAK,WAAW,KAAK,YAAY,KAAK,sBAAsB,SAAS,KAAK,gBAAgB,KAAK,qBAAqB;AAAA,EAC5K;AAAA;AAAA,EAEA,cAAc,OAAO;AACjB,QAAI,KAAK;AACT,WAAO,MAAM,GAAG,cAAc,MAAM,WAAW;AAC3C,UAAI,GAAG,WAAW,MAAM,QAAQ;AAC5B,eAAO;AAAA,MACX;AACA,WAAK,GAAG;AAAA,IACZ;AACA,WAAO;AAAA,EACX;AAAA,EACA,oBAAoB;AA7pFxB;AA8pFQ,WAAO;AAAA,MACH,QAAQ,eAAe,KAAK,MAAM;AAAA,MAClC,sBAAsB,KAAK;AAAA,MAC3B,SAAS,KAAK;AAAA,MACd,kBAAgB,UAAK,mBAAL,mBAAqB,wBAAsB,UAAK,WAAL,mBAAa,mBAAkB,UAAS,CAAC;AAAA,MACpG,yBAAuB,UAAK,0BAAL,mBAA4B,sBAAsB,KAAK,oBAAmB,CAAC;AAAA,IACtG;AAAA,EACJ;AAAA,EACA,OAAO,UAAU,MAAM,OAAO;AAC1B,UAAM,iBAAiB,qBAAqB,eAAc,6BAAM,mBAAkB,MAAM,MAAM,cAAc;AAC5G,WAAO,IAAI,gBAAe,MAAM,iBAAiB,MAAM,MAAM,GAAG,MAAM,YAAY,IAAI,MAAM,aAAa,IAAI,MAAM,sBAAsB,MAAM,SAAS,gBAAgB,qBAAqB,cAAc,gBAAgB,MAAM,qBAAqB,CAAC;AAAA,EAC3P;AACJ;AAAA;AA3LI,cATE,iBASK,QAAO,IAAI,gBAAe,MAAM,GAAG,GAAG,GAAG,OAAO,MAAM,MAAM,IAAI;AAT3E,IAAM,iBAAN;AAqMA,IAAM,2BAAN,MAA+B;AAAA,EAI3B,YAAY,uBAAuB,yBAAyB;AAH5D;AACA;AACA,oCAAW;AAEP,SAAK,wBAAwB,sBAAsB,QAAQ,CAAC,aAAa;AACrE,UAAI,aAAa,KAAK;AAClB,aAAK,WAAW;AAChB,eAAO,CAAC;AAAA,MACZ;AACA,aAAO,eAAe,UAAU,WAAW,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO;AAAA,IACrE,CAAC;AACD,SAAK,0BAA0B,wBAAwB,QAAQ,CAAC,aAAa,eAAe,UAAU,WAAW,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC;AAAA,EAC5I;AAAA,EACA,IAAI,gBAAgB;AAChB,WAAO,KAAK,YAAY,KAAK,wBAAwB,WAAW;AAAA,EACpE;AAAA,EACA,IAAI,eAAe;AACf,WAAO,KAAK,sBAAsB,WAAW,KAAK,CAAC,KAAK;AAAA,EAC5D;AAAA,EACA,MAAM,QAAQ;AACV,eAAW,YAAY,KAAK,yBAAyB;AACjD,UAAI,SAAS,MAAM,GAAG;AAClB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,eAAW,YAAY,KAAK,uBAAuB;AAC/C,UAAI,SAAS,MAAM,GAAG;AAClB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO,KAAK;AAAA,EAChB;AACJ;AACA,IAAM,aAAN,MAAiB;AAAA,EAiBb,YAAY,kBAAkB,UAAU,oBAAoB,0BAA0B;AAhBtF;AACA;AAIA;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA;AACA;AACA;AAEI,SAAK,2BAA2B;AAChC,SAAK,oBAAoB;AACzB,SAAK,sBAAsB;AAC3B;AACI,WAAK,YAAY;AAAA,IACrB;AACA,SAAK,UAAU,CAAC;AAChB,SAAK,gBAAgB,CAAC;AACtB,SAAK,qBAAqB;AAAA,EAC9B;AAAA,EACA,QAAQ,OAAO,UAAU;AACrB,SAAK,kBAAkB,MAAM,uBAAuB,QAAQ;AAAA,EAChE;AAAA,EACA,kBAAkB,YAAY,UAAU;AA5uF5C;AA6uFQ,QAAI,KAAK,sBAAsB,UAAU;AACrC;AAAA,IACJ;AACA,QAAI,KAAK,mBAAmB;AACxB,UAAI,YAAW,yCAAY,oBAAmB;AAC9C,UAAI,2BAA2B;AAC/B,WAAI,UAAK,6BAAL,mBAA+B,eAAe;AAC9C,mCAA2B;AAAA,MAC/B;AACA,UAAI,KAAK,oBAAoB,SAAS,KAAM,KAAK,4BAA4B,CAAC,KAAK,yBAAyB,iBAAiB,CAAC,KAAK,yBAAyB,cAAe;AAEvK,cAAMC,WAAS,yCAAY,oBAAmB,CAAC;AAC/C,mBAAW,aAAa,KAAK,qBAAqB;AAC9C,cAAI,UAAU,QAAQA,OAAM,GAAG;AAC3B,uBAAW,uBAAuB,IAAI,UAAU,GAAG,oBAAoB,UAAU,IAAI,GAAG,MAAM,IAA2B,GAAG,CAAC;AAAA,UACjI;AAAA,QACJ;AACA,YAAI,KAAK,0BAA0B;AAC/B,qCAA2B,KAAK,yBAAyB,MAAMA,OAAM;AAAA,QACzE;AAAA,MACJ;AACA,UAAI,0BAA0B;AAC1B,mBAAW,uBAAuB,IAAI,UAAU,GAAG,GAA0C,0BAA0B,IAA2B,GAAG,CAAC;AAAA,MAC1J;AACA,UAAI,KAAK,cAAc,SAAS,KAAK,KAAK,cAAc,KAAK,cAAc,SAAS,CAAC,MAAM,UAAU;AAEjG,aAAK,qBAAqB;AAC1B;AAAA,MACJ;AACA,WAAK,cAAc,KAAK,KAAK,kBAAkB;AAC/C,WAAK,cAAc,KAAK,QAAQ;AAChC,WAAK,qBAAqB;AAC1B;AAAA,IACJ;AACA,UAAM,UAAS,yCAAY,oBAAmB,CAAC;AAC/C,SAAK,QAAQ,KAAK;AAAA,MACd,YAAY,KAAK;AAAA,MACjB;AAAA;AAAA,MAEA;AAAA,IACJ,CAAC;AACD,SAAK,qBAAqB;AAAA,EAC9B;AAAA,EACA,UAAU,OAAO,YAAY;AACzB,QAAI,KAAK,QAAQ,SAAS,KAAK,KAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC,EAAE,eAAe,aAAa,GAAG;AAEhG,WAAK,QAAQ,IAAI;AAAA,IACrB;AACA,QAAI,KAAK,QAAQ,WAAW,GAAG;AAC3B,WAAK,qBAAqB;AAC1B,WAAK,QAAQ,OAAO,UAAU;AAC9B,WAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC,EAAE,aAAa;AAAA,IACvD;AACA,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,gBAAgB,OAAO,YAAY;AAC/B,QAAI,KAAK,cAAc,SAAS,KAAK,KAAK,cAAc,KAAK,cAAc,SAAS,CAAC,MAAM,aAAa,GAAG;AAEvG,WAAK,cAAc,IAAI;AACvB,WAAK,cAAc,IAAI;AAAA,IAC3B;AACA,QAAI,KAAK,cAAc,WAAW,GAAG;AACjC,WAAK,qBAAqB;AAC1B,WAAK,QAAQ,OAAO,UAAU;AAC9B,WAAK,cAAc,KAAK,cAAc,SAAS,CAAC,IAAI;AAAA,IACxD;AACA,UAAM,SAAS,IAAI,YAAY,KAAK,cAAc,MAAM;AACxD,aAAS,IAAI,GAAG,MAAM,KAAK,cAAc,QAAQ,IAAI,KAAK,KAAK;AAC3D,aAAO,CAAC,IAAI,KAAK,cAAc,CAAC;AAAA,IACpC;AACA,WAAO;AAAA,EACX;AACJ;AAKA,IAAM,eAAN,MAAmB;AAAA,EAMf,YAAY,OAAO,iBAAiB;AALpC;AACA,qCAAY,oBAAI,IAAI;AACpB,wCAAe,oBAAI,IAAI;AACvB,8CAAqB,oBAAI,IAAI;AAC7B;AAEI,SAAK,kBAAkB;AACvB,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,UAAU;AACN,eAAW,WAAW,KAAK,UAAU,OAAO,GAAG;AAC3C,cAAQ,QAAQ;AAAA,IACpB;AAAA,EACJ;AAAA,EACA,SAAS,OAAO;AACZ,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,cAAc;AACV,WAAO,KAAK,OAAO,YAAY;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,SAAS,qBAAqB;AACrC,SAAK,aAAa,IAAI,QAAQ,WAAW,OAAO;AAChD,QAAI,qBAAqB;AACrB,WAAK,mBAAmB,IAAI,QAAQ,WAAW,mBAAmB;AAAA,IACtE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,WAAW;AACd,WAAO,KAAK,aAAa,IAAI,SAAS;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,aAAa;AACpB,WAAO,KAAK,mBAAmB,IAAI,WAAW;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACV,WAAO,KAAK,OAAO,YAAY;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,WAAW;AAClB,WAAO,KAAK,OAAO,MAAM,SAAS;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,oBAAoB,WAAW,iBAAiB,mBAAmB,YAAY,0BAA0B;AAC3G,QAAI,CAAC,KAAK,UAAU,IAAI,SAAS,GAAG;AAChC,UAAI,aAAa,KAAK,aAAa,IAAI,SAAS;AAChD,UAAI,CAAC,YAAY;AACb,eAAO;AAAA,MACX;AACA,WAAK,UAAU,IAAI,WAAW,cAAc,WAAW,YAAY,iBAAiB,mBAAmB,YAAY,0BAA0B,MAAM,MAAM,KAAK,eAAe,CAAC;AAAA,IAClL;AACA,WAAO,KAAK,UAAU,IAAI,SAAS;AAAA,EACvC;AACJ;AAQA,IAAI,aAAa,MAAM,SAAS;AAAA,EAI5B,YAAY,SAAS;AAHrB;AACA;AACA;AAEI,SAAK,WAAW;AAChB,SAAK,gBAAgB,IAAI,aAAa,MAAM,mBAAmB,QAAQ,OAAO,QAAQ,QAAQ,GAAG,QAAQ,OAAO;AAChH,SAAK,sBAAsB,oBAAI,IAAI;AAAA,EACvC;AAAA,EACA,UAAU;AACN,SAAK,cAAc,QAAQ;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,OAAO,UAAU;AACtB,SAAK,cAAc,SAAS,MAAM,mBAAmB,OAAO,QAAQ,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACV,WAAO,KAAK,cAAc,YAAY;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iCAAiC,kBAAkB,iBAAiB,mBAAmB;AACnF,WAAO,KAAK,6BAA6B,kBAAkB,iBAAiB,EAAE,kBAAkB,CAAC;AAAA,EACrG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,6BAA6B,kBAAkB,iBAAiB,eAAe;AAC3E,WAAO,KAAK,aAAa,kBAAkB,iBAAiB,cAAc,mBAAmB,cAAc,YAAY,IAAI,yBAAyB,cAAc,4BAA4B,CAAC,GAAG,cAAc,8BAA8B,CAAC,CAAC,CAAC;AAAA,EACrP;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,kBAAkB;AAC1B,WAAO,KAAK,aAAa,kBAAkB,GAAG,MAAM,MAAM,IAAI;AAAA,EAClE;AAAA,EACA,MAAM,aAAa,kBAAkB,iBAAiB,mBAAmB,YAAY,0BAA0B;AAC3G,UAAM,sBAAsB,IAAI,yBAAyB,KAAK,eAAe,gBAAgB;AAC7F,WAAO,oBAAoB,EAAE,SAAS,GAAG;AACrC,YAAM,QAAQ,IAAI,oBAAoB,EAAE,IAAI,CAAC,YAAY,KAAK,mBAAmB,QAAQ,SAAS,CAAC,CAAC;AACpG,0BAAoB,aAAa;AAAA,IACrC;AACA,WAAO,KAAK,qBAAqB,kBAAkB,iBAAiB,mBAAmB,YAAY,wBAAwB;AAAA,EAC/H;AAAA,EACA,MAAM,mBAAmB,WAAW;AAChC,QAAI,CAAC,KAAK,oBAAoB,IAAI,SAAS,GAAG;AAC1C,WAAK,oBAAoB,IAAI,WAAW,KAAK,qBAAqB,SAAS,CAAC;AAAA,IAChF;AACA,WAAO,KAAK,oBAAoB,IAAI,SAAS;AAAA,EACjD;AAAA,EACA,MAAM,qBAAqB,WAAW;AAClC,UAAM,UAAU,MAAM,KAAK,SAAS,YAAY,SAAS;AACzD,QAAI,SAAS;AACT,YAAM,aAAa,OAAO,KAAK,SAAS,kBAAkB,aAAa,KAAK,SAAS,cAAc,SAAS,IAAI;AAChH,WAAK,cAAc,WAAW,SAAS,UAAU;AAAA,IACrD;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,WAAW,YAAY,aAAa,CAAC,GAAG,kBAAkB,GAAG,oBAAoB,MAAM;AACzF,SAAK,cAAc,WAAW,YAAY,UAAU;AACpD,WAAQ,MAAM,KAAK,qBAAqB,WAAW,WAAW,iBAAiB,iBAAiB;AAAA,EACpG;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB,WAAW,kBAAkB,GAAG,oBAAoB,MAAM,aAAa,MAAM,2BAA2B,MAAM;AAC/H,WAAO,KAAK,cAAc,oBAAoB,WAAW,iBAAiB,mBAAmB,YAAY,wBAAwB;AAAA,EACrI;AACJ;AACA,IAAM,UAAU,eAAe;AAsB/B,IAAM,iBAAiB;AAAA,EACnB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,mBAAmB;AACvB;AACA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACvB,OAAO,YAAY,UAAU;AACzB,QAAI,IAAI,SAAS,SAAS,CAAC;AAC3B,WAAO,EAAE,SAAS;AACd,UAAI,IAAI,CAAC;AACb,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,OAAO,cAAc,UAAU;AAC3B,YAAQ,WAAW,eAAe,qBAAqB,eAAe;AAAA,EAC1E;AAAA,EACA,OAAO,aAAa,UAAU;AAC1B,YAAQ,WAAW,eAAe,qBAAqB,eAAe;AAAA,EAC1E;AAAA,EACA,OAAO,aAAa,UAAU;AAC1B,YAAQ,WAAW,eAAe,qBAAqB,eAAe;AAAA,EAC1E;AAAA,EACA,OAAO,cAAc,UAAU;AAC3B,YAAQ,WAAW,eAAe,qBAAqB,eAAe;AAAA,EAC1E;AAAA,EACA,OAAO,cAAc,UAAU;AAC3B,YAAQ,WAAW,eAAe,qBAAqB,eAAe;AAAA,EAC1E;AAAA,EACA,OAAO,yBAAyB,UAAU;AACtC,YAAQ,WAAW,eAAe,4BAA4B;AAAA,EAClE;AAAA,EACA,OAAO,IAAI,UAAU,YAAY,WAAW,WAAW,YAAY,YAAY;AAC3E,QAAI,cAAc,sBAAqB,cAAc,QAAQ;AAC7D,QAAI,aAAa,sBAAqB,aAAa,QAAQ;AAC3D,QAAI,aAAa,sBAAqB,aAAa,QAAQ;AAC3D,QAAI,cAAc,sBAAqB,cAAc,QAAQ;AAC7D,QAAI,cAAc,sBAAqB,cAAc,QAAQ;AAC7D,UAAM,+BAA+B,sBAAqB,yBAAyB,QAAQ,IACrF,IACA;AACN,QAAI,eAAe;AACf,oBAAc;AAClB,QAAI,cAAc,GAA0C;AACxD,mBACM,cAAc,IAAkD,IAAkC;AAAA,IAC5G;AACA,QAAI,cAAc,UAAU;AACxB,mBAAa;AACjB,QAAI,eAAe;AACf,oBAAc;AAClB,QAAI,eAAe;AACf,oBAAc;AAClB,YAAU,eAAe,eAAe,oBACjC,cAAc,eAAe,oBAC7B,cAAc,eAAe,oBAC7B,gCAAgC,eAAe,2BAC/C,eAAe,eAAe,oBAC9B,eAAe,eAAe,uBAC7B;AAAA,EACZ;AACJ;;;ACzjGA,SAAS,QAAQ,GAAG;AAChB,SAAO,MAAM,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC;AACpC;AAIA,SAAS,WAAW,KAAK;AACrB,SAAO,MAAM,KAAK,IAAI,SAAS,QAAQ,CAAC,EAAE,IAAI,OAAK,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC;AACtE;AAMA,SAAS,YAAY,MAAM;AACvB,SAAO,CAAC,QAAQ,CAAC,aAAa,OAAO,QAAQ,OAAO,EAAE,SAAS,IAAI;AACvE;AAMA,SAAS,cAAc,MAAM;AACzB,SAAO,SAAS,UAAU,YAAY,IAAI;AAC9C;AAMA,SAAS,YAAY,OAAO;AACxB,SAAO,UAAU;AACrB;AAMA,SAAS,eAAe,OAAO;AAC3B,SAAO,YAAY,KAAK;AAC5B;AAMA,SAAS,eAAe,MAAM,WAAW;AAjDzC;AAkDI,MAAI,CAAC;AACD;AACJ,OAAK,eAAL,KAAK,aAAe,CAAC;AACrB,aAAK,YAAW,UAAhB,GAAgB,QAAU,CAAC;AAC3B,MAAI,OAAO,KAAK,WAAW,UAAU;AACjC,SAAK,WAAW,QAAQ,KAAK,WAAW,MAAM,MAAM,MAAM;AAC9D,MAAI,CAAC,MAAM,QAAQ,KAAK,WAAW,KAAK;AACpC,SAAK,WAAW,QAAQ,CAAC;AAC7B,QAAM,UAAU,MAAM,QAAQ,SAAS,IAAI,YAAY,UAAU,MAAM,MAAM;AAC7E,aAAW,KAAK,SAAS;AACrB,QAAI,KAAK,CAAC,KAAK,WAAW,MAAM,SAAS,CAAC;AACtC,WAAK,WAAW,MAAM,KAAK,CAAC;AAAA,EACpC;AACJ;AAMA,SAAS,WAAW,OAAO,SAAS;AAChC,MAAI,aAAa;AACjB,QAAM,SAAS,CAAC;AAChB,aAAW,UAAU,SAAS;AAC1B,QAAI,SAAS,YAAY;AACrB,aAAO,KAAK;AAAA,QACR,GAAG;AAAA,QACH,SAAS,MAAM,QAAQ,MAAM,YAAY,MAAM;AAAA,QAC/C,QAAQ,MAAM,SAAS;AAAA,MAC3B,CAAC;AAAA,IACL;AACA,iBAAa;AAAA,EACjB;AACA,MAAI,aAAa,MAAM,QAAQ,QAAQ;AACnC,WAAO,KAAK;AAAA,MACR,GAAG;AAAA,MACH,SAAS,MAAM,QAAQ,MAAM,UAAU;AAAA,MACvC,QAAQ,MAAM,SAAS;AAAA,IAC3B,CAAC;AAAA,EACL;AACA,SAAO;AACX;AACA,SAAS,uBAAuB,OAAO,cAAc;AACjD,UAAO,6CAAe,MAAM,YAAY,OAAM;AAClD;AAIA,IAAM,cAAc;AAGpB,IAAI,cAAc;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGA,IAAI,cAAc;AAAA,EAChB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACL;AAGA,SAAS,aAAa,OAAO,UAAU;AACrC,QAAM,aAAa,MAAM,QAAQ,SAAS,QAAQ;AAClD,MAAI,eAAe,IAAI;AACrB,UAAM,YAAY,MAAM,QAAQ,KAAK,UAAU;AAC/C,WAAO;AAAA,MACL,UAAU,MAAM,UAAU,aAAa,GAAG,SAAS,EAAE,MAAM,GAAG;AAAA,MAC9D,eAAe;AAAA,MACf,UAAU,YAAY;AAAA,IACxB;AAAA,EACF;AACA,SAAO;AAAA,IACL,UAAU,MAAM;AAAA,EAClB;AACF;AACA,SAAS,WAAW,UAAU,OAAO;AACnC,MAAI,SAAS;AACb,QAAM,YAAY,SAAS,QAAQ,QAAQ;AAC3C,MAAI;AACJ,MAAI,cAAc,KAAK;AACrB,UAAM,MAAM;AAAA,MACV,SAAS,QAAQ,QAAQ;AAAA,MACzB,SAAS,QAAQ,QAAQ;AAAA,MACzB,SAAS,QAAQ,MAAM;AAAA,IACzB,EAAE,IAAI,CAAC,MAAM,OAAO,SAAS,CAAC,CAAC;AAC/B,QAAI,IAAI,WAAW,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG;AACzD,cAAQ;AAAA,QACN,MAAM;AAAA,QACN;AAAA,MACF;AAAA,IACF;AAAA,EACF,WAAW,cAAc,KAAK;AAC5B,UAAM,aAAa,OAAO,SAAS,SAAS,QAAQ,MAAM,CAAC;AAC3D,QAAI,CAAC,OAAO,MAAM,UAAU,GAAG;AAC7B,cAAQ,EAAE,MAAM,SAAS,OAAO,OAAO,UAAU,EAAE;AAAA,IACrD;AAAA,EACF;AACA,SAAO,CAAC,QAAQ,KAAK;AACvB;AACA,SAAS,cAAc,UAAU;AAC/B,QAAM,WAAW,CAAC;AAClB,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAM,OAAO,SAAS,CAAC;AACvB,UAAM,UAAU,OAAO,SAAS,IAAI;AACpC,QAAI,OAAO,MAAM,OAAO;AACtB;AACF,QAAI,YAAY,GAAG;AACjB,eAAS,KAAK,EAAE,MAAM,WAAW,CAAC;AAAA,IACpC,WAAW,WAAW,GAAG;AACvB,YAAM,aAAa,YAAY,OAAO;AACtC,UAAI,YAAY;AACd,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,OAAO,YAAY,OAAO;AAAA,QAC5B,CAAC;AAAA,MACH;AAAA,IACF,WAAW,WAAW,IAAI;AACxB,YAAM,aAAa,YAAY,UAAU,EAAE;AAC3C,UAAI,YAAY;AACd,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF,WAAW,WAAW,IAAI;AACxB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,OAAO,EAAE,MAAM,SAAS,MAAM,YAAY,UAAU,EAAE,EAAE;AAAA,MAC1D,CAAC;AAAA,IACH,WAAW,YAAY,IAAI;AACzB,YAAM,CAAC,QAAQ,KAAK,IAAI,WAAW,UAAU,CAAC;AAC9C,UAAI,OAAO;AACT,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AACA,WAAK;AAAA,IACP,WAAW,YAAY,IAAI;AACzB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,MACR,CAAC;AAAA,IACH,WAAW,WAAW,IAAI;AACxB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,OAAO,EAAE,MAAM,SAAS,MAAM,YAAY,UAAU,EAAE,EAAE;AAAA,MAC1D,CAAC;AAAA,IACH,WAAW,YAAY,IAAI;AACzB,YAAM,CAAC,QAAQ,KAAK,IAAI,WAAW,UAAU,CAAC;AAC9C,UAAI,OAAO;AACT,iBAAS,KAAK;AAAA,UACZ,MAAM;AAAA,UACN,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AACA,WAAK;AAAA,IACP,WAAW,YAAY,IAAI;AACzB,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,MACR,CAAC;AAAA,IACH,WAAW,WAAW,MAAM,WAAW,IAAI;AACzC,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,OAAO,EAAE,MAAM,SAAS,MAAM,YAAY,UAAU,KAAK,CAAC,EAAE;AAAA,MAC9D,CAAC;AAAA,IACH,WAAW,WAAW,OAAO,WAAW,KAAK;AAC3C,eAAS,KAAK;AAAA,QACZ,MAAM;AAAA,QACN,OAAO,EAAE,MAAM,SAAS,MAAM,YAAY,UAAU,MAAM,CAAC,EAAE;AAAA,MAC/D,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,2BAA2B;AAClC,MAAI,aAAa;AACjB,MAAI,aAAa;AACjB,MAAI,eAA+B,oBAAI,IAAI;AAC3C,SAAO;AAAA,IACL,MAAM,OAAO;AACX,YAAM,SAAS,CAAC;AAChB,UAAI,WAAW;AACf,SAAG;AACD,cAAM,aAAa,aAAa,OAAO,QAAQ;AAC/C,cAAMC,QAAO,WAAW,WAAW,MAAM,UAAU,UAAU,WAAW,aAAa,IAAI,MAAM,UAAU,QAAQ;AACjH,YAAIA,MAAK,SAAS,GAAG;AACnB,iBAAO,KAAK;AAAA,YACV,OAAOA;AAAA,YACP;AAAA,YACA;AAAA,YACA,aAAa,IAAI,IAAI,YAAY;AAAA,UACnC,CAAC;AAAA,QACH;AACA,YAAI,WAAW,UAAU;AACvB,gBAAM,WAAW,cAAc,WAAW,QAAQ;AAClD,qBAAW,cAAc,UAAU;AACjC,gBAAI,WAAW,SAAS,YAAY;AAClC,2BAAa;AACb,2BAAa;AACb,2BAAa,MAAM;AAAA,YACrB,WAAW,WAAW,SAAS,wBAAwB;AACrD,2BAAa;AAAA,YACf,WAAW,WAAW,SAAS,wBAAwB;AACrD,2BAAa;AAAA,YACf,WAAW,WAAW,SAAS,mBAAmB;AAChD,2BAAa,OAAO,WAAW,KAAK;AAAA,YACtC;AAAA,UACF;AACA,qBAAW,cAAc,UAAU;AACjC,gBAAI,WAAW,SAAS,sBAAsB;AAC5C,2BAAa,WAAW;AAAA,YAC1B,WAAW,WAAW,SAAS,sBAAsB;AACnD,2BAAa,WAAW;AAAA,YAC1B,WAAW,WAAW,SAAS,iBAAiB;AAC9C,2BAAa,IAAI,WAAW,KAAK;AAAA,YACnC;AAAA,UACF;AAAA,QACF;AACA,mBAAW,WAAW;AAAA,MACxB,SAAS,WAAW,MAAM;AAC1B,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAGA,IAAI,wBAAwB;AAAA,EAC1B,OAAO;AAAA,EACP,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,SAAS;AAAA,EACT,MAAM;AAAA,EACN,OAAO;AAAA,EACP,aAAa;AAAA,EACb,WAAW;AAAA,EACX,aAAa;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,aAAa;AACf;AACA,SAAS,mBAAmB,iBAAiB,uBAAuB;AAClE,WAAS,WAAW,MAAM;AACxB,WAAO,eAAe,IAAI;AAAA,EAC5B;AACA,WAAS,SAAS,KAAK;AACrB,WAAO,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC;AAAA,EACjG;AACA,MAAI;AACJ,WAAS,gBAAgB;AACvB,QAAI,YAAY;AACd,aAAO;AAAA,IACT;AACA,iBAAa,CAAC;AACd,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,iBAAW,KAAK,WAAW,YAAY,CAAC,CAAC,CAAC;AAAA,IAC5C;AACA,QAAI,SAAS,CAAC,GAAG,IAAI,KAAK,KAAK,KAAK,GAAG;AACvC,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,qBAAW,KAAK,SAAS,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAAA,QAC7D;AAAA,MACF;AAAA,IACF;AACA,QAAI,QAAQ;AACZ,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,SAAS,IAAI;AACxC,iBAAW,KAAK,SAAS,CAAC,OAAO,OAAO,KAAK,CAAC,CAAC;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AACA,WAAS,WAAW,OAAO;AACzB,WAAO,cAAc,EAAE,KAAK;AAAA,EAC9B;AACA,WAAS,MAAM,OAAO;AACpB,YAAQ,MAAM,MAAM;AAAA,MAClB,KAAK;AACH,eAAO,WAAW,MAAM,IAAI;AAAA,MAC9B,KAAK;AACH,eAAO,SAAS,MAAM,GAAG;AAAA,MAC3B,KAAK;AACH,eAAO,WAAW,MAAM,KAAK;AAAA,IACjC;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,EACF;AACF;AAEA,SAAS,sBAAsB,OAAO,cAAc,SAAS;AACzD,QAAM,oBAAoB;AAAA,IACtB,GAAG,MAAM;AAAA,IACT,GAAG,mCAAS;AAAA,EAChB;AACA,QAAM,QAAQ,WAAW,YAAY;AACrC,QAAM,eAAe,mBAAmB,OAAO,YAAY,YAAY,IAAI,UAAK;AA/WpF;AA+WuF;AAAA,MAC/E;AAAA,OACA,WAAM,WAAN,mBAAe,gBAAgB,KAAK,CAAC,EAAE,YAAY,CAAC,GAAG,KAAK,UAAU,CAAC,CAAC;AAAA,IAC5E;AAAA,GAAC,CAAC,CAAC;AACH,QAAM,SAAS,yBAAyB;AACxC,SAAO,MAAM,IAAI,UAAQ,OAAO,MAAM,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU;AAC1D,QAAI;AACJ,QAAI,MAAM,YAAY,IAAI,SAAS;AAC/B,cAAQ,MAAM,aAAa,aAAa,MAAM,MAAM,UAAU,IAAI,MAAM;AAAA;AAExE,cAAQ,MAAM,aAAa,aAAa,MAAM,MAAM,UAAU,IAAI,MAAM;AAC5E,YAAQ,uBAAuB,OAAO,iBAAiB;AACvD,QAAI,MAAM,YAAY,IAAI,KAAK;AAC3B,cAAQ,SAAS,KAAK;AAC1B,QAAI,YAAY,UAAU;AAC1B,QAAI,MAAM,YAAY,IAAI,MAAM;AAC5B,mBAAa,UAAU;AAC3B,QAAI,MAAM,YAAY,IAAI,QAAQ;AAC9B,mBAAa,UAAU;AAC3B,QAAI,MAAM,YAAY,IAAI,WAAW;AACjC,mBAAa,UAAU;AAC3B,WAAO;AAAA,MACH,SAAS,MAAM;AAAA,MACf,QAAQ,KAAK,CAAC;AAAA;AAAA,MACd;AAAA,MACA;AAAA,IACJ;AAAA,EACJ,CAAC,CAAC;AACN;AAIA,SAAS,SAAS,OAAO;AACrB,QAAM,WAAW,MAAM,MAAM,4CAA4C;AACzE,MAAI,UAAU;AACV,QAAI,SAAS,CAAC,GAAG;AAEb,YAAM,QAAQ,KAAK,MAAM,OAAO,SAAS,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,EACxD,SAAS,EAAE,EACX,SAAS,GAAG,GAAG;AACpB,aAAO,IAAI,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,KAAK;AAAA,IAChD,WACS,SAAS,CAAC,GAAG;AAElB,aAAO,IAAI,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;AAAA,IACxC,OACK;AAED,aAAO,IAAI,MAAM,KAAK,SAAS,CAAC,CAAC,EAC5B,IAAI,OAAK,GAAG,CAAC,GAAG,CAAC,EAAE,EACnB,KAAK,EAAE,CAAC;AAAA,IACjB;AAAA,EACJ;AACA,QAAM,cAAc,MAAM,MAAM,+BAA+B;AAC/D,MAAI;AACA,WAAO,OAAO,YAAY,CAAC,CAAC;AAChC,SAAO;AACX;AAEA,SAAS,mBAAmB,UAAU,MAAM,UAAU,CAAC,GAAG;AACtD,QAAM,EAAE,OAAO,QAAQ,OAAO,YAAY,SAAS,gBAAgB,EAAE,CAAC,EAAG,IAAI;AAC7E,MAAI,YAAY,IAAI,KAAK,YAAY,SAAS;AAC1C,WAAO,WAAW,IAAI,EAAE,IAAI,UAAQ,CAAC,EAAE,SAAS,KAAK,CAAC,GAAG,QAAQ,KAAK,CAAC,EAAE,CAAC,CAAC;AAC/E,QAAM,EAAE,OAAO,SAAS,IAAI,SAAS,SAAS,SAAS;AACvD,MAAI,SAAS;AACT,WAAO,sBAAsB,OAAO,MAAM,OAAO;AACrD,QAAM,WAAW,SAAS,eAAe,IAAI;AAC7C,SAAO,kBAAkB,MAAM,UAAU,OAAO,UAAU,OAAO;AACrE;AACA,SAAS,kBAAkB,MAAM,SAAS,OAAO,UAAU,SAAS;AAChE,QAAM,oBAAoB;AAAA,IACtB,GAAG,MAAM;AAAA,IACT,GAAG,mCAAS;AAAA,EAChB;AACA,QAAM,QAAQ,WAAW,IAAI;AAC7B,MAAI,YAAY;AAChB,MAAI,SAAS,CAAC;AACd,QAAM,QAAQ,CAAC;AACf,WAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAC9C,UAAM,CAAC,MAAM,UAAU,IAAI,MAAM,CAAC;AAClC,QAAI,SAAS,IAAI;AACb,eAAS,CAAC;AACV,YAAM,KAAK,CAAC,CAAC;AACb;AAAA,IACJ;AACA,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,QAAQ,oBAAoB;AAC5B,yBAAmB,QAAQ,aAAa,MAAM,SAAS;AACvD,yBAAmB,iBAAiB;AACpC,8BAAwB;AAAA,IAC5B;AACA,UAAM,SAAS,QAAQ,cAAc,MAAM,SAAS;AACpD,UAAM,eAAe,OAAO,OAAO,SAAS;AAC5C,aAAS,IAAI,GAAG,IAAI,cAAc,KAAK;AACnC,YAAM,aAAa,OAAO,OAAO,IAAI,CAAC;AACtC,YAAM,iBAAiB,IAAI,IAAI,eAAe,OAAO,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK;AAC9E,UAAI,eAAe;AACf;AACJ,YAAM,WAAW,OAAO,OAAO,IAAI,IAAI,CAAC;AACxC,YAAM,aAAa,qBAAqB,cAAc,QAAQ;AAC9D,YAAM,kBAAkB,uBAAuB,SAAS,UAAU,GAAG,iBAAiB;AACtF,YAAM,YAAY,qBAAqB,aAAa,QAAQ;AAC5D,YAAM,QAAQ;AAAA,QACV,SAAS,KAAK,UAAU,YAAY,cAAc;AAAA,QAClD,QAAQ,aAAa;AAAA,QACrB,OAAO;AAAA,QACP;AAAA,MACJ;AACA,UAAI,QAAQ,oBAAoB;AAC5B,cAAM,cAAc,CAAC;AACrB,YAAI,SAAS;AACb,eAAO,aAAa,SAAS,gBAAgB;AACzC,gBAAM,kBAAkB,iBAAiB,qBAAqB;AAC9D,gBAAM,sBAAsB,KAAK,UAAU,gBAAgB,YAAY,gBAAgB,QAAQ;AAC/F,oBAAU,oBAAoB;AAC9B,gBAAM,YAAY,KAAK;AAAA,YACnB,SAAS;AAAA,YACT,QAAQ,mBAAmB,OAAO,gBAAgB,MAAM;AAAA,UAC5D,CAAC;AACD,mCAAyB;AAAA,QAC7B;AAAA,MACJ;AACA,aAAO,KAAK,KAAK;AAAA,IACrB;AACA,UAAM,KAAK,MAAM;AACjB,aAAS,CAAC;AACV,gBAAY,OAAO;AAAA,EACvB;AACA,SAAO;AACX;AACA,SAAS,mBAAmB,OAAO,QAAQ;AACvC,QAAM,SAAS,CAAC;AAChB,WAAS,IAAI,GAAG,MAAM,OAAO,QAAQ,IAAI,KAAK,KAAK;AAC/C,UAAM,eAAe,OAAO,MAAM,GAAG,CAAC;AACtC,UAAM,QAAQ,OAAO,CAAC;AACtB,WAAO,CAAC,IAAI;AAAA,MACR,WAAW;AAAA,MACX,cAAc,kBAAkB,OAAO,OAAO,YAAY;AAAA,IAC9D;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,WAAW,UAAU,OAAO;AACjC,QAAM,iBAAiB,GAAG,QAAQ;AAClC,MAAI,aAAa,SAAS,MAAM,UAAU,GAAG,eAAe,MAAM,MAAM;AACpE,WAAO;AACX,SAAO;AACX;AACA,SAAS,QAAQ,UAAU,sBAAsB,OAAO,cAAc;AAClE,MAAI,CAAC,WAAW,UAAU,KAAK;AAC3B,WAAO;AACX,MAAI,sBAAsB,qBAAqB,SAAS;AACxD,MAAI,cAAc,aAAa,SAAS;AACxC,SAAO,uBAAuB,KAAK,eAAe,GAAG;AACjD,QAAI,WAAW,qBAAqB,mBAAmB,GAAG,aAAa,WAAW,CAAC;AAC/E,6BAAuB;AAC3B,mBAAe;AAAA,EACnB;AACA,MAAI,wBAAwB;AACxB,WAAO;AACX,SAAO;AACX;AACA,SAAS,kBAAkB,OAAO,OAAO,cAAc;AACnD,QAAM,SAAS,CAAC;AAChB,MAAI,YAAY;AAChB,WAAS,IAAI,GAAG,MAAM,MAAM,SAAS,QAAQ,IAAI,KAAK,KAAK;AACvD,UAAM,UAAU,MAAM,SAAS,CAAC;AAChC,QAAI;AACJ,QAAI,OAAO,QAAQ,UAAU;AACzB,kBAAY,QAAQ,MAAM,MAAM,GAAG,EAAE,IAAI,CAAAC,WAASA,OAAM,KAAK,CAAC;AAAA,aACzD,MAAM,QAAQ,QAAQ,KAAK;AAChC,kBAAY,QAAQ;AAAA;AAEpB;AACJ,aAAS,IAAI,GAAG,OAAO,UAAU,QAAQ,IAAI,MAAM,KAAK;AACpD,YAAM,cAAc,UAAU,CAAC;AAC/B,YAAM,oBAAoB,YAAY,MAAM,GAAG;AAC/C,YAAM,WAAW,kBAAkB,kBAAkB,SAAS,CAAC;AAC/D,YAAM,uBAAuB,kBAAkB,MAAM,GAAG,kBAAkB,SAAS,CAAC;AACpF,UAAI,QAAQ,UAAU,sBAAsB,OAAO,YAAY,GAAG;AAE9D,eAAO,WAAW,IAAI;AAEtB,YAAI;AAAA,MACR;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAKA,SAAS,uBAAuB,UAAU,MAAM,SAAS;AACrD,QAAM,SAAS,OAAO,QAAQ,QAAQ,MAAM,EACvC,OAAO,OAAK,EAAE,CAAC,CAAC,EAChB,IAAI,QAAM,EAAE,OAAO,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;AAC5C,QAAM,SAAS,uBAAuB,GAAG,OAAO,IAAI,OAAK,mBAAmB,UAAU,MAAM;AAAA,IACxF,GAAG;AAAA,IACH,OAAO,EAAE;AAAA,IACT,oBAAoB;AAAA,EACxB,CAAC,CAAC,CAAC;AACH,QAAM,eAAe,OAAO,CAAC,EACxB,IAAI,CAAC,MAAM,YAAY,KACvB,IAAI,CAAC,QAAQ,aAAa;AAC3B,UAAM,cAAc;AAAA,MAChB,SAAS,OAAO;AAAA,MAChB,UAAU,CAAC;AAAA,MACX,QAAQ,OAAO;AAAA,IACnB;AACA,WAAO,QAAQ,CAAC,GAAG,aAAa;AAC5B,YAAM,EAAE,SAAS,GAAG,aAAa,IAAI,QAAQ,KAAK,GAAG,OAAO,IAAI,EAAE,OAAO,EAAE,QAAQ;AACnF,kBAAY,SAAS,OAAO,QAAQ,EAAE,KAAK,IAAI;AAAA,IACnD,CAAC;AACD,WAAO;AAAA,EACX,CAAC,CAAC;AACF,SAAO;AACX;AAcA,SAAS,0BAA0B,QAAQ;AACvC,QAAM,YAAY,OAAO,IAAI,MAAM,CAAC,CAAC;AACrC,QAAM,QAAQ,OAAO;AACrB,WAAS,IAAI,GAAG,IAAI,OAAO,CAAC,EAAE,QAAQ,KAAK;AACvC,UAAM,QAAQ,OAAO,IAAI,OAAK,EAAE,CAAC,CAAC;AAClC,UAAM,WAAW,UAAU,IAAI,MAAM,CAAC,CAAC;AACvC,cAAU,QAAQ,CAAC,GAAGC,OAAM,EAAE,KAAK,SAASA,EAAC,CAAC,CAAC;AAC/C,UAAM,UAAU,MAAM,IAAI,MAAM,CAAC;AACjC,UAAM,UAAU,MAAM,IAAI,OAAK,EAAE,CAAC,CAAC;AACnC,WAAO,QAAQ,MAAM,OAAK,CAAC,GAAG;AAC1B,YAAM,YAAY,KAAK,IAAI,GAAG,QAAQ,IAAI,OAAK,EAAE,QAAQ,MAAM,CAAC;AAChE,eAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC5B,cAAM,QAAQ,QAAQ,CAAC;AACvB,YAAI,MAAM,QAAQ,WAAW,WAAW;AACpC,mBAAS,CAAC,EAAE,KAAK,KAAK;AACtB,kBAAQ,CAAC,KAAK;AACd,kBAAQ,CAAC,IAAI,MAAM,CAAC,EAAE,QAAQ,CAAC,CAAC;AAAA,QACpC,OACK;AACD,mBAAS,CAAC,EAAE,KAAK;AAAA,YACb,GAAG;AAAA,YACH,SAAS,MAAM,QAAQ,MAAM,GAAG,SAAS;AAAA,UAC7C,CAAC;AACD,kBAAQ,CAAC,IAAI;AAAA,YACT,GAAG;AAAA,YACH,SAAS,MAAM,QAAQ,MAAM,SAAS;AAAA,YACtC,QAAQ,MAAM,SAAS;AAAA,UAC3B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,WAAW,UAAU,MAAM,SAAS,qBAAqB;AAAA,EAC9D,MAAM,CAAC;AAAA,EACP;AAAA,EACA,YAAY,CAAC,OAAO,aAAa,WAAW,UAAU,OAAO,QAAQ;AACzE,GAAG;AA/nBH;AAgoBI,MAAI,QAAQ;AACZ,aAAW,eAAe,QAAQ,gBAAgB,CAAC;AAC/C,cAAQ,iBAAY,eAAZ,mBAAwB,KAAK,oBAAoB,OAAO,aAAY;AAChF,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,YAAY,SAAS;AACrB,UAAM,EAAE,eAAe,SAAS,oBAAoB,WAAY,IAAI;AACpE,UAAM,SAAS,OAAO,QAAQ,QAAQ,MAAM,EACvC,OAAO,OAAK,EAAE,CAAC,CAAC,EAChB,IAAI,QAAM,EAAE,OAAO,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE,EACvC,KAAK,CAAC,GAAG,MAAM,EAAE,UAAU,eAAe,KAAK,EAAE,UAAU,eAAe,IAAI,CAAC;AACpF,QAAI,OAAO,WAAW;AAClB,YAAM,IAAI,MAAM,6CAA6C;AACjE,UAAM,cAAc,uBAAuB,UAAU,OAAO,OAAO;AACnE,QAAI,gBAAgB,CAAC,OAAO,KAAK,OAAK,EAAE,UAAU,YAAY;AAC1D,YAAM,IAAI,MAAM,mEAAmE,YAAY,IAAI;AACvG,UAAM,YAAY,OAAO,IAAI,OAAK,SAAS,SAAS,EAAE,KAAK,CAAC;AAC5D,UAAM,cAAc,OAAO,IAAI,OAAK,EAAE,KAAK;AAC3C,aAAS,YACJ,IAAI,UAAQ,KAAK,IAAI,WAAS,WAAW,OAAO,aAAa,mBAAmB,YAAY,CAAC,CAAC;AACnG,SAAK,OAAO,IAAI,CAAC,GAAG,SAAS,QAAQ,KAAK,eAAe,KAAK,GAAG,oBAAoB,EAAE,KAAK,QAAQ,UAAU,GAAG,EAAE,MAAM,UAAU,EAAE,KAAK,GAAG;AAC7I,SAAK,OAAO,IAAI,CAAC,GAAG,SAAS,QAAQ,KAAK,eAAe,KAAK,GAAG,oBAAoB,EAAE,KAAK,WAAW,UAAU,GAAG,EAAE,MAAM,UAAU,EAAE,KAAK,GAAG;AAChJ,gBAAY,gBAAgB,UAAU,IAAI,OAAK,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC;AAChE,gBAAY,eAAe,SAAY,CAAC,IAAI,EAAE,EAAE,KAAK,GAAG;AAAA,EAC5D,WACS,WAAW,SAAS;AACzB,aAAS,mBAAmB,UAAU,OAAO;AAAA,MACzC,GAAG;AAAA,MACH,oBAAoB;AAAA,IACxB,CAAC;AACD,UAAM,SAAS,SAAS,SAAS,QAAQ,KAAK;AAC9C,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,gBAAY,OAAO;AAAA,EACvB,OACK;AACD,UAAM,IAAI,MAAM,wEAAwE;AAAA,EAC5F;AACA,QAAM,EAAE,mBAAmB,KAAM,IAAI;AACrC,MAAI,qBAAqB;AACrB,aAAS,sBAAsB,MAAM;AAAA,WAChC,qBAAqB;AAC1B,aAAS,sBAAsB,MAAM;AACzC,aAAW,eAAe,QAAQ,gBAAgB,CAAC;AAC/C,eAAS,iBAAY,WAAZ,mBAAoB,KAAK,oBAAoB,YAAW;AACrE,SAAO,aAAa,QAAQ;AAAA,IACxB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,GAAG,kBAAkB;AACzB;AACA,SAAS,WAAW,QAAQ,eAAe,mBAAmB,cAAc;AACxE,QAAM,QAAQ;AAAA,IACV,SAAS,OAAO;AAAA,IAChB,aAAa,OAAO;AAAA,IACpB,QAAQ,OAAO;AAAA,EACnB;AACA,QAAM,SAAS,cAAc,IAAI,OAAK,oBAAoB,OAAO,SAAS,CAAC,CAAC,CAAC;AAE7E,QAAM,YAAY,IAAI,IAAI,OAAO,QAAQ,OAAK,OAAO,KAAK,CAAC,CAAC,CAAC;AAC7D,QAAM,eAAe,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ;AAClD,eAAWC,QAAO,WAAW;AACzB,YAAM,QAAQ,IAAIA,IAAG,KAAK;AAC1B,UAAI,QAAQ,KAAK,cAAc;AAC3B,YAAIA,IAAG,IAAI;AAAA,MACf,OACK;AACD,cAAM,SAAS,oBAAoB,cAAc,GAAG,KAAKA,SAAQ,UAAU,KAAK,IAAIA,IAAG;AACvF,YAAI,IAAIA,IAAG;AACP,cAAIA,IAAG,KAAK,IAAI,MAAM,IAAI,KAAK;AAAA;AAE/B,cAAIA,IAAG,IAAI,GAAG,MAAM,IAAI,KAAK;AAAA,MACrC;AAAA,IACJ;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,QAAM,YAAY,eACZ,oBAAoB,YAAY,IAChC,OAAO,OAAO,YAAY,EAAE,KAAK,GAAG;AAC1C,SAAO;AACX;AACA,SAAS,aAAa,QAAQ,SAAS,oBAAoB;AAttB3D;AAutBI,QAAM,EAAE,eAAe,CAAC,EAAG,IAAI;AAC/B,QAAM,QAAQ,CAAC;AACf,QAAM,OAAO;AAAA,IACT,MAAM;AAAA,IACN,UAAU,CAAC;AAAA,EACf;AACA,MAAI,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY;AAAA,MACR,OAAO,SAAS,QAAQ,aAAa,EAAE;AAAA,MACvC,OAAO,QAAQ,aAAa,oBAAoB,QAAQ,EAAE,UAAU,QAAQ,EAAE;AAAA,MAC9E,UAAU;AAAA,MACV,GAAG,OAAO,YAAY,MAAM,KAAK,OAAO,QAAQ,QAAQ,QAAQ,CAAC,CAAC,CAAC,EAC9D,OAAO,CAAC,CAACA,IAAG,MAAM,CAACA,KAAI,WAAW,GAAG,CAAC,CAAC;AAAA,IAChD;AAAA,IACA,UAAU,CAAC;AAAA,EACf;AACA,MAAI,WAAW;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU;AAAA,EACd;AACA,QAAM,YAAY,CAAC;AACnB,QAAM,UAAU;AAAA,IACZ,GAAG;AAAA,IACH,IAAI,SAAS;AACT,aAAO;AAAA,IACX;AAAA,IACA,IAAI,UAAU;AACV,aAAO;AAAA,IACX;AAAA,IACA,IAAI,OAAO;AACP,aAAO;AAAA,IACX;AAAA,IACA,IAAI,MAAM;AACN,aAAO;AAAA,IACX;AAAA,IACA,IAAI,OAAO;AACP,aAAO;AAAA,IACX;AAAA,IACA,IAAI,QAAQ;AACR,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO,QAAQ,CAAC,MAAM,QAAQ;AArwBlC,QAAAC,KAAAC;AAswBQ,QAAI;AACA,YAAM,KAAK,EAAE,MAAM,QAAQ,OAAO,KAAK,CAAC;AAC5C,QAAI,WAAW;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY,EAAE,OAAO,OAAO;AAAA,MAC5B,UAAU,CAAC;AAAA,IACf;AACA,QAAI,MAAM;AACV,eAAW,SAAS,MAAM;AACtB,UAAI,YAAY;AAAA,QACZ,MAAM;AAAA,QACN,SAAS;AAAA,QACT,YAAY,CAAC;AAAA,QACb,UAAU,CAAC,EAAE,MAAM,QAAQ,OAAO,MAAM,QAAQ,CAAC;AAAA,MACrD;AACA,YAAM,QAAQ,MAAM,aAAa,oBAAoB,oBAAoB,KAAK,CAAC;AAC/E,UAAI;AACA,kBAAU,WAAW,QAAQ;AACjC,iBAAW,eAAe;AACtB,sBAAaD,OAAA,2CAAa,UAAQ,2CAAa,WAAlC,gBAAAA,IAA0C,KAAK,SAAS,WAAW,MAAM,GAAG,KAAK,cAAa;AAC/G,eAAS,SAAS,KAAK,SAAS;AAChC,aAAO,MAAM,QAAQ;AAAA,IACzB;AACA,eAAW,eAAe;AACtB,mBAAWC,MAAA,2CAAa,SAAb,gBAAAA,IAAmB,KAAK,SAAS,UAAU,MAAM,OAAM;AACtE,cAAU,KAAK,QAAQ;AACvB,UAAM,KAAK,QAAQ;AAAA,EACvB,CAAC;AACD,aAAW,eAAe;AACtB,iBAAW,gDAAa,SAAb,mBAAmB,KAAK,SAAS,cAAa;AAC7D,UAAQ,SAAS,KAAK,QAAQ;AAC9B,aAAW,eAAe;AACtB,gBAAU,gDAAa,QAAb,mBAAkB,KAAK,SAAS,aAAY;AAC1D,OAAK,SAAS,KAAK,OAAO;AAC1B,MAAI,SAAS;AACb,aAAW,eAAe;AACtB,eAAS,gDAAa,SAAb,mBAAmB,KAAK,SAAS,YAAW;AACzD,SAAO;AACX;AACA,SAAS,oBAAoB,OAAO;AAChC,QAAM,SAAS,CAAC;AAChB,MAAI,MAAM;AACN,WAAO,QAAQ,MAAM;AACzB,MAAI,MAAM,WAAW;AACjB,QAAI,MAAM,YAAY,UAAU;AAC5B,aAAO,YAAY,IAAI;AAC3B,QAAI,MAAM,YAAY,UAAU;AAC5B,aAAO,aAAa,IAAI;AAC5B,QAAI,MAAM,YAAY,UAAU;AAC5B,aAAO,iBAAiB,IAAI;AAAA,EACpC;AACA,SAAO;AACX;AACA,SAAS,oBAAoB,OAAO;AAChC,SAAO,OAAO,QAAQ,KAAK,EAAE,IAAI,CAAC,CAACF,MAAK,KAAK,MAAM,GAAGA,IAAG,IAAI,KAAK,EAAE,EAAE,KAAK,GAAG;AAClF;AACA,SAAS,sBAAsB,QAAQ;AACnC,SAAO,OAAO,IAAI,CAAC,SAAS;AACxB,UAAM,UAAU,CAAC;AACjB,QAAI,iBAAiB;AACrB,QAAI,cAAc;AAClB,SAAK,QAAQ,CAAC,OAAO,QAAQ;AACzB,YAAM,cAAc,MAAM,aAAa,MAAM,YAAY,UAAU;AACnE,YAAM,aAAa,CAAC;AACpB,UAAI,cAAc,MAAM,QAAQ,MAAM,OAAO,KAAK,KAAK,MAAM,CAAC,GAAG;AAC7D,YAAI,CAAC;AACD,wBAAc,MAAM;AACxB,0BAAkB,MAAM;AAAA,MAC5B,OACK;AACD,YAAI,gBAAgB;AAChB,cAAI,YAAY;AACZ,oBAAQ,KAAK;AAAA,cACT,GAAG;AAAA,cACH,SAAS,iBAAiB,MAAM;AAAA,YACpC,CAAC;AAAA,UACL,OACK;AACD,oBAAQ,KAAK;AAAA,cACT,SAAS;AAAA,cACT,QAAQ;AAAA,YACZ,GAAG,KAAK;AAAA,UACZ;AACA,2BAAiB;AAAA,QACrB,OACK;AACD,kBAAQ,KAAK,KAAK;AAAA,QACtB;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX,CAAC;AACL;AACA,SAAS,sBAAsB,QAAQ;AACnC,SAAO,OAAO,IAAI,CAAC,SAAS;AACxB,WAAO,KAAK,QAAQ,CAAC,UAAU;AAC3B,UAAI,MAAM,QAAQ,MAAM,OAAO;AAC3B,eAAO;AACX,YAAM,QAAQ,MAAM,QAAQ,MAAM,mBAAmB;AACrD,UAAI,CAAC;AACD,eAAO;AACX,YAAM,CAAC,EAAE,SAAS,SAAS,QAAQ,IAAI;AACvC,UAAI,CAAC,WAAW,CAAC;AACb,eAAO;AACX,YAAM,WAAW,CAAC;AAAA,QACV,GAAG;AAAA,QACH,QAAQ,MAAM,SAAS,QAAQ;AAAA,QAC/B;AAAA,MACJ,CAAC;AACL,UAAI,SAAS;AACT,iBAAS,QAAQ;AAAA,UACb,SAAS;AAAA,UACT,QAAQ,MAAM;AAAA,QAClB,CAAC;AAAA,MACL;AACA,UAAI,UAAU;AACV,iBAAS,KAAK;AAAA,UACV,SAAS;AAAA,UACT,QAAQ,MAAM,SAAS,QAAQ,SAAS,QAAQ;AAAA,QACpD,CAAC;AAAA,MACL;AACA,aAAO;AAAA,IACX,CAAC;AAAA,EACL,CAAC;AACL;AAOA,IAAM,mBAAmB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAQA,IAAM,SAAN,MAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOX,YAAY,UAAU,QAAQ,OAAO;AACnC,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,QAAI,OAAO;AACT,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AACF;AAGA,OAAO,UAAU,WAAW,CAAC;AAE7B,OAAO,UAAU,SAAS,CAAC;AAE3B,OAAO,UAAU,QAAQ;AAazB,SAAS,MAAM,aAAa,OAAO;AAEjC,QAAM,WAAW,CAAC;AAElB,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,YAAY,QAAQ;AACnC,WAAO,OAAO,UAAU,YAAY,KAAK,EAAE,QAAQ;AACnD,WAAO,OAAO,QAAQ,YAAY,KAAK,EAAE,MAAM;AAAA,EACjD;AAEA,SAAO,IAAI,OAAO,UAAU,QAAQ,KAAK;AAC3C;AAMA,SAAS,UAAU,OAAO;AACxB,SAAO,MAAM,YAAY;AAC3B;AAEA,IAAM,OAAN,MAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMT,YAAY,UAAU,WAAW;AAE/B,SAAK,WAAW;AAEhB,SAAK,YAAY;AAAA,EACnB;AACF;AAGA,KAAK,UAAU,QAAQ;AACvB,KAAK,UAAU,UAAU;AACzB,KAAK,UAAU,aAAa;AAC5B,KAAK,UAAU,oBAAoB;AACnC,KAAK,UAAU,SAAS;AACxB,KAAK,UAAU,iBAAiB;AAChC,KAAK,UAAU,iBAAiB;AAChC,KAAK,UAAU,wBAAwB;AACvC,KAAK,UAAU,kBAAkB;AACjC,KAAK,UAAU,UAAU;AAEzB,IAAI,SAAS;AAEb,IAAM,UAAU,UAAU;AAC1B,IAAM,aAAa,UAAU;AAC7B,IAAM,oBAAoB,UAAU;AACpC,IAAM,SAAS,UAAU;AACzB,IAAM,iBAAiB,UAAU;AACjC,IAAM,iBAAiB,UAAU;AACjC,IAAM,wBAAwB,UAAU;AAExC,SAAS,YAAY;AACnB,SAAO,KAAK,EAAE;AAChB;AAEA,IAAI,QAAqB,OAAO,OAAO;AAAA,EACrC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAID,IAAM,SAAS,OAAO,KAAK,KAAK;AAEhC,IAAM,cAAN,cAA0B,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ7B,YAAY,UAAU,WAAW,MAAM,OAAO;AAC5C,QAAI,QAAQ;AAEZ,UAAM,UAAU,SAAS;AAEzB,SAAK,MAAM,SAAS,KAAK;AAEzB,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,EAAE,QAAQ,OAAO,QAAQ;AAC9B,cAAM,QAAQ,OAAO,KAAK;AAC1B,aAAK,MAAM,OAAO,KAAK,IAAI,OAAO,MAAM,KAAK,OAAO,MAAM,KAAK,CAAC;AAAA,MAClE;AAAA,IACF;AAAA,EACF;AACF;AAEA,YAAY,UAAU,UAAU;AAOhC,SAAS,KAAK,QAAQA,MAAK,OAAO;AAChC,MAAI,OAAO;AAET,WAAOA,IAAG,IAAI;AAAA,EAChB;AACF;AAiBA,IAAM,QAAQ,CAAC,EAAE;AAMjB,SAAS,OAAO,YAAY;AAE1B,QAAM,WAAW,CAAC;AAElB,QAAM,SAAS,CAAC;AAEhB,MAAI;AAEJ,OAAK,QAAQ,WAAW,YAAY;AAClC,QAAI,MAAM,KAAK,WAAW,YAAY,IAAI,GAAG;AAC3C,YAAM,QAAQ,WAAW,WAAW,IAAI;AACxC,YAAM,OAAO,IAAI;AAAA,QACf;AAAA,QACA,WAAW,UAAU,WAAW,cAAc,CAAC,GAAG,IAAI;AAAA,QACtD;AAAA,QACA,WAAW;AAAA,MACb;AAEA,UACE,WAAW,mBACX,WAAW,gBAAgB,SAAS,IAAI,GACxC;AACA,aAAK,kBAAkB;AAAA,MACzB;AAEA,eAAS,IAAI,IAAI;AAEjB,aAAO,UAAU,IAAI,CAAC,IAAI;AAC1B,aAAO,UAAU,KAAK,SAAS,CAAC,IAAI;AAAA,IACtC;AAAA,EACF;AAEA,SAAO,IAAI,OAAO,UAAU,QAAQ,WAAW,KAAK;AACtD;AAEA,IAAM,QAAQ,OAAO;AAAA,EACnB,OAAO;AAAA,EACP,UAAU,GAAG,MAAM;AACjB,WAAO,WAAW,KAAK,MAAM,CAAC,EAAE,YAAY;AAAA,EAC9C;AAAA,EACA,YAAY;AAAA,IACV,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW;AAAA,IACX,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACF,CAAC;AAED,IAAM,MAAM,OAAO;AAAA,EACjB,OAAO;AAAA,EACP,UAAU,GAAG,MAAM;AACjB,WAAO,SAAS,KAAK,MAAM,CAAC,EAAE,YAAY;AAAA,EAC5C;AAAA,EACA,YAAY,EAAC,SAAS,MAAM,SAAS,MAAM,UAAU,KAAI;AAC3D,CAAC;AAOD,SAAS,uBAAuB,YAAY,WAAW;AACrD,SAAO,aAAa,aAAa,WAAW,SAAS,IAAI;AAC3D;AAOA,SAAS,yBAAyB,YAAY,UAAU;AACtD,SAAO,uBAAuB,YAAY,SAAS,YAAY,CAAC;AAClE;AAEA,IAAM,QAAQ,OAAO;AAAA,EACnB,OAAO;AAAA,EACP,YAAY,EAAC,YAAY,cAAa;AAAA,EACtC,WAAW;AAAA,EACX,YAAY,EAAC,OAAO,MAAM,YAAY,KAAI;AAC5C,CAAC;AAED,IAAM,OAAO,OAAO;AAAA,EAClB,UAAU,GAAG,MAAM;AACjB,WAAO,SAAS,SAAS,OAAO,UAAU,KAAK,MAAM,CAAC,EAAE,YAAY;AAAA,EACtE;AAAA,EACA,YAAY;AAAA,IACV,sBAAsB;AAAA,IACtB,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,aAAa;AAAA,IACb,cAAc;AAAA,IACd,cAAc;AAAA,IACd,aAAa;AAAA,IACb,cAAc;AAAA,IACd,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,IACf,qBAAqB;AAAA,IACrB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,aAAa;AAAA,IACb,cAAc;AAAA,IACd,cAAc;AAAA,IACd,cAAc;AAAA,IACd,qBAAqB;AAAA,IACrB,cAAc;AAAA,IACd,cAAc;AAAA,IACd,aAAa;AAAA,IACb,cAAc;AAAA,IACd,aAAa;AAAA,IACb,UAAU;AAAA,IACV,cAAc;AAAA,IACd,cAAc;AAAA,IACd,cAAc;AAAA,IACd,eAAe;AAAA,IACf,MAAM;AAAA,EACR;AACF,CAAC;AAED,IAAM,SAAS,OAAO;AAAA,EACpB,OAAO;AAAA,EACP,YAAY;AAAA,IACV,eAAe;AAAA,IACf,WAAW;AAAA,IACX,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AAAA,EACA,WAAW;AAAA,EACX,iBAAiB,CAAC,WAAW,YAAY,SAAS,UAAU;AAAA,EAC5D,YAAY;AAAA;AAAA,IAEV,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,WAAW;AAAA,IACX,UAAU;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,MAAM;AAAA,IACN,WAAW;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,cAAc;AAAA,IACd,QAAQ,SAAS;AAAA,IACjB,aAAa;AAAA,IACb,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,SAAS;AAAA,IACT,OAAO;AAAA,IACP,KAAK;AAAA,IACL,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,SAAS;AAAA,IACT,cAAc;AAAA,IACd,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS;AAAA,IACT,WAAW;AAAA,IACX,IAAI;AAAA,IACJ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,WAAW;AAAA,IACX,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,IACT,WAAW;AAAA,IACX,UAAU;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,KAAK;AAAA,IACL,UAAU;AAAA,IACV,KAAK;AAAA,IACL,WAAW;AAAA,IACX,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,WAAW;AAAA,IACX,UAAU;AAAA,IACV,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,WAAW;AAAA,IACX,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,SAAS;AAAA,IACT,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,aAAa;AAAA,IACb,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,cAAc;AAAA,IACd,cAAc;AAAA,IACd,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb,2BAA2B;AAAA,IAC3B,UAAU;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,cAAc;AAAA,IACd,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IACd,UAAU;AAAA,IACV,sBAAsB;AAAA,IACtB,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,SAAS;AAAA,IACT,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,IACb,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,KAAK;AAAA,IACL,UAAU;AAAA,IACV,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU;AAAA,IACV,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,WAAW;AAAA,IACX,MAAM;AAAA,IACN,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA;AAAA;AAAA,IAIN,OAAO;AAAA;AAAA,IACP,OAAO;AAAA;AAAA,IACP,SAAS;AAAA;AAAA,IACT,MAAM;AAAA;AAAA,IACN,YAAY;AAAA;AAAA,IACZ,SAAS;AAAA;AAAA,IACT,QAAQ;AAAA;AAAA,IACR,aAAa;AAAA;AAAA,IACb,cAAc;AAAA;AAAA,IACd,aAAa;AAAA;AAAA,IACb,aAAa;AAAA;AAAA,IACb,MAAM;AAAA;AAAA,IACN,SAAS;AAAA;AAAA,IACT,SAAS;AAAA;AAAA,IACT,OAAO;AAAA;AAAA,IACP,MAAM;AAAA;AAAA,IACN,UAAU;AAAA;AAAA,IACV,UAAU;AAAA;AAAA,IACV,OAAO;AAAA;AAAA,IACP,SAAS;AAAA;AAAA,IACT,SAAS;AAAA;AAAA,IACT,OAAO;AAAA;AAAA,IACP,MAAM;AAAA;AAAA,IACN,OAAO;AAAA;AAAA,IACP,aAAa;AAAA;AAAA,IACb,QAAQ;AAAA;AAAA,IACR,YAAY;AAAA;AAAA,IACZ,MAAM;AAAA;AAAA,IACN,UAAU;AAAA;AAAA,IACV,QAAQ;AAAA;AAAA,IACR,cAAc;AAAA;AAAA,IACd,aAAa;AAAA;AAAA,IACb,UAAU;AAAA;AAAA,IACV,QAAQ;AAAA;AAAA,IACR,SAAS;AAAA;AAAA,IACT,QAAQ;AAAA;AAAA,IACR,QAAQ;AAAA;AAAA,IACR,SAAS;AAAA;AAAA,IACT,QAAQ;AAAA;AAAA,IACR,KAAK;AAAA;AAAA,IACL,aAAa;AAAA;AAAA,IACb,OAAO;AAAA;AAAA,IACP,QAAQ;AAAA;AAAA,IACR,WAAW;AAAA;AAAA,IACX,SAAS;AAAA;AAAA,IACT,SAAS;AAAA;AAAA,IACT,MAAM;AAAA;AAAA,IACN,WAAW;AAAA;AAAA,IACX,WAAW;AAAA;AAAA,IACX,SAAS;AAAA;AAAA,IACT,QAAQ;AAAA;AAAA,IACR,OAAO;AAAA;AAAA,IACP,QAAQ;AAAA;AAAA;AAAA,IAGR,mBAAmB;AAAA,IACnB,aAAa;AAAA,IACb,UAAU;AAAA,IACV,yBAAyB;AAAA,IACzB,uBAAuB;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,IACV,cAAc;AAAA,EAChB;AACF,CAAC;AAED,IAAM,QAAQ,OAAO;AAAA,EACnB,OAAO;AAAA,EACP,YAAY;AAAA,IACV,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,UAAU;AAAA,IACV,oBAAoB;AAAA,IACpB,2BAA2B;AAAA,IAC3B,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,WAAW;AAAA,IACX,aAAa;AAAA,IACb,SAAS;AAAA,IACT,aAAa;AAAA,IACb,cAAc;AAAA,IACd,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,IACP,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,eAAe;AAAA,IACf,SAAS;AAAA,IACT,UAAU;AAAA,IACV,WAAW;AAAA,IACX,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,SAAS;AAAA,IACT,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,cAAc;AAAA,IACd,cAAc;AAAA,IACd,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,UAAU;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IACd,UAAU;AAAA,IACV,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,aAAa;AAAA,IACb,uBAAuB;AAAA,IACvB,wBAAwB;AAAA,IACxB,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,aAAa;AAAA,IACb,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,aAAa;AAAA,IACb,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,UAAU;AAAA,IACV,cAAc;AAAA,IACd,eAAe;AAAA,IACf,cAAc;AAAA,IACd,UAAU;AAAA,IACV,aAAa;AAAA,IACb,aAAa;AAAA,IACb,aAAa;AAAA,IACb,aAAa;AAAA,IACb,SAAS;AAAA;AAAA,IAET,eAAe;AAAA,IACf,eAAe;AAAA,EACjB;AAAA,EACA,WAAW;AAAA,EACX,YAAY;AAAA,IACV,OAAO;AAAA,IACP,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,eAAe;AAAA,IACf,SAAS;AAAA,IACT,WAAW;AAAA,IACX,eAAe;AAAA,IACf,eAAe;AAAA,IACf,aAAa;AAAA,IACb,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,IACX,MAAM;AAAA,IACN,UAAU;AAAA,IACV,eAAe;AAAA,IACf,UAAU;AAAA,IACV,OAAO;AAAA,IACP,oBAAoB;AAAA,IACpB,2BAA2B;AAAA,IAC3B,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,GAAG;AAAA,IACH,UAAU;AAAA,IACV,eAAe;AAAA,IACf,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,SAAS;AAAA,IACT,KAAK;AAAA,IACL,SAAS;AAAA,IACT,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,kBAAkB;AAAA,IAClB,KAAK;AAAA,IACL,OAAO;AAAA,IACP,UAAU;AAAA,IACV,2BAA2B;AAAA,IAC3B,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,WAAW;AAAA,IACX,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,SAAS;AAAA,IACT,SAAS;AAAA,IACT,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IACd,cAAc;AAAA,IACd,IAAI;AAAA,IACJ,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,WAAW;AAAA,IACX,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,WAAW;AAAA;AAAA,IACX,YAAY;AAAA;AAAA,IACZ,UAAU;AAAA;AAAA,IACV,SAAS;AAAA,IACT,MAAM;AAAA,IACN,cAAc;AAAA,IACd,eAAe;AAAA,IACf,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,OAAO;AAAA,IACP,WAAW;AAAA,IACX,WAAW;AAAA,IACX,aAAa;AAAA,IACb,cAAc;AAAA,IACd,aAAa;AAAA,IACb,aAAa;AAAA,IACb,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,KAAK;AAAA,IACL,OAAO;AAAA,IACP,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,aAAa;AAAA,IACb,cAAc;AAAA,IACd,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,IACP,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,eAAe;AAAA,IACf,SAAS;AAAA,IACT,UAAU;AAAA,IACV,WAAW;AAAA,IACX,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,WAAW;AAAA,IACX,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,SAAS;AAAA,IACT,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,cAAc;AAAA,IACd,cAAc;AAAA,IACd,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,cAAc;AAAA,IACd,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,UAAU;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IACd,UAAU;AAAA,IACV,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,IACT,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,qBAAqB;AAAA,IACrB,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,eAAe;AAAA,IACf,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,WAAW;AAAA,IACX,WAAW;AAAA,IACX,eAAe;AAAA,IACf,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,GAAG;AAAA,IACH,QAAQ;AAAA,IACR,gBAAgB;AAAA,IAChB,MAAM;AAAA,IACN,MAAM;AAAA,IACN,KAAK;AAAA,IACL,KAAK;AAAA,IACL,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,WAAW;AAAA,IACX,oBAAoB;AAAA,IACpB,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,gBAAgB;AAAA,IAChB,MAAM;AAAA,IACN,OAAO;AAAA,IACP,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,SAAS;AAAA,IACT,aAAa;AAAA,IACb,cAAc;AAAA,IACd,OAAO;AAAA,IACP,OAAO;AAAA,IACP,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,uBAAuB;AAAA,IACvB,wBAAwB;AAAA,IACxB,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,aAAa;AAAA,IACb,OAAO;AAAA,IACP,cAAc;AAAA,IACd,cAAc;AAAA,IACd,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,sBAAsB;AAAA,IACtB,gBAAgB;AAAA,IAChB,UAAU;AAAA,IACV,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,OAAO;AAAA,IACP,mBAAmB;AAAA,IACnB,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,IAAI;AAAA,IACJ,WAAW;AAAA,IACX,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,SAAS;AAAA,IACT,aAAa;AAAA,IACb,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,eAAe;AAAA,IACf,cAAc;AAAA,IACd,UAAU;AAAA,IACV,cAAc;AAAA,IACd,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,aAAa;AAAA,IACb,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,aAAa;AAAA,IACb,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACT,GAAG;AAAA,IACH,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,kBAAkB;AAAA,IAClB,GAAG;AAAA,IACH,YAAY;AAAA,EACd;AACF,CAAC;AAOD,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AAOZ,SAAS,KAAK,QAAQ,OAAO;AAC3B,QAAM,SAAS,UAAU,KAAK;AAC9B,MAAI,OAAO;AACX,MAAI,OAAO;AAEX,MAAI,UAAU,OAAO,QAAQ;AAC3B,WAAO,OAAO,SAAS,OAAO,OAAO,MAAM,CAAC;AAAA,EAC9C;AAEA,MAAI,OAAO,SAAS,KAAK,OAAO,MAAM,GAAG,CAAC,MAAM,UAAU,MAAM,KAAK,KAAK,GAAG;AAE3E,QAAI,MAAM,OAAO,CAAC,MAAM,KAAK;AAE3B,YAAM,OAAO,MAAM,MAAM,CAAC,EAAE,QAAQ,MAAM,SAAS;AACnD,aAAO,SAAS,KAAK,OAAO,CAAC,EAAE,YAAY,IAAI,KAAK,MAAM,CAAC;AAAA,IAC7D,OAAO;AAEL,YAAM,OAAO,MAAM,MAAM,CAAC;AAE1B,UAAI,CAAC,KAAK,KAAK,IAAI,GAAG;AACpB,YAAI,SAAS,KAAK,QAAQ,KAAK,KAAK;AAEpC,YAAI,OAAO,OAAO,CAAC,MAAM,KAAK;AAC5B,mBAAS,MAAM;AAAA,QACjB;AAEA,gBAAQ,SAAS;AAAA,MACnB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,IAAI,KAAK,MAAM,KAAK;AAC7B;AAMA,SAAS,MAAM,IAAI;AACjB,SAAO,MAAM,GAAG,YAAY;AAC9B;AAMA,SAAS,UAAU,IAAI;AACrB,SAAO,GAAG,OAAO,CAAC,EAAE,YAAY;AAClC;AAOA,IAAM,SAAS,MAAM,CAAC,KAAK,OAAO,OAAO,MAAM,MAAM,GAAG,MAAM;AAC9D,IAAM,MAAM,MAAM,CAAC,KAAK,OAAO,OAAO,MAAM,KAAK,GAAG,KAAK;AAmDzD,IAAM,QAAQ,CAAC,EAAE;AAcjB,SAAS,OAAOA,MAAK,SAAS;AAC5B,QAAM,WAAW,WAAW,CAAC;AA8B7B,WAASG,KAAI,UAAU,YAAY;AAEjC,QAAI,KAAKA,KAAI;AACb,UAAM,WAAWA,KAAI;AAErB,QAAI,SAAS,MAAM,KAAK,OAAOH,IAAG,GAAG;AAEnC,YAAM,KAAK,OAAO,MAAMA,IAAG,CAAC;AAE5B,WAAK,MAAM,KAAK,UAAU,EAAE,IAAI,SAAS,EAAE,IAAIG,KAAI;AAAA,IACrD;AAEA,QAAI,IAAI;AACN,aAAO,GAAG,KAAK,MAAM,OAAO,GAAG,UAAU;AAAA,IAC3C;AAAA,EACF;AAEA,EAAAA,KAAI,WAAW,SAAS,YAAY,CAAC;AACrC,EAAAA,KAAI,UAAU,SAAS;AACvB,EAAAA,KAAI,UAAU,SAAS;AAGvB,SAAOA;AACT;AAwBA,SAAS,KAAK,OAAO,SAAS;AAC5B,UAAQ,MAAM;AAAA,IACZ,QAAQ,SAAS,uBAAuB,QAAQ,MAAM,IAAI;AAAA,IAC1D;AAAA,EACF;AAEA,MAAI,QAAQ,UAAU,QAAQ,YAAY;AACxC,WAAO;AAAA,EACT;AAEA,SACE,MAEG,QAAQ,mCAAmC,SAAS,EAGpD;AAAA;AAAA,IAEC;AAAA,IACA;AAAA,EACF;AAQJ,WAAS,UAAU,MAAM,OAAOC,MAAK;AACnC,WAAO,QAAQ;AAAA,OACZ,KAAK,WAAW,CAAC,IAAI,SAAU,OAC9B,KAAK,WAAW,CAAC,IACjB,QACA;AAAA,MACFA,KAAI,WAAW,QAAQ,CAAC;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAOA,WAAS,MAAM,WAAW,OAAOA,MAAK;AACpC,WAAO,QAAQ;AAAA,MACb,UAAU,WAAW,CAAC;AAAA,MACtBA,KAAI,WAAW,QAAQ,CAAC;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACF;AAMA,SAAS,uBAAuB,QAAQ;AAEtC,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,OAAO,QAAQ;AAC9B,WAAO,KAAK,OAAO,KAAK,EAAE,QAAQ,uBAAuB,MAAM,CAAC;AAAA,EAClE;AAEA,SAAO,IAAI,OAAO,QAAQ,OAAO,KAAK,GAAG,IAAI,KAAK,GAAG;AACvD;AAUA,SAAS,cAAc,MAAM,MAAM,MAAM;AACvC,QAAM,QAAQ,QAAQ,KAAK,SAAS,EAAE,EAAE,YAAY;AACpD,SAAO,QAAQ,QAAQ,CAAC,aAAa,KAAK,OAAO,aAAa,IAAI,CAAC,IAC/D,QACA,QAAQ;AACd;AAUA,SAAS,UAAU,MAAM,MAAM,MAAM;AACnC,QAAM,QAAQ,OAAO,OAAO,IAAI;AAChC,SAAO,QAAQ,QAAQ,CAAC,KAAK,KAAK,OAAO,aAAa,IAAI,CAAC,IACvD,QACA,QAAQ;AACd;AAOA,IAAM,0BAA0B;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAOA,IAAM,yBAAyB;AAAA,EAC7B,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,KAAK;AAAA,EACL,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,MAAM;AAAA,EACN,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,SAAS;AAAA,EACT,MAAM;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,SAAS;AAAA,EACT,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,OAAO;AAAA,EACP,KAAK;AAAA,EACL,SAAS;AAAA,EACT,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,SAAS;AAAA,EACT,MAAM;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,SAAS;AAAA,EACT,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,KAAK;AAAA,EACL,SAAS;AAAA,EACT,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,UAAU;AAAA,EACV,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,KAAK;AAAA,EACL,KAAK;AAAA,EACL,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,MAAM;AAAA,EACN,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,KAAK;AAAA,EACL,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,KAAK;AAAA,EACL,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AACR;AAQA,IAAM,YAAY;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,QAAQ,CAAC,EAAE;AAOjB,IAAM,aAAa,CAAC;AAGpB,IAAI;AAEJ,KAAK,OAAO,wBAAwB;AAClC,MAAI,MAAM,KAAK,wBAAwB,GAAG,GAAG;AAC3C,eAAW,uBAAuB,GAAG,CAAC,IAAI;AAAA,EAC5C;AACF;AAWA,SAAS,QAAQ,MAAM,MAAM,MAAM,WAAW;AAC5C,QAAM,YAAY,OAAO,aAAa,IAAI;AAE1C,MAAI,MAAM,KAAK,YAAY,SAAS,GAAG;AACrC,UAAM,OAAO,WAAW,SAAS;AACjC,UAAM,QAAQ,MAAM;AAEpB,QACE,QACA,wBAAwB,SAAS,IAAI,KACrC,CAAC,UAAU,SAAS,IAAI,MACvB,CAAC,aACC,QACC,SAAS,MACT,YAAY,KAAK,OAAO,aAAa,IAAI,CAAC,IAC9C;AACA,aAAO;AAAA,IACT;AAEA,WAAO,QAAQ;AAAA,EACjB;AAEA,SAAO;AACT;AA4BA,SAAS,YAAY,MAAM,MAAM,SAAS;AACxC,MAAI,UAAU,cAAc,MAAM,MAAM,QAAQ,sBAAsB;AAEtE,MAAI;AAEJ,MAAI,QAAQ,sBAAsB,QAAQ,uBAAuB;AAC/D,YAAQ;AAAA,MACN;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,EACF;AAYA,OACG,QAAQ,yBAAyB,CAAC,UACnC,QAAQ,uBACR;AACA,UAAM,UAAU,UAAU,MAAM,MAAM,QAAQ,sBAAsB;AAEpE,QAAI,QAAQ,SAAS,QAAQ,QAAQ;AACnC,gBAAU;AAAA,IACZ;AAAA,EACF;AAEA,SAAO,UACJ,CAAC,QAAQ,yBAAyB,MAAM,SAAS,QAAQ,UACxD,QACA;AACN;AAkBA,SAAS,kBAAkB,OAAO,SAAS;AACzC,SAAO,KAAK,OAAO,OAAO,OAAO,EAAC,QAAQ,YAAW,GAAG,OAAO,CAAC;AAClE;AAwBA,SAAS,QAAQ,MAAM,IAAI,IAAI,OAAO;AAEpC,SAAO,MAAM,SAAS,gBAClB,OACE;AAAA,IACE,KAAK;AAAA,IACL,OAAO,OAAO,CAAC,GAAG,MAAM,SAAS,qBAAqB,EAAC,QAAQ,CAAC,GAAG,EAAC,CAAC;AAAA,EACvE,IACA,MACF,SAAS,KAAK,MAAM,QAAQ,8BAA8B,MAAM,IAAI;AAKxE,WAAS,OAAO,IAAI;AAClB,WAAO;AAAA,MACL;AAAA,MACA,OAAO,OAAO,CAAC,GAAG,MAAM,SAAS,qBAAqB;AAAA,QACpD,QAAQ,CAAC,KAAK,GAAG;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAwBA,SAAS,QAAQ,IAAI,IAAI,IAAI,OAAO;AAClC,SACE,QACC,MAAM,SAAS,eAAe,YAAY,cAC1C,MAAM,SAAS,eAAe,KAAK,OACpC;AAEJ;AAYA,SAAS,OAAO,OAAO,WAAW;AAChC,QAAM,SAAS,OAAO,KAAK;AAE3B,MAAI,OAAO,cAAc,UAAU;AACjC,UAAM,IAAI,UAAU,oBAAoB;AAAA,EAC1C;AAEA,MAAI,QAAQ;AACZ,MAAI,QAAQ,OAAO,QAAQ,SAAS;AAEpC,SAAO,UAAU,IAAI;AACnB;AACA,YAAQ,OAAO,QAAQ,WAAW,QAAQ,UAAU,MAAM;AAAA,EAC5D;AAEA,SAAO;AACT;AAsBA,SAAS,YAAY,QAAQ,SAAS;AACpC,QAAM,WAAW,WAAW,CAAC;AAG7B,QAAM,QAAQ,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,CAAC,GAAG,QAAQ,EAAE,IAAI;AAEnE,SAAO,MACJ;AAAA,KACE,SAAS,WAAW,MAAM,MACzB,OACC,SAAS,YAAY,QAAQ,KAAK;AAAA,EACvC,EACC,KAAK;AACV;AAmBA,SAAS,UAAU,QAAQ;AACzB,SAAO,OAAO,KAAK,GAAG,EAAE,KAAK;AAC/B;AAQA,IAAM,KAAK;AAaX,SAAS,WAAW,OAAO;AACzB,SAAO,OAAO,UAAU,WACpB,MAAM,SAAS,SACb,MAAM,MAAM,KAAK,IACjB,QACF,MAAM,KAAK;AACjB;AAMA,SAAS,MAAM,OAAO;AACpB,SAAO,MAAM,QAAQ,IAAI,EAAE,MAAM;AACnC;AAQA,IAAM,eAAe,SAAS,CAAC;AAC/B,IAAM,gBAAgB,SAAS,EAAE;AAGjC,IAAM,kBAAkB,CAAC;AAOzB,SAAS,SAASC,YAAW;AAC3B,SAAO;AAgBP,WAAS,QAAQ,QAAQ,OAAO,mBAAmB;AACjD,UAAMC,YAAW,SAAS,OAAO,WAAW;AAC5C,QAAI,UAAU,SAAS,KAAKD;AAC5B,QAAI,OAAOC,UAAS,MAAM;AAE1B,QAAI,CAAC,mBAAmB;AACtB,aAAO,QAAQ,WAAW,IAAI,GAAG;AAC/B,kBAAUD;AACV,eAAOC,UAAS,MAAM;AAAA,MACxB;AAAA,IACF;AAGA,WAAO;AAAA,EACT;AACF;AAqBA,IAAM,MAAM,CAAC,EAAE;AAWf,SAAS,SAAS,UAAU;AAC1B,SAAO;AAOP,WAAS,KAAK,MAAM,OAAO,QAAQ;AACjC,WACE,IAAI,KAAK,UAAU,KAAK,OAAO,KAC/B,SAAS,KAAK,OAAO,EAAE,MAAM,OAAO,MAAM;AAAA,EAE9C;AACF;AAQA,IAAM,UAAU,SAAS;AAAA,EACvB,MAAM;AAAA,EACN,SAAS;AAAA,EACT,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,IAAI;AAAA,EACJ;AAAA,EACA,IAAI;AAAA,EACJ;AAAA,EACA;AACF,CAAC;AAcD,SAAS,wBAAwB,GAAG,OAAO,QAAQ;AACjD,QAAM,OAAO,aAAa,QAAQ,OAAO,IAAI;AAC7C,SACE,CAAC,QACA,KAAK,SAAS,aACb,EAAE,KAAK,SAAS,UAAU,WAAW,KAAK,MAAM,OAAO,CAAC,CAAC;AAE/D;AAcA,SAAS,OAAO,GAAG,OAAO,QAAQ;AAChC,QAAM,OAAO,aAAa,QAAQ,KAAK;AACvC,SAAO,CAAC,QAAQ,KAAK,SAAS;AAChC;AAcA,SAAS,OAAO,GAAG,OAAO,QAAQ;AAChC,QAAM,OAAO,aAAa,QAAQ,KAAK;AACvC,SAAO,CAAC,QAAQ,KAAK,SAAS;AAChC;AAcA,SAAS,EAAE,GAAG,OAAO,QAAQ;AAC3B,QAAM,OAAO,aAAa,QAAQ,KAAK;AACvC,SAAO,OACH,KAAK,SAAS,cACX,KAAK,YAAY,aAChB,KAAK,YAAY,aACjB,KAAK,YAAY,WACjB,KAAK,YAAY,gBACjB,KAAK,YAAY,aACjB,KAAK,YAAY,SACjB,KAAK,YAAY,QACjB,KAAK,YAAY,cACjB,KAAK,YAAY,gBACjB,KAAK,YAAY,YACjB,KAAK,YAAY,YACjB,KAAK,YAAY,UACjB,KAAK,YAAY,QACjB,KAAK,YAAY,QACjB,KAAK,YAAY,QACjB,KAAK,YAAY,QACjB,KAAK,YAAY,QACjB,KAAK,YAAY,QACjB,KAAK,YAAY,YACjB,KAAK,YAAY,YACjB,KAAK,YAAY,QACjB,KAAK,YAAY,UACjB,KAAK,YAAY,UACjB,KAAK,YAAY,SACjB,KAAK,YAAY,QACjB,KAAK,YAAY,OACjB,KAAK,YAAY,SACjB,KAAK,YAAY,aACjB,KAAK,YAAY,WACjB,KAAK,YAAY,QACrB,CAAC;AAAA,EAEC,EACE,OAAO,SAAS,cACf,OAAO,YAAY,OAClB,OAAO,YAAY,WACnB,OAAO,YAAY,SACnB,OAAO,YAAY,SACnB,OAAO,YAAY,SACnB,OAAO,YAAY,cACnB,OAAO,YAAY;AAE/B;AAcA,SAAS,GAAG,GAAG,OAAO,QAAQ;AAC5B,QAAM,OAAO,aAAa,QAAQ,KAAK;AACvC,SAAO,CAAC,QAAS,KAAK,SAAS,aAAa,KAAK,YAAY;AAC/D;AAcA,SAAS,GAAG,GAAG,OAAO,QAAQ;AAC5B,QAAM,OAAO,aAAa,QAAQ,KAAK;AACvC,SAAO;AAAA,IACL,QACE,KAAK,SAAS,cACb,KAAK,YAAY,QAAQ,KAAK,YAAY;AAAA,EAC/C;AACF;AAcA,SAAS,GAAG,GAAG,OAAO,QAAQ;AAC5B,QAAM,OAAO,aAAa,QAAQ,KAAK;AACvC,SACE,CAAC,QACA,KAAK,SAAS,cACZ,KAAK,YAAY,QAAQ,KAAK,YAAY;AAEjD;AAcA,SAAS,YAAY,GAAG,OAAO,QAAQ;AACrC,QAAM,OAAO,aAAa,QAAQ,KAAK;AACvC,SACE,CAAC,QACA,KAAK,SAAS,cACZ,KAAK,YAAY,QAAQ,KAAK,YAAY;AAEjD;AAcA,SAAS,SAAS,GAAG,OAAO,QAAQ;AAClC,QAAM,OAAO,aAAa,QAAQ,KAAK;AACvC,SAAO,CAAC,QAAS,KAAK,SAAS,aAAa,KAAK,YAAY;AAC/D;AAcA,SAAS,OAAO,GAAG,OAAO,QAAQ;AAChC,QAAM,OAAO,aAAa,QAAQ,KAAK;AACvC,SACE,CAAC,QACA,KAAK,SAAS,cACZ,KAAK,YAAY,YAAY,KAAK,YAAY;AAErD;AAcA,SAAS,MAAM,GAAG,OAAO,QAAQ;AAC/B,QAAM,OAAO,aAAa,QAAQ,KAAK;AACvC,SAAO;AAAA,IACL,QACE,KAAK,SAAS,cACb,KAAK,YAAY,WAAW,KAAK,YAAY;AAAA,EAClD;AACF;AAcA,SAAS,QAAQ,GAAG,OAAO,QAAQ;AACjC,QAAM,OAAO,aAAa,QAAQ,KAAK;AACvC,SACE,CAAC,QACA,KAAK,SAAS,cACZ,KAAK,YAAY,WAAW,KAAK,YAAY;AAEpD;AAcA,SAAS,MAAM,GAAG,OAAO,QAAQ;AAC/B,SAAO,CAAC,aAAa,QAAQ,KAAK;AACpC;AAcA,SAAS,GAAG,GAAG,OAAO,QAAQ;AAC5B,QAAM,OAAO,aAAa,QAAQ,KAAK;AACvC,SAAO,CAAC,QAAS,KAAK,SAAS,aAAa,KAAK,YAAY;AAC/D;AAcA,SAAS,MAAM,GAAG,OAAO,QAAQ;AAC/B,QAAM,OAAO,aAAa,QAAQ,KAAK;AACvC,SACE,CAAC,QACA,KAAK,SAAS,cACZ,KAAK,YAAY,QAAQ,KAAK,YAAY;AAEjD;AAQA,IAAM,UAAU,SAAS;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAUD,SAAS,KAAK,MAAM;AAClB,QAAMC,QAAO,aAAa,MAAM,EAAE;AAClC,SAAO,CAACA,SAAQA,MAAK,SAAS;AAChC;AAUA,SAAS,KAAK,MAAM;AAClB,QAAM,WAAW,KAAK;AAEtB,QAAM,OAAO,CAAC;AACd,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,SAAS,QAAQ;AAChC,UAAM,QAAQ,SAAS,KAAK;AAC5B,QACE,MAAM,SAAS,cACd,MAAM,YAAY,WAAW,MAAM,YAAY,SAChD;AACA,UAAI,KAAK,SAAS,MAAM,OAAO;AAAG,eAAO;AACzC,WAAK,KAAK,MAAM,OAAO;AAAA,IACzB;AAAA,EACF;AAEA,SAAO,SAAS,SAAS;AAC3B;AAUA,SAAS,KAAK,MAAM;AAClB,QAAMA,QAAO,aAAa,MAAM,IAAI,IAAI;AAExC,SACE,CAACA,SACAA,MAAK,SAAS,aACb,EAAEA,MAAK,SAAS,UAAU,WAAWA,MAAK,MAAM,OAAO,CAAC,CAAC,MACzD,EACEA,MAAK,SAAS,cACbA,MAAK,YAAY,UAChBA,MAAK,YAAY,UACjBA,MAAK,YAAY,YACjBA,MAAK,YAAY,WACjBA,MAAK,YAAY;AAG3B;AAiBA,SAAS,SAAS,MAAM,OAAO,QAAQ;AACrC,QAAM,WAAW,cAAc,QAAQ,KAAK;AAC5C,QAAMA,QAAO,aAAa,MAAM,IAAI,IAAI;AAGxC,MACE,UACA,YACA,SAAS,SAAS,aAClB,SAAS,YAAY,cACrB,QAAQ,UAAU,OAAO,SAAS,QAAQ,QAAQ,GAAG,MAAM,GAC3D;AACA,WAAO;AAAA,EACT;AAEA,SAAO,QAAQA,SAAQA,MAAK,SAAS,aAAaA,MAAK,YAAY,KAAK;AAC1E;AAcA,SAAS,MAAM,MAAM,OAAO,QAAQ;AAClC,QAAM,WAAW,cAAc,QAAQ,KAAK;AAC5C,QAAMA,QAAO,aAAa,MAAM,EAAE;AAGlC,MACE,UACA,YACA,SAAS,SAAS,cACjB,SAAS,YAAY,WAAW,SAAS,YAAY,YACtD,QAAQ,UAAU,OAAO,SAAS,QAAQ,QAAQ,GAAG,MAAM,GAC3D;AACA,WAAO;AAAA,EACT;AAEA,SAAO,QAAQA,SAAQA,MAAK,SAAS,aAAaA,MAAK,YAAY,IAAI;AACzE;AAoBA,IAAM,YAAY;AAAA;AAAA,EAEhB,MAAM;AAAA,IACJ,CAAC,eAAgB,MAAM,EAAE,GAAG,mBAAoB,MAAM,EAAE,CAAC;AAAA,IACzD,CAAC;AAAA,cAAsB,MAAM,EAAE,GAAG,sBAAuB,MAAM,EAAE,CAAC;AAAA,EACpE;AAAA;AAAA,EAEA,UAAU;AAAA,IACR,CAAC,aAAc,MAAM,EAAE,GAAG,qBAAsB,MAAM,EAAE,CAAC;AAAA,IACzD,CAAC,qBAAsB,MAAM,EAAE,GAAG,qBAAsB,MAAM,EAAE,CAAC;AAAA,EACnE;AAAA;AAAA,EAEA,QAAQ;AAAA,IACN,CAAC,KAAK,MAAM,EAAE,GAAG,QAAQ,MAAM,EAAE,CAAC;AAAA,IAClC,CAAC,OAAO,MAAM,EAAE,GAAG,UAAU,MAAM,EAAE,CAAC;AAAA,EACxC;AAAA;AAAA,EAEA,QAAQ;AAAA,IACN,CAAC,KAAK,MAAM,EAAE,GAAG,QAAQ,MAAM,EAAE,CAAC;AAAA,IAClC,CAAC,OAAO,MAAM,EAAE,GAAG,UAAU,MAAM,EAAE,CAAC;AAAA,EACxC;AACF;AAgBA,SAAS,QAAQ,MAAM,OAAO,QAAQ,OAAO;AAC3C,QAAM,SAAS,MAAM;AACrB,QAAM,OAAO,OAAO,UAAU,QAAQ,QAAQ,MAAM,SAAS;AAC7D,MAAI,cACF,OAAO,UAAU,QACb,MAAM,SAAS,qBACf,MAAM,SAAS,MAAM,SAAS,KAAK,QAAQ,YAAY,CAAC;AAE9D,QAAM,QAAQ,CAAC;AAEf,MAAI;AAEJ,MAAI,OAAO,UAAU,UAAU,KAAK,YAAY,OAAO;AACrD,UAAM,SAAS;AAAA,EACjB;AAEA,QAAM,QAAQ,oBAAoB,OAAO,KAAK,UAAU;AAExD,QAAM,UAAU,MAAM;AAAA,IACpB,OAAO,UAAU,UAAU,KAAK,YAAY,aAAa,KAAK,UAAU;AAAA,EAC1E;AAEA,QAAM,SAAS;AAQf,MAAI;AAAS,kBAAc;AAE3B,MAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,MAAM,OAAO,MAAM,GAAG;AACnD,UAAM,KAAK,KAAK,KAAK,SAAS,QAAQ,MAAM,QAAQ,EAAE;AAEtD,QACE,gBACC,OAAO,UAAU,SAAS,MAAM,SAAS,mBAC1C;AACA,aAAO,MAAM,OAAO,MAAM,SAAS,CAAC;AACpC,UACE,CAAC,MAAM,SAAS,oBAChB,SAAS,OACR,QAAQ,SAAS,OAAO,SAAS,KAClC;AACA,cAAM,KAAK,GAAG;AAAA,MAChB;AAEA,YAAM,KAAK,GAAG;AAAA,IAChB;AAEA,UAAM,KAAK,GAAG;AAAA,EAChB;AAEA,QAAM,KAAK,OAAO;AAElB,MAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,MAAM,OAAO,MAAM,IAAI;AAC5D,UAAM,KAAK,OAAO,KAAK,UAAU,GAAG;AAAA,EACtC;AAEA,SAAO,MAAM,KAAK,EAAE;AACtB;AAOA,SAAS,oBAAoB,OAAO,OAAO;AAEzC,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAEZ,MAAIP;AAEJ,MAAI,OAAO;AACT,SAAKA,QAAO,OAAO;AACjB,UAAI,MAAMA,IAAG,MAAM,QAAQ,MAAMA,IAAG,MAAM,QAAW;AACnD,cAAM,QAAQ,mBAAmB,OAAOA,MAAK,MAAMA,IAAG,CAAC;AACvD,YAAI;AAAO,iBAAO,KAAK,KAAK;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAEA,SAAO,EAAE,QAAQ,OAAO,QAAQ;AAC9B,UAAM,OAAO,MAAM,SAAS,kBACxB,OAAO,KAAK,EAAE,OAAO,OAAO,KAAK,EAAE,SAAS,CAAC,IAC7C;AAGJ,QAAI,UAAU,OAAO,SAAS,KAAK,SAAS,OAAO,SAAS,KAAK;AAC/D,aAAO,KAAK,KAAK;AAAA,IACnB;AAAA,EACF;AAEA,SAAO,OAAO,KAAK,EAAE;AACvB;AAQA,SAAS,mBAAmB,OAAOA,MAAK,OAAO;AAC7C,QAAM,OAAO,KAAK,MAAM,QAAQA,IAAG;AACnC,QAAM,IACJ,MAAM,SAAS,oBAAoB,MAAM,OAAO,UAAU,SAAS,IAAI;AACzE,QAAM,IAAI,MAAM,SAAS,2BAA2B,IAAI;AACxD,MAAI,QAAQ,MAAM;AAElB,MAAI;AAEJ,MAAI,KAAK,sBAAsB,UAAU,KAAK,aAAa,UAAU,KAAK;AACxE,YAAQ;AAAA,EACV,WACE,KAAK,WACJ,KAAK,qBAAqB,OAAO,UAAU,UAC5C;AACA,YAAQ,QAAQ,KAAK;AAAA,EACvB;AAEA,MACE,UAAU,QACV,UAAU,UACV,UAAU,SACT,OAAO,UAAU,YAAY,OAAO,MAAM,KAAK,GAChD;AACA,WAAO;AAAA,EACT;AAEA,QAAM,OAAO;AAAA,IACX,KAAK;AAAA,IACL,OAAO,OAAO,CAAC,GAAG,MAAM,SAAS,qBAAqB;AAAA;AAAA,MAEpD,QAAQ,UAAU,KAAK,CAAC,EAAE,CAAC;AAAA,IAC7B,CAAC;AAAA,EACH;AAmBA,MAAI,UAAU;AAAM,WAAO;AAI3B,UAAQ,MAAM,QAAQ,KAAK,KACtB,KAAK,iBAAiB,cAAc,WAAW,OAAO;AAAA,IACrD,SAAS,CAAC,MAAM,SAAS;AAAA,EAC3B,CAAC,IACD,OAAO,KAAK;AAEhB,MAAI,MAAM,SAAS,2BAA2B,CAAC;AAAO,WAAO;AAG7D,MAAI,MAAM,SAAS,gBAAgB;AACjC,aAAS;AAAA,MACP;AAAA,MACA,OAAO,OAAO,CAAC,GAAG,MAAM,SAAS,qBAAqB;AAAA,QACpD,WAAW;AAAA,QACX,QAAQ,UAAU,SAAS,CAAC,EAAE,CAAC;AAAA,MACjC,CAAC;AAAA,IACH;AAAA,EACF;AAIA,MAAI,WAAW,OAAO;AAEpB,QACE,MAAM,SAAS,cACf,OAAO,OAAO,KAAK,IAAI,OAAO,OAAO,MAAM,WAAW,GACtD;AACA,cAAQ,MAAM;AAAA,IAChB;AAEA,aACE,QACA;AAAA,MACE;AAAA,MACA,OAAO,OAAO,CAAC,GAAG,MAAM,SAAS,qBAAqB;AAAA;AAAA,QAEpD,SAAS,UAAU,MAAM,UAAU,SAAS,UAAU,QAAQ,CAAC,EAAE,CAAC;AAAA,QAClE,WAAW;AAAA,MACb,CAAC;AAAA,IACH,IACA;AAAA,EACJ;AAGA,SAAO,QAAQ,SAAS,MAAM,SAAS;AACzC;AA0BA,SAAS,KAAK,MAAM,GAAG,QAAQ,OAAO;AAEpC,SAAO,UACL,OAAO,SAAS,cACf,OAAO,YAAY,YAAY,OAAO,YAAY,WACjD,KAAK,QACL;AAAA,IACE,KAAK;AAAA,IACL,OAAO,OAAO,CAAC,GAAG,MAAM,SAAS,qBAAqB;AAAA,MACpD,QAAQ,CAAC,KAAK,GAAG;AAAA,IACnB,CAAC;AAAA,EACH;AACN;AAyBA,SAAS,IAAI,MAAM,OAAO,QAAQ,OAAO;AACvC,SAAO,MAAM,SAAS,qBAClB,KAAK,QACL,KAAK,MAAM,OAAO,QAAQ,KAAK;AACrC;AAwBA,SAAS,KAAK,MAAM,IAAI,IAAI,OAAO;AACjC,SAAO,MAAM,IAAI,IAAI;AACvB;AAaA,IAAM,SAAS,OAAO,QAAQ;AAAA,EAC5B;AAAA,EACA;AAAA,EACA,UAAU,EAAC,SAAS,SAAS,SAAS,KAAK,MAAM,KAAI;AACvD,CAAC;AAUD,SAAS,QAAQ,MAAM;AACrB,QAAM,IAAI,MAAM,yBAAyB,OAAO,GAAG;AACrD;AAUA,SAAS,QAAQ,OAAO;AAEtB,QAAM;AAAA;AAAA,IAA6B;AAAA;AACnC,QAAM,IAAI,MAAM,kCAAkC,KAAK,OAAO,GAAG;AACnE;AAcA,IAAM,eAAe,CAAC;AAGtB,IAAM,2BAA2B,CAAC;AAGlC,IAAM,gBAAgB,CAAC;AAYvB,SAAS,OAAO,MAAM,SAAS;AAC7B,QAAM,WAAW,WAAW;AAC5B,QAAM,QAAQ,SAAS,SAAS;AAChC,QAAM,cAAc,UAAU,MAAM,MAAM;AAE1C,MAAI,UAAU,OAAO,UAAU,KAAK;AAClC,UAAM,IAAI,MAAM,oBAAoB,QAAQ,yBAAyB;AAAA,EACvE;AAGA,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA,UAAU;AAAA,MACR,kBAAkB,SAAS,oBAAoB;AAAA,MAC/C,kBAAkB,SAAS,oBAAoB;AAAA,MAC/C,0BAA0B,SAAS,4BAA4B;AAAA,MAC/D,YAAY,SAAS,cAAc;AAAA,MACnC,gBAAgB,SAAS,kBAAkB;AAAA,MAC3C,iBAAiB,SAAS,mBAAmB;AAAA,MAC7C,cAAc,SAAS,gBAAgB;AAAA,MACvC,cAAc,SAAS,gBAAgB;AAAA,MACvC,eAAe,SAAS,iBAAiB;AAAA,MACzC,0BAA0B,SAAS,4BAA4B;AAAA,MAC/D,kBAAkB,SAAS,oBAAoB;AAAA,MAC/C,yBAAyB,SAAS,2BAA2B;AAAA,MAC7D,oBAAoB,SAAS,sBAAsB;AAAA,MACnD,OAAO,SAAS,SAAS;AAAA,MACzB,qBACE,SAAS,uBAAuB;AAAA,MAClC,kBAAkB,SAAS,oBAAoB;AAAA,MAC/C,oBAAoB,SAAS,sBAAsB;AAAA,IACrD;AAAA,IACA,QAAQ,SAAS,UAAU,QAAQ,MAAM;AAAA,IACzC;AAAA,IACA;AAAA,EACF;AAEA,SAAO,MAAM;AAAA,IACX,MAAM,QAAQ,IAAI,IAAI,EAAC,MAAM,QAAQ,UAAU,KAAI,IAAI;AAAA,IACvD;AAAA,IACA;AAAA,EACF;AACF;AAgBA,SAAS,IAAI,MAAM,OAAO,QAAQ;AAChC,SAAO,OAAO,MAAM,OAAO,QAAQ,IAAI;AACzC;AAWA,SAAS,IAAI,QAAQ;AAEnB,QAAM,UAAU,CAAC;AACjB,QAAM,WAAY,UAAU,OAAO,YAAa;AAChD,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,SAAS,QAAQ;AAChC,YAAQ,KAAK,IAAI,KAAK,IAAI,SAAS,KAAK,GAAG,OAAO,MAAM;AAAA,EAC1D;AAEA,SAAO,QAAQ,KAAK,EAAE;AACxB;AAKA,SAAS,WAAW,UAAU,MAAM,SAAS;AAlqI7C;AAmqII,QAAM,UAAU;AAAA,IACZ,MAAM,CAAC;AAAA,IACP;AAAA,IACA,YAAY,CAAC,OAAO,aAAa,WAAW,UAAU,OAAO,QAAQ;AAAA,EACzE;AACA,MAAI,SAAS,OAAO,WAAW,UAAU,MAAM,SAAS,OAAO,CAAC;AAChE,aAAW,eAAe,QAAQ,gBAAgB,CAAC;AAC/C,eAAS,iBAAY,gBAAZ,mBAAyB,KAAK,SAAS,QAAQ,aAAY;AACxE,SAAO;AACX;AAEA,eAAe,KAAK,MAAM;AACtB,MAAI;AACJ,MAAI;AACJ,QAAM,UAAU,CAAC;AACjB,WAAS,2BAA2B,KAAK;AACrC,aAAS;AACT,YAAQ,SAAS,IAAI,WAAW,GAAG;AACnC,YAAQ,UAAU,IAAI,YAAY,GAAG;AAAA,EACzC;AACA,WAAS,sBAAsB;AAC3B,WAAO,OAAO,gBAAgB,cAAc,YAAY,IAAI,IAAI,KAAK,IAAI;AAAA,EAC7E;AACA,WAAS,uBAAuB,MAAM,KAAK,KAAK;AAC5C,YAAQ,OAAO,WAAW,MAAM,KAAK,MAAM,GAAG;AAAA,EAClD;AACA,WAAS,aAAa;AAClB,WAAO;AAAA,EACX;AACA,WAAS,0BAA0B,MAAM;AACrC,QAAI;AACA,iBAAW,KAAM,OAAO,OAAO,aAAa,UAAW,EAAE;AACzD,iCAA2B,WAAW,MAAM;AAC5C,aAAO;AAAA,IACX,SACO,GAAG;AAAA,IAAE;AAAA,EAChB;AACA,WAAS,wBAAwB,eAAe;AAC5C,UAAM,UAAU,QAAQ,OAAO;AAC/B,oBAAgB,kBAAkB;AAClC,UAAM,cAAc,WAAW;AAC/B,QAAI,gBAAgB;AAChB,aAAO;AACX,UAAM,UAAU,CAAC,GAAG,aAAa,KAAM,WAAY,IAAI,YAAa;AACpE,aAAS,UAAU,GAAG,WAAW,GAAG,WAAW,GAAG;AAC9C,UAAI,oBAAoB,WAAW,IAAI,MAAM;AAC7C,0BAAoB,KAAK,IAAI,mBAAmB,gBAAgB,SAAS;AACzE,YAAM,UAAU,KAAK,IAAI,aAAa,QAAQ,KAAK,IAAI,eAAe,iBAAiB,GAAG,KAAK,CAAC;AAChG,YAAM,cAAc,0BAA0B,OAAO;AACrD,UAAI;AACA,eAAO;AAAA,IACf;AACA,WAAO;AAAA,EACX;AACA,QAAM,gBAAgB;AAAA,IAClB,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,wBAAwB;AAAA,IACxB,UAAU,MAAM;AAAA,EACpB;AACA,iBAAe,aAAa;AACxB,UAAM,OAAO;AAAA,MACT,KAAK;AAAA,MACL,wBAAwB;AAAA,IAC5B;AACA,UAAM,UAAU,MAAM,KAAK,IAAI;AAC/B,iBAAa,QAAQ;AACrB,+BAA2B,WAAW,MAAM;AAC5C,WAAO,OAAO,SAAS,OAAO;AAAA,EAClC;AACA,QAAM,WAAW;AACjB,SAAO;AACX;AAKA,IAAI,cAAc;AAClB,IAAI,mBAAmB;AACvB,SAAS,mBAAmBQ,cAAa;AACrC,QAAM,IAAI,MAAMA,aAAY,aAAaA,aAAY,iBAAiB,CAAC,CAAC;AAC5E;AACA,IAAM,YAAN,MAAM,WAAU;AAAA,EAqCZ,YAAY,KAAK;AANjB;AACA;AACA;AACA;AACA;AACA;AAEI,UAAM,cAAc,IAAI;AACxB,UAAM,aAAa,WAAU,gBAAgB,GAAG;AAChD,UAAM,wBAAyB,eAAe;AAC9C,UAAM,oBAAoB,wBAAwB,IAAI,YAAY,cAAc,CAAC,IAAI;AACrF,QAAI;AACA,wBAAkB,WAAW,IAAI;AACrC,UAAM,oBAAoB,wBAAwB,IAAI,YAAY,aAAa,CAAC,IAAI;AACpF,QAAI;AACA,wBAAkB,UAAU,IAAI;AACpC,UAAM,YAAY,IAAI,WAAW,UAAU;AAC3C,QAAI,KAAK;AACT,aAAS,MAAM,GAAG,MAAM,aAAa,OAAO;AACxC,YAAM,WAAW,IAAI,WAAW,GAAG;AACnC,UAAI,YAAY;AAChB,UAAI,mBAAmB;AACvB,UAAI,YAAY,SAAU,YAAY,OAAQ;AAE1C,YAAI,MAAM,IAAI,aAAa;AACvB,gBAAM,eAAe,IAAI,WAAW,MAAM,CAAC;AAC3C,cAAI,gBAAgB,SAAU,gBAAgB,OAAQ;AAElD,yBAAe,WAAW,SAAW,MAAM,QAAY,eAAe;AACtE,+BAAmB;AAAA,UACvB;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,uBAAuB;AACvB,0BAAkB,GAAG,IAAI;AACzB,YAAI;AACA,4BAAkB,MAAM,CAAC,IAAI;AACjC,YAAI,aAAa,KAAM;AACnB,4BAAkB,KAAK,CAAC,IAAI;AAAA,QAChC,WACS,aAAa,MAAO;AACzB,4BAAkB,KAAK,CAAC,IAAI;AAC5B,4BAAkB,KAAK,CAAC,IAAI;AAAA,QAChC,WACS,aAAa,OAAQ;AAC1B,4BAAkB,KAAK,CAAC,IAAI;AAC5B,4BAAkB,KAAK,CAAC,IAAI;AAC5B,4BAAkB,KAAK,CAAC,IAAI;AAAA,QAChC,OACK;AACD,4BAAkB,KAAK,CAAC,IAAI;AAC5B,4BAAkB,KAAK,CAAC,IAAI;AAC5B,4BAAkB,KAAK,CAAC,IAAI;AAC5B,4BAAkB,KAAK,CAAC,IAAI;AAAA,QAChC;AAAA,MACJ;AACA,UAAI,aAAa,KAAM;AACnB,kBAAU,IAAI,IAAI;AAAA,MACtB,WACS,aAAa,MAAO;AACzB,kBAAU,IAAI,IAAI,OAAe,YAAY,UAAwC;AACrF,kBAAU,IAAI,IAAI,OAAe,YAAY,QAAwC;AAAA,MACzF,WACS,aAAa,OAAQ;AAC1B,kBAAU,IAAI,IAAI,OAAe,YAAY,WAAwC;AACrF,kBAAU,IAAI,IAAI,OAAe,YAAY,UAAwC;AACrF,kBAAU,IAAI,IAAI,OAAe,YAAY,QAAwC;AAAA,MACzF,OACK;AACD,kBAAU,IAAI,IAAI,OAAe,YAAY,aAAwC;AACrF,kBAAU,IAAI,IAAI,OAAe,YAAY,YAAwC;AACrF,kBAAU,IAAI,IAAI,OAAe,YAAY,UAAwC;AACrF,kBAAU,IAAI,IAAI,OAAe,YAAY,QAAwC;AAAA,MACzF;AACA,UAAI;AACA;AAAA,IACR;AACA,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,oBAAoB;AACzB,SAAK,oBAAoB;AAAA,EAC7B;AAAA,EAjHA,OAAO,gBAAgB,KAAK;AACxB,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC5C,YAAM,WAAW,IAAI,WAAW,CAAC;AACjC,UAAI,YAAY;AAChB,UAAI,mBAAmB;AACvB,UAAI,YAAY,SAAU,YAAY,OAAQ;AAE1C,YAAI,IAAI,IAAI,KAAK;AACb,gBAAM,eAAe,IAAI,WAAW,IAAI,CAAC;AACzC,cAAI,gBAAgB,SAAU,gBAAgB,OAAQ;AAElD,yBAAe,WAAW,SAAW,MAAM,QAAY,eAAe;AACtE,+BAAmB;AAAA,UACvB;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,aAAa;AACb,kBAAU;AAAA,eACL,aAAa;AAClB,kBAAU;AAAA,eACL,aAAa;AAClB,kBAAU;AAAA;AAEV,kBAAU;AACd,UAAI;AACA;AAAA,IACR;AACA,WAAO;AAAA,EACX;AAAA,EAqFA,aAAaA,cAAa;AACtB,UAAM,SAASA,aAAY,QAAQ,KAAK,UAAU;AAClD,IAAAA,aAAY,OAAO,IAAI,KAAK,WAAW,MAAM;AAC7C,WAAO;AAAA,EACX;AACJ;AACA,IAAM,cAAN,MAAM,YAAW;AAAA,EAYb,YAAY,KAAK;AARjB,8BAAM,EAAE,YAAW;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AAEI,QAAI,CAAC;AACD,YAAM,IAAI,MAAM,6BAA6B;AACjD,SAAK,eAAe;AACpB,SAAK,UAAU;AACf,UAAM,YAAY,IAAI,UAAU,GAAG;AACnC,SAAK,cAAc,UAAU;AAC7B,SAAK,aAAa,UAAU;AAC5B,SAAK,oBAAoB,UAAU;AACnC,SAAK,oBAAoB,UAAU;AACnC,QAAI,KAAK,aAAa,OAAS,CAAC,YAAW,iBAAiB;AACxD,UAAI,CAAC,YAAW;AACZ,oBAAW,aAAa,YAAY,QAAQ,GAAK;AACrD,kBAAW,kBAAkB;AAC7B,kBAAY,OAAO,IAAI,UAAU,WAAW,YAAW,UAAU;AACjE,WAAK,MAAM,YAAW;AAAA,IAC1B,OACK;AACD,WAAK,MAAM,UAAU,aAAa,WAAW;AAAA,IACjD;AAAA,EACJ;AAAA,EACA,yBAAyB,YAAY;AACjC,QAAI,KAAK,mBAAmB;AACxB,UAAI,aAAa;AACb,eAAO;AACX,UAAI,aAAa,KAAK;AAClB,eAAO,KAAK;AAChB,aAAO,KAAK,kBAAkB,UAAU;AAAA,IAC5C;AACA,WAAO;AAAA,EACX;AAAA,EACA,yBAAyB,aAAa;AAClC,QAAI,KAAK,mBAAmB;AACxB,UAAI,cAAc;AACd,eAAO;AACX,UAAI,cAAc,KAAK;AACnB,eAAO,KAAK;AAChB,aAAO,KAAK,kBAAkB,WAAW;AAAA,IAC7C;AACA,WAAO;AAAA,EACX;AAAA,EACA,UAAU;AACN,QAAI,KAAK,QAAQ,YAAW;AACxB,kBAAW,kBAAkB;AAAA;AAE7B,WAAK,aAAa,MAAM,KAAK,GAAG;AAAA,EACxC;AACJ;AA1DI,cADE,aACK,WAAU;AACjB,cAFE,aAEK,cAAa;AAAA;AACpB,cAHE,aAGK,mBAAkB;AAH7B,IAAM,aAAN;AA4DA,IAAM,cAAN,MAAkB;AAAA,EAGd,YAAY,UAAU;AAFtB;AACA;AAEI,QAAI,CAAC;AACD,YAAM,IAAI,MAAM,6BAA6B;AACjD,UAAM,aAAa,CAAC;AACpB,UAAM,YAAY,CAAC;AACnB,aAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,IAAI,KAAK,KAAK;AACjD,YAAM,YAAY,IAAI,UAAU,SAAS,CAAC,CAAC;AAC3C,iBAAW,CAAC,IAAI,UAAU,aAAa,WAAW;AAClD,gBAAU,CAAC,IAAI,UAAU;AAAA,IAC7B;AACA,UAAM,aAAa,YAAY,QAAQ,IAAI,SAAS,MAAM;AAC1D,gBAAY,QAAQ,IAAI,YAAY,aAAa,CAAC;AAClD,UAAM,YAAY,YAAY,QAAQ,IAAI,SAAS,MAAM;AACzD,gBAAY,QAAQ,IAAI,WAAW,YAAY,CAAC;AAChD,UAAM,aAAa,YAAY,kBAAkB,YAAY,WAAW,SAAS,MAAM;AACvF,aAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,IAAI,KAAK;AAC5C,kBAAY,MAAM,WAAW,CAAC,CAAC;AACnC,gBAAY,MAAM,SAAS;AAC3B,gBAAY,MAAM,UAAU;AAC5B,QAAI,eAAe;AACf,yBAAmB,WAAW;AAClC,SAAK,eAAe;AACpB,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,UAAU;AACN,SAAK,aAAa,gBAAgB,KAAK,IAAI;AAAA,EAC/C;AAAA,EACA,kBAAkB,QAAQ,eAAe,KAAK;AAC1C,QAAI,YAAY;AAChB,QAAI,UAAU;AACd,QAAI,OAAO,QAAQ,UAAU;AACzB,UAAI,MAAM;AACN,oBAAY;AAChB,gBAAU;AAAA,IACd,WACS,OAAO,QAAQ,WAAW;AAC/B,kBAAY;AAAA,IAChB;AACA,QAAI,OAAO,WAAW,UAAU;AAC5B,eAAS,IAAI,WAAW,MAAM;AAC9B,YAAM,SAAS,KAAK,mBAAmB,QAAQ,eAAe,WAAW,OAAO;AAChF,aAAO,QAAQ;AACf,aAAO;AAAA,IACX;AACA,WAAO,KAAK,mBAAmB,QAAQ,eAAe,WAAW,OAAO;AAAA,EAC5E;AAAA,EACA,mBAAmB,QAAQ,eAAe,WAAW,SAAS;AAC1D,UAAMA,eAAc,KAAK;AACzB,QAAI;AACJ,QAAI;AACA,kBAAYA,aAAY,4BAA4B,KAAK,MAAM,OAAO,IAAI,OAAO,KAAK,OAAO,YAAY,OAAO,yBAAyB,aAAa,GAAG,OAAO;AAAA;AAEhK,kBAAYA,aAAY,yBAAyB,KAAK,MAAM,OAAO,IAAI,OAAO,KAAK,OAAO,YAAY,OAAO,yBAAyB,aAAa,GAAG,OAAO;AACjK,QAAI,cAAc,GAAG;AAEjB,aAAO;AAAA,IACX;AACA,UAAM,UAAUA,aAAY;AAC5B,QAAI,SAAS,YAAY;AACzB,UAAM,QAAQ,QAAQ,QAAQ;AAC9B,UAAM,QAAQ,QAAQ,QAAQ;AAC9B,UAAM,iBAAiB,CAAC;AACxB,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC5B,YAAM,MAAM,OAAO,yBAAyB,QAAQ,QAAQ,CAAC;AAC7D,YAAM,MAAM,OAAO,yBAAyB,QAAQ,QAAQ,CAAC;AAC7D,qBAAe,CAAC,IAAI;AAAA,QAChB,OAAO;AAAA,QACP;AAAA,QACA,QAAQ,MAAM;AAAA,MAClB;AAAA,IACJ;AACA,WAAO;AAAA,MACH;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,4BAA4B,eAAe;AAChD,SAAQ,OAAO,cAAc,iBAAiB;AAClD;AACA,SAAS,qBAAqB,eAAe;AACzC,SAAQ,OAAO,cAAc,YAAY;AAC7C;AACA,SAAS,oBAAoB,eAAe;AACxC,SAAQ,OAAO,cAAc,SAAS;AAC1C;AACA,SAAS,WAAW,eAAe;AAC/B,SAAQ,OAAO,aAAa,eAAe,yBAAyB;AACxE;AACA,SAAS,cAAc,MAAM;AACzB,SAAQ,OAAO,gBAAgB,gBAAgB,gBAAgB,eAAe,YAAY,OAAO,IAAI,MAE7F,OAAO,WAAW,eAAe,OAAO,SAAS,IAAI,KACrD,OAAO,sBAAsB,eAAe,gBAAgB,qBAC5D,OAAO,gBAAgB,eAAe,gBAAgB;AAClE;AACA,IAAI;AACJ,SAAS,SAAS,SAAS;AACvB,MAAI;AACA,WAAO;AACX,iBAAe,QAAQ;AACnB,kBAAc,MAAM,KAAK,OAAO,SAAS;AACrC,UAAI,WAAW;AACf,iBAAW,MAAM;AACjB,UAAI,OAAO,aAAa;AACpB,mBAAW,MAAM,SAAS,IAAI;AAClC,UAAI,OAAO,aAAa;AACpB,mBAAW,MAAM,SAAS,IAAI;AAClC,UAAI,4BAA4B,QAAQ,GAAG;AACvC,mBAAW,MAAM,SAAS,aAAa,IAAI;AAAA,MAC/C,WACS,qBAAqB,QAAQ,GAAG;AACrC,mBAAW,MAAM,SAAS,QAAQ,IAAI;AAAA,MAC1C,OACK;AACD,YAAI,oBAAoB,QAAQ;AAC5B,qBAAW,SAAS;AACxB,YAAI,WAAW,QAAQ,GAAG;AACtB,cAAI,OAAO,YAAY,yBAAyB;AAC5C,uBAAW,MAAM,6BAA6B,QAAQ,EAAE,IAAI;AAAA;AAE5D,uBAAW,MAAM,gCAAgC,QAAQ,EAAE,IAAI;AAAA,QACvE,WACS,cAAc,QAAQ,GAAG;AAC9B,qBAAW,MAAM,uBAAuB,QAAQ,EAAE,IAAI;AAAA,QAC1D;AAAA,MACJ;AACA,UAAI,cAAc;AACd,mBAAW,SAAS;AACxB,UAAI,aAAa;AACb,mBAAW,SAAS;AACxB,aAAO;AAAA,IACX,CAAC;AAAA,EACL;AACA,gBAAc,MAAM;AACpB,SAAO;AACX;AACA,SAAS,uBAAuB,MAAM;AAClC,SAAO,kBAAgB,YAAY,YAAY,MAAM,YAAY;AACrE;AACA,SAAS,6BAA6B,MAAM;AACxC,SAAO,kBAAgB,YAAY,qBAAqB,MAAM,YAAY;AAC9E;AACA,SAAS,gCAAgC,MAAM;AAC3C,SAAO,OAAO,iBAAiB;AAC3B,UAAM,cAAc,MAAM,KAAK,YAAY;AAC3C,WAAO,YAAY,YAAY,aAAa,YAAY;AAAA,EAC5D;AACJ;AACA,SAAS,iBAAiB,KAAK;AAC3B,SAAO,IAAI,WAAW,GAAG;AAC7B;AACA,SAAS,kBAAkB,UAAU;AACjC,SAAO,IAAI,YAAY,QAAQ;AACnC;AAKA,IAAM,4BAA4B,EAAE,OAAO,WAAW,MAAM,UAAU;AACtE,IAAM,4BAA4B,EAAE,OAAO,WAAW,MAAM,UAAU;AACtE,IAAM,eAAe;AAIrB,SAAS,eAAe,UAAU;AAllJlC;AAolJI,MAAI,qCAAW;AACX,WAAO;AACX,QAAM,QAAQ;AAAA,IACV,GAAG;AAAA,EACP;AAEA,MAAI,MAAM,eAAe,CAAC,MAAM,UAAU;AACtC,UAAM,WAAW,MAAM;AACvB,WAAO,MAAM;AAAA,EACjB;AACA,QAAM,SAAN,MAAM,OAAS;AACf,QAAM,oBAAoB,EAAE,GAAG,MAAM,kBAAkB;AACvD,QAAM,aAAN,MAAM,WAAa,CAAC;AAEpB,MAAI,EAAE,IAAI,GAAG,IAAI;AACjB,MAAI,CAAC,MAAM,CAAC,IAAI;AAMZ,UAAM,gBAAgB,MAAM,WACtB,MAAM,SAAS,KAAK,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,KAAK,IAC9C;AACN,SAAI,oDAAe,aAAf,mBAAyB;AACzB,WAAK,cAAc,SAAS;AAChC,SAAI,oDAAe,aAAf,mBAAyB;AACzB,WAAK,cAAc,SAAS;AAMhC,QAAI,CAAC,QAAM,oCAAO,WAAP,mBAAgB;AACvB,WAAK,MAAM,OAAO,mBAAmB;AACzC,QAAI,CAAC,QAAM,oCAAO,WAAP,mBAAgB;AACvB,WAAK,MAAM,OAAO,mBAAmB;AAKzC,QAAI,CAAC;AACD,WAAK,MAAM,SAAS,UAAU,0BAA0B,QAAQ,0BAA0B;AAC9F,QAAI,CAAC;AACD,WAAK,MAAM,SAAS,UAAU,0BAA0B,QAAQ,0BAA0B;AAC9F,UAAM,KAAK;AACX,UAAM,KAAK;AAAA,EACf;AAEA,MAAI,EAAE,MAAM,SAAS,CAAC,KAAK,MAAM,SAAS,CAAC,EAAE,YAAY,CAAC,MAAM,SAAS,CAAC,EAAE,QAAQ;AAChF,UAAM,SAAS,QAAQ;AAAA,MACnB,UAAU;AAAA,QACN,YAAY,MAAM;AAAA,QAClB,YAAY,MAAM;AAAA,MACtB;AAAA,IACJ,CAAC;AAAA,EACL;AAEA,MAAI,mBAAmB;AACvB,QAAM,iBAAiB,oBAAI,IAAI;AAC/B,WAAS,oBAAoB,OAAO;AAhpJxC,QAAAP;AAipJQ,QAAI,eAAe,IAAI,KAAK;AACxB,aAAO,eAAe,IAAI,KAAK;AACnC,wBAAoB;AACpB,UAAM,MAAM,IAAI,iBAAiB,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,EAAE,YAAY,CAAC;AAC5E,SAAIA,MAAA,MAAM,sBAAN,gBAAAA,IAA0B,IAAI,GAAG;AACjC,aAAO,oBAAoB,KAAK;AACpC,mBAAe,IAAI,OAAO,GAAG;AAC7B,WAAO;AAAA,EACX;AACA,QAAM,WAAW,MAAM,SAAS,IAAI,CAAC,YAAY;AA1pJrD,QAAAA,KAAAC;AA2pJQ,UAAM,cAAYD,MAAA,QAAQ,aAAR,gBAAAA,IAAkB,eAAc,CAAC,QAAQ,SAAS,WAAW,WAAW,GAAG;AAC7F,UAAM,cAAYC,MAAA,QAAQ,aAAR,gBAAAA,IAAkB,eAAc,CAAC,QAAQ,SAAS,WAAW,WAAW,GAAG;AAC7F,QAAI,CAAC,aAAa,CAAC;AACf,aAAO;AACX,UAAMO,SAAQ;AAAA,MACV,GAAG;AAAA,MACH,UAAU;AAAA,QACN,GAAG,QAAQ;AAAA,MACf;AAAA,IACJ;AACA,QAAI,WAAW;AACX,YAAM,cAAc,oBAAoB,QAAQ,SAAS,UAAU;AACnE,YAAM,kBAAkB,WAAW,IAAI,QAAQ,SAAS;AACxD,MAAAA,OAAM,SAAS,aAAa;AAAA,IAChC;AACA,QAAI,WAAW;AACX,YAAM,cAAc,oBAAoB,QAAQ,SAAS,UAAU;AACnE,YAAM,kBAAkB,WAAW,IAAI,QAAQ,SAAS;AACxD,MAAAA,OAAM,SAAS,aAAa;AAAA,IAChC;AACA,WAAOA;AAAA,EACX,CAAC;AACD,aAAWT,QAAO,OAAO,KAAK,MAAM,UAAU,CAAC,CAAC,GAAG;AAE/C,QAAIA,SAAQ,uBAAuBA,SAAQ,uBAAuBA,KAAI,WAAW,eAAe,GAAG;AAC/F,UAAI,GAAC,WAAM,OAAOA,IAAG,MAAhB,mBAAmB,WAAW,OAAM;AACrC,cAAM,cAAc,oBAAoB,MAAM,OAAOA,IAAG,CAAC;AACzD,cAAM,kBAAkB,WAAW,IAAI,MAAM,OAAOA,IAAG;AACvD,cAAM,OAAOA,IAAG,IAAI;AAAA,MACxB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,eAAe,OAAO,cAAc;AAAA,IACvC,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,OAAO;AAAA,EACX,CAAC;AACD,SAAO;AACX;AAEA,IAAMU,YAAN,cAAuB,WAAW;AAAA,EAS9B,YAAY,WAAW,SAAS,QAAQ;AACpC,UAAM,SAAS;AATnB;AACA;AACA;AACA,2CAAkB,CAAC;AACnB,6CAAoB,CAAC;AACrB,oCAAW,CAAC;AACZ,sCAAa,oBAAI,IAAI;AACrB,iCAAQ,CAAC;AAGL,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,SAAS;AACd,YAAQ,QAAQ,OAAK,KAAK,UAAU,CAAC,CAAC;AACtC,WAAO,QAAQ,OAAK,KAAK,aAAa,CAAC,CAAC;AAAA,EAC5C;AAAA,EACA,SAAS,OAAO;AACZ,QAAI,OAAO,UAAU;AACjB,aAAO,KAAK,gBAAgB,KAAK;AAAA;AAEjC,aAAO,KAAK,UAAU,KAAK;AAAA,EACnC;AAAA,EACA,UAAU,OAAO;AACb,UAAM,SAAS,eAAe,KAAK;AACnC,QAAI,OAAO;AACP,WAAK,gBAAgB,OAAO,IAAI,IAAI;AACxC,WAAO;AAAA,EACX;AAAA,EACA,kBAAkB;AACd,WAAO,OAAO,KAAK,KAAK,eAAe;AAAA,EAC3C;AAAA,EACA,WAAW,MAAM;AACb,QAAI,KAAK,MAAM,IAAI,GAAG;AAClB,YAAM,WAAW,oBAAI,IAAI,CAAC,IAAI,CAAC;AAC/B,aAAO,KAAK,MAAM,IAAI,GAAG;AACrB,eAAO,KAAK,MAAM,IAAI;AACtB,YAAI,SAAS,IAAI,IAAI;AACjB,gBAAM,IAAI,MAAM,8BAA8B,MAAM,KAAK,QAAQ,EAAE,KAAK,MAAM,CAAC,OAAO,IAAI,IAAI;AAClG,iBAAS,IAAI,IAAI;AAAA,MACrB;AAAA,IACJ;AACA,WAAO,KAAK,kBAAkB,IAAI;AAAA,EACtC;AAAA,EACA,MAAM,aAAa,MAAM;AA/uJ7B;AAgvJQ,QAAI,KAAK,WAAW,KAAK,IAAI;AACzB;AACJ,UAAM,mBAAmB,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,OAAO,OAAE;AAlvJ/E,UAAAT;AAkvJkF,cAAAA,MAAA,EAAE,sBAAF,gBAAAA,IAAqB,SAAS,KAAK;AAAA,KAAK,CAAC;AACnH,SAAK,UAAU,YAAY,IAAI;AAC/B,UAAM,gBAAgB;AAAA,MAClB,0BAA0B,KAAK,4BAA4B,CAAC,GAAG;AAAA,MAC/D,4BAA4B,KAAK,8BAA8B,CAAC;AAAA,IACpE;AAEA,SAAK,cAAc,aAAa,IAAI,KAAK,WAAW,IAAI;AACxD,UAAM,IAAI,MAAM,KAAK,6BAA6B,KAAK,WAAW,GAAG,aAAa;AAClF,SAAK,kBAAkB,KAAK,IAAI,IAAI;AACpC,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,QAAQ,CAAC,UAAU;AAC5B,aAAK,MAAM,KAAK,IAAI,KAAK;AAAA,MAC7B,CAAC;AAAA,IACL;AAEA,QAAI,iBAAiB,MAAM;AACvB,iBAAW,KAAK,kBAAkB;AAC9B,eAAO,KAAK,kBAAkB,EAAE,IAAI;AAEpC,yBAAK,kBAAL,mBAAoB,uBAApB,mBAAwC,OAAO,EAAE;AAEjD,yBAAK,kBAAL,mBAAoB,cAApB,mBAA+B,OAAO,EAAE;AACxC,cAAM,KAAK,aAAa,KAAK,SAAS,EAAE,IAAI,CAAC;AAAA,MACjD;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,MAAM,OAAO;AACT,SAAK,QAAQ,IAAI,OAAK,KAAK,UAAU,CAAC,CAAC;AACvC,UAAM,KAAK,cAAc,KAAK,MAAM;AAAA,EACxC;AAAA,EACA,MAAM,cAAc,OAAO;AACvB,eAAW,QAAQ;AACf,WAAK,yBAAyB,IAAI;AACtC,UAAM,kBAAkB,MAAM,KAAK,KAAK,WAAW,QAAQ,CAAC;AAC5D,UAAM,eAAe,gBAAgB,OAAO,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC,IAAI;AAChE,QAAI,aAAa,QAAQ;AACrB,YAAM,aAAa,gBACd,OAAO,CAAC,CAAC,GAAG,IAAI,MAAG;AAxxJpC;AAwxJuC,yBAAQ,UAAK,kBAAL,mBAAoB,KAAK,OAAK,aAAa,IAAI,CAAC,CAAC,IAAI,MAAM,IAAI,EAAE,SAAS,CAAC;AAAA,OAAE,EAC3G,OAAO,UAAQ,CAAC,aAAa,SAAS,IAAI,CAAC;AAChD,YAAM,IAAI,MAAM,+BAA+B,aAAa,IAAI,CAAC,CAAC,IAAI,MAAM,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,CAAC,iBAAiB,WAAW,IAAI,CAAC,CAAC,IAAI,MAAM,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE;AAAA,IAChL;AACA,eAAW,CAAC,GAAG,IAAI,KAAK;AACpB,WAAK,UAAU,YAAY,IAAI;AACnC,eAAW,CAAC,GAAG,IAAI,KAAK;AACpB,YAAM,KAAK,aAAa,IAAI;AAAA,EACpC;AAAA,EACA,qBAAqB;AACjB,WAAO,OAAO,KAAK,EAAE,GAAG,KAAK,mBAAmB,GAAG,KAAK,MAAM,CAAC;AAAA,EACnE;AAAA,EACA,yBAAyB,MAAM;AAC3B,SAAK,SAAS,KAAK,IAAI,IAAI;AAC3B,SAAK,WAAW,IAAI,KAAK,MAAM,IAAI;AACnC,QAAI,KAAK,eAAe;AACpB,iBAAW,gBAAgB,KAAK;AAC5B,aAAK,WAAW,IAAI,cAAc,KAAK,SAAS,YAAY,CAAC;AAAA,IACrE;AAAA,EACJ;AACJ;AAEA,IAAM,WAAN,MAAe;AAAA,EAKX,YAAY,gBAAgB,OAAO;AAJnC,kCAAS,oBAAI,IAAI;AACjB,wCAAe,oBAAI,IAAI;AACvB,uCAAc,oBAAI,IAAI;AACtB;AAEI,SAAK,kBAAkB;AACvB,UAAM,QAAQ,OAAK,KAAK,YAAY,CAAC,CAAC;AAAA,EAC1C;AAAA,EACA,IAAI,UAAU;AACV,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,oBAAoB,eAAe;AAC/B,WAAO,KAAK,OAAO,IAAI,aAAa;AAAA,EACxC;AAAA,EACA,MAAM,YAAY,WAAW;AACzB,WAAO,KAAK,aAAa,IAAI,SAAS;AAAA,EAC1C;AAAA,EACA,YAAY,GAAG;AACX,SAAK,OAAO,IAAI,EAAE,MAAM,CAAC;AACzB,QAAI,EAAE,SAAS;AACX,QAAE,QAAQ,QAAQ,CAAC,MAAM;AACrB,aAAK,OAAO,IAAI,GAAG,CAAC;AAAA,MACxB,CAAC;AAAA,IACL;AACA,SAAK,aAAa,IAAI,EAAE,WAAW,CAAC;AACpC,QAAI,EAAE,UAAU;AACZ,QAAE,SAAS,QAAQ,CAAC,MAAM;AACtB,YAAI,CAAC,KAAK,YAAY,IAAI,CAAC;AACvB,eAAK,YAAY,IAAI,GAAG,CAAC,CAAC;AAC9B,aAAK,YAAY,IAAI,CAAC,EAAE,KAAK,EAAE,SAAS;AAAA,MAC5C,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,cAAc,WAAW;AACrB,UAAM,aAAa,UAAU,MAAM,GAAG;AACtC,QAAI,aAAa,CAAC;AAClB,aAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,KAAK;AACzC,YAAM,eAAe,WAAW,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG;AACpD,mBAAa,CAAC,GAAG,YAAY,GAAI,KAAK,YAAY,IAAI,YAAY,KAAK,CAAC,CAAE;AAAA,IAC9E;AACA,WAAO;AAAA,EACX;AACJ;AAEA,IAAI;AAKJ,SAAS,qBAAqB,SAAS;AACnC,uBAAqB;AACzB;AAIA,eAAe,iBAAiB,UAAU,CAAC,GAAG;AAC1C,iBAAe,gBAAgBU,IAAG;AAC9B,WAAO,QAAQ,QAAQ,OAAOA,OAAM,aAAaA,GAAE,IAAIA,EAAC,EAAE,KAAK,OAAK,EAAE,WAAW,CAAC;AAAA,EACtF;AACA,iBAAe,aAAaC,QAAO;AAC/B,WAAO,MAAM,KAAK,IAAI,KAAK,MAAM,QAAQ,IAAIA,OAAM,IAAI,OAAO,SAAS,MAAM,gBAAgB,IAAI,EAAE,KAAK,OAAK,MAAM,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,EACvJ;AACA,QAAM,aAAa,QAAQ,YAAY;AACvC,QAAM,CAAC,QAAQ,KAAM,IAAI,MAAM,QAAQ,IAAI;AAAA,IACvC,QAAQ,KAAK,QAAQ,UAAU,CAAC,GAAG,IAAI,eAAe,CAAC,EAAE,KAAK,OAAK,EAAE,IAAI,cAAc,CAAC;AAAA,IACxF,aAAa,QAAQ,SAAS,CAAC,CAAC;AAAA,IAChC,aAAa,SAAS,UAAU,IAAI;AAAA,EACxC,CAAC;AACD,QAAM,WAAW,IAAI,SAAS,QAAQ,QAAQ;AAAA,IAC1C,kBAAkB,UAAU;AACxB,aAAO,kBAAkB,QAAQ;AAAA,IACrC;AAAA,IACA,iBAAiB,GAAG;AAChB,aAAO,iBAAiB,CAAC;AAAA,IAC7B;AAAA,EACJ,CAAC,GAAG,KAAK;AACT,QAAM,YAAY,IAAIF,UAAS,UAAU,QAAQ,KAAK;AACtD,SAAO,OAAO,UAAU,OAAO,QAAQ,SAAS;AAChD,QAAM,UAAU,KAAK;AACrB,MAAI;AACJ,WAAS,eAAe,MAAM;AAC1B,UAAM,QAAQ,UAAU,WAAW,IAAI;AACvC,QAAI,CAAC;AACD,YAAM,IAAI,MAAM,wBAAwB,IAAI,6CAA6C;AAC7F,WAAO;AAAA,EACX;AACA,WAAS,SAAS,MAAM;AACpB,QAAI,SAAS;AACT,aAAO,EAAE,IAAI,IAAI,IAAI,IAAI,MAAM,QAAQ,UAAU,CAAC,GAAG,MAAM,OAAO;AACtE,UAAM,SAAS,UAAU,SAAS,IAAI;AACtC,QAAI,CAAC;AACD,YAAM,IAAI,MAAM,qBAAqB,IAAI,6CAA6C;AAC1F,WAAO;AAAA,EACX;AACA,WAAS,SAAS,MAAM;AACpB,UAAM,QAAQ,SAAS,IAAI;AAC3B,QAAI,eAAe,MAAM;AACrB,gBAAU,SAAS,KAAK;AACxB,mBAAa;AAAA,IACjB;AACA,UAAM,WAAW,UAAU,YAAY;AACvC,WAAO;AAAA,MACH;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,kBAAkB;AACvB,WAAO,UAAU,gBAAgB;AAAA,EACrC;AACA,WAAS,qBAAqB;AAC1B,WAAO,UAAU,mBAAmB;AAAA,EACxC;AACA,iBAAe,gBAAgBE,QAAO;AAClC,UAAM,UAAU,cAAc,MAAM,aAAaA,MAAK,CAAC;AAAA,EAC3D;AACA,iBAAe,aAAaC,SAAQ;AAChC,UAAM,QAAQ,IAAIA,QAAO,IAAI,OAAO,UAAU,eAAe,KAAK,IAC5D,OACA,UAAU,UAAU,MAAM,gBAAgB,KAAK,CAAC,CAAC,CAAC;AAAA,EAC5D;AACA,WAAS,YAAY,OAAO;AACxB,WAAO,OAAO,UAAU,OAAO,KAAK;AAAA,EACxC;AACA,WAAS,WAAW;AAChB,WAAO,UAAU;AAAA,EACrB;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAQA,eAAe,mBAAmB,UAAU,CAAC,GAAG;AAC5C,QAAM,WAAW,MAAM,iBAAiB,OAAO;AAC/C,SAAO;AAAA,IACH,oBAAoB,CAAC,MAAMC,aAAY,mBAAmB,UAAU,MAAMA,QAAO;AAAA,IACjF,wBAAwB,CAAC,MAAMA,aAAY,uBAAuB,UAAU,MAAMA,QAAO;AAAA,IACzF,YAAY,CAAC,MAAMA,aAAY,WAAW,UAAU,MAAMA,QAAO;AAAA,IACjE,YAAY,CAAC,MAAMA,aAAY,WAAW,UAAU,MAAMA,QAAO;AAAA,IACjE,cAAc,SAAS;AAAA,IACvB,WAAW,SAAS;AAAA,IACpB,UAAU,SAAS;AAAA,IACnB,gBAAgB,SAAS;AAAA,IACzB,UAAU,SAAS;AAAA,IACnB,iBAAiB,SAAS;AAAA,IAC1B,oBAAoB,SAAS;AAAA,IAC7B,oBAAoB,MAAM;AAAA,EAC9B;AACJ;AASA,SAAS,0BAA0BC,mBAAkBC,gBAAeC,WAAU;AAC1E,iBAAeC,gBAAe,UAAU,CAAC,GAAG;AACxC,aAAS,YAAY,MAAM;AACvB,UAAI,OAAO,SAAS,UAAU;AAC1B,YAAI,cAAc,IAAI;AAClB,iBAAO,CAAC;AACZ,cAAM,SAASH,kBAAiB,IAAI;AACpC,YAAI,CAAC;AACD,gBAAM,IAAI,MAAM,wBAAwB,IAAI,qBAAqB;AACrE,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AACA,aAAS,aAAa,OAAO;AACzB,UAAI,eAAe,KAAK;AACpB,eAAO;AACX,UAAI,OAAO,UAAU,UAAU;AAC3B,cAAM,SAASC,eAAc,KAAK;AAClC,YAAI,CAAC;AACD,gBAAM,IAAI,MAAM,qBAAqB,KAAK,qBAAqB;AACnE,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AACA,UAAM,WAAW,QAAQ,UAAU,CAAC,GAAG,IAAI,OAAK,aAAa,CAAC,CAAC;AAC/D,UAAM,SAAS,QAAQ,SAAS,CAAC,GAC5B,IAAI,OAAK,YAAY,CAAC,CAAC;AAC5B,UAAMG,QAAO,MAAM,mBAAmB;AAAA,MAClC,GAAG;AAAA,MACH,QAAQ;AAAA,MACR;AAAA,MACA,UAAAF;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,MACH,GAAGE;AAAA,MACH,gBAAgBP,QAAO;AACnB,eAAOO,MAAK,aAAa,GAAGP,OAAM,IAAI,WAAW,CAAC;AAAA,MACtD;AAAA,MACA,aAAa,QAAQ;AACjB,eAAOO,MAAK,UAAU,GAAG,OAAO,IAAI,YAAY,CAAC;AAAA,MACrD;AAAA,IACJ;AAAA,EACJ;AACA,SAAOD;AACX;AACA,SAAS,0BAA0BA,iBAAgB;AAC/C,MAAI;AACJ,iBAAe,gBAAgB,UAAU,CAAC,GAAG;AACzC,QAAI,CAAC,QAAQ;AACT,eAASA,gBAAe;AAAA,QACpB,QAAQ,QAAQ,QAAQ,SAAS,CAAC,CAAC;AAAA,QACnC,OAAO,QAAQ,QAAQ,QAAQ,CAAC,CAAC;AAAA,MACrC,CAAC;AACD,aAAO;AAAA,IACX,OACK;AACD,YAAM,IAAI,MAAM;AAChB,YAAM,QAAQ,IAAI;AAAA,QACd,EAAE,UAAU,GAAG,QAAQ,QAAQ,SAAS,CAAC,CAAC,CAAC;AAAA,QAC3C,EAAE,aAAa,GAAG,QAAQ,QAAQ,QAAQ,CAAC,CAAC,CAAC;AAAA,MACjD,CAAC;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AAAA,IACH,yBAAyB,MAAM,gBAAgB;AAAA,IAC/C,MAAM,WAAW,MAAM,SAAS;AAC5B,YAAM,QAAQ,MAAM,gBAAgB;AAAA,QAChC,MAAM,QAAQ;AAAA,QACd,OAAQ,WAAW,UAAU,CAAC,QAAQ,KAAK,IAAI,OAAO,OAAO,QAAQ,MAAM;AAAA,MAC/E,CAAC;AACD,aAAO,MAAM,WAAW,MAAM,OAAO;AAAA,IACzC;AAAA,IACA,MAAM,WAAW,MAAM,SAAS;AAC5B,YAAM,QAAQ,MAAM,gBAAgB;AAAA,QAChC,MAAM,QAAQ;AAAA,QACd,OAAQ,WAAW,UAAU,CAAC,QAAQ,KAAK,IAAI,OAAO,OAAO,QAAQ,MAAM;AAAA,MAC/E,CAAC;AACD,aAAO,MAAM,WAAW,MAAM,OAAO;AAAA,IACzC;AAAA,IACA,MAAM,mBAAmB,MAAM,SAAS;AACpC,YAAM,QAAQ,MAAM,gBAAgB,OAAO;AAC3C,aAAO,MAAM,mBAAmB,MAAM,OAAO;AAAA,IACjD;AAAA,IACA,MAAM,uBAAuB,MAAM,SAAS;AACxC,YAAM,QAAQ,MAAM,gBAAgB;AAAA,QAChC,MAAM,QAAQ;AAAA,QACd,OAAO,OAAO,OAAO,QAAQ,MAAM,EAAE,OAAO,OAAO;AAAA,MACvD,CAAC;AACD,aAAO,MAAM,uBAAuB,MAAM,OAAO;AAAA,IACrD;AAAA,EACJ;AACJ;;;AC5iKA,IAAM,iBAAiC;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM;AAAA,EACJ,YAAAE;AAAA,EACA,YAAAC;AAAA,EACA,oBAAAC;AAAA,EACA,wBAAAC;AAAA,EACA;AACF,IAAoB;AAAA,EAClB;AACF;;;ACtBA,SAAS,wBAAwB,UAAU,CAAC,GAAG;AAA/C;AACE,QAAM;AAAA,IACJ,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,WAAW,CAAC,UAAU;AAN9B,QAAAC;AAOI,SAAIA,MAAA,QAAQ,qBAAR,gBAAAA,IAA2B;AAC7B,aAAO,OAAO,cAAc,GAAG,KAAK,KAAK,QAAQ,iBAAiB,KAAK,CAAC;AAC1E,WAAO,OAAO,cAAc,GAAG,KAAK;AAAA,EACtC;AACA,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA,MAAM;AAAA,IACN,QAAQ;AAAA,MACN,qBAAqB,SAAS,YAAY;AAAA,MAC1C,qBAAqB,SAAS,YAAY;AAAA,MAC1C,sBAAsB,SAAS,YAAY;AAAA,MAC3C,oBAAoB,SAAS,UAAU;AAAA,MACvC,sBAAsB,SAAS,YAAY;AAAA,MAC3C,uBAAuB,SAAS,aAAa;AAAA,MAC7C,qBAAqB,SAAS,WAAW;AAAA,MACzC,wBAAwB,SAAS,cAAc;AAAA,MAC/C,qBAAqB,SAAS,WAAW;AAAA,MACzC,sBAAsB,SAAS,YAAY;AAAA,MAC3C,4BAA4B,SAAS,mBAAmB;AAAA,MACxD,0BAA0B,SAAS,iBAAiB;AAAA,MACpD,4BAA4B,SAAS,mBAAmB;AAAA,MACxD,6BAA6B,SAAS,oBAAoB;AAAA,MAC1D,2BAA2B,SAAS,kBAAkB;AAAA,MACtD,8BAA8B,SAAS,qBAAqB;AAAA,MAC5D,2BAA2B,SAAS,kBAAkB;AAAA,MACtD,4BAA4B,SAAS,mBAAmB;AAAA,IAC1D;AAAA,IACA,aAAa;AAAA,MACX;AAAA,QACE,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,YAAY,SAAS,YAAY;AAAA,QACnC;AAAA,MACF;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,UAAU;AAAA,UACR,WAAW;AAAA,QACb;AAAA,MACF;AAAA,MACA;AAAA,QACE,OAAO,CAAC,UAAU,2BAA2B,sBAAsB;AAAA,QACnE,UAAU;AAAA,UACR,WAAW;AAAA,QACb;AAAA,MACF;AAAA,MACA;AAAA,QACE,OAAO,CAAC,wBAAwB;AAAA,QAChC,UAAU;AAAA,UACR,WAAW;AAAA,QACb;AAAA,MACF;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,UAAU;AAAA,UACR,WAAW;AAAA,UACX,YAAY,SAAS,YAAY;AAAA,QACnC;AAAA,MACF;AAAA,MACA;AAAA,QACE,OAAO,CAAC,UAAU,sBAAsB,eAAe;AAAA,QACvD,UAAU;AAAA,UACR,YAAY,SAAS,cAAc;AAAA,QACrC;AAAA,MACF;AAAA,MACA;AAAA,QACE,OAAO,CAAC,WAAW,+BAA+B;AAAA,QAClD,UAAU;AAAA,UACR,YAAY,SAAS,eAAe;AAAA,QACtC;AAAA,MACF;AAAA,MACA;AAAA,QACE,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,YAAY,SAAS,gBAAgB;AAAA,QACvC;AAAA,MACF;AAAA,MACA;AAAA,QACE,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,YAAY,SAAS,eAAe;AAAA,QACtC;AAAA,MACF;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,UAAU;AAAA,UACR,YAAY,SAAS,iBAAiB;AAAA,QACxC;AAAA,MACF;AAAA,MACA;AAAA,QACE,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,YAAY,SAAS,gBAAgB;AAAA,QACvC;AAAA,MACF;AAAA,MACA;AAAA,QACE,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,YAAY,SAAS,yBAAyB;AAAA,QAChD;AAAA,MACF;AAAA,MACA;AAAA,QACE,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,YAAY,SAAS,mBAAmB;AAAA,QAC1C;AAAA,MACF;AAAA,MACA;AAAA;AAAA,QAEE,OAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,YAAY,SAAS,YAAY;AAAA,QACnC;AAAA,MACF;AAAA,MACA;AAAA;AAAA,QAEE,OAAO,CAAC,gDAAgD;AAAA,QACxD,UAAU;AAAA,UACR,YAAY,SAAS,cAAc;AAAA,QACrC;AAAA,MACF;AAAA,MACA;AAAA;AAAA,QAEE,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,YAAY,SAAS,eAAe;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,WAAW;AACd,UAAM,eAAc,WAAM,gBAAN,mBAAmB,IAAI,CAAC,eAAe;AApM/D,UAAAA;AAqMM,WAAIA,MAAA,WAAW,aAAX,gBAAAA,IAAqB;AACvB,eAAO,WAAW,SAAS;AAC7B,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;", "names": ["FontStyle", "EncodedTokenAttributes", "containsBalancedBrackets", "matcher", "key", "root", "head", "scopeName", "scopes", "text", "scope", "i", "key", "_a", "_b", "one", "all", "increment", "siblings", "head", "on<PERSON><PERSON><PERSON>ing", "clone", "Registry", "p", "langs", "themes", "options", "bundledLanguages", "bundledThemes", "loadWasm", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "core", "codeToHtml", "codeToHast", "codeToThemedTokens", "codeToTokensWithThemes", "_a"]}