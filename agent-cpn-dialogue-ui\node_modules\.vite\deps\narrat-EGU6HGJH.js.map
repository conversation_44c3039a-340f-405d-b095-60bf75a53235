{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/narrat.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Narrat Language\", \"name\": \"narrat\", \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#expression\" }], \"repository\": { \"commands\": { \"patterns\": [{ \"match\": \"\\\\b(set|var)\\\\b\", \"name\": \"keyword.commands.variables.narrat\" }, { \"match\": \"\\\\b(talk|think)\\\\b\", \"name\": \"keyword.commands.text.narrat\" }, { \"match\": \"\\\\b(jump|run|wait|return|save|save_prompt)\", \"name\": \"keyword.commands.flow.narrat\" }, { \"match\": \"\\\\b(log|clear_dialog)\\\\b\", \"name\": \"keyword.commands.helpers.narrat\" }, { \"match\": \"\\\\b(set_screen|empty_layer|set_button)\", \"name\": \"keyword.commands.screens.narrat\" }, { \"match\": \"\\\\b(play|pause|stop)\\\\b\", \"name\": \"keyword.commands.audio.narrat\" }, { \"match\": \"\\\\b(notify|enable_notifications|disable_notifications)\\\\b\", \"name\": \"keyword.commands.notifications.narrat\" }, { \"match\": \"\\\\b(set_stat|get_stat_value|add_stat)\", \"name\": \"keyword.commands.stats.narrat\" }, { \"match\": \"\\\\b(neg|abs|random|random_float|random_from_args|min|max|clamp|floor|round|ceil|sqrt|^)\\\\b\", \"name\": \"keyword.commands.math.narrat\" }, { \"match\": \"\\\\b(concat|join)\\\\b\", \"name\": \"keyword.commands.string.narrat\" }, { \"match\": \"\\\\b(text_field)\\\\b\", \"name\": \"keyword.commands.text_field.narrat\" }, { \"match\": \"\\\\b(add_level|set_level|add_xp|roll|get_level|get_xp)\\\\b\", \"name\": \"keyword.commands.skills.narrat\" }, { \"match\": \"\\\\b(add_item|remove_item|enable_interaction|disable_interaction|has_item?|item_amount?)\", \"name\": \"keyword.commands.inventory.narrat\" }, { \"match\": \"\\\\b(start_quest|start_objective|complete_objective|complete_quest|quest_started?|objective_started?|quest_completed?|objective_completed?)\", \"name\": \"keyword.commands.quests.narrat\" }] }, \"comments\": { \"patterns\": [{ \"match\": \"\\\\/\\\\/.*$\", \"name\": \"comment.line.narrat\" }] }, \"expression\": { \"patterns\": [{ \"include\": \"#keywords\" }, { \"include\": \"#commands\" }, { \"include\": \"#operators\" }, { \"include\": \"#primitives\" }, { \"include\": \"#strings\" }, { \"include\": \"#paren-expression\" }] }, \"interpolation\": { \"patterns\": [{ \"match\": \"(\\\\w|\\\\.)+\", \"name\": \"variable.interpolation.narrat\" }] }, \"keywords\": { \"patterns\": [{ \"match\": \"\\\\b(if|else|choice)\\\\b\", \"name\": \"keyword.control.narrat\" }, { \"match\": \"\\\\$[\\\\w|\\\\.]+\\\\b\", \"name\": \"variable.value.narrat\" }, { \"match\": \"(?x)\\n^\\\\w+\\n(?=(\\\\s|\\\\w)*:)\\n\", \"name\": \"entity.name.function.narrat\" }, { \"match\": \"(?x)\\n^\\\\w+\\n(?!(\\\\s|\\\\w)*:)\\n\", \"name\": \"invalid.label.narrat\" }, { \"match\": \"(?<=\\\\w)[^^](\\\\b\\\\w+\\\\b)(?=(\\\\s|\\\\w)*:)\", \"name\": \"entity.other.attribute-name\" }] }, \"operators\": { \"patterns\": [{ \"match\": \"(&&|\\\\|\\\\||!=|==|>=|<=|<|>|!|\\\\?)\\\\s\", \"name\": \"keyword.operator.logic.narrat\" }, { \"match\": \"(\\\\+|-|\\\\*|\\\\/)\\\\s\", \"name\": \"keyword.operator.arithmetic.narrat\" }] }, \"paren-expression\": { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.paren.open\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.paren.close\" } }, \"name\": \"expression.group\", \"patterns\": [{ \"include\": \"#expression\" }] }, \"primitives\": { \"patterns\": [{ \"match\": \"\\\\b\\\\d+\\\\b\", \"name\": \"constant.numeric.narrat\" }, { \"match\": \"\\\\btrue\\\\b\", \"name\": \"constant.language.true.narrat\" }, { \"match\": \"\\\\bfalse\\\\b\", \"name\": \"constant.language.false.narrat\" }, { \"match\": \"\\\\bnull\\\\b\", \"name\": \"constant.language.null.narrat\" }, { \"match\": \"\\\\bundefined\\\\b\", \"name\": \"constant.language.undefined.narrat\" }] }, \"strings\": { \"begin\": '\"', \"end\": '\"', \"name\": \"string.quoted.double.narrat\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.narrat\" }, { \"begin\": \"%{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.template.open\" } }, \"end\": \"}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.template.close.narrat\" } }, \"name\": \"expression.template\", \"patterns\": [{ \"include\": \"#expression\" }, { \"include\": \"#interpolation\" }] }] } }, \"scopeName\": \"source.narrat\", \"aliases\": [\"nar\"] });\nvar narrat = [\n  lang\n];\n\nexport { narrat as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,mBAAmB,QAAQ,UAAU,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,cAAc,CAAC,GAAG,cAAc,EAAE,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,mBAAmB,QAAQ,oCAAoC,GAAG,EAAE,SAAS,sBAAsB,QAAQ,+BAA+B,GAAG,EAAE,SAAS,8CAA8C,QAAQ,+BAA+B,GAAG,EAAE,SAAS,4BAA4B,QAAQ,kCAAkC,GAAG,EAAE,SAAS,0CAA0C,QAAQ,kCAAkC,GAAG,EAAE,SAAS,2BAA2B,QAAQ,gCAAgC,GAAG,EAAE,SAAS,6DAA6D,QAAQ,wCAAwC,GAAG,EAAE,SAAS,yCAAyC,QAAQ,gCAAgC,GAAG,EAAE,SAAS,8FAA8F,QAAQ,+BAA+B,GAAG,EAAE,SAAS,uBAAuB,QAAQ,iCAAiC,GAAG,EAAE,SAAS,sBAAsB,QAAQ,qCAAqC,GAAG,EAAE,SAAS,4DAA4D,QAAQ,iCAAiC,GAAG,EAAE,SAAS,2FAA2F,QAAQ,oCAAoC,GAAG,EAAE,SAAS,8IAA8I,QAAQ,iCAAiC,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,aAAa,QAAQ,sBAAsB,CAAC,EAAE,GAAG,cAAc,EAAE,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,oBAAoB,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,SAAS,cAAc,QAAQ,gCAAgC,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,0BAA0B,QAAQ,yBAAyB,GAAG,EAAE,SAAS,oBAAoB,QAAQ,wBAAwB,GAAG,EAAE,SAAS,kCAAkC,QAAQ,8BAA8B,GAAG,EAAE,SAAS,kCAAkC,QAAQ,uBAAuB,GAAG,EAAE,SAAS,2CAA2C,QAAQ,8BAA8B,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,SAAS,wCAAwC,QAAQ,gCAAgC,GAAG,EAAE,SAAS,sBAAsB,QAAQ,qCAAqC,CAAC,EAAE,GAAG,oBAAoB,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yBAAyB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,QAAQ,oBAAoB,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,cAAc,EAAE,YAAY,CAAC,EAAE,SAAS,cAAc,QAAQ,0BAA0B,GAAG,EAAE,SAAS,cAAc,QAAQ,gCAAgC,GAAG,EAAE,SAAS,eAAe,QAAQ,iCAAiC,GAAG,EAAE,SAAS,cAAc,QAAQ,gCAAgC,GAAG,EAAE,SAAS,mBAAmB,QAAQ,qCAAqC,CAAC,EAAE,GAAG,WAAW,EAAE,SAAS,KAAK,OAAO,KAAK,QAAQ,+BAA+B,YAAY,CAAC,EAAE,SAAS,SAAS,QAAQ,mCAAmC,GAAG,EAAE,SAAS,MAAM,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,aAAa,iBAAiB,WAAW,CAAC,KAAK,EAAE,CAAC;AAC/wH,IAAI,SAAS;AAAA,EACX;AACF;", "names": []}