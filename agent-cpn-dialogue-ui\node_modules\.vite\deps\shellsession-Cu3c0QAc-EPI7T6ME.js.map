{"version": 3, "sources": ["../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/shiki/node_modules/@shikijs/langs/dist/shellsession.mjs"], "sourcesContent": ["import shellscript from './shellscript.mjs'\n\nconst lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Shell Session\\\",\\\"fileTypes\\\":[\\\"sh-session\\\"],\\\"name\\\":\\\"shellsession\\\",\\\"patterns\\\":[{\\\"captures\\\":{\\\"1\\\":{\\\"name\\\":\\\"entity.other.prompt-prefix.shell-session\\\"},\\\"2\\\":{\\\"name\\\":\\\"punctuation.separator.prompt.shell-session\\\"},\\\"3\\\":{\\\"name\\\":\\\"source.shell\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"source.shell\\\"}]}},\\\"match\\\":\\\"^(?:((?:\\\\\\\\(\\\\\\\\S+\\\\\\\\)\\\\\\\\s*)?(?:sh\\\\\\\\S*?|\\\\\\\\w+\\\\\\\\S+[:@]\\\\\\\\S+(?:\\\\\\\\s+\\\\\\\\S+)?|\\\\\\\\[\\\\\\\\S+?[:@]\\\\\\\\N+?].*?))\\\\\\\\s*)?([#$%>❯➜\\\\\\\\p{Greek}])\\\\\\\\s+(.*)$\\\"},{\\\"match\\\":\\\"^.+$\\\",\\\"name\\\":\\\"meta.output.shell-session\\\"}],\\\"scopeName\\\":\\\"text.shell-session\\\",\\\"embeddedLangs\\\":[\\\"shellscript\\\"],\\\"aliases\\\":[\\\"console\\\"]}\"))\n\nexport default [\n...shellscript,\nlang\n]\n"], "mappings": ";;;;;;AAEA,IAAM,OAAO,OAAO,OAAO,KAAK,MAAM,qlBAAqpB,CAAC;AAE5rB,IAAA,iBAAe;EACf,GAAG;EACH;AACA;;;;;;;;;", "names": []}