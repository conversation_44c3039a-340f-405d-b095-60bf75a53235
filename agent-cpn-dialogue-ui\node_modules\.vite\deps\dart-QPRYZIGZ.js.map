{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/dart.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Dart\", \"name\": \"dart\", \"patterns\": [{ \"match\": \"^(#!.*)$\", \"name\": \"meta.preprocessor.script.dart\" }, { \"begin\": \"^\\\\w*\\\\b(library|import|part of|part|export)\\\\b\", \"beginCaptures\": { \"0\": { \"name\": \"keyword.other.import.dart\" } }, \"end\": \";\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.terminator.dart\" } }, \"name\": \"meta.declaration.dart\", \"patterns\": [{ \"include\": \"#strings\" }, { \"include\": \"#comments\" }, { \"match\": \"\\\\b(as|show|hide)\\\\b\", \"name\": \"keyword.other.import.dart\" }, { \"match\": \"\\\\b(if)\\\\b\", \"name\": \"keyword.control.dart\" }] }, { \"include\": \"#comments\" }, { \"include\": \"#punctuation\" }, { \"include\": \"#annotations\" }, { \"include\": \"#keywords\" }, { \"include\": \"#constants-and-special-vars\" }, { \"include\": \"#operators\" }, { \"include\": \"#strings\" }], \"repository\": { \"annotations\": { \"patterns\": [{ \"match\": \"@[a-zA-Z]+\", \"name\": \"storage.type.annotation.dart\" }] }, \"class-identifier\": { \"patterns\": [{ \"match\": \"(?<!\\\\$)\\\\b(bool|num|int|double|dynamic)\\\\b(?!\\\\$)\", \"name\": \"support.class.dart\" }, { \"match\": \"(?<!\\\\$)\\\\bvoid\\\\b(?!\\\\$)\", \"name\": \"storage.type.primitive.dart\" }, { \"begin\": \"(?<![a-zA-Z0-9_$])([_$]*[A-Z][a-zA-Z0-9_$]*)\\\\b\", \"beginCaptures\": { \"1\": { \"name\": \"support.class.dart\" } }, \"end\": \"(?!<)\", \"patterns\": [{ \"include\": \"#type-args\" }] }] }, \"comments\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"punctuation.definition.comment.dart\" } }, \"match\": \"/\\\\*\\\\*/\", \"name\": \"comment.block.empty.dart\" }, { \"include\": \"#comments-doc-oldschool\" }, { \"include\": \"#comments-doc\" }, { \"include\": \"#comments-inline\" }] }, \"comments-block\": { \"patterns\": [{ \"begin\": \"/\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.block.dart\", \"patterns\": [{ \"include\": \"#comments-block\" }] }] }, \"comments-doc\": { \"patterns\": [{ \"begin\": \"///\", \"name\": \"comment.block.documentation.dart\", \"patterns\": [{ \"include\": \"#dartdoc\" }], \"while\": \"^\\\\s*///\" }] }, \"comments-doc-oldschool\": { \"patterns\": [{ \"begin\": \"/\\\\*\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.block.documentation.dart\", \"patterns\": [{ \"include\": \"#comments-doc-oldschool\" }, { \"include\": \"#comments-block\" }, { \"include\": \"#dartdoc\" }] }] }, \"comments-inline\": { \"patterns\": [{ \"include\": \"#comments-block\" }, { \"captures\": { \"1\": { \"name\": \"comment.line.double-slash.dart\" } }, \"match\": \"((//).*)$\" }] }, \"constants-and-special-vars\": { \"patterns\": [{ \"match\": \"(?<!\\\\$)\\\\b(true|false|null)\\\\b(?!\\\\$)\", \"name\": \"constant.language.dart\" }, { \"match\": \"(?<!\\\\$)\\\\b(this|super)\\\\b(?!\\\\$)\", \"name\": \"variable.language.dart\" }, { \"match\": \"(?<!\\\\$)\\\\b((0(x|X)[0-9a-fA-F]*)|(([0-9]+\\\\.?[0-9]*)|(\\\\.[0-9]+))((e|E)(\\\\+|-)?[0-9]+)?)\\\\b(?!\\\\$)\", \"name\": \"constant.numeric.dart\" }, { \"include\": \"#class-identifier\" }, { \"include\": \"#function-identifier\" }] }, \"dartdoc\": { \"patterns\": [{ \"captures\": { \"0\": { \"name\": \"variable.name.source.dart\" } }, \"match\": \"(\\\\[.*?\\\\])\" }, { \"captures\": { \"0\": { \"name\": \"variable.name.source.dart\" } }, \"match\": \"^ {4,}(?![ \\\\*]).*\" }, { \"begin\": \"```.*?$\", \"contentName\": \"variable.other.source.dart\", \"end\": \"```\" }, { \"captures\": { \"0\": { \"name\": \"variable.other.source.dart\" } }, \"match\": \"(`[^`]+?`)\" }, { \"captures\": { \"2\": { \"name\": \"variable.other.source.dart\" } }, \"match\": \"(\\\\* ((    ).*))$\" }] }, \"function-identifier\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.name.function.dart\" }, \"2\": { \"patterns\": [{ \"include\": \"#type-args\" }] } }, \"match\": \"([_$]*[a-z][a-zA-Z0-9_$]*)(<(?:[a-zA-Z0-9_$<>?]|,\\\\s*|\\\\s+extends\\\\s+)+>)?[!?]?\\\\(\" }] }, \"keywords\": { \"patterns\": [{ \"match\": \"(?<!\\\\$)\\\\bas\\\\b(?!\\\\$)\", \"name\": \"keyword.cast.dart\" }, { \"match\": \"(?<!\\\\$)\\\\b(try|on|catch|finally|throw|rethrow)\\\\b(?!\\\\$)\", \"name\": \"keyword.control.catch-exception.dart\" }, { \"match\": \"(?<!\\\\$)\\\\b(break|case|continue|default|do|else|for|if|in|return|switch|while|when)\\\\b(?!\\\\$)\", \"name\": \"keyword.control.dart\" }, { \"match\": \"(?<!\\\\$)\\\\b(sync(\\\\*)?|async(\\\\*)?|await|yield(\\\\*)?)\\\\b(?!\\\\$)\", \"name\": \"keyword.control.dart\" }, { \"match\": \"(?<!\\\\$)\\\\bassert\\\\b(?!\\\\$)\", \"name\": \"keyword.control.dart\" }, { \"match\": \"(?<!\\\\$)\\\\b(new)\\\\b(?!\\\\$)\", \"name\": \"keyword.control.new.dart\" }, { \"match\": \"(?<!\\\\$)\\\\b(abstract|sealed|base|interface|class|enum|extends|extension type|extension|external|factory|implements|get(?!\\\\()|mixin|native|operator|set(?!\\\\()|typedef|with|covariant)\\\\b(?!\\\\$)\", \"name\": \"keyword.declaration.dart\" }, { \"match\": \"(?<!\\\\$)\\\\b(static|final|const|required|late)\\\\b(?!\\\\$)\", \"name\": \"storage.modifier.dart\" }, { \"match\": \"(?<!\\\\$)\\\\b(?:void|var)\\\\b(?!\\\\$)\", \"name\": \"storage.type.primitive.dart\" }] }, \"operators\": { \"patterns\": [{ \"match\": \"(?<!\\\\$)\\\\b(is\\\\!?)\\\\b(?!\\\\$)\", \"name\": \"keyword.operator.dart\" }, { \"match\": \"\\\\?|:\", \"name\": \"keyword.operator.ternary.dart\" }, { \"match\": \"(<<|>>>?|~|\\\\^|\\\\||&)\", \"name\": \"keyword.operator.bitwise.dart\" }, { \"match\": \"((&|\\\\^|\\\\||<<|>>>?)=)\", \"name\": \"keyword.operator.assignment.bitwise.dart\" }, { \"match\": \"(=>)\", \"name\": \"keyword.operator.closure.dart\" }, { \"match\": \"(==|!=|<=?|>=?)\", \"name\": \"keyword.operator.comparison.dart\" }, { \"match\": \"(([+*/%-]|\\\\~)=)\", \"name\": \"keyword.operator.assignment.arithmetic.dart\" }, { \"match\": \"(=)\", \"name\": \"keyword.operator.assignment.dart\" }, { \"match\": \"(\\\\-\\\\-|\\\\+\\\\+)\", \"name\": \"keyword.operator.increment-decrement.dart\" }, { \"match\": \"(\\\\-|\\\\+|\\\\*|\\\\/|\\\\~\\\\/|%)\", \"name\": \"keyword.operator.arithmetic.dart\" }, { \"match\": \"(!|&&|\\\\|\\\\|)\", \"name\": \"keyword.operator.logical.dart\" }] }, \"punctuation\": { \"patterns\": [{ \"match\": \",\", \"name\": \"punctuation.comma.dart\" }, { \"match\": \";\", \"name\": \"punctuation.terminator.dart\" }, { \"match\": \"\\\\.\", \"name\": \"punctuation.dot.dart\" }] }, \"string-interp\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.parameter.dart\" } }, \"match\": \"\\\\$([a-zA-Z0-9_]+)\", \"name\": \"string.interpolated.expression.dart\" }, { \"begin\": \"\\\\$\\\\{\", \"end\": \"\\\\}\", \"name\": \"string.interpolated.expression.dart\", \"patterns\": [{ \"include\": \"#constants-and-special-vars\" }, { \"include\": \"#strings\" }, { \"match\": \"[a-zA-Z0-9_]+\", \"name\": \"variable.parameter.dart\" }] }, { \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.dart\" }] }, \"strings\": { \"patterns\": [{ \"begin\": '(?<!r)\"\"\"', \"end\": '\"\"\"(?!\")', \"name\": \"string.interpolated.triple.double.dart\", \"patterns\": [{ \"include\": \"#string-interp\" }] }, { \"begin\": \"(?<!r)'''\", \"end\": \"'''(?!')\", \"name\": \"string.interpolated.triple.single.dart\", \"patterns\": [{ \"include\": \"#string-interp\" }] }, { \"begin\": 'r\"\"\"', \"end\": '\"\"\"(?!\")', \"name\": \"string.quoted.triple.double.dart\" }, { \"begin\": \"r'''\", \"end\": \"'''(?!')\", \"name\": \"string.quoted.triple.single.dart\" }, { \"begin\": '(?<!\\\\|r)\"', \"end\": '\"', \"name\": \"string.interpolated.double.dart\", \"patterns\": [{ \"match\": \"\\\\n\", \"name\": \"invalid.string.newline\" }, { \"include\": \"#string-interp\" }] }, { \"begin\": 'r\"', \"end\": '\"', \"name\": \"string.quoted.double.dart\", \"patterns\": [{ \"match\": \"\\\\n\", \"name\": \"invalid.string.newline\" }] }, { \"begin\": \"(?<!\\\\|r)'\", \"end\": \"'\", \"name\": \"string.interpolated.single.dart\", \"patterns\": [{ \"match\": \"\\\\n\", \"name\": \"invalid.string.newline\" }, { \"include\": \"#string-interp\" }] }, { \"begin\": \"r'\", \"end\": \"'\", \"name\": \"string.quoted.single.dart\", \"patterns\": [{ \"match\": \"\\\\n\", \"name\": \"invalid.string.newline\" }] }] }, \"type-args\": { \"begin\": \"(<)\", \"beginCaptures\": { \"1\": { \"name\": \"other.source.dart\" } }, \"end\": \"(>)\", \"endCaptures\": { \"1\": { \"name\": \"other.source.dart\" } }, \"patterns\": [{ \"include\": \"#class-identifier\" }, { \"match\": \",\" }, { \"match\": \"extends\", \"name\": \"keyword.declaration.dart\" }, { \"include\": \"#comments\" }] } }, \"scopeName\": \"source.dart\" });\nvar dart = [\n  lang\n];\n\nexport { dart as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,QAAQ,QAAQ,QAAQ,YAAY,CAAC,EAAE,SAAS,YAAY,QAAQ,gCAAgC,GAAG,EAAE,SAAS,mDAAmD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,QAAQ,yBAAyB,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,SAAS,wBAAwB,QAAQ,4BAA4B,GAAG,EAAE,SAAS,cAAc,QAAQ,uBAAuB,CAAC,EAAE,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,8BAA8B,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,WAAW,CAAC,GAAG,cAAc,EAAE,eAAe,EAAE,YAAY,CAAC,EAAE,SAAS,cAAc,QAAQ,+BAA+B,CAAC,EAAE,GAAG,oBAAoB,EAAE,YAAY,CAAC,EAAE,SAAS,sDAAsD,QAAQ,qBAAqB,GAAG,EAAE,SAAS,6BAA6B,QAAQ,8BAA8B,GAAG,EAAE,SAAS,mDAAmD,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qBAAqB,EAAE,GAAG,OAAO,SAAS,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sCAAsC,EAAE,GAAG,SAAS,YAAY,QAAQ,2BAA2B,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,CAAC,EAAE,SAAS,QAAQ,OAAO,QAAQ,QAAQ,sBAAsB,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,QAAQ,oCAAoC,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,GAAG,SAAS,WAAW,CAAC,EAAE,GAAG,0BAA0B,EAAE,YAAY,CAAC,EAAE,SAAS,WAAW,OAAO,QAAQ,QAAQ,oCAAoC,YAAY,CAAC,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,SAAS,YAAY,CAAC,EAAE,GAAG,8BAA8B,EAAE,YAAY,CAAC,EAAE,SAAS,0CAA0C,QAAQ,yBAAyB,GAAG,EAAE,SAAS,qCAAqC,QAAQ,yBAAyB,GAAG,EAAE,SAAS,sGAAsG,QAAQ,wBAAwB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,SAAS,cAAc,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,SAAS,qBAAqB,GAAG,EAAE,SAAS,WAAW,eAAe,8BAA8B,OAAO,MAAM,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,SAAS,aAAa,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,SAAS,oBAAoB,CAAC,EAAE,GAAG,uBAAuB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,EAAE,GAAG,SAAS,qFAAqF,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,2BAA2B,QAAQ,oBAAoB,GAAG,EAAE,SAAS,6DAA6D,QAAQ,uCAAuC,GAAG,EAAE,SAAS,iGAAiG,QAAQ,uBAAuB,GAAG,EAAE,SAAS,mEAAmE,QAAQ,uBAAuB,GAAG,EAAE,SAAS,+BAA+B,QAAQ,uBAAuB,GAAG,EAAE,SAAS,8BAA8B,QAAQ,2BAA2B,GAAG,EAAE,SAAS,oMAAoM,QAAQ,2BAA2B,GAAG,EAAE,SAAS,2DAA2D,QAAQ,wBAAwB,GAAG,EAAE,SAAS,qCAAqC,QAAQ,8BAA8B,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,SAAS,iCAAiC,QAAQ,wBAAwB,GAAG,EAAE,SAAS,SAAS,QAAQ,gCAAgC,GAAG,EAAE,SAAS,yBAAyB,QAAQ,gCAAgC,GAAG,EAAE,SAAS,0BAA0B,QAAQ,2CAA2C,GAAG,EAAE,SAAS,QAAQ,QAAQ,gCAAgC,GAAG,EAAE,SAAS,mBAAmB,QAAQ,mCAAmC,GAAG,EAAE,SAAS,oBAAoB,QAAQ,8CAA8C,GAAG,EAAE,SAAS,OAAO,QAAQ,mCAAmC,GAAG,EAAE,SAAS,mBAAmB,QAAQ,4CAA4C,GAAG,EAAE,SAAS,8BAA8B,QAAQ,mCAAmC,GAAG,EAAE,SAAS,iBAAiB,QAAQ,gCAAgC,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,SAAS,KAAK,QAAQ,yBAAyB,GAAG,EAAE,SAAS,KAAK,QAAQ,8BAA8B,GAAG,EAAE,SAAS,OAAO,QAAQ,uBAAuB,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0BAA0B,EAAE,GAAG,SAAS,sBAAsB,QAAQ,sCAAsC,GAAG,EAAE,SAAS,UAAU,OAAO,OAAO,QAAQ,uCAAuC,YAAY,CAAC,EAAE,WAAW,8BAA8B,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,iBAAiB,QAAQ,0BAA0B,CAAC,EAAE,GAAG,EAAE,SAAS,SAAS,QAAQ,iCAAiC,CAAC,EAAE,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,SAAS,aAAa,OAAO,YAAY,QAAQ,0CAA0C,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,SAAS,aAAa,OAAO,YAAY,QAAQ,0CAA0C,YAAY,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,SAAS,QAAQ,OAAO,YAAY,QAAQ,mCAAmC,GAAG,EAAE,SAAS,QAAQ,OAAO,YAAY,QAAQ,mCAAmC,GAAG,EAAE,SAAS,cAAc,OAAO,KAAK,QAAQ,mCAAmC,YAAY,CAAC,EAAE,SAAS,OAAO,QAAQ,yBAAyB,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,SAAS,MAAM,OAAO,KAAK,QAAQ,6BAA6B,YAAY,CAAC,EAAE,SAAS,OAAO,QAAQ,yBAAyB,CAAC,EAAE,GAAG,EAAE,SAAS,cAAc,OAAO,KAAK,QAAQ,mCAAmC,YAAY,CAAC,EAAE,SAAS,OAAO,QAAQ,yBAAyB,GAAG,EAAE,WAAW,iBAAiB,CAAC,EAAE,GAAG,EAAE,SAAS,MAAM,OAAO,KAAK,QAAQ,6BAA6B,YAAY,CAAC,EAAE,SAAS,OAAO,QAAQ,yBAAyB,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,oBAAoB,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,oBAAoB,GAAG,EAAE,SAAS,IAAI,GAAG,EAAE,SAAS,WAAW,QAAQ,2BAA2B,GAAG,EAAE,WAAW,YAAY,CAAC,EAAE,EAAE,GAAG,aAAa,cAAc,CAAC;AACl6O,IAAI,OAAO;AAAA,EACT;AACF;", "names": []}