import {
  handlebars
} from "./chunk-5TULGCP7.js";
import "./chunk-5WCIUZ4S.js";
import "./chunk-ESK7UDUX.js";
import {
  javascript
} from "./chunk-B3KVBTAC.js";
import "./chunk-2DKB27CY.js";
import "./chunk-2LSFTFF7.js";

// node_modules/.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/glimmer-js.mjs
var lang = Object.freeze({ "displayName": "Glimmer JS", "injections": { "L:source.gjs -comment -string": { "patterns": [{ "begin": "\\s*(<)(template)\\s*(>)", "beginCaptures": { "1": { "name": "punctuation.definition.tag.html" }, "2": { "name": "entity.name.tag.other.html" }, "3": { "name": "punctuation.definition.tag.html" } }, "end": "(</)(template)(>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.html" }, "2": { "name": "entity.name.tag.other.html" }, "3": { "name": "punctuation.definition.tag.html" } }, "name": "meta.js.embeddedTemplateWithoutArgs", "patterns": [{ "include": "text.html.handlebars" }] }, { "begin": "(<)(template)", "beginCaptures": { "1": { "name": "punctuation.definition.tag.html" }, "2": { "name": "entity.name.tag.other.html" } }, "end": "(</)(template)(>)", "endCaptures": { "1": { "name": "punctuation.definition.tag.html" }, "2": { "name": "entity.name.tag.other.html" }, "3": { "name": "punctuation.definition.tag.html" } }, "name": "meta.js.embeddedTemplateWithArgs", "patterns": [{ "begin": "(?<=\\<template)", "end": "(?=\\>)", "patterns": [{ "include": "text.html.handlebars#tag-stuff" }] }, { "begin": "(>)", "beginCaptures": { "1": { "name": "punctuation.definition.tag.end.js" } }, "contentName": "meta.html.embedded.block", "end": "(?=</template>)", "patterns": [{ "include": "text.html.handlebars" }] }] }] } }, "name": "glimmer-js", "patterns": [{ "include": "source.js" }], "scopeName": "source.gjs", "embeddedLangs": ["javascript", "handlebars"], "aliases": ["gjs"] });
var glimmerJs = [
  ...javascript,
  ...handlebars,
  lang
];
export {
  glimmerJs as default
};
//# sourceMappingURL=glimmer-js-E7T7SJA2.js.map
