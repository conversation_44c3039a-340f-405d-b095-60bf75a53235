{"version": 3, "sources": ["../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/mermaid/dist/chunks/mermaid.core/diagram-5UYTHUR4.mjs"], "sourcesContent": ["import {\n  populateCommonDb\n} from \"./chunk-353BL4L5.mjs\";\nimport {\n  cleanAndMerge\n} from \"./chunk-55PJQP7W.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-P3VETL53.mjs\";\nimport {\n  __name,\n  clear,\n  configureSvgSize,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-3XYRH5AP.mjs\";\n\n// src/diagrams/packet/db.ts\nvar defaultPacketData = {\n  packet: []\n};\nvar data = structuredClone(defaultPacketData);\nvar DEFAULT_PACKET_CONFIG = defaultConfig_default.packet;\nvar getConfig2 = /* @__PURE__ */ __name(() => {\n  const config = cleanAndMerge({\n    ...DEFAULT_PACKET_CONFIG,\n    ...getConfig().packet\n  });\n  if (config.showBits) {\n    config.paddingY += 10;\n  }\n  return config;\n}, \"getConfig\");\nvar getPacket = /* @__PURE__ */ __name(() => data.packet, \"getPacket\");\nvar pushWord = /* @__PURE__ */ __name((word) => {\n  if (word.length > 0) {\n    data.packet.push(word);\n  }\n}, \"pushWord\");\nvar clear2 = /* @__PURE__ */ __name(() => {\n  clear();\n  data = structuredClone(defaultPacketData);\n}, \"clear\");\nvar db = {\n  pushWord,\n  getPacket,\n  getConfig: getConfig2,\n  clear: clear2,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription\n};\n\n// src/diagrams/packet/parser.ts\nimport { parse } from \"@mermaid-js/parser\";\nvar maxPacketSize = 1e4;\nvar populate = /* @__PURE__ */ __name((ast) => {\n  populateCommonDb(ast, db);\n  let lastBit = -1;\n  let word = [];\n  let row = 1;\n  const { bitsPerRow } = db.getConfig();\n  for (let { start, end, bits, label } of ast.blocks) {\n    if (start !== void 0 && end !== void 0 && end < start) {\n      throw new Error(`Packet block ${start} - ${end} is invalid. End must be greater than start.`);\n    }\n    start ??= lastBit + 1;\n    if (start !== lastBit + 1) {\n      throw new Error(\n        `Packet block ${start} - ${end ?? start} is not contiguous. It should start from ${lastBit + 1}.`\n      );\n    }\n    if (bits === 0) {\n      throw new Error(`Packet block ${start} is invalid. Cannot have a zero bit field.`);\n    }\n    end ??= start + (bits ?? 1) - 1;\n    bits ??= end - start + 1;\n    lastBit = end;\n    log.debug(`Packet block ${start} - ${lastBit} with label ${label}`);\n    while (word.length <= bitsPerRow + 1 && db.getPacket().length < maxPacketSize) {\n      const [block, nextBlock] = getNextFittingBlock({ start, end, bits, label }, row, bitsPerRow);\n      word.push(block);\n      if (block.end + 1 === row * bitsPerRow) {\n        db.pushWord(word);\n        word = [];\n        row++;\n      }\n      if (!nextBlock) {\n        break;\n      }\n      ({ start, end, bits, label } = nextBlock);\n    }\n  }\n  db.pushWord(word);\n}, \"populate\");\nvar getNextFittingBlock = /* @__PURE__ */ __name((block, row, bitsPerRow) => {\n  if (block.start === void 0) {\n    throw new Error(\"start should have been set during first phase\");\n  }\n  if (block.end === void 0) {\n    throw new Error(\"end should have been set during first phase\");\n  }\n  if (block.start > block.end) {\n    throw new Error(`Block start ${block.start} is greater than block end ${block.end}.`);\n  }\n  if (block.end + 1 <= row * bitsPerRow) {\n    return [block, void 0];\n  }\n  const rowEnd = row * bitsPerRow - 1;\n  const rowStart = row * bitsPerRow;\n  return [\n    {\n      start: block.start,\n      end: rowEnd,\n      label: block.label,\n      bits: rowEnd - block.start\n    },\n    {\n      start: rowStart,\n      end: block.end,\n      label: block.label,\n      bits: block.end - rowStart\n    }\n  ];\n}, \"getNextFittingBlock\");\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"packet\", input);\n    log.debug(ast);\n    populate(ast);\n  }, \"parse\")\n};\n\n// src/diagrams/packet/renderer.ts\nvar draw = /* @__PURE__ */ __name((_text, id, _version, diagram2) => {\n  const db2 = diagram2.db;\n  const config = db2.getConfig();\n  const { rowHeight, paddingY, bitWidth, bitsPerRow } = config;\n  const words = db2.getPacket();\n  const title = db2.getDiagramTitle();\n  const totalRowHeight = rowHeight + paddingY;\n  const svgHeight = totalRowHeight * (words.length + 1) - (title ? 0 : rowHeight);\n  const svgWidth = bitWidth * bitsPerRow + 2;\n  const svg = selectSvgElement(id);\n  svg.attr(\"viewbox\", `0 0 ${svgWidth} ${svgHeight}`);\n  configureSvgSize(svg, svgHeight, svgWidth, config.useMaxWidth);\n  for (const [word, packet] of words.entries()) {\n    drawWord(svg, packet, word, config);\n  }\n  svg.append(\"text\").text(title).attr(\"x\", svgWidth / 2).attr(\"y\", svgHeight - totalRowHeight / 2).attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\").attr(\"class\", \"packetTitle\");\n}, \"draw\");\nvar drawWord = /* @__PURE__ */ __name((svg, word, rowNumber, { rowHeight, paddingX, paddingY, bitWidth, bitsPerRow, showBits }) => {\n  const group = svg.append(\"g\");\n  const wordY = rowNumber * (rowHeight + paddingY) + paddingY;\n  for (const block of word) {\n    const blockX = block.start % bitsPerRow * bitWidth + 1;\n    const width = (block.end - block.start + 1) * bitWidth - paddingX;\n    group.append(\"rect\").attr(\"x\", blockX).attr(\"y\", wordY).attr(\"width\", width).attr(\"height\", rowHeight).attr(\"class\", \"packetBlock\");\n    group.append(\"text\").attr(\"x\", blockX + width / 2).attr(\"y\", wordY + rowHeight / 2).attr(\"class\", \"packetLabel\").attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\").text(block.label);\n    if (!showBits) {\n      continue;\n    }\n    const isSingleBlock = block.end === block.start;\n    const bitNumberY = wordY - 2;\n    group.append(\"text\").attr(\"x\", blockX + (isSingleBlock ? width / 2 : 0)).attr(\"y\", bitNumberY).attr(\"class\", \"packetByte start\").attr(\"dominant-baseline\", \"auto\").attr(\"text-anchor\", isSingleBlock ? \"middle\" : \"start\").text(block.start);\n    if (!isSingleBlock) {\n      group.append(\"text\").attr(\"x\", blockX + width).attr(\"y\", bitNumberY).attr(\"class\", \"packetByte end\").attr(\"dominant-baseline\", \"auto\").attr(\"text-anchor\", \"end\").text(block.end);\n    }\n  }\n}, \"drawWord\");\nvar renderer = { draw };\n\n// src/diagrams/packet/styles.ts\nvar defaultPacketStyleOptions = {\n  byteFontSize: \"10px\",\n  startByteColor: \"black\",\n  endByteColor: \"black\",\n  labelColor: \"black\",\n  labelFontSize: \"12px\",\n  titleColor: \"black\",\n  titleFontSize: \"14px\",\n  blockStrokeColor: \"black\",\n  blockStrokeWidth: \"1\",\n  blockFillColor: \"#efefef\"\n};\nvar styles = /* @__PURE__ */ __name(({ packet } = {}) => {\n  const options = cleanAndMerge(defaultPacketStyleOptions, packet);\n  return `\n\t.packetByte {\n\t\tfont-size: ${options.byteFontSize};\n\t}\n\t.packetByte.start {\n\t\tfill: ${options.startByteColor};\n\t}\n\t.packetByte.end {\n\t\tfill: ${options.endByteColor};\n\t}\n\t.packetLabel {\n\t\tfill: ${options.labelColor};\n\t\tfont-size: ${options.labelFontSize};\n\t}\n\t.packetTitle {\n\t\tfill: ${options.titleColor};\n\t\tfont-size: ${options.titleFontSize};\n\t}\n\t.packetBlock {\n\t\tstroke: ${options.blockStrokeColor};\n\t\tstroke-width: ${options.blockStrokeWidth};\n\t\tfill: ${options.blockFillColor};\n\t}\n\t`;\n}, \"styles\");\n\n// src/diagrams/packet/diagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer,\n  styles\n};\nexport {\n  diagram\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,IAAI,oBAAoB;EACtB,QAAQ,CAAA;AACV;AACA,IAAI,OAAO,gBAAgB,iBAAiB;AAC5C,IAAI,wBAAwB,sBAAsB;AAClD,IAAI,aAA6B,OAAO,MAAM;AAC5C,QAAM,SAAS,cAAc;IAC3B,GAAG;IACH,GAAG,UAAS,EAAG;EACnB,CAAG;AACD,MAAI,OAAO,UAAU;AACnB,WAAO,YAAY;EACrB;AACA,SAAO;AACT,GAAG,WAAW;AACd,IAAI,YAA4B,OAAO,MAAM,KAAK,QAAQ,WAAW;AACrE,IAAI,WAA2B,OAAO,CAAC,SAAS;AAC9C,MAAI,KAAK,SAAS,GAAG;AACnB,SAAK,OAAO,KAAK,IAAI;EACvB;AACF,GAAG,UAAU;AACb,IAAI,SAAyB,OAAO,MAAM;AACxC,UAAK;AACL,SAAO,gBAAgB,iBAAiB;AAC1C,GAAG,OAAO;AACV,IAAI,KAAK;EACP;EACA;EACA,WAAW;EACX,OAAO;EACP;EACA;EACA;EACA;EACA;EACA;AACF;AAIA,IAAI,gBAAgB;AACpB,IAAI,WAA2B,OAAO,CAAC,QAAQ;AAC7C,mBAAiB,KAAK,EAAE;AACxB,MAAI,UAAU;AACd,MAAI,OAAO,CAAA;AACX,MAAI,MAAM;AACV,QAAM,EAAE,WAAU,IAAK,GAAG,UAAS;AACnC,WAAS,EAAE,OAAO,KAAK,MAAM,MAAK,KAAM,IAAI,QAAQ;AAClD,QAAI,UAAU,UAAU,QAAQ,UAAU,MAAM,OAAO;AACrD,YAAM,IAAI,MAAM,gBAAgB,KAAK,MAAM,GAAG,8CAA8C;IAC9F;AACA,cAAA,QAAU,UAAU;AACpB,QAAI,UAAU,UAAU,GAAG;AACzB,YAAM,IAAI;QACR,gBAAgB,KAAK,MAAM,OAAO,KAAK,4CAA4C,UAAU,CAAC;MACtG;IACI;AACA,QAAI,SAAS,GAAG;AACd,YAAM,IAAI,MAAM,gBAAgB,KAAK,4CAA4C;IACnF;AACA,YAAA,MAAQ,SAAS,QAAQ,KAAK;AAC9B,aAAA,OAAS,MAAM,QAAQ;AACvB,cAAU;AACV,QAAI,MAAM,gBAAgB,KAAK,MAAM,OAAO,eAAe,KAAK,EAAE;AAClE,WAAO,KAAK,UAAU,aAAa,KAAK,GAAG,UAAS,EAAG,SAAS,eAAe;AAC7E,YAAM,CAAC,OAAO,SAAS,IAAI,oBAAoB,EAAE,OAAO,KAAK,MAAM,MAAA,GAAS,KAAK,UAAU;AAC3F,WAAK,KAAK,KAAK;AACf,UAAI,MAAM,MAAM,MAAM,MAAM,YAAY;AACtC,WAAG,SAAS,IAAI;AAChB,eAAO,CAAA;AACP;MACF;AACA,UAAI,CAAC,WAAW;AACd;MACF;AACA,OAAC,EAAE,OAAO,KAAK,MAAM,MAAK,IAAK;IACjC;EACF;AACA,KAAG,SAAS,IAAI;AAClB,GAAG,UAAU;AACb,IAAI,sBAAsC,OAAO,CAAC,OAAO,KAAK,eAAe;AAC3E,MAAI,MAAM,UAAU,QAAQ;AAC1B,UAAM,IAAI,MAAM,+CAA+C;EACjE;AACA,MAAI,MAAM,QAAQ,QAAQ;AACxB,UAAM,IAAI,MAAM,6CAA6C;EAC/D;AACA,MAAI,MAAM,QAAQ,MAAM,KAAK;AAC3B,UAAM,IAAI,MAAM,eAAe,MAAM,KAAK,8BAA8B,MAAM,GAAG,GAAG;EACtF;AACA,MAAI,MAAM,MAAM,KAAK,MAAM,YAAY;AACrC,WAAO,CAAC,OAAO,MAAM;EACvB;AACA,QAAM,SAAS,MAAM,aAAa;AAClC,QAAM,WAAW,MAAM;AACvB,SAAO;IACL;MACE,OAAO,MAAM;MACb,KAAK;MACL,OAAO,MAAM;MACb,MAAM,SAAS,MAAM;IAC3B;IACI;MACE,OAAO;MACP,KAAK,MAAM;MACX,OAAO,MAAM;MACb,MAAM,MAAM,MAAM;IACxB;EACA;AACA,GAAG,qBAAqB;AACxB,IAAI,SAAS;EACX,OAAuB,OAAO,OAAO,UAAU;AAC7C,UAAM,MAAM,MAAM,MAAM,UAAU,KAAK;AACvC,QAAI,MAAM,GAAG;AACb,aAAS,GAAG;EACd,GAAG,OAAO;AACZ;AAGA,IAAI,OAAuB,OAAO,CAAC,OAAO,IAAI,UAAU,aAAa;AACnE,QAAM,MAAM,SAAS;AACrB,QAAM,SAAS,IAAI,UAAS;AAC5B,QAAM,EAAE,WAAW,UAAU,UAAU,WAAU,IAAK;AACtD,QAAM,QAAQ,IAAI,UAAS;AAC3B,QAAM,QAAQ,IAAI,gBAAe;AACjC,QAAM,iBAAiB,YAAY;AACnC,QAAM,YAAY,kBAAkB,MAAM,SAAS,MAAM,QAAQ,IAAI;AACrE,QAAM,WAAW,WAAW,aAAa;AACzC,QAAM,MAAM,iBAAiB,EAAE;AAC/B,MAAI,KAAK,WAAW,OAAO,QAAQ,IAAI,SAAS,EAAE;AAClD,mBAAiB,KAAK,WAAW,UAAU,OAAO,WAAW;AAC7D,aAAW,CAAC,MAAM,MAAM,KAAK,MAAM,QAAO,GAAI;AAC5C,aAAS,KAAK,QAAQ,MAAM,MAAM;EACpC;AACA,MAAI,OAAO,MAAM,EAAE,KAAK,KAAK,EAAE,KAAK,KAAK,WAAW,CAAC,EAAE,KAAK,KAAK,YAAY,iBAAiB,CAAC,EAAE,KAAK,qBAAqB,QAAQ,EAAE,KAAK,eAAe,QAAQ,EAAE,KAAK,SAAS,aAAa;AAChM,GAAG,MAAM;AACT,IAAI,WAA2B,OAAO,CAAC,KAAK,MAAM,WAAW,EAAE,WAAW,UAAU,UAAU,UAAU,YAAY,SAAQ,MAAO;AACjI,QAAM,QAAQ,IAAI,OAAO,GAAG;AAC5B,QAAM,QAAQ,aAAa,YAAY,YAAY;AACnD,aAAW,SAAS,MAAM;AACxB,UAAM,SAAS,MAAM,QAAQ,aAAa,WAAW;AACrD,UAAM,SAAS,MAAM,MAAM,MAAM,QAAQ,KAAK,WAAW;AACzD,UAAM,OAAO,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE,KAAK,KAAK,KAAK,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,SAAS,EAAE,KAAK,SAAS,aAAa;AAClI,UAAM,OAAO,MAAM,EAAE,KAAK,KAAK,SAAS,QAAQ,CAAC,EAAE,KAAK,KAAK,QAAQ,YAAY,CAAC,EAAE,KAAK,SAAS,aAAa,EAAE,KAAK,qBAAqB,QAAQ,EAAE,KAAK,eAAe,QAAQ,EAAE,KAAK,MAAM,KAAK;AACnM,QAAI,CAAC,UAAU;AACb;IACF;AACA,UAAM,gBAAgB,MAAM,QAAQ,MAAM;AAC1C,UAAM,aAAa,QAAQ;AAC3B,UAAM,OAAO,MAAM,EAAE,KAAK,KAAK,UAAU,gBAAgB,QAAQ,IAAI,EAAE,EAAE,KAAK,KAAK,UAAU,EAAE,KAAK,SAAS,kBAAkB,EAAE,KAAK,qBAAqB,MAAM,EAAE,KAAK,eAAe,gBAAgB,WAAW,OAAO,EAAE,KAAK,MAAM,KAAK;AAC3O,QAAI,CAAC,eAAe;AAClB,YAAM,OAAO,MAAM,EAAE,KAAK,KAAK,SAAS,KAAK,EAAE,KAAK,KAAK,UAAU,EAAE,KAAK,SAAS,gBAAgB,EAAE,KAAK,qBAAqB,MAAM,EAAE,KAAK,eAAe,KAAK,EAAE,KAAK,MAAM,GAAG;IAClL;EACF;AACF,GAAG,UAAU;AACb,IAAI,WAAW,EAAE,KAAI;AAGrB,IAAI,4BAA4B;EAC9B,cAAc;EACd,gBAAgB;EAChB,cAAc;EACd,YAAY;EACZ,eAAe;EACf,YAAY;EACZ,eAAe;EACf,kBAAkB;EAClB,kBAAkB;EAClB,gBAAgB;AAClB;AACA,IAAI,SAAyB,OAAO,CAAC,EAAE,OAAM,IAAK,CAAA,MAAO;AACvD,QAAM,UAAU,cAAc,2BAA2B,MAAM;AAC/D,SAAO;;eAEM,QAAQ,YAAY;;;UAGzB,QAAQ,cAAc;;;UAGtB,QAAQ,YAAY;;;UAGpB,QAAQ,UAAU;eACb,QAAQ,aAAa;;;UAG1B,QAAQ,UAAU;eACb,QAAQ,aAAa;;;YAGxB,QAAQ,gBAAgB;kBAClB,QAAQ,gBAAgB;UAChC,QAAQ,cAAc;;;AAGhC,GAAG,QAAQ;AAGR,IAAC,UAAU;EACZ;EACA;EACA;EACA;AACF;", "names": []}