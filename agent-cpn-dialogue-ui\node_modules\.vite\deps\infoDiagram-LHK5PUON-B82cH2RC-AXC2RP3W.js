import {
  parse
} from "./chunk-66ULY3BI.js";
import "./chunk-FBORRAPK.js";
import "./chunk-CWMD4UTM.js";
import "./chunk-2CUI6UCJ.js";
import {
  __name,
  configureSvgSize,
  log,
  package_default,
  selectSvgElement
} from "./chunk-WBJSNHLM.js";
import "./chunk-RAZ5VACY.js";
import "./chunk-777NXQNP.js";
import "./chunk-YLC3IX5K.js";
import "./chunk-SNBSRBFP.js";
import "./chunk-PT45IBK3.js";
import "./chunk-CO6MSXMG.js";
import "./chunk-2LSFTFF7.js";

// node_modules/.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/vue-element-plus-x/dist/infoDiagram-LHK5PUON-B82cH2RC.js
var parser = {
  parse: __name(async (input) => {
    const ast = await parse("info", input);
    log.debug(ast);
  }, "parse")
};
var DEFAULT_INFO_DB = {
  version: package_default.version + ""
};
var getVersion = __name(() => DEFAULT_INFO_DB.version, "getVersion");
var db = {
  getVersion
};
var draw = __name((text, id, version) => {
  log.debug("rendering info diagram\n" + text);
  const svg = selectSvgElement(id);
  configureSvgSize(svg, 100, 400, true);
  const group = svg.append("g");
  group.append("text").attr("x", 100).attr("y", 40).attr("class", "version").attr("font-size", 32).style("text-anchor", "middle").text(`v${version}`);
}, "draw");
var renderer = { draw };
var diagram = {
  parser,
  db,
  renderer
};
export {
  diagram
};
//# sourceMappingURL=infoDiagram-LHK5PUON-B82cH2RC-AXC2RP3W.js.map
