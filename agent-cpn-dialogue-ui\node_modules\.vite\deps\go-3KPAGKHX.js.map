{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/go.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Go\", \"name\": \"go\", \"patterns\": [{ \"include\": \"#statements\" }], \"repository\": { \"after_control_variables\": { \"patterns\": [{ \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations-without-brackets\" }, { \"match\": \"\\\\[\", \"name\": \"punctuation.definition.begin.bracket.square.go\" }, { \"match\": \"\\\\]\", \"name\": \"punctuation.definition.end.bracket.square.go\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"variable.other.go\" }] } }, \"comment\": \"After control variables, to not highlight as a struct/interface\", \"match\": \"(?:(?<=\\\\brange\\\\b|\\\\bswitch\\\\b|\\\\;|\\\\bif\\\\b|\\\\bfor\\\\b|\\\\<|\\\\>|\\\\<\\\\=|\\\\>\\\\=|\\\\=\\\\=|\\\\!\\\\=|\\\\w(?:\\\\+|/|\\\\-|\\\\*|\\\\%)(?:\\\\=)?|\\\\|\\\\||\\\\&\\\\&)(?:\\\\s*)([[:alnum:]\\\\-\\\\_\\\\!\\\\.\\\\[\\\\]\\\\<\\\\>\\\\=\\\\*/\\\\+\\\\%\\\\:]+)(?:\\\\s*)(?=\\\\{))\" }] }, \"brackets\": { \"patterns\": [{ \"begin\": \"\\\\{\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.begin.bracket.curly.go\" } }, \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.curly.go\" } }, \"patterns\": [{ \"include\": \"$self\" }] }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.begin.bracket.round.go\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.round.go\" } }, \"patterns\": [{ \"include\": \"$self\" }] }, { \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.begin.bracket.square.go\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.square.go\" } }, \"patterns\": [{ \"include\": \"$self\" }] }] }, \"built_in_functions\": { \"comment\": \"Built-in functions\", \"patterns\": [{ \"match\": \"\\\\b(append|cap|close|complex|copy|delete|imag|len|panic|print|println|real|recover|min|max|clear)\\\\b(?=\\\\()\", \"name\": \"entity.name.function.support.builtin.go\" }, { \"begin\": \"(?:(\\\\bnew\\\\b)(\\\\())\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.support.builtin.go\" }, \"2\": { \"name\": \"punctuation.definition.begin.bracket.round.go\" } }, \"comment\": \"new keyword\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.round.go\" } }, \"patterns\": [{ \"include\": \"#functions\" }, { \"include\": \"#struct_variables_types\" }, { \"include\": \"#type-declarations\" }, { \"include\": \"#generic_types\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }, { \"include\": \"$self\" }] }, { \"captures\": { \"1\": { \"name\": \"entity.name.function.support.builtin.go\" }, \"2\": { \"name\": \"punctuation.definition.begin.bracket.round.go\" }, \"3\": { \"patterns\": [{ \"include\": \"#type-declarations-without-brackets\" }, { \"include\": \"#parameter-variable-types\" }, { \"match\": \"\\\\w+\", \"name\": \"entity.name.type.go\" }] }, \"4\": { \"patterns\": [{ \"include\": \"$self\" }] }, \"5\": { \"name\": \"punctuation.definition.end.bracket.round.go\" } }, \"comment\": \"make keyword\", \"match\": \"(?:(\\\\bmake\\\\b)(?:(\\\\()((?:(?:(?:[\\\\*\\\\[\\\\]]+)?(?:\\\\<\\\\-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*\\\\<\\\\-)?\\\\s*)+)?(?:[\\\\w\\\\.\\\\*\\\\[\\\\]\\\\{\\\\}]+)(?:\\\\[(?:[^\\\\]]+)?\\\\])?(?:[\\\\w\\\\.\\\\*\\\\[\\\\]\\\\{\\\\}]+)?)?((?:\\\\,\\\\s*[\\\\w\\\\.\\\\(\\\\)]+)+)?(\\\\))))\" }] }, \"comments\": { \"patterns\": [{ \"begin\": \"(\\\\/\\\\*)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.go\" } }, \"end\": \"(\\\\*\\\\/)\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.go\" } }, \"name\": \"comment.block.go\" }, { \"begin\": \"(\\\\/\\\\/)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.comment.go\" } }, \"end\": \"(?:\\\\n|$)\", \"name\": \"comment.line.double-slash.go\" }] }, \"delimiters\": { \"patterns\": [{ \"match\": \"\\\\,\", \"name\": \"punctuation.other.comma.go\" }, { \"match\": \"\\\\.(?!\\\\.\\\\.)\", \"name\": \"punctuation.other.period.go\" }, { \"match\": \":(?!=)\", \"name\": \"punctuation.other.colon.go\" }] }, \"double_parentheses_types\": { \"captures\": { \"1\": { \"name\": \"punctuation.definition.begin.bracket.round.go\" }, \"2\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"\\\\w+\", \"name\": \"entity.name.type.go\" }] }, \"3\": { \"name\": \"punctuation.definition.end.bracket.round.go\" } }, \"comment\": \"double parentheses types\", \"match\": \"(?:(?:(\\\\()([^\\\\)]+)(\\\\)))(?=\\\\((?:[\\\\w\\\\.\\\\*\\\\&]+)\\\\)))\" }, \"function_declaration\": { \"begin\": \"(?:^(\\\\bfunc\\\\b)(?:\\\\s*(\\\\([^\\\\)]+\\\\)\\\\s*)?(?:(\\\\w+)(?=\\\\(|\\\\[))?))\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.function.go\" }, \"2\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.begin.bracket.round.go\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.round.go\" } }, \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"variable.parameter.go\" }, \"2\": { \"patterns\": [{ \"include\": \"#type-declarations-without-brackets\" }, { \"include\": \"#parameter-variable-types\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"match\": \"(?:(\\\\w+(?:\\\\s+))?((?:[\\\\w\\\\.\\\\*]+)(?:\\\\[(?:(?:(?:[\\\\w\\\\.\\\\*]+)(?:\\\\,\\\\s+)?)+)?\\\\])?))\" }, { \"include\": \"$self\" }] }] }, \"3\": { \"patterns\": [{ \"match\": \"\\\\d\\\\w*\", \"name\": \"invalid.illegal.identifier.go\" }, { \"match\": \"\\\\w+\", \"name\": \"entity.name.function.go\" }] }, \"patterns\": [{ \"include\": \"#type-declarations\" }] }, \"comment\": \"Function declarations\", \"end\": \"(?:(?<=\\\\))\\\\s*((?:(?:(?:[\\\\*\\\\[\\\\]]+)?(?:\\\\<\\\\-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*\\\\<\\\\-)?\\\\s*)+)?(?!(?:[\\\\[\\\\]\\\\*]+)?(?:\\\\bstruct\\\\b|\\\\binterface\\\\b))[\\\\w\\\\.\\\\-\\\\*\\\\[\\\\]]+)?\\\\s*(?=\\\\{))\", \"endCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations-without-brackets\" }, { \"include\": \"#parameter-variable-types\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.begin.bracket.round.go\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.round.go\" } }, \"patterns\": [{ \"include\": \"#function_param_types\" }] }, { \"begin\": \"(?:([\\\\w\\\\.\\\\*]+)?(\\\\[))\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] }, \"2\": { \"name\": \"punctuation.definition.begin.bracket.square.go\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.square.go\" } }, \"patterns\": [{ \"include\": \"#generic_param_types\" }] }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations-without-brackets\" }, { \"include\": \"#parameter-variable-types\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"comment\": \"single function as a type returned type(s) declaration\", \"match\": \"(?:(?<=\\\\))\\\\s+((?:(?:\\\\s*(?:[\\\\*\\\\[\\\\]]+)?(?:\\\\<\\\\-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*\\\\<\\\\-)?\\\\s*)+)?[\\\\w\\\\*\\\\.\\\\[\\\\]\\\\<\\\\>\\\\-]+(?:\\\\s*)(?:\\\\/(?:\\\\/|\\\\*).*)?)$)\" }, { \"include\": \"$self\" }] }, \"function_param_types\": { \"comment\": \"function parameter variables and types\", \"patterns\": [{ \"include\": \"#struct_variables_types\" }, { \"include\": \"#type-declarations-without-brackets\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"\\\\w+\", \"name\": \"variable.parameter.go\" }] } }, \"comment\": \"struct type declaration\", \"match\": \"((?:(?:\\\\w+\\\\,\\\\s*)+)?\\\\w+)\\\\s+(?=(?:(?:\\\\s*(?:[\\\\*\\\\[\\\\]]+)?(?:\\\\<\\\\-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*\\\\<\\\\-)?\\\\s*)+)?(?:[\\\\[\\\\]\\\\*]+)?\\\\bstruct\\\\b\\\\s*\\\\{)\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"\\\\w+\", \"name\": \"variable.parameter.go\" }] } }, \"comment\": \"multiple parameters one type -with multilines\", \"match\": \"(?:(?:(?<=\\\\()|^\\\\s*)((?:(?:\\\\w+\\\\,\\\\s*)+)(?:/(?:/|\\\\*).*)?)$)\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#delimiters\" }, { \"match\": \"\\\\w+\", \"name\": \"variable.parameter.go\" }] }, \"2\": { \"patterns\": [{ \"include\": \"#type-declarations-without-brackets\" }, { \"include\": \"#parameter-variable-types\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"comment\": \"multiple params and types | multiple params one type | one param one type\", \"match\": \"(?:((?:(?:\\\\w+\\\\,\\\\s*)+)?\\\\w+)(?:\\\\s+)((?:(?:\\\\s*(?:[\\\\*\\\\[\\\\]]+)?(?:\\\\<\\\\-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*\\\\<\\\\-)?\\\\s*)+)?(?:(?:(?:[\\\\w\\\\[\\\\]\\\\.\\\\*]+)?(?:(?:\\\\bfunc\\\\b\\\\((?:[^\\\\)]+)?\\\\))(?:(?:\\\\s*(?:[\\\\*\\\\[\\\\]]+)?(?:\\\\<\\\\-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*\\\\<\\\\-)?\\\\s*)+)?(?:\\\\s*))+(?:(?:(?:[\\\\w\\\\*\\\\.\\\\[\\\\]]+)|(?:\\\\((?:[^\\\\)]+)?\\\\))))?)|(?:(?:[\\\\[\\\\]\\\\*]+)?[\\\\w\\\\*\\\\.]+(?:\\\\[(?:[^\\\\]]+)\\\\])?(?:[\\\\w\\\\.\\\\*]+)?)+)))\" }, { \"include\": \"#parameter-variable-types\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"comment\": \"other types\", \"match\": \"([\\\\w\\\\.]+)\" }, { \"include\": \"$self\" }] }, \"functions\": { \"begin\": \"(?:(\\\\bfunc\\\\b)(?=\\\\())\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.function.go\" } }, \"comment\": \"Functions\", \"end\": \"(?:(?<=\\\\))(\\\\s*(?:(?:[\\\\*\\\\[\\\\]]+)?(?:\\\\<\\\\-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*\\\\<\\\\-)?\\\\s*)+)?((?:(?:\\\\s*(?:(?:[\\\\[\\\\]\\\\*]+)?[\\\\w\\\\.\\\\*]+)?(?:(?:\\\\[(?:(?:[\\\\w\\\\.\\\\*]+)?(?:\\\\[(?:[^\\\\]]+)?\\\\])?(?:\\\\,\\\\s+)?)+\\\\])|(?:\\\\((?:[^\\\\)]+)?\\\\)))?(?:[\\\\w\\\\.\\\\*]+)?)(?:\\\\s*)(?=\\\\{))|(?:\\\\s*(?:(?:(?:[\\\\[\\\\]\\\\*]+)?(?!\\\\bfunc\\\\b)(?:[\\\\w\\\\.\\\\*]+)(?:\\\\[(?:(?:[\\\\w\\\\.\\\\*]+)?(?:\\\\[(?:[^\\\\]]+)?\\\\])?(?:\\\\,\\\\s+)?)+\\\\])?(?:[\\\\w\\\\.\\\\*]+)?)|(?:\\\\((?:[^\\\\)]+)?\\\\)))))?)\", \"endCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations\" }] }, \"2\": { \"patterns\": [{ \"include\": \"#type-declarations-without-brackets\" }, { \"include\": \"#parameter-variable-types\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"patterns\": [{ \"include\": \"#parameter-variable-types\" }] }, \"functions_inline\": { \"captures\": { \"1\": { \"name\": \"keyword.function.go\" }, \"2\": { \"patterns\": [{ \"include\": \"#type-declarations-without-brackets\" }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.begin.bracket.round.go\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.round.go\" } }, \"patterns\": [{ \"include\": \"#function_param_types\" }, { \"include\": \"$self\" }] }, { \"match\": \"\\\\[\", \"name\": \"punctuation.definition.begin.bracket.square.go\" }, { \"match\": \"\\\\]\", \"name\": \"punctuation.definition.end.bracket.square.go\" }, { \"match\": \"\\\\{\", \"name\": \"punctuation.definition.begin.bracket.curly.go\" }, { \"match\": \"\\\\}\", \"name\": \"punctuation.definition.end.bracket.curly.go\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"comment\": \"functions in-line with multi return types\", \"match\": \"(?:(\\\\bfunc\\\\b)((?:\\\\((?:.*)\\\\))(?:\\\\s+)(?:\\\\((?:.*)\\\\)))(?:\\\\s+)(?=\\\\{))\" }, \"generic_param_types\": { \"comment\": \"generic parameter variables and types\", \"patterns\": [{ \"include\": \"#type-declarations-without-brackets\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"\\\\w+\", \"name\": \"variable.parameter.go\" }] } }, \"comment\": \"multiple parameters one type -with multilines\", \"match\": \"(?:(?:(?<=\\\\()|^\\\\s*)((?:(?:\\\\w+\\\\,\\\\s*)+)(?:/(?:/|\\\\*).*)?)$)\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#delimiters\" }, { \"match\": \"\\\\w+\", \"name\": \"variable.parameter.go\" }] }, \"2\": { \"patterns\": [{ \"include\": \"#type-declarations-without-brackets\" }, { \"include\": \"#parameter-variable-types\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] }, \"3\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"comment\": \"multiple params and types | multiple types one param\", \"match\": \"(?:((?:(?:\\\\w+\\\\,\\\\s*)+)?\\\\w+)(?:\\\\s+)((?:(?:\\\\s*(?:[\\\\*\\\\[\\\\]]+)?(?:\\\\<\\\\-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*\\\\<\\\\-)?\\\\s*)+)?(?:(?:(?:[\\\\w\\\\[\\\\]\\\\.\\\\*]+)?(?:(?:\\\\bfunc\\\\b\\\\((?:[^\\\\)]+)?\\\\))(?:(?:\\\\s*(?:[\\\\*\\\\[\\\\]]+)?(?:\\\\<\\\\-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*\\\\<\\\\-)?\\\\s*)+)?(?:\\\\s*))+(?:(?:(?:[\\\\w\\\\*\\\\.]+)|(?:\\\\((?:[^\\\\)]+)?\\\\))))?)|(?:(?:(?:[\\\\w\\\\*\\\\.\\\\~]+)|(?:\\\\[(?:(?:[\\\\w\\\\.\\\\*]+)?(?:\\\\[(?:[^\\\\]]+)?\\\\])?(?:\\\\,\\\\s+)?)+\\\\]))(?:[\\\\w\\\\.\\\\*]+)?)+)))\" }, { \"include\": \"#parameter-variable-types\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"comment\": \"other types\", \"match\": \"([\\\\w\\\\.]+)\" }, { \"include\": \"$self\" }] }, \"generic_types\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"\\\\w+\", \"name\": \"entity.name.type.go\" }] }, \"2\": { \"patterns\": [{ \"include\": \"#parameter-variable-types\" }] } }, \"comment\": \"Generic support for all types\", \"match\": \"(?:([\\\\w\\\\.\\\\*]+)(\\\\[(?:[^\\\\]]+)?\\\\]))\" }, \"group-functions\": { \"comment\": \"all statements related to functions\", \"patterns\": [{ \"include\": \"#function_declaration\" }, { \"include\": \"#functions_inline\" }, { \"include\": \"#functions\" }, { \"include\": \"#built_in_functions\" }, { \"include\": \"#support_functions\" }] }, \"group-types\": { \"comment\": \"all statements related to types\", \"patterns\": [{ \"include\": \"#other_struct_interface_expressions\" }, { \"include\": \"#struct_variables_types\" }, { \"include\": \"#interface_variables_types\" }, { \"include\": \"#single_type\" }, { \"include\": \"#multi_types\" }, { \"include\": \"#struct_interface_declaration\" }, { \"include\": \"#double_parentheses_types\" }, { \"include\": \"#switch_types\" }, { \"include\": \"#type-declarations\" }] }, \"group-variables\": { \"comment\": \"all statements related to variables\", \"patterns\": [{ \"include\": \"#after_control_variables\" }, { \"include\": \"#var_const_single_assignment\" }, { \"include\": \"#var_assignment\" }, { \"include\": \"#var_other_assignment\" }, { \"include\": \"#var_const_multi_assignment\" }, { \"include\": \"#other_variables\" }] }, \"import\": { \"comment\": \"import\", \"patterns\": [{ \"begin\": \"\\\\b(import)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.import.go\" } }, \"comment\": \"import\", \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"include\": \"#imports\" }] }] }, \"imports\": { \"comment\": \"import package(s)\", \"patterns\": [{ \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#delimiters\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"variable.other.import.go\" }] }, \"2\": { \"name\": \"string.quoted.double.go\" }, \"3\": { \"name\": \"punctuation.definition.string.begin.go\" }, \"4\": { \"name\": \"entity.name.import.go\" }, \"5\": { \"name\": \"punctuation.definition.string.end.go\" } }, \"match\": '(\\\\s*[\\\\w\\\\.]+)?\\\\s*((\")([^\"]*)(\"))' }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.imports.begin.bracket.round.go\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.imports.end.bracket.round.go\" } }, \"patterns\": [{ \"include\": \"#comments\" }, { \"include\": \"#imports\" }] }, { \"include\": \"$self\" }] }, \"interface_variables_types\": { \"patterns\": [{ \"begin\": \"(\\\\binterface\\\\b)\\\\s*(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.interface.go\" }, \"2\": { \"name\": \"punctuation.definition.begin.bracket.curly.go\" } }, \"comment\": \"interface variable types\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.curly.go\" } }, \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.begin.bracket.round.go\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.round.go\" } }, \"patterns\": [{ \"include\": \"#function_param_types\" }, { \"include\": \"$self\" }] }, { \"include\": \"#support_functions\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"include\": \"#generic_types\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"comment\": \"single declaration | with or declarations\", \"match\": \"((?:\\\\s+\\\\|)?(?:[\\\\w\\\\.\\\\[\\\\]\\\\*]+)(?:\\\\s+\\\\|)?)\" }, { \"include\": \"$self\" }] }] }, \"keywords\": { \"patterns\": [{ \"comment\": \"Flow control keywords\", \"match\": \"\\\\b(break|case|continue|default|defer|else|fallthrough|for|go|goto|if|range|return|select|switch)\\\\b\", \"name\": \"keyword.control.go\" }, { \"match\": \"\\\\bchan\\\\b\", \"name\": \"keyword.channel.go\" }, { \"match\": \"\\\\bconst\\\\b\", \"name\": \"keyword.const.go\" }, { \"match\": \"\\\\bvar\\\\b\", \"name\": \"keyword.var.go\" }, { \"match\": \"\\\\bfunc\\\\b\", \"name\": \"keyword.function.go\" }, { \"match\": \"\\\\binterface\\\\b\", \"name\": \"keyword.interface.go\" }, { \"match\": \"\\\\bmap\\\\b\", \"name\": \"keyword.map.go\" }, { \"match\": \"\\\\bstruct\\\\b\", \"name\": \"keyword.struct.go\" }, { \"match\": \"\\\\bimport\\\\b\", \"name\": \"keyword.control.import.go\" }, { \"match\": \"\\\\btype\\\\b\", \"name\": \"keyword.type.go\" }] }, \"label_loop_variable\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"\\\\w+\", \"name\": \"variable.other.label.go\" }] } }, \"comment\": \"labeled loop variable name\", \"match\": \"((?:^\\\\s*\\\\w+:\\\\s*$)|(?:^\\\\s*(?:\\\\bbreak\\\\b|\\\\bgoto\\\\b|\\\\bcontinue\\\\b)\\\\s+\\\\w+(?:\\\\s*/(?:/|\\\\*)\\\\s*.*)?$))\" }, \"language_constants\": { \"comment\": \"Language constants\", \"match\": \"\\\\b(true|false|nil|iota)\\\\b\", \"name\": \"constant.language.go\" }, \"map_other_types\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.map.go\" }, \"2\": { \"patterns\": [{ \"include\": \"#type-declarations-without-brackets\" }, { \"match\": \"\\\\[\", \"name\": \"punctuation.definition.begin.bracket.square.go\" }, { \"match\": \"\\\\]\", \"name\": \"punctuation.definition.end.bracket.square.go\" }, { \"match\": \"\\\\{\", \"name\": \"punctuation.definition.begin.bracket.curly.go\" }, { \"match\": \"\\\\}\", \"name\": \"punctuation.definition.end.bracket.curly.go\" }, { \"match\": \"\\\\(\", \"name\": \"punctuation.definition.begin.bracket.round.go\" }, { \"match\": \"\\\\)\", \"name\": \"punctuation.definition.end.bracket.round.go\" }, { \"match\": \"\\\\w+\", \"name\": \"entity.name.type.go\" }] }, \"3\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"\\\\w+\", \"name\": \"entity.name.type.go\" }] } }, \"comment\": \"Other map type names (struct/interface)\", \"match\": \"(?:(\\\\bmap\\\\b)(\\\\[(?:.*)\\\\])?((?!\\\\bfunc\\\\b|\\\\bstruct\\\\b)[\\\\w\\\\.\\\\*]+)?)\" }] }, \"multi_types\": { \"patterns\": [{ \"begin\": \"(\\\\btype\\\\b)\\\\s*(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.type.go\" }, \"2\": { \"name\": \"punctuation.definition.begin.bracket.round.go\" } }, \"comment\": \"multi type declaration\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.round.go\" } }, \"patterns\": [{ \"include\": \"#struct_variables_types\" }, { \"include\": \"#type-declarations-without-brackets\" }, { \"include\": \"#parameter-variable-types\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] }, { \"begin\": \"(?:(?:^|\\\\s+)(\\\\btype\\\\b)(?:\\\\s*)([\\\\w\\\\.\\\\*]+)(?=\\\\[))\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.type.go\" }, \"2\": { \"patterns\": [{ \"include\": \"#type-declarations-without-brackets\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"comment\": \"single type declaration with multi-lines generics\", \"end\": \"(?:(?<=\\\\])((?:\\\\s+)(?:(?:(?:[\\\\*\\\\[\\\\]]+)?(?:\\\\<\\\\-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*\\\\<\\\\-)?\\\\s*)+)?(?:(?!(?:[\\\\[\\\\]\\\\*]+)?(?:\\\\bstruct\\\\b|\\\\binterface\\\\b|\\\\bfunc\\\\b))[\\\\w\\\\.\\\\-\\\\*\\\\[\\\\]]+))?)\", \"endCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations-without-brackets\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"patterns\": [{ \"include\": \"#struct_variables_types\" }, { \"include\": \"#type-declarations-without-brackets\" }, { \"include\": \"#parameter-variable-types\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] }] }, \"numeric_literals\": { \"captures\": { \"0\": { \"patterns\": [{ \"begin\": \"(?=.)\", \"end\": \"(?:\\\\n|$)\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"constant.numeric.decimal.go\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"punctuation.separator.constant.numeric.go\" }] }, \"2\": { \"name\": \"punctuation.separator.constant.numeric.go\" }, \"3\": { \"name\": \"constant.numeric.decimal.point.go\" }, \"4\": { \"name\": \"constant.numeric.decimal.go\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"punctuation.separator.constant.numeric.go\" }] }, \"5\": { \"name\": \"punctuation.separator.constant.numeric.go\" }, \"6\": { \"name\": \"keyword.other.unit.exponent.decimal.go\" }, \"7\": { \"name\": \"keyword.operator.plus.exponent.decimal.go\" }, \"8\": { \"name\": \"keyword.operator.minus.exponent.decimal.go\" }, \"9\": { \"name\": \"constant.numeric.exponent.decimal.go\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"punctuation.separator.constant.numeric.go\" }] }, \"10\": { \"name\": \"keyword.other.unit.imaginary.go\" }, \"11\": { \"name\": \"constant.numeric.decimal.go\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"punctuation.separator.constant.numeric.go\" }] }, \"12\": { \"name\": \"punctuation.separator.constant.numeric.go\" }, \"13\": { \"name\": \"keyword.other.unit.exponent.decimal.go\" }, \"14\": { \"name\": \"keyword.operator.plus.exponent.decimal.go\" }, \"15\": { \"name\": \"keyword.operator.minus.exponent.decimal.go\" }, \"16\": { \"name\": \"constant.numeric.exponent.decimal.go\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"punctuation.separator.constant.numeric.go\" }] }, \"17\": { \"name\": \"keyword.other.unit.imaginary.go\" }, \"18\": { \"name\": \"constant.numeric.decimal.point.go\" }, \"19\": { \"name\": \"constant.numeric.decimal.go\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"punctuation.separator.constant.numeric.go\" }] }, \"20\": { \"name\": \"punctuation.separator.constant.numeric.go\" }, \"21\": { \"name\": \"keyword.other.unit.exponent.decimal.go\" }, \"22\": { \"name\": \"keyword.operator.plus.exponent.decimal.go\" }, \"23\": { \"name\": \"keyword.operator.minus.exponent.decimal.go\" }, \"24\": { \"name\": \"constant.numeric.exponent.decimal.go\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"punctuation.separator.constant.numeric.go\" }] }, \"25\": { \"name\": \"keyword.other.unit.imaginary.go\" }, \"26\": { \"name\": \"keyword.other.unit.hexadecimal.go\" }, \"27\": { \"name\": \"constant.numeric.hexadecimal.go\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"punctuation.separator.constant.numeric.go\" }] }, \"28\": { \"name\": \"punctuation.separator.constant.numeric.go\" }, \"29\": { \"name\": \"constant.numeric.hexadecimal.go\" }, \"30\": { \"name\": \"constant.numeric.hexadecimal.go\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"punctuation.separator.constant.numeric.go\" }] }, \"31\": { \"name\": \"punctuation.separator.constant.numeric.go\" }, \"32\": { \"name\": \"keyword.other.unit.exponent.hexadecimal.go\" }, \"33\": { \"name\": \"keyword.operator.plus.exponent.hexadecimal.go\" }, \"34\": { \"name\": \"keyword.operator.minus.exponent.hexadecimal.go\" }, \"35\": { \"name\": \"constant.numeric.exponent.hexadecimal.go\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"punctuation.separator.constant.numeric.go\" }] }, \"36\": { \"name\": \"keyword.other.unit.imaginary.go\" }, \"37\": { \"name\": \"keyword.other.unit.hexadecimal.go\" }, \"38\": { \"name\": \"constant.numeric.hexadecimal.go\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"punctuation.separator.constant.numeric.go\" }] }, \"39\": { \"name\": \"punctuation.separator.constant.numeric.go\" }, \"40\": { \"name\": \"keyword.other.unit.exponent.hexadecimal.go\" }, \"41\": { \"name\": \"keyword.operator.plus.exponent.hexadecimal.go\" }, \"42\": { \"name\": \"keyword.operator.minus.exponent.hexadecimal.go\" }, \"43\": { \"name\": \"constant.numeric.exponent.hexadecimal.go\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"punctuation.separator.constant.numeric.go\" }] }, \"44\": { \"name\": \"keyword.other.unit.imaginary.go\" }, \"45\": { \"name\": \"keyword.other.unit.hexadecimal.go\" }, \"46\": { \"name\": \"constant.numeric.hexadecimal.go\" }, \"47\": { \"name\": \"constant.numeric.hexadecimal.go\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"punctuation.separator.constant.numeric.go\" }] }, \"48\": { \"name\": \"punctuation.separator.constant.numeric.go\" }, \"49\": { \"name\": \"keyword.other.unit.exponent.hexadecimal.go\" }, \"50\": { \"name\": \"keyword.operator.plus.exponent.hexadecimal.go\" }, \"51\": { \"name\": \"keyword.operator.minus.exponent.hexadecimal.go\" }, \"52\": { \"name\": \"constant.numeric.exponent.hexadecimal.go\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"punctuation.separator.constant.numeric.go\" }] }, \"53\": { \"name\": \"keyword.other.unit.imaginary.go\" } }, \"match\": \"(?:(?:(?:(?:(?:\\\\G(?=[0-9.])(?!0[xXbBoO])([0-9](?:[0-9]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)((?:(?<=[0-9])\\\\.|\\\\.(?=[0-9])))([0-9](?:[0-9]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)?(?:(?<!_)([eE])(\\\\+?)(\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)))?(i(?!\\\\w))?(?:\\\\n|$)|\\\\G(?=[0-9.])(?!0[xXbBoO])([0-9](?:[0-9]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)(?<!_)([eE])(\\\\+?)(\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*))(i(?!\\\\w))?(?:\\\\n|$))|\\\\G((?:(?<=[0-9])\\\\.|\\\\.(?=[0-9])))([0-9](?:[0-9]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)(?:(?<!_)([eE])(\\\\+?)(\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)))?(i(?!\\\\w))?(?:\\\\n|$))|(\\\\G0[xX])_?([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)((?:(?<=[0-9a-fA-F])\\\\.|\\\\.(?=[0-9a-fA-F])))([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)?(?<!_)([pP])(\\\\+?)(\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*))(i(?!\\\\w))?(?:\\\\n|$))|(\\\\G0[xX])_?([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)(?<!_)([pP])(\\\\+?)(\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*))(i(?!\\\\w))?(?:\\\\n|$))|(\\\\G0[xX])((?:(?<=[0-9a-fA-F])\\\\.|\\\\.(?=[0-9a-fA-F])))([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)(?<!_)([pP])(\\\\+?)(\\\\-?)((?:[0-9](?:[0-9]|(?:(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*))(i(?!\\\\w))?(?:\\\\n|$))\" }, { \"captures\": { \"1\": { \"name\": \"constant.numeric.decimal.go\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"punctuation.separator.constant.numeric.go\" }] }, \"2\": { \"name\": \"punctuation.separator.constant.numeric.go\" }, \"3\": { \"name\": \"keyword.other.unit.imaginary.go\" }, \"4\": { \"name\": \"keyword.other.unit.binary.go\" }, \"5\": { \"name\": \"constant.numeric.binary.go\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"punctuation.separator.constant.numeric.go\" }] }, \"6\": { \"name\": \"punctuation.separator.constant.numeric.go\" }, \"7\": { \"name\": \"keyword.other.unit.imaginary.go\" }, \"8\": { \"name\": \"keyword.other.unit.octal.go\" }, \"9\": { \"name\": \"constant.numeric.octal.go\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"punctuation.separator.constant.numeric.go\" }] }, \"10\": { \"name\": \"punctuation.separator.constant.numeric.go\" }, \"11\": { \"name\": \"keyword.other.unit.imaginary.go\" }, \"12\": { \"name\": \"keyword.other.unit.hexadecimal.go\" }, \"13\": { \"name\": \"constant.numeric.hexadecimal.go\", \"patterns\": [{ \"match\": \"(?<=[0-9a-fA-F])_(?=[0-9a-fA-F])\", \"name\": \"punctuation.separator.constant.numeric.go\" }] }, \"14\": { \"name\": \"punctuation.separator.constant.numeric.go\" }, \"15\": { \"name\": \"keyword.other.unit.imaginary.go\" } }, \"match\": \"(?:(?:(?:\\\\G(?=[0-9.])(?!0[xXbBoO])([0-9](?:[0-9]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)(i(?!\\\\w))?(?:\\\\n|$)|(\\\\G0[bB])_?([01](?:[01]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)(i(?!\\\\w))?(?:\\\\n|$))|(\\\\G0[oO]?)_?((?:[0-7]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))+)(i(?!\\\\w))?(?:\\\\n|$))|(\\\\G0[xX])_?([0-9a-fA-F](?:[0-9a-fA-F]|((?<=[0-9a-fA-F])_(?=[0-9a-fA-F])))*)(i(?!\\\\w))?(?:\\\\n|$))\" }, { \"match\": \"(?:(?:[0-9a-zA-Z_\\\\.])|(?<=[eEpP])[+-])+\", \"name\": \"invalid.illegal.constant.numeric.go\" }] }] } }, \"match\": \"(?<!\\\\w)\\\\.?\\\\d(?:(?:[0-9a-zA-Z_\\\\.])|(?<=[eEpP])[+-])*\" }, \"operators\": { \"comment\": \"Note that the order here is very important!\", \"patterns\": [{ \"match\": \"((?:\\\\*|&)+)(?:(?!\\\\d)(?=(?:[\\\\w\\\\[\\\\]])|(?:\\\\<\\\\-)))\", \"name\": \"keyword.operator.address.go\" }, { \"match\": \"<\\\\-\", \"name\": \"keyword.operator.channel.go\" }, { \"match\": \"\\\\-\\\\-\", \"name\": \"keyword.operator.decrement.go\" }, { \"match\": \"\\\\+\\\\+\", \"name\": \"keyword.operator.increment.go\" }, { \"match\": \"(==|!=|<=|>=|<(?!<)|>(?!>))\", \"name\": \"keyword.operator.comparison.go\" }, { \"match\": \"(&&|\\\\|\\\\||!)\", \"name\": \"keyword.operator.logical.go\" }, { \"match\": \"(=|\\\\+=|\\\\-=|\\\\|=|\\\\^=|\\\\*=|/=|:=|%=|<<=|>>=|&\\\\^=|&=)\", \"name\": \"keyword.operator.assignment.go\" }, { \"match\": \"(\\\\+|\\\\-|\\\\*|/|%)\", \"name\": \"keyword.operator.arithmetic.go\" }, { \"match\": \"(&(?!\\\\^)|\\\\||\\\\^|&\\\\^|<<|>>|\\\\~)\", \"name\": \"keyword.operator.arithmetic.bitwise.go\" }, { \"match\": \"\\\\.\\\\.\\\\.\", \"name\": \"keyword.operator.ellipsis.go\" }] }, \"other_struct_interface_expressions\": { \"patterns\": [{ \"include\": \"#storage_types\" }, { \"include\": \"#label_loop_variable\" }, { \"include\": \"#property_variables\" }, { \"include\": \"#switch_select_case_variables\" }, { \"include\": \"#after_control_variables\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"\\\\w+\", \"name\": \"entity.name.type.go\" }] }, \"2\": { \"patterns\": [{ \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.begin.bracket.square.go\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.square.go\" } }, \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"\\\\w+\", \"name\": \"entity.name.type.go\" }, { \"include\": \"$self\" }] }] } }, \"comment\": \"struct expression before curly bracket\", \"match\": \"((?:(?:\\\\w+\\\\.)+)?\\\\w+)(\\\\[(?:[^\\\\]]+)?\\\\])?(?=\\\\{)(?<!\\\\bstruct\\\\b|\\\\binterface\\\\b|\\\\bnil\\\\b|\\\\belse\\\\b|\\\\bif\\\\b|\\\\bfor\\\\b|\\\\bselect\\\\b|\\\\bswitch\\\\b|\\\\brange\\\\b|\\\\bcase\\\\b|\\\\bgo\\\\b|\\\\bdefault\\\\b|\\\\bdefer\\\\b|\\\\breturn\\\\b)\" }, { \"captures\": { \"1\": { \"name\": \"keyword.type.go\" }, \"2\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"comment\": \"struct/interface types in-line (type assertion) | switch type keyword\", \"match\": \"(?:(?<=\\\\.\\\\()(?:(\\\\btype\\\\b)|((?:(?:\\\\s*(?:[\\\\*\\\\[\\\\]]+)?(?:\\\\<\\\\-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*\\\\<\\\\-)?\\\\s*)+)?[\\\\w\\\\.\\\\[\\\\]\\\\*]+))(?=\\\\)))\" }] }, \"other_variables\": { \"match\": \"(?:\\\\w+)\", \"name\": \"variable.other.go\", \"patterns\": [{ \"include\": \"#storage_types\" }, { \"include\": \"$self\" }] }, \"package_name\": { \"patterns\": [{ \"begin\": \"\\\\b(package)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.package.go\" } }, \"comment\": \"package name\", \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"match\": \"\\\\d\\\\w*\", \"name\": \"invalid.illegal.identifier.go\" }, { \"match\": \"\\\\w+\", \"name\": \"entity.name.type.package.go\" }] }] }, \"parameter-variable-types\": { \"comment\": \"function and generic parameter types\", \"patterns\": [{ \"match\": \"\\\\{\", \"name\": \"punctuation.definition.begin.bracket.curly.go\" }, { \"match\": \"\\\\}\", \"name\": \"punctuation.definition.end.bracket.curly.go\" }, { \"begin\": \"(?:([\\\\w\\\\.\\\\*]+)?(\\\\[))\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] }, \"2\": { \"name\": \"punctuation.definition.begin.bracket.square.go\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.square.go\" } }, \"patterns\": [{ \"include\": \"#generic_param_types\" }] }, { \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.begin.bracket.round.go\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.round.go\" } }, \"patterns\": [{ \"include\": \"#function_param_types\" }] }] }, \"property_variables\": { \"patterns\": [{ \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"\\\\w+\", \"name\": \"variable.other.property.go\" }] } }, \"comment\": \"Property variables in struct\", \"match\": \"(?:((?:[\\\\w\\\\.]+)(?:\\\\:))(?!\\\\=))\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"$self\" }] }, \"2\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"variable.other.property.field.go\" }] } }, \"comment\": \"property variables as parameter field in struct initialization\", \"match\": \"(?<=[\\\\w\\\\.]\\\\:)(?:\\\\s*)([\\\\w\\\\.\\\\*\\\\&\\\\[\\\\]]+)(\\\\.\\\\w+)(?![\\\\w\\\\.\\\\*\\\\&\\\\[\\\\]]*(?:\\\\{|\\\\())\" }] }, \"raw_strings_literals\": { \"begin\": \"`\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.go\" } }, \"comment\": \"Raw string literals\", \"end\": \"`\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.go\" } }, \"name\": \"string.quoted.raw.go\", \"patterns\": [{ \"include\": \"#string_placeholder\" }] }, \"runes\": { \"patterns\": [{ \"begin\": \"'\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.go\" } }, \"end\": \"'\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.go\" } }, \"name\": \"string.quoted.rune.go\", \"patterns\": [{ \"match\": `\\\\G(\\\\\\\\([0-7]{3}|[abfnrtv\\\\\\\\'\"]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4}|U[0-9a-fA-F]{8})|.)(?=')`, \"name\": \"constant.other.rune.go\" }, { \"match\": \"[^']+\", \"name\": \"invalid.illegal.unknown-rune.go\" }] }] }, \"single_type\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.type.go\" }, \"2\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] }, \"3\": { \"patterns\": [{ \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.begin.bracket.square.go\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.square.go\" } }, \"patterns\": [{ \"include\": \"#generic_param_types\" }, { \"include\": \"$self\" }] }] }, \"4\": { \"patterns\": [{ \"begin\": \"\\\\(\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.begin.bracket.round.go\" } }, \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.round.go\" } }, \"patterns\": [{ \"include\": \"#function_param_types\" }, { \"include\": \"$self\" }] }, { \"include\": \"#type-declarations\" }, { \"include\": \"#generic_types\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"comment\": \"single type declaration\", \"match\": \"(?:(?:^|\\\\s+)(\\\\btype\\\\b)(?:\\\\s*)([\\\\w\\\\.\\\\*]+)(\\\\[(?:.*)\\\\])?(?:\\\\s+)((?!(?:[\\\\[\\\\]\\\\*]+)?(?:\\\\bstruct\\\\b|\\\\binterface\\\\b))([\\\\s\\\\S]+)))\" }] }, \"statements\": { \"patterns\": [{ \"include\": \"#package_name\" }, { \"include\": \"#import\" }, { \"include\": \"#syntax_errors\" }, { \"include\": \"#group-functions\" }, { \"include\": \"#group-types\" }, { \"include\": \"#group-variables\" }] }, \"storage_types\": { \"patterns\": [{ \"match\": \"\\\\bbool\\\\b\", \"name\": \"storage.type.boolean.go\" }, { \"match\": \"\\\\bbyte\\\\b\", \"name\": \"storage.type.byte.go\" }, { \"match\": \"\\\\berror\\\\b\", \"name\": \"storage.type.error.go\" }, { \"match\": \"\\\\b(complex(64|128)|float(32|64)|u?int(8|16|32|64)?)\\\\b\", \"name\": \"storage.type.numeric.go\" }, { \"match\": \"\\\\brune\\\\b\", \"name\": \"storage.type.rune.go\" }, { \"match\": \"\\\\bstring\\\\b\", \"name\": \"storage.type.string.go\" }, { \"match\": \"\\\\buintptr\\\\b\", \"name\": \"storage.type.uintptr.go\" }, { \"match\": \"\\\\bany\\\\b\", \"name\": \"entity.name.type.any.go\" }] }, \"string_escaped_char\": { \"patterns\": [{ \"match\": `\\\\\\\\([0-7]{3}|[abfnrtv\\\\\\\\'\"]|x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4}|U[0-9a-fA-F]{8})`, \"name\": \"constant.character.escape.go\" }, { \"match\": `\\\\\\\\[^0-7xuUabfnrtv\\\\'\"]`, \"name\": \"invalid.illegal.unknown-escape.go\" }] }, \"string_literals\": { \"patterns\": [{ \"begin\": '\"', \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.begin.go\" } }, \"comment\": \"Interpreted string literals\", \"end\": '\"', \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.string.end.go\" } }, \"name\": \"string.quoted.double.go\", \"patterns\": [{ \"include\": \"#string_escaped_char\" }, { \"include\": \"#string_placeholder\" }] }] }, \"string_placeholder\": { \"patterns\": [{ \"match\": \"%(\\\\[\\\\d+\\\\])?([\\\\+#\\\\-0\\\\x20]{,2}((\\\\d+|\\\\*)?(\\\\.?(\\\\d+|\\\\*|(\\\\[\\\\d+\\\\])\\\\*?)?(\\\\[\\\\d+\\\\])?)?))?[vT%tbcdoqxXUbeEfFgGspw]\", \"name\": \"constant.other.placeholder.go\" }] }, \"struct_interface_declaration\": { \"captures\": { \"1\": { \"name\": \"keyword.type.go\" }, \"2\": { \"patterns\": [{ \"match\": \"\\\\w+\", \"name\": \"entity.name.type.go\" }] }, \"3\": { \"patterns\": [{ \"begin\": \"\\\\[\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.begin.bracket.square.go\" } }, \"end\": \"\\\\]\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.square.go\" } }, \"patterns\": [{ \"include\": \"#generic_param_types\" }, { \"include\": \"$self\" }] }] } }, \"comment\": \"struct, interface type declarations\", \"match\": \"(?:(?:^|\\\\s+)(\\\\btype\\\\b)(?:\\\\s+)([\\\\w\\\\.]+)(\\\\[(?:.*)\\\\])?)\" }, \"struct_variable_types_fields_multi\": { \"patterns\": [{ \"begin\": \"(?:\\\\s*)?([\\\\s\\\\,\\\\w]+)(?:\\\\s+)(?:(?:[\\\\[\\\\]\\\\*])+)?(\\\\bstruct\\\\b)\\\\s*(\\\\{)\", \"beginCaptures\": { \"1\": { \"match\": \"(?:\\\\w+)\", \"name\": \"variable.other.property.go\" }, \"2\": { \"name\": \"keyword.struct.go\" }, \"3\": { \"name\": \"punctuation.definition.begin.bracket.curly.go\" } }, \"comment\": \"Struct variable for struct in struct types\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.curly.go\" } }, \"patterns\": [{ \"include\": \"#struct_variables_types_fields\" }, { \"include\": \"$self\" }] }] }, \"struct_variables_types\": { \"patterns\": [{ \"begin\": \"(\\\\bstruct\\\\b)\\\\s*(\\\\{)\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.struct.go\" }, \"2\": { \"name\": \"punctuation.definition.begin.bracket.curly.go\" } }, \"comment\": \"Struct variable type\", \"end\": \"\\\\}\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.curly.go\" } }, \"patterns\": [{ \"include\": \"#struct_variables_types_fields\" }, { \"include\": \"$self\" }] }] }, \"struct_variables_types_fields\": { \"comment\": \"Struct variable type fields\", \"patterns\": [{ \"include\": \"#struct_variable_types_fields_multi\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"comment\": \"one line - single type\", \"match\": \"(?:(?<=\\\\{)\\\\s*((?:(?:\\\\s*(?:[\\\\*\\\\[\\\\]]+)?(?:\\\\<\\\\-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*\\\\<\\\\-)?\\\\s*)+)?(?:[\\\\w\\\\.\\\\*\\\\[\\\\]]+))\\\\s*(?=\\\\}))\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"variable.other.property.go\" }] }, \"2\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"comment\": \"one line - property variables and types\", \"match\": \"(?:(?<=\\\\{)\\\\s*((?:(?:\\\\w+\\\\,\\\\s*)+)?(?:\\\\w+\\\\s+))((?:(?:\\\\s*(?:[\\\\*\\\\[\\\\]]+)?(?:\\\\<\\\\-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*\\\\<\\\\-)?\\\\s*)+)?(?:[\\\\w\\\\.\\\\*\\\\[\\\\]]+))\\\\s*(?=\\\\}))\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"variable.other.property.go\" }] }, \"2\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"match\": \"(?:((?:(?:\\\\w+\\\\,\\\\s*)+)?(?:\\\\w+\\\\s+))?((?:(?:\\\\s*(?:[\\\\*\\\\[\\\\]]+)?(?:\\\\<\\\\-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*\\\\<\\\\-)?\\\\s*)+)?(?:[\\\\w\\\\.\\\\*\\\\[\\\\]]+)(?:\\\\;)?))\" }] } }, \"comment\": \"one line with semicolon(;) without formatting gofmt - single type | property variables and types\", \"match\": \"(?:(?<=\\\\{)((?:\\\\s*(?:(?:(?:\\\\w+\\\\,\\\\s*)+)?(?:\\\\w+\\\\s+))?(?:(?:(?:\\\\s*(?:[\\\\*\\\\[\\\\]]+)?(?:\\\\<\\\\-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*\\\\<\\\\-)?\\\\s*)+)?(?:[\\\\w\\\\.\\\\*\\\\[\\\\]\\\\(\\\\)\\\\{\\\\}]+)(?:\\\\;)?))+)\\\\s*(?=\\\\}))\" }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"comment\": \"one type only\", \"match\": '(?:((?:(?:\\\\s*(?:[\\\\*\\\\[\\\\]]+)?(?:\\\\<\\\\-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*\\\\<\\\\-)?\\\\s*)+)?(?<!\\\\,\\\\s*)(?:[\\\\w\\\\.\\\\*]+)\\\\s*(?:(?:(?:\".*\")|(?:\\\\`.*\\\\`))\\\\s*)?(?:/(?:/|\\\\*).*)?)$)' }, { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"variable.other.property.go\" }] }, \"2\": { \"patterns\": [{ \"include\": \"#type-declarations-without-brackets\" }, { \"include\": \"#parameter-variable-types\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"comment\": \"property variables and types\", \"match\": \"(?:((?:(?:\\\\w+\\\\,\\\\s*)+)?(?:\\\\w+\\\\s+))([\\\\s\\\\S]+))\" }] }, \"support_functions\": { \"captures\": { \"1\": { \"name\": \"entity.name.function.support.go\" }, \"2\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"\\\\d\\\\w*\", \"name\": \"invalid.illegal.identifier.go\" }, { \"match\": \"\\\\w+\", \"name\": \"entity.name.function.support.go\" }] }, \"3\": { \"patterns\": [{ \"include\": \"#type-declarations-without-brackets\" }, { \"match\": \"\\\\[\", \"name\": \"punctuation.definition.begin.bracket.square.go\" }, { \"match\": \"\\\\]\", \"name\": \"punctuation.definition.end.bracket.square.go\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"comment\": \"Support Functions\", \"match\": \"(?:(?:((?<=\\\\.)\\\\w+)|(\\\\w+))(\\\\[(?:.*)?\\\\])?(?=\\\\())\" }, \"switch_select_case_variables\": { \"captures\": { \"1\": { \"name\": \"keyword.control.go\" }, \"2\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"include\": \"#support_functions\" }, { \"match\": \"\\\\w+\", \"name\": \"variable.other.go\" }] } }, \"comment\": \"variables after case control keyword in switch/select expression\", \"match\": \"(?:(?:^\\\\s*(\\\\bcase\\\\b))(?:\\\\s+)([\\\\s\\\\S]+(?:\\\\:)\\\\s*(?:/(?:/|\\\\*).*)?)$)\" }, \"switch_types\": { \"begin\": \"(?<=\\\\bswitch\\\\b)(?:\\\\s*)(?:(\\\\w+\\\\s*\\\\:\\\\=)?\\\\s*([\\\\w\\\\.\\\\*\\\\(\\\\)\\\\[\\\\]]+))(\\\\.\\\\(\\\\btype\\\\b\\\\)\\\\s*)(\\\\{)\", \"beginCaptures\": { \"1\": { \"patterns\": [{ \"include\": \"#operators\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"variable.other.assignment.go\" }] }, \"2\": { \"patterns\": [{ \"include\": \"#support_functions\" }, { \"include\": \"#type-declarations\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"variable.other.go\" }] }, \"3\": { \"patterns\": [{ \"include\": \"#delimiters\" }, { \"include\": \"#brackets\" }, { \"match\": \"\\\\btype\\\\b\", \"name\": \"keyword.type.go\" }] }, \"4\": { \"name\": \"punctuation.definition.begin.bracket.curly.go\" } }, \"end\": \"(?:\\\\})\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.curly.go\" } }, \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.go\" }, \"2\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"match\": \"\\\\w+\", \"name\": \"entity.name.type.go\" }] }, \"3\": { \"name\": \"punctuation.other.colon.go\" }, \"4\": { \"patterns\": [{ \"include\": \"#comments\" }] } }, \"match\": \"(?:^\\\\s*(\\\\bcase\\\\b))(?:\\\\s+)([\\\\w\\\\.\\\\,\\\\*\\\\=\\\\<\\\\>\\\\!\\\\s]+)(:)(\\\\s*/(?:/|\\\\*)\\\\s*.*)?$\" }, { \"include\": \"$self\" }] }, \"syntax_errors\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"invalid.illegal.slice.go\" } }, \"comment\": \"Syntax error using slices\", \"match\": \"\\\\[\\\\](\\\\s+)\" }, { \"comment\": \"Syntax error numeric literals\", \"match\": \"\\\\b0[0-7]*[89]\\\\d*\\\\b\", \"name\": \"invalid.illegal.numeric.go\" }] }, \"terminators\": { \"comment\": \"Terminators\", \"match\": \";\", \"name\": \"punctuation.terminator.go\" }, \"type-declarations\": { \"comment\": \"includes all type declarations\", \"patterns\": [{ \"include\": \"#language_constants\" }, { \"include\": \"#comments\" }, { \"include\": \"#map_other_types\" }, { \"include\": \"#brackets\" }, { \"include\": \"#delimiters\" }, { \"include\": \"#keywords\" }, { \"include\": \"#operators\" }, { \"include\": \"#runes\" }, { \"include\": \"#storage_types\" }, { \"include\": \"#raw_strings_literals\" }, { \"include\": \"#string_literals\" }, { \"include\": \"#numeric_literals\" }, { \"include\": \"#terminators\" }] }, \"type-declarations-without-brackets\": { \"comment\": \"includes all type declarations without brackets (in some cases, brackets need to be captured manually)\", \"patterns\": [{ \"include\": \"#language_constants\" }, { \"include\": \"#comments\" }, { \"include\": \"#map_other_types\" }, { \"include\": \"#delimiters\" }, { \"include\": \"#keywords\" }, { \"include\": \"#operators\" }, { \"include\": \"#runes\" }, { \"include\": \"#storage_types\" }, { \"include\": \"#raw_strings_literals\" }, { \"include\": \"#string_literals\" }, { \"include\": \"#numeric_literals\" }, { \"include\": \"#terminators\" }] }, \"var_assignment\": { \"captures\": { \"1\": { \"patterns\": [{ \"match\": \"\\\\d\\\\w*\", \"name\": \"invalid.illegal.identifier.go\" }, { \"captures\": { \"0\": { \"patterns\": [{ \"include\": \"#delimiters\" }] } }, \"match\": \"\\\\w+(?:\\\\.\\\\w+)*\", \"name\": \"variable.other.assignment.go\" }, { \"include\": \"#delimiters\" }] } }, \"comment\": \"variable assignment\", \"match\": \"(?<!var)\\\\s*(\\\\w+(?:\\\\.\\\\w+)*(?>,\\\\s*\\\\w+(?:\\\\.\\\\w+)*)*)(?=\\\\s*=(?!=))\" }, \"var_const_multi_assignment\": { \"patterns\": [{ \"begin\": \"(?:(?<=\\\\bvar\\\\b|\\\\bconst\\\\b)(?:\\\\s*)(\\\\())\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.begin.bracket.round.go\" } }, \"comment\": \"var and const with multi type assignment\", \"end\": \"\\\\)\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.end.bracket.round.go\" } }, \"patterns\": [{ \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#delimiters\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"variable.other.assignment.go\" }] }, \"2\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"include\": \"#generic_types\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"match\": \"(?:(?:^\\\\s+)([\\\\w\\\\.]+(?:(?:\\\\,\\\\s*[\\\\w\\\\.]+)+)?)(?:\\\\s*)((?:(?!(?:(?:[\\\\[\\\\]\\\\*]+)?\\\\bstruct\\\\b\\\\s*\\\\{)|(?:(?:[\\\\[\\\\]\\\\*]+)?\\\\bfunc\\\\b))((?:(?:(?:[\\\\*\\\\[\\\\]]+)?(?:\\\\<\\\\-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*\\\\<\\\\-)?\\\\s*)+)?(?:(?:(?:[\\\\w\\\\.\\\\*]+)?(?:\\\\[(?:.*)\\\\])?(?:[\\\\w\\\\.\\\\*]+)?))?\\\\s*(?:\\\\=)?))?))\" }, { \"include\": \"$self\" }] }] }, \"var_const_single_assignment\": { \"captures\": { \"1\": { \"patterns\": [{ \"include\": \"#delimiters\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"variable.other.assignment.go\" }] }, \"2\": { \"patterns\": [{ \"include\": \"#type-declarations\" }, { \"include\": \"#generic_types\" }, { \"match\": \"(?:\\\\w+)\", \"name\": \"entity.name.type.go\" }] } }, \"comment\": \"var and const with single type assignment\", \"match\": \"(?:(?<=\\\\bvar\\\\b|\\\\bconst\\\\b)(?:\\\\s*)([\\\\w\\\\.]+(?:(?:\\\\,\\\\s*[\\\\w\\\\.]+)+)?)(?:\\\\s*)(?:(?!(?:(?:[\\\\[\\\\]\\\\*]+)?\\\\bstruct\\\\b\\\\s*\\\\{)|(?:(?:[\\\\[\\\\]\\\\*]+)?\\\\bfunc\\\\b))((?:(?:(?:[\\\\*\\\\[\\\\]]+)?(?:\\\\<\\\\-\\\\s*)?\\\\bchan\\\\b(?:\\\\s*\\\\<\\\\-)?\\\\s*)+)?(?:(?:(?:[\\\\w\\\\.\\\\*]+)?(?:\\\\[(?:.*)\\\\])?(?:[\\\\w\\\\.\\\\*]+)?))?\\\\s*(?:\\\\=)?))?)\" }, \"var_other_assignment\": { \"captures\": { \"0\": { \"patterns\": [{ \"match\": \"\\\\d\\\\w*\", \"name\": \"invalid.illegal.identifier.go\" }, { \"match\": \"\\\\w+\", \"name\": \"variable.other.assignment.go\" }, { \"include\": \"#delimiters\" }] } }, \"comment\": \"variable other assignment\", \"match\": \"\\\\b\\\\w+(?:,\\\\s*\\\\w+)*(?=\\\\s*:=)\" } }, \"scopeName\": \"source.go\" });\nvar go = [\n  lang\n];\n\nexport { go as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,MAAM,QAAQ,MAAM,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,GAAG,cAAc,EAAE,2BAA2B,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,sCAAsC,GAAG,EAAE,SAAS,OAAO,QAAQ,iDAAiD,GAAG,EAAE,SAAS,OAAO,QAAQ,+CAA+C,GAAG,EAAE,SAAS,YAAY,QAAQ,oBAAoB,CAAC,EAAE,EAAE,GAAG,WAAW,mEAAmE,SAAS,2NAA2N,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,sBAAsB,EAAE,WAAW,sBAAsB,YAAY,CAAC,EAAE,SAAS,+GAA+G,QAAQ,0CAA0C,GAAG,EAAE,SAAS,wBAAwB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,WAAW,eAAe,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,gDAAgD,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,sCAAsC,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,SAAS,QAAQ,QAAQ,sBAAsB,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,WAAW,gBAAgB,SAAS,yNAAyN,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,YAAY,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,YAAY,eAAe,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,QAAQ,mBAAmB,GAAG,EAAE,SAAS,YAAY,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,OAAO,aAAa,QAAQ,+BAA+B,CAAC,EAAE,GAAG,cAAc,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,QAAQ,6BAA6B,GAAG,EAAE,SAAS,iBAAiB,QAAQ,8BAA8B,GAAG,EAAE,SAAS,UAAU,QAAQ,6BAA6B,CAAC,EAAE,GAAG,4BAA4B,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,gDAAgD,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,QAAQ,QAAQ,sBAAsB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,WAAW,4BAA4B,SAAS,2DAA2D,GAAG,wBAAwB,EAAE,SAAS,uEAAuE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,sCAAsC,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,SAAS,yFAAyF,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,WAAW,QAAQ,gCAAgC,GAAG,EAAE,SAAS,QAAQ,QAAQ,0BAA0B,CAAC,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,WAAW,yBAAyB,OAAO,mLAAmL,eAAe,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,sCAAsC,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,wBAAwB,CAAC,EAAE,GAAG,EAAE,SAAS,4BAA4B,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,sCAAsC,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,WAAW,0DAA0D,SAAS,yJAAyJ,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,wBAAwB,EAAE,WAAW,0CAA0C,YAAY,CAAC,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,sCAAsC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,QAAQ,QAAQ,wBAAwB,CAAC,EAAE,EAAE,GAAG,WAAW,2BAA2B,SAAS,qJAAqJ,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,QAAQ,QAAQ,wBAAwB,CAAC,EAAE,EAAE,GAAG,WAAW,iDAAiD,SAAS,iEAAiE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,SAAS,QAAQ,QAAQ,wBAAwB,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,sCAAsC,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,WAAW,6EAA6E,SAAS,8YAA8Y,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,WAAW,eAAe,SAAS,cAAc,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,aAAa,EAAE,SAAS,2BAA2B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,sBAAsB,EAAE,GAAG,WAAW,aAAa,OAAO,qbAAqb,eAAe,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,sCAAsC,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,4BAA4B,CAAC,EAAE,GAAG,oBAAoB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,sBAAsB,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,sCAAsC,GAAG,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,QAAQ,iDAAiD,GAAG,EAAE,SAAS,OAAO,QAAQ,+CAA+C,GAAG,EAAE,SAAS,OAAO,QAAQ,gDAAgD,GAAG,EAAE,SAAS,OAAO,QAAQ,8CAA8C,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,WAAW,6CAA6C,SAAS,4EAA4E,GAAG,uBAAuB,EAAE,WAAW,yCAAyC,YAAY,CAAC,EAAE,WAAW,sCAAsC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,QAAQ,QAAQ,wBAAwB,CAAC,EAAE,EAAE,GAAG,WAAW,iDAAiD,SAAS,iEAAiE,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,SAAS,QAAQ,QAAQ,wBAAwB,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,sCAAsC,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,WAAW,wDAAwD,SAAS,gbAAgb,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,WAAW,eAAe,SAAS,cAAc,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,QAAQ,QAAQ,sBAAsB,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,4BAA4B,CAAC,EAAE,EAAE,GAAG,WAAW,iCAAiC,SAAS,yCAAyC,GAAG,mBAAmB,EAAE,WAAW,uCAAuC,YAAY,CAAC,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,eAAe,EAAE,WAAW,mCAAmC,YAAY,CAAC,EAAE,WAAW,sCAAsC,GAAG,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,6BAA6B,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,qBAAqB,CAAC,EAAE,GAAG,mBAAmB,EAAE,WAAW,uCAAuC,YAAY,CAAC,EAAE,WAAW,2BAA2B,GAAG,EAAE,WAAW,+BAA+B,GAAG,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,8BAA8B,GAAG,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,UAAU,EAAE,WAAW,UAAU,YAAY,CAAC,EAAE,SAAS,mBAAmB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,WAAW,UAAU,OAAO,WAAW,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,CAAC,EAAE,GAAG,WAAW,EAAE,WAAW,qBAAqB,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,SAAS,YAAY,QAAQ,2BAA2B,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,0BAA0B,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,SAAS,sCAAsC,GAAG,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wDAAwD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,sDAAsD,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,6BAA6B,EAAE,YAAY,CAAC,EAAE,SAAS,8BAA8B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,uBAAuB,GAAG,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,WAAW,4BAA4B,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,WAAW,6CAA6C,SAAS,mDAAmD,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,WAAW,yBAAyB,SAAS,wGAAwG,QAAQ,qBAAqB,GAAG,EAAE,SAAS,cAAc,QAAQ,qBAAqB,GAAG,EAAE,SAAS,eAAe,QAAQ,mBAAmB,GAAG,EAAE,SAAS,aAAa,QAAQ,iBAAiB,GAAG,EAAE,SAAS,cAAc,QAAQ,sBAAsB,GAAG,EAAE,SAAS,mBAAmB,QAAQ,uBAAuB,GAAG,EAAE,SAAS,aAAa,QAAQ,iBAAiB,GAAG,EAAE,SAAS,gBAAgB,QAAQ,oBAAoB,GAAG,EAAE,SAAS,gBAAgB,QAAQ,4BAA4B,GAAG,EAAE,SAAS,cAAc,QAAQ,kBAAkB,CAAC,EAAE,GAAG,uBAAuB,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,QAAQ,QAAQ,0BAA0B,CAAC,EAAE,EAAE,GAAG,WAAW,8BAA8B,SAAS,6GAA6G,GAAG,sBAAsB,EAAE,WAAW,sBAAsB,SAAS,+BAA+B,QAAQ,uBAAuB,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,iBAAiB,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,sCAAsC,GAAG,EAAE,SAAS,OAAO,QAAQ,iDAAiD,GAAG,EAAE,SAAS,OAAO,QAAQ,+CAA+C,GAAG,EAAE,SAAS,OAAO,QAAQ,gDAAgD,GAAG,EAAE,SAAS,OAAO,QAAQ,8CAA8C,GAAG,EAAE,SAAS,OAAO,QAAQ,gDAAgD,GAAG,EAAE,SAAS,OAAO,QAAQ,8CAA8C,GAAG,EAAE,SAAS,QAAQ,QAAQ,sBAAsB,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,QAAQ,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,WAAW,2CAA2C,SAAS,2EAA2E,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,SAAS,yBAAyB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kBAAkB,GAAG,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,WAAW,0BAA0B,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,sCAAsC,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,GAAG,EAAE,SAAS,2DAA2D,iBAAiB,EAAE,KAAK,EAAE,QAAQ,kBAAkB,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,sCAAsC,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,WAAW,qDAAqD,OAAO,2LAA2L,eAAe,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,sCAAsC,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,0BAA0B,GAAG,EAAE,WAAW,sCAAsC,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,CAAC,EAAE,GAAG,oBAAoB,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,SAAS,OAAO,aAAa,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,+BAA+B,YAAY,CAAC,EAAE,SAAS,oCAAoC,QAAQ,4CAA4C,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,oCAAoC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,YAAY,CAAC,EAAE,SAAS,oCAAoC,QAAQ,4CAA4C,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,wCAAwC,YAAY,CAAC,EAAE,SAAS,oCAAoC,QAAQ,4CAA4C,CAAC,EAAE,GAAG,MAAM,EAAE,QAAQ,kCAAkC,GAAG,MAAM,EAAE,QAAQ,+BAA+B,YAAY,CAAC,EAAE,SAAS,oCAAoC,QAAQ,4CAA4C,CAAC,EAAE,GAAG,MAAM,EAAE,QAAQ,4CAA4C,GAAG,MAAM,EAAE,QAAQ,yCAAyC,GAAG,MAAM,EAAE,QAAQ,4CAA4C,GAAG,MAAM,EAAE,QAAQ,6CAA6C,GAAG,MAAM,EAAE,QAAQ,wCAAwC,YAAY,CAAC,EAAE,SAAS,oCAAoC,QAAQ,4CAA4C,CAAC,EAAE,GAAG,MAAM,EAAE,QAAQ,kCAAkC,GAAG,MAAM,EAAE,QAAQ,oCAAoC,GAAG,MAAM,EAAE,QAAQ,+BAA+B,YAAY,CAAC,EAAE,SAAS,oCAAoC,QAAQ,4CAA4C,CAAC,EAAE,GAAG,MAAM,EAAE,QAAQ,4CAA4C,GAAG,MAAM,EAAE,QAAQ,yCAAyC,GAAG,MAAM,EAAE,QAAQ,4CAA4C,GAAG,MAAM,EAAE,QAAQ,6CAA6C,GAAG,MAAM,EAAE,QAAQ,wCAAwC,YAAY,CAAC,EAAE,SAAS,oCAAoC,QAAQ,4CAA4C,CAAC,EAAE,GAAG,MAAM,EAAE,QAAQ,kCAAkC,GAAG,MAAM,EAAE,QAAQ,oCAAoC,GAAG,MAAM,EAAE,QAAQ,mCAAmC,YAAY,CAAC,EAAE,SAAS,oCAAoC,QAAQ,4CAA4C,CAAC,EAAE,GAAG,MAAM,EAAE,QAAQ,4CAA4C,GAAG,MAAM,EAAE,QAAQ,kCAAkC,GAAG,MAAM,EAAE,QAAQ,mCAAmC,YAAY,CAAC,EAAE,SAAS,oCAAoC,QAAQ,4CAA4C,CAAC,EAAE,GAAG,MAAM,EAAE,QAAQ,4CAA4C,GAAG,MAAM,EAAE,QAAQ,6CAA6C,GAAG,MAAM,EAAE,QAAQ,gDAAgD,GAAG,MAAM,EAAE,QAAQ,iDAAiD,GAAG,MAAM,EAAE,QAAQ,4CAA4C,YAAY,CAAC,EAAE,SAAS,oCAAoC,QAAQ,4CAA4C,CAAC,EAAE,GAAG,MAAM,EAAE,QAAQ,kCAAkC,GAAG,MAAM,EAAE,QAAQ,oCAAoC,GAAG,MAAM,EAAE,QAAQ,mCAAmC,YAAY,CAAC,EAAE,SAAS,oCAAoC,QAAQ,4CAA4C,CAAC,EAAE,GAAG,MAAM,EAAE,QAAQ,4CAA4C,GAAG,MAAM,EAAE,QAAQ,6CAA6C,GAAG,MAAM,EAAE,QAAQ,gDAAgD,GAAG,MAAM,EAAE,QAAQ,iDAAiD,GAAG,MAAM,EAAE,QAAQ,4CAA4C,YAAY,CAAC,EAAE,SAAS,oCAAoC,QAAQ,4CAA4C,CAAC,EAAE,GAAG,MAAM,EAAE,QAAQ,kCAAkC,GAAG,MAAM,EAAE,QAAQ,oCAAoC,GAAG,MAAM,EAAE,QAAQ,kCAAkC,GAAG,MAAM,EAAE,QAAQ,mCAAmC,YAAY,CAAC,EAAE,SAAS,oCAAoC,QAAQ,4CAA4C,CAAC,EAAE,GAAG,MAAM,EAAE,QAAQ,4CAA4C,GAAG,MAAM,EAAE,QAAQ,6CAA6C,GAAG,MAAM,EAAE,QAAQ,gDAAgD,GAAG,MAAM,EAAE,QAAQ,iDAAiD,GAAG,MAAM,EAAE,QAAQ,4CAA4C,YAAY,CAAC,EAAE,SAAS,oCAAoC,QAAQ,4CAA4C,CAAC,EAAE,GAAG,MAAM,EAAE,QAAQ,kCAAkC,EAAE,GAAG,SAAS,60CAA60C,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,+BAA+B,YAAY,CAAC,EAAE,SAAS,oCAAoC,QAAQ,4CAA4C,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,+BAA+B,GAAG,KAAK,EAAE,QAAQ,8BAA8B,YAAY,CAAC,EAAE,SAAS,oCAAoC,QAAQ,4CAA4C,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,6BAA6B,YAAY,CAAC,EAAE,SAAS,oCAAoC,QAAQ,4CAA4C,CAAC,EAAE,GAAG,MAAM,EAAE,QAAQ,4CAA4C,GAAG,MAAM,EAAE,QAAQ,kCAAkC,GAAG,MAAM,EAAE,QAAQ,oCAAoC,GAAG,MAAM,EAAE,QAAQ,mCAAmC,YAAY,CAAC,EAAE,SAAS,oCAAoC,QAAQ,4CAA4C,CAAC,EAAE,GAAG,MAAM,EAAE,QAAQ,4CAA4C,GAAG,MAAM,EAAE,QAAQ,kCAAkC,EAAE,GAAG,SAAS,sXAAsX,GAAG,EAAE,SAAS,4CAA4C,QAAQ,sCAAsC,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,SAAS,0DAA0D,GAAG,aAAa,EAAE,WAAW,+CAA+C,YAAY,CAAC,EAAE,SAAS,yDAAyD,QAAQ,8BAA8B,GAAG,EAAE,SAAS,QAAQ,QAAQ,8BAA8B,GAAG,EAAE,SAAS,UAAU,QAAQ,gCAAgC,GAAG,EAAE,SAAS,UAAU,QAAQ,gCAAgC,GAAG,EAAE,SAAS,+BAA+B,QAAQ,iCAAiC,GAAG,EAAE,SAAS,iBAAiB,QAAQ,8BAA8B,GAAG,EAAE,SAAS,0DAA0D,QAAQ,iCAAiC,GAAG,EAAE,SAAS,qBAAqB,QAAQ,iCAAiC,GAAG,EAAE,SAAS,qCAAqC,QAAQ,yCAAyC,GAAG,EAAE,SAAS,aAAa,QAAQ,+BAA+B,CAAC,EAAE,GAAG,sCAAsC,EAAE,YAAY,CAAC,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,gCAAgC,GAAG,EAAE,WAAW,2BAA2B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,QAAQ,QAAQ,sBAAsB,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,QAAQ,QAAQ,sBAAsB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,WAAW,0CAA0C,SAAS,gOAAgO,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,kBAAkB,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,WAAW,yEAAyE,SAAS,yIAAyI,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,YAAY,QAAQ,qBAAqB,YAAY,CAAC,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,gBAAgB,EAAE,YAAY,CAAC,EAAE,SAAS,oBAAoB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,qBAAqB,EAAE,GAAG,WAAW,gBAAgB,OAAO,WAAW,YAAY,CAAC,EAAE,SAAS,WAAW,QAAQ,gCAAgC,GAAG,EAAE,SAAS,QAAQ,QAAQ,8BAA8B,CAAC,EAAE,CAAC,EAAE,GAAG,4BAA4B,EAAE,WAAW,wCAAwC,YAAY,CAAC,EAAE,SAAS,OAAO,QAAQ,gDAAgD,GAAG,EAAE,SAAS,OAAO,QAAQ,8CAA8C,GAAG,EAAE,SAAS,4BAA4B,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,uBAAuB,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,wBAAwB,CAAC,EAAE,CAAC,EAAE,GAAG,sBAAsB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,QAAQ,QAAQ,6BAA6B,CAAC,EAAE,EAAE,GAAG,WAAW,gCAAgC,SAAS,oCAAoC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,YAAY,QAAQ,mCAAmC,CAAC,EAAE,EAAE,GAAG,WAAW,kEAAkE,SAAS,+FAA+F,CAAC,EAAE,GAAG,wBAAwB,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,WAAW,uBAAuB,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,sBAAsB,CAAC,EAAE,GAAG,SAAS,EAAE,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,QAAQ,yBAAyB,YAAY,CAAC,EAAE,SAAS,8FAA8F,QAAQ,yBAAyB,GAAG,EAAE,SAAS,SAAS,QAAQ,kCAAkC,CAAC,EAAE,CAAC,EAAE,GAAG,eAAe,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,kBAAkB,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,WAAW,2BAA2B,SAAS,4IAA4I,CAAC,EAAE,GAAG,cAAc,EAAE,YAAY,CAAC,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,mBAAmB,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,SAAS,cAAc,QAAQ,0BAA0B,GAAG,EAAE,SAAS,cAAc,QAAQ,uBAAuB,GAAG,EAAE,SAAS,eAAe,QAAQ,wBAAwB,GAAG,EAAE,SAAS,2DAA2D,QAAQ,0BAA0B,GAAG,EAAE,SAAS,cAAc,QAAQ,uBAAuB,GAAG,EAAE,SAAS,gBAAgB,QAAQ,yBAAyB,GAAG,EAAE,SAAS,iBAAiB,QAAQ,0BAA0B,GAAG,EAAE,SAAS,aAAa,QAAQ,0BAA0B,CAAC,EAAE,GAAG,uBAAuB,EAAE,YAAY,CAAC,EAAE,SAAS,kFAAkF,QAAQ,+BAA+B,GAAG,EAAE,SAAS,4BAA4B,QAAQ,oCAAoC,CAAC,EAAE,GAAG,mBAAmB,EAAE,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,WAAW,+BAA+B,OAAO,KAAK,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,sBAAsB,CAAC,EAAE,CAAC,EAAE,GAAG,sBAAsB,EAAE,YAAY,CAAC,EAAE,SAAS,6HAA6H,QAAQ,gCAAgC,CAAC,EAAE,GAAG,gCAAgC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,kBAAkB,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,QAAQ,QAAQ,sBAAsB,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,iDAAiD,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,uBAAuB,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,WAAW,uCAAuC,SAAS,+DAA+D,GAAG,sCAAsC,EAAE,YAAY,CAAC,EAAE,SAAS,+EAA+E,iBAAiB,EAAE,KAAK,EAAE,SAAS,YAAY,QAAQ,6BAA6B,GAAG,KAAK,EAAE,QAAQ,oBAAoB,GAAG,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,WAAW,8CAA8C,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,0BAA0B,EAAE,YAAY,CAAC,EAAE,SAAS,2BAA2B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oBAAoB,GAAG,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,WAAW,wBAAwB,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,iCAAiC,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,iCAAiC,EAAE,WAAW,+BAA+B,YAAY,CAAC,EAAE,WAAW,sCAAsC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,WAAW,0BAA0B,SAAS,iIAAiI,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,YAAY,QAAQ,6BAA6B,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,WAAW,2CAA2C,SAAS,oKAAoK,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,YAAY,QAAQ,6BAA6B,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,SAAS,sJAAsJ,CAAC,EAAE,EAAE,GAAG,WAAW,oGAAoG,SAAS,oMAAoM,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,WAAW,iBAAiB,SAAS,wKAAwK,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,YAAY,QAAQ,6BAA6B,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,sCAAsC,GAAG,EAAE,WAAW,4BAA4B,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,WAAW,gCAAgC,SAAS,qDAAqD,CAAC,EAAE,GAAG,qBAAqB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,kCAAkC,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,WAAW,QAAQ,gCAAgC,GAAG,EAAE,SAAS,QAAQ,QAAQ,kCAAkC,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,sCAAsC,GAAG,EAAE,SAAS,OAAO,QAAQ,iDAAiD,GAAG,EAAE,SAAS,OAAO,QAAQ,+CAA+C,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,WAAW,qBAAqB,SAAS,uDAAuD,GAAG,gCAAgC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qBAAqB,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,QAAQ,QAAQ,oBAAoB,CAAC,EAAE,EAAE,GAAG,WAAW,oEAAoE,SAAS,4EAA4E,GAAG,gBAAgB,EAAE,SAAS,8GAA8G,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,YAAY,QAAQ,+BAA+B,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,YAAY,QAAQ,oBAAoB,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,SAAS,cAAc,QAAQ,kBAAkB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,WAAW,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qBAAqB,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,SAAS,QAAQ,QAAQ,sBAAsB,CAAC,EAAE,GAAG,KAAK,EAAE,QAAQ,6BAA6B,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,EAAE,GAAG,SAAS,2FAA2F,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,iBAAiB,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2BAA2B,EAAE,GAAG,WAAW,6BAA6B,SAAS,eAAe,GAAG,EAAE,WAAW,iCAAiC,SAAS,yBAAyB,QAAQ,6BAA6B,CAAC,EAAE,GAAG,eAAe,EAAE,WAAW,eAAe,SAAS,KAAK,QAAQ,4BAA4B,GAAG,qBAAqB,EAAE,WAAW,kCAAkC,YAAY,CAAC,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,sCAAsC,EAAE,WAAW,0GAA0G,YAAY,CAAC,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,wBAAwB,GAAG,EAAE,WAAW,mBAAmB,GAAG,EAAE,WAAW,oBAAoB,GAAG,EAAE,WAAW,eAAe,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,WAAW,QAAQ,gCAAgC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,EAAE,GAAG,SAAS,oBAAoB,QAAQ,+BAA+B,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,EAAE,GAAG,WAAW,uBAAuB,SAAS,yEAAyE,GAAG,8BAA8B,EAAE,YAAY,CAAC,EAAE,SAAS,+CAA+C,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,WAAW,4CAA4C,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,SAAS,YAAY,QAAQ,+BAA+B,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,SAAS,iSAAiS,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,+BAA+B,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,SAAS,YAAY,QAAQ,+BAA+B,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,SAAS,YAAY,QAAQ,sBAAsB,CAAC,EAAE,EAAE,GAAG,WAAW,6CAA6C,SAAS,wTAAwT,GAAG,wBAAwB,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,SAAS,WAAW,QAAQ,gCAAgC,GAAG,EAAE,SAAS,QAAQ,QAAQ,+BAA+B,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,EAAE,GAAG,WAAW,6BAA6B,SAAS,kCAAkC,EAAE,GAAG,aAAa,YAAY,CAAC;AACr33C,IAAI,KAAK;AAAA,EACP;AACF;", "names": []}