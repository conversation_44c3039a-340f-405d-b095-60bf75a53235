{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/themes/rose-pine-moon.mjs"], "sourcesContent": ["var rosePineMoon = Object.freeze({\n  \"colors\": {\n    \"activityBar.activeBorder\": \"#e0def4\",\n    \"activityBar.background\": \"#232136\",\n    \"activityBar.dropBorder\": \"#393552\",\n    \"activityBar.foreground\": \"#e0def4\",\n    \"activityBar.inactiveForeground\": \"#908caa\",\n    \"activityBarBadge.background\": \"#ea9a97\",\n    \"activityBarBadge.foreground\": \"#232136\",\n    \"badge.background\": \"#ea9a97\",\n    \"badge.foreground\": \"#232136\",\n    \"banner.background\": \"#2a273f\",\n    \"banner.foreground\": \"#e0def4\",\n    \"banner.iconForeground\": \"#908caa\",\n    \"breadcrumb.activeSelectionForeground\": \"#ea9a97\",\n    \"breadcrumb.background\": \"#232136\",\n    \"breadcrumb.focusForeground\": \"#908caa\",\n    \"breadcrumb.foreground\": \"#6e6a86\",\n    \"breadcrumbPicker.background\": \"#2a273f\",\n    \"button.background\": \"#ea9a97\",\n    \"button.foreground\": \"#232136\",\n    \"button.hoverBackground\": \"#ea9a97e6\",\n    \"button.secondaryBackground\": \"#2a273f\",\n    \"button.secondaryForeground\": \"#e0def4\",\n    \"button.secondaryHoverBackground\": \"#393552\",\n    \"charts.blue\": \"#9ccfd8\",\n    \"charts.foreground\": \"#e0def4\",\n    \"charts.green\": \"#3e8fb0\",\n    \"charts.lines\": \"#908caa\",\n    \"charts.orange\": \"#ea9a97\",\n    \"charts.purple\": \"#c4a7e7\",\n    \"charts.red\": \"#eb6f92\",\n    \"charts.yellow\": \"#f6c177\",\n    \"checkbox.background\": \"#2a273f\",\n    \"checkbox.border\": \"#817c9c26\",\n    \"checkbox.foreground\": \"#e0def4\",\n    \"debugExceptionWidget.background\": \"#2a273f\",\n    \"debugExceptionWidget.border\": \"#817c9c26\",\n    \"debugIcon.breakpointCurrentStackframeForeground\": \"#908caa\",\n    \"debugIcon.breakpointDisabledForeground\": \"#908caa\",\n    \"debugIcon.breakpointForeground\": \"#908caa\",\n    \"debugIcon.breakpointStackframeForeground\": \"#908caa\",\n    \"debugIcon.breakpointUnverifiedForeground\": \"#908caa\",\n    \"debugIcon.continueForeground\": \"#908caa\",\n    \"debugIcon.disconnectForeground\": \"#908caa\",\n    \"debugIcon.pauseForeground\": \"#908caa\",\n    \"debugIcon.restartForeground\": \"#908caa\",\n    \"debugIcon.startForeground\": \"#908caa\",\n    \"debugIcon.stepBackForeground\": \"#908caa\",\n    \"debugIcon.stepIntoForeground\": \"#908caa\",\n    \"debugIcon.stepOutForeground\": \"#908caa\",\n    \"debugIcon.stepOverForeground\": \"#908caa\",\n    \"debugIcon.stopForeground\": \"#eb6f92\",\n    \"debugToolBar.background\": \"#2a273f\",\n    \"debugToolBar.border\": \"#393552\",\n    \"descriptionForeground\": \"#908caa\",\n    \"diffEditor.border\": \"#393552\",\n    \"diffEditor.diagonalFill\": \"#817c9c4d\",\n    \"diffEditor.insertedLineBackground\": \"#9ccfd826\",\n    \"diffEditor.insertedTextBackground\": \"#9ccfd826\",\n    \"diffEditor.removedLineBackground\": \"#eb6f9226\",\n    \"diffEditor.removedTextBackground\": \"#eb6f9226\",\n    \"diffEditorOverview.insertedForeground\": \"#9ccfd880\",\n    \"diffEditorOverview.removedForeground\": \"#eb6f9280\",\n    \"dropdown.background\": \"#2a273f\",\n    \"dropdown.border\": \"#817c9c26\",\n    \"dropdown.foreground\": \"#e0def4\",\n    \"dropdown.listBackground\": \"#2a273f\",\n    \"editor.background\": \"#232136\",\n    \"editor.findMatchBackground\": \"#817c9c4d\",\n    \"editor.findMatchHighlightBackground\": \"#817c9c4d\",\n    \"editor.findRangeHighlightBackground\": \"#817c9c4d\",\n    \"editor.findRangeHighlightBorder\": \"#0000\",\n    \"editor.focusedStackFrameHighlightBackground\": \"#817c9c26\",\n    \"editor.foldBackground\": \"#2a273f\",\n    \"editor.foreground\": \"#e0def4\",\n    \"editor.hoverHighlightBackground\": \"#0000\",\n    \"editor.inactiveSelectionBackground\": \"#817c9c14\",\n    \"editor.inlineValuesBackground\": \"#0000\",\n    \"editor.inlineValuesForeground\": \"#908caa\",\n    \"editor.lineHighlightBackground\": \"#817c9c14\",\n    \"editor.lineHighlightBorder\": \"#0000\",\n    \"editor.linkedEditingBackground\": \"#2a273f\",\n    \"editor.rangeHighlightBackground\": \"#817c9c14\",\n    \"editor.selectionBackground\": \"#817c9c26\",\n    \"editor.selectionForeground\": \"#e0def4\",\n    \"editor.selectionHighlightBackground\": \"#817c9c26\",\n    \"editor.selectionHighlightBorder\": \"#232136\",\n    \"editor.snippetFinalTabstopHighlightBackground\": \"#817c9c26\",\n    \"editor.snippetFinalTabstopHighlightBorder\": \"#2a273f\",\n    \"editor.snippetTabstopHighlightBackground\": \"#817c9c26\",\n    \"editor.snippetTabstopHighlightBorder\": \"#2a273f\",\n    \"editor.stackFrameHighlightBackground\": \"#817c9c26\",\n    \"editor.symbolHighlightBackground\": \"#817c9c26\",\n    \"editor.symbolHighlightBorder\": \"#0000\",\n    \"editor.wordHighlightBackground\": \"#817c9c26\",\n    \"editor.wordHighlightBorder\": \"#0000\",\n    \"editor.wordHighlightStrongBackground\": \"#817c9c26\",\n    \"editor.wordHighlightStrongBorder\": \"#817c9c26\",\n    \"editorBracketHighlight.foreground1\": \"#eb6f9280\",\n    \"editorBracketHighlight.foreground2\": \"#3e8fb080\",\n    \"editorBracketHighlight.foreground3\": \"#f6c17780\",\n    \"editorBracketHighlight.foreground4\": \"#9ccfd880\",\n    \"editorBracketHighlight.foreground5\": \"#ea9a9780\",\n    \"editorBracketHighlight.foreground6\": \"#c4a7e780\",\n    \"editorBracketMatch.background\": \"#0000\",\n    \"editorBracketMatch.border\": \"#908caa\",\n    \"editorBracketPairGuide.activeBackground1\": \"#3e8fb0\",\n    \"editorBracketPairGuide.activeBackground2\": \"#ea9a97\",\n    \"editorBracketPairGuide.activeBackground3\": \"#c4a7e7\",\n    \"editorBracketPairGuide.activeBackground4\": \"#9ccfd8\",\n    \"editorBracketPairGuide.activeBackground5\": \"#f6c177\",\n    \"editorBracketPairGuide.activeBackground6\": \"#eb6f92\",\n    \"editorBracketPairGuide.background1\": \"#3e8fb080\",\n    \"editorBracketPairGuide.background2\": \"#ea9a9780\",\n    \"editorBracketPairGuide.background3\": \"#c4a7e780\",\n    \"editorBracketPairGuide.background4\": \"#9ccfd880\",\n    \"editorBracketPairGuide.background5\": \"#f6c17780\",\n    \"editorBracketPairGuide.background6\": \"#eb6f9280\",\n    \"editorCodeLens.foreground\": \"#ea9a97\",\n    \"editorCursor.background\": \"#e0def4\",\n    \"editorCursor.foreground\": \"#6e6a86\",\n    \"editorError.border\": \"#0000\",\n    \"editorError.foreground\": \"#eb6f92\",\n    \"editorGhostText.foreground\": \"#908caa\",\n    \"editorGroup.border\": \"#0000\",\n    \"editorGroup.dropBackground\": \"#2a273f\",\n    \"editorGroup.emptyBackground\": \"#0000\",\n    \"editorGroup.focusedEmptyBorder\": \"#0000\",\n    \"editorGroupHeader.noTabsBackground\": \"#0000\",\n    \"editorGroupHeader.tabsBackground\": \"#0000\",\n    \"editorGroupHeader.tabsBorder\": \"#0000\",\n    \"editorGutter.addedBackground\": \"#9ccfd8\",\n    \"editorGutter.background\": \"#232136\",\n    \"editorGutter.commentRangeForeground\": \"#908caa\",\n    \"editorGutter.deletedBackground\": \"#eb6f92\",\n    \"editorGutter.foldingControlForeground\": \"#c4a7e7\",\n    \"editorGutter.modifiedBackground\": \"#ea9a97\",\n    \"editorHint.border\": \"#0000\",\n    \"editorHint.foreground\": \"#908caa\",\n    \"editorHoverWidget.background\": \"#2a273f\",\n    \"editorHoverWidget.border\": \"#6e6a8680\",\n    \"editorHoverWidget.foreground\": \"#908caa\",\n    \"editorHoverWidget.highlightForeground\": \"#e0def4\",\n    \"editorHoverWidget.statusBarBackground\": \"#0000\",\n    \"editorIndentGuide.activeBackground\": \"#6e6a86\",\n    \"editorIndentGuide.background\": \"#817c9c4d\",\n    \"editorInfo.border\": \"#393552\",\n    \"editorInfo.foreground\": \"#9ccfd8\",\n    \"editorInlayHint.background\": \"#393552\",\n    \"editorInlayHint.foreground\": \"#908caa\",\n    \"editorInlayHint.parameterBackground\": \"#393552\",\n    \"editorInlayHint.parameterForeground\": \"#c4a7e7\",\n    \"editorInlayHint.typeBackground\": \"#393552\",\n    \"editorInlayHint.typeForeground\": \"#9ccfd8\",\n    \"editorLightBulb.foreground\": \"#3e8fb0\",\n    \"editorLightBulbAutoFix.foreground\": \"#ea9a97\",\n    \"editorLineNumber.activeForeground\": \"#e0def4\",\n    \"editorLineNumber.foreground\": \"#908caa\",\n    \"editorLink.activeForeground\": \"#ea9a97\",\n    \"editorMarkerNavigation.background\": \"#2a273f\",\n    \"editorMarkerNavigationError.background\": \"#2a273f\",\n    \"editorMarkerNavigationInfo.background\": \"#2a273f\",\n    \"editorMarkerNavigationWarning.background\": \"#2a273f\",\n    \"editorOverviewRuler.addedForeground\": \"#9ccfd880\",\n    \"editorOverviewRuler.background\": \"#232136\",\n    \"editorOverviewRuler.border\": \"#817c9c4d\",\n    \"editorOverviewRuler.bracketMatchForeground\": \"#908caa\",\n    \"editorOverviewRuler.commonContentForeground\": \"#817c9c14\",\n    \"editorOverviewRuler.currentContentForeground\": \"#817c9c26\",\n    \"editorOverviewRuler.deletedForeground\": \"#eb6f9280\",\n    \"editorOverviewRuler.errorForeground\": \"#eb6f9280\",\n    \"editorOverviewRuler.findMatchForeground\": \"#817c9c4d\",\n    \"editorOverviewRuler.incomingContentForeground\": \"#c4a7e780\",\n    \"editorOverviewRuler.infoForeground\": \"#9ccfd880\",\n    \"editorOverviewRuler.modifiedForeground\": \"#ea9a9780\",\n    \"editorOverviewRuler.rangeHighlightForeground\": \"#817c9c4d\",\n    \"editorOverviewRuler.selectionHighlightForeground\": \"#817c9c4d\",\n    \"editorOverviewRuler.warningForeground\": \"#f6c17780\",\n    \"editorOverviewRuler.wordHighlightForeground\": \"#817c9c26\",\n    \"editorOverviewRuler.wordHighlightStrongForeground\": \"#817c9c4d\",\n    \"editorPane.background\": \"#0000\",\n    \"editorRuler.foreground\": \"#817c9c4d\",\n    \"editorSuggestWidget.background\": \"#2a273f\",\n    \"editorSuggestWidget.border\": \"#0000\",\n    \"editorSuggestWidget.focusHighlightForeground\": \"#ea9a97\",\n    \"editorSuggestWidget.foreground\": \"#908caa\",\n    \"editorSuggestWidget.highlightForeground\": \"#ea9a97\",\n    \"editorSuggestWidget.selectedBackground\": \"#817c9c26\",\n    \"editorSuggestWidget.selectedForeground\": \"#e0def4\",\n    \"editorSuggestWidget.selectedIconForeground\": \"#e0def4\",\n    \"editorUnnecessaryCode.border\": \"#0000\",\n    \"editorUnnecessaryCode.opacity\": \"#e0def480\",\n    \"editorWarning.border\": \"#0000\",\n    \"editorWarning.foreground\": \"#f6c177\",\n    \"editorWhitespace.foreground\": \"#6e6a86\",\n    \"editorWidget.background\": \"#2a273f\",\n    \"editorWidget.border\": \"#393552\",\n    \"editorWidget.foreground\": \"#908caa\",\n    \"editorWidget.resizeBorder\": \"#6e6a86\",\n    \"errorForeground\": \"#eb6f92\",\n    \"extensionBadge.remoteBackground\": \"#c4a7e7\",\n    \"extensionBadge.remoteForeground\": \"#232136\",\n    \"extensionButton.prominentBackground\": \"#ea9a97\",\n    \"extensionButton.prominentForeground\": \"#232136\",\n    \"extensionButton.prominentHoverBackground\": \"#ea9a97e6\",\n    \"extensionIcon.preReleaseForeground\": \"#3e8fb0\",\n    \"extensionIcon.starForeground\": \"#ea9a97\",\n    \"extensionIcon.verifiedForeground\": \"#c4a7e7\",\n    \"focusBorder\": \"#817c9c26\",\n    \"foreground\": \"#e0def4\",\n    \"gitDecoration.addedResourceForeground\": \"#9ccfd8\",\n    \"gitDecoration.conflictingResourceForeground\": \"#eb6f92\",\n    \"gitDecoration.deletedResourceForeground\": \"#908caa\",\n    \"gitDecoration.ignoredResourceForeground\": \"#6e6a86\",\n    \"gitDecoration.modifiedResourceForeground\": \"#ea9a97\",\n    \"gitDecoration.renamedResourceForeground\": \"#3e8fb0\",\n    \"gitDecoration.stageDeletedResourceForeground\": \"#eb6f92\",\n    \"gitDecoration.stageModifiedResourceForeground\": \"#c4a7e7\",\n    \"gitDecoration.submoduleResourceForeground\": \"#f6c177\",\n    \"gitDecoration.untrackedResourceForeground\": \"#f6c177\",\n    \"icon.foreground\": \"#908caa\",\n    \"input.background\": \"#39355280\",\n    \"input.border\": \"#817c9c26\",\n    \"input.foreground\": \"#e0def4\",\n    \"input.placeholderForeground\": \"#908caa\",\n    \"inputOption.activeBackground\": \"#ea9a9726\",\n    \"inputOption.activeForeground\": \"#ea9a97\",\n    \"inputValidation.errorBackground\": \"#2a273f\",\n    \"inputValidation.errorBorder\": \"#817c9c4d\",\n    \"inputValidation.errorForeground\": \"#eb6f92\",\n    \"inputValidation.infoBackground\": \"#2a273f\",\n    \"inputValidation.infoBorder\": \"#817c9c4d\",\n    \"inputValidation.infoForeground\": \"#9ccfd8\",\n    \"inputValidation.warningBackground\": \"#2a273f\",\n    \"inputValidation.warningBorder\": \"#817c9c4d\",\n    \"inputValidation.warningForeground\": \"#9ccfd880\",\n    \"keybindingLabel.background\": \"#393552\",\n    \"keybindingLabel.border\": \"#817c9c4d\",\n    \"keybindingLabel.bottomBorder\": \"#817c9c4d\",\n    \"keybindingLabel.foreground\": \"#c4a7e7\",\n    \"keybindingTable.headerBackground\": \"#393552\",\n    \"keybindingTable.rowsBackground\": \"#2a273f\",\n    \"list.activeSelectionBackground\": \"#817c9c26\",\n    \"list.activeSelectionForeground\": \"#e0def4\",\n    \"list.deemphasizedForeground\": \"#908caa\",\n    \"list.dropBackground\": \"#2a273f\",\n    \"list.errorForeground\": \"#eb6f92\",\n    \"list.filterMatchBackground\": \"#2a273f\",\n    \"list.filterMatchBorder\": \"#ea9a97\",\n    \"list.focusBackground\": \"#817c9c4d\",\n    \"list.focusForeground\": \"#e0def4\",\n    \"list.focusOutline\": \"#817c9c26\",\n    \"list.highlightForeground\": \"#ea9a97\",\n    \"list.hoverBackground\": \"#817c9c14\",\n    \"list.hoverForeground\": \"#e0def4\",\n    \"list.inactiveFocusBackground\": \"#817c9c14\",\n    \"list.inactiveSelectionBackground\": \"#2a273f\",\n    \"list.inactiveSelectionForeground\": \"#e0def4\",\n    \"list.invalidItemForeground\": \"#eb6f92\",\n    \"list.warningForeground\": \"#f6c177\",\n    \"listFilterWidget.background\": \"#2a273f\",\n    \"listFilterWidget.noMatchesOutline\": \"#eb6f92\",\n    \"listFilterWidget.outline\": \"#393552\",\n    \"menu.background\": \"#2a273f\",\n    \"menu.border\": \"#817c9c14\",\n    \"menu.foreground\": \"#e0def4\",\n    \"menu.selectionBackground\": \"#817c9c26\",\n    \"menu.selectionBorder\": \"#393552\",\n    \"menu.selectionForeground\": \"#e0def4\",\n    \"menu.separatorBackground\": \"#817c9c4d\",\n    \"menubar.selectionBackground\": \"#817c9c26\",\n    \"menubar.selectionBorder\": \"#817c9c14\",\n    \"menubar.selectionForeground\": \"#e0def4\",\n    \"merge.border\": \"#393552\",\n    \"merge.commonContentBackground\": \"#817c9c26\",\n    \"merge.commonHeaderBackground\": \"#817c9c26\",\n    \"merge.currentContentBackground\": \"#f6c17780\",\n    \"merge.currentHeaderBackground\": \"#f6c17780\",\n    \"merge.incomingContentBackground\": \"#9ccfd880\",\n    \"merge.incomingHeaderBackground\": \"#9ccfd880\",\n    \"minimap.background\": \"#2a273f\",\n    \"minimap.errorHighlight\": \"#eb6f9280\",\n    \"minimap.findMatchHighlight\": \"#817c9c26\",\n    \"minimap.selectionHighlight\": \"#817c9c26\",\n    \"minimap.warningHighlight\": \"#f6c17780\",\n    \"minimapGutter.addedBackground\": \"#9ccfd8\",\n    \"minimapGutter.deletedBackground\": \"#eb6f92\",\n    \"minimapGutter.modifiedBackground\": \"#ea9a97\",\n    \"minimapSlider.activeBackground\": \"#817c9c4d\",\n    \"minimapSlider.background\": \"#817c9c26\",\n    \"minimapSlider.hoverBackground\": \"#817c9c26\",\n    \"notebook.cellBorderColor\": \"#9ccfd880\",\n    \"notebook.cellEditorBackground\": \"#2a273f\",\n    \"notebook.cellHoverBackground\": \"#39355280\",\n    \"notebook.focusedCellBackground\": \"#817c9c14\",\n    \"notebook.focusedCellBorder\": \"#9ccfd8\",\n    \"notebook.outputContainerBackgroundColor\": \"#817c9c14\",\n    \"notificationCenter.border\": \"#817c9c26\",\n    \"notificationCenterHeader.background\": \"#2a273f\",\n    \"notificationCenterHeader.foreground\": \"#908caa\",\n    \"notificationLink.foreground\": \"#c4a7e7\",\n    \"notificationToast.border\": \"#817c9c26\",\n    \"notifications.background\": \"#2a273f\",\n    \"notifications.border\": \"#817c9c26\",\n    \"notifications.foreground\": \"#e0def4\",\n    \"notificationsErrorIcon.foreground\": \"#eb6f92\",\n    \"notificationsInfoIcon.foreground\": \"#9ccfd8\",\n    \"notificationsWarningIcon.foreground\": \"#f6c177\",\n    \"panel.background\": \"#2a273f\",\n    \"panel.border\": \"#0000\",\n    \"panel.dropBorder\": \"#393552\",\n    \"panelInput.border\": \"#2a273f\",\n    \"panelSection.dropBackground\": \"#817c9c26\",\n    \"panelSectionHeader.background\": \"#2a273f\",\n    \"panelSectionHeader.foreground\": \"#e0def4\",\n    \"panelTitle.activeBorder\": \"#817c9c4d\",\n    \"panelTitle.activeForeground\": \"#e0def4\",\n    \"panelTitle.inactiveForeground\": \"#908caa\",\n    \"peekView.border\": \"#393552\",\n    \"peekViewEditor.background\": \"#2a273f\",\n    \"peekViewEditor.matchHighlightBackground\": \"#817c9c4d\",\n    \"peekViewResult.background\": \"#2a273f\",\n    \"peekViewResult.fileForeground\": \"#908caa\",\n    \"peekViewResult.lineForeground\": \"#908caa\",\n    \"peekViewResult.matchHighlightBackground\": \"#817c9c4d\",\n    \"peekViewResult.selectionBackground\": \"#817c9c26\",\n    \"peekViewResult.selectionForeground\": \"#e0def4\",\n    \"peekViewTitle.background\": \"#393552\",\n    \"peekViewTitleDescription.foreground\": \"#908caa\",\n    \"pickerGroup.border\": \"#817c9c4d\",\n    \"pickerGroup.foreground\": \"#c4a7e7\",\n    \"ports.iconRunningProcessForeground\": \"#ea9a97\",\n    \"problemsErrorIcon.foreground\": \"#eb6f92\",\n    \"problemsInfoIcon.foreground\": \"#9ccfd8\",\n    \"problemsWarningIcon.foreground\": \"#f6c177\",\n    \"progressBar.background\": \"#ea9a97\",\n    \"quickInput.background\": \"#2a273f\",\n    \"quickInput.foreground\": \"#908caa\",\n    \"quickInputList.focusBackground\": \"#817c9c26\",\n    \"quickInputList.focusForeground\": \"#e0def4\",\n    \"quickInputList.focusIconForeground\": \"#e0def4\",\n    \"scrollbar.shadow\": \"#2a273f4d\",\n    \"scrollbarSlider.activeBackground\": \"#3e8fb080\",\n    \"scrollbarSlider.background\": \"#817c9c26\",\n    \"scrollbarSlider.hoverBackground\": \"#817c9c4d\",\n    \"searchEditor.findMatchBackground\": \"#817c9c26\",\n    \"selection.background\": \"#817c9c4d\",\n    \"settings.focusedRowBackground\": \"#2a273f\",\n    \"settings.focusedRowBorder\": \"#817c9c26\",\n    \"settings.headerForeground\": \"#e0def4\",\n    \"settings.modifiedItemIndicator\": \"#ea9a97\",\n    \"settings.rowHoverBackground\": \"#2a273f\",\n    \"sideBar.background\": \"#232136\",\n    \"sideBar.dropBackground\": \"#2a273f\",\n    \"sideBar.foreground\": \"#908caa\",\n    \"sideBarSectionHeader.background\": \"#0000\",\n    \"sideBarSectionHeader.border\": \"#817c9c26\",\n    \"statusBar.background\": \"#232136\",\n    \"statusBar.debuggingBackground\": \"#c4a7e7\",\n    \"statusBar.debuggingForeground\": \"#232136\",\n    \"statusBar.foreground\": \"#908caa\",\n    \"statusBar.noFolderBackground\": \"#232136\",\n    \"statusBar.noFolderForeground\": \"#908caa\",\n    \"statusBarItem.activeBackground\": \"#817c9c4d\",\n    \"statusBarItem.errorBackground\": \"#232136\",\n    \"statusBarItem.errorForeground\": \"#eb6f92\",\n    \"statusBarItem.hoverBackground\": \"#817c9c26\",\n    \"statusBarItem.prominentBackground\": \"#393552\",\n    \"statusBarItem.prominentForeground\": \"#e0def4\",\n    \"statusBarItem.prominentHoverBackground\": \"#817c9c26\",\n    \"statusBarItem.remoteBackground\": \"#232136\",\n    \"statusBarItem.remoteForeground\": \"#f6c177\",\n    \"symbolIcon.arrayForeground\": \"#908caa\",\n    \"symbolIcon.classForeground\": \"#908caa\",\n    \"symbolIcon.colorForeground\": \"#908caa\",\n    \"symbolIcon.constantForeground\": \"#908caa\",\n    \"symbolIcon.constructorForeground\": \"#908caa\",\n    \"symbolIcon.enumeratorForeground\": \"#908caa\",\n    \"symbolIcon.enumeratorMemberForeground\": \"#908caa\",\n    \"symbolIcon.eventForeground\": \"#908caa\",\n    \"symbolIcon.fieldForeground\": \"#908caa\",\n    \"symbolIcon.fileForeground\": \"#908caa\",\n    \"symbolIcon.folderForeground\": \"#908caa\",\n    \"symbolIcon.functionForeground\": \"#908caa\",\n    \"symbolIcon.interfaceForeground\": \"#908caa\",\n    \"symbolIcon.keyForeground\": \"#908caa\",\n    \"symbolIcon.keywordForeground\": \"#908caa\",\n    \"symbolIcon.methodForeground\": \"#908caa\",\n    \"symbolIcon.moduleForeground\": \"#908caa\",\n    \"symbolIcon.namespaceForeground\": \"#908caa\",\n    \"symbolIcon.nullForeground\": \"#908caa\",\n    \"symbolIcon.numberForeground\": \"#908caa\",\n    \"symbolIcon.objectForeground\": \"#908caa\",\n    \"symbolIcon.operatorForeground\": \"#908caa\",\n    \"symbolIcon.packageForeground\": \"#908caa\",\n    \"symbolIcon.propertyForeground\": \"#908caa\",\n    \"symbolIcon.referenceForeground\": \"#908caa\",\n    \"symbolIcon.snippetForeground\": \"#908caa\",\n    \"symbolIcon.stringForeground\": \"#908caa\",\n    \"symbolIcon.structForeground\": \"#908caa\",\n    \"symbolIcon.textForeground\": \"#908caa\",\n    \"symbolIcon.typeParameterForeground\": \"#908caa\",\n    \"symbolIcon.unitForeground\": \"#908caa\",\n    \"symbolIcon.variableForeground\": \"#908caa\",\n    \"tab.activeBackground\": \"#817c9c14\",\n    \"tab.activeForeground\": \"#e0def4\",\n    \"tab.activeModifiedBorder\": \"#9ccfd8\",\n    \"tab.border\": \"#0000\",\n    \"tab.hoverBackground\": \"#817c9c26\",\n    \"tab.inactiveBackground\": \"#0000\",\n    \"tab.inactiveForeground\": \"#908caa\",\n    \"tab.inactiveModifiedBorder\": \"#9ccfd880\",\n    \"tab.lastPinnedBorder\": \"#6e6a86\",\n    \"tab.unfocusedActiveBackground\": \"#0000\",\n    \"tab.unfocusedHoverBackground\": \"#0000\",\n    \"tab.unfocusedInactiveBackground\": \"#0000\",\n    \"tab.unfocusedInactiveModifiedBorder\": \"#9ccfd880\",\n    \"terminal.ansiBlack\": \"#393552\",\n    \"terminal.ansiBlue\": \"#9ccfd8\",\n    \"terminal.ansiBrightBlack\": \"#908caa\",\n    \"terminal.ansiBrightBlue\": \"#9ccfd8\",\n    \"terminal.ansiBrightCyan\": \"#ea9a97\",\n    \"terminal.ansiBrightGreen\": \"#3e8fb0\",\n    \"terminal.ansiBrightMagenta\": \"#c4a7e7\",\n    \"terminal.ansiBrightRed\": \"#eb6f92\",\n    \"terminal.ansiBrightWhite\": \"#e0def4\",\n    \"terminal.ansiBrightYellow\": \"#f6c177\",\n    \"terminal.ansiCyan\": \"#ea9a97\",\n    \"terminal.ansiGreen\": \"#3e8fb0\",\n    \"terminal.ansiMagenta\": \"#c4a7e7\",\n    \"terminal.ansiRed\": \"#eb6f92\",\n    \"terminal.ansiWhite\": \"#e0def4\",\n    \"terminal.ansiYellow\": \"#f6c177\",\n    \"terminal.dropBackground\": \"#817c9c26\",\n    \"terminal.foreground\": \"#e0def4\",\n    \"terminal.selectionBackground\": \"#817c9c26\",\n    \"terminal.tab.activeBorder\": \"#e0def4\",\n    \"terminalCursor.background\": \"#e0def4\",\n    \"terminalCursor.foreground\": \"#6e6a86\",\n    \"textBlockQuote.background\": \"#2a273f\",\n    \"textBlockQuote.border\": \"#817c9c26\",\n    \"textCodeBlock.background\": \"#2a273f\",\n    \"textLink.activeForeground\": \"#c4a7e7e6\",\n    \"textLink.foreground\": \"#c4a7e7\",\n    \"textPreformat.foreground\": \"#f6c177\",\n    \"textSeparator.foreground\": \"#908caa\",\n    \"titleBar.activeBackground\": \"#232136\",\n    \"titleBar.activeForeground\": \"#908caa\",\n    \"titleBar.inactiveBackground\": \"#2a273f\",\n    \"titleBar.inactiveForeground\": \"#908caa\",\n    \"toolbar.activeBackground\": \"#817c9c4d\",\n    \"toolbar.hoverBackground\": \"#817c9c26\",\n    \"tree.indentGuidesStroke\": \"#908caa\",\n    \"walkThrough.embeddedEditorBackground\": \"#232136\",\n    \"welcomePage.background\": \"#232136\",\n    \"welcomePage.buttonBackground\": \"#2a273f\",\n    \"welcomePage.buttonHoverBackground\": \"#393552\",\n    \"widget.shadow\": \"#2a273f4d\",\n    \"window.activeBorder\": \"#2a273f\",\n    \"window.inactiveBorder\": \"#2a273f\"\n  },\n  \"displayName\": \"Ros\\xE9 Pine Moon\",\n  \"name\": \"rose-pine-moon\",\n  \"tokenColors\": [\n    {\n      \"scope\": [\n        \"comment\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#6e6a86\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#3e8fb0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"constant.numeric\",\n        \"constant.language\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ea9a97\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#ea9a97\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.name.section\",\n        \"entity.name.tag\",\n        \"entity.name.namespace\",\n        \"entity.name.type\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9ccfd8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"entity.other.attribute-name\",\n        \"entity.other.inherited-class\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c4a7e7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"invalid\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eb6f92\"\n      }\n    },\n    {\n      \"scope\": [\n        \"invalid.deprecated\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#908caa\"\n      }\n    },\n    {\n      \"scope\": [\n        \"keyword\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#3e8fb0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.inserted.diff\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9ccfd8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"markup.deleted.diff\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#eb6f92\"\n      }\n    },\n    {\n      \"scope\": \"markup.heading\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"markup.bold.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"bold\"\n      }\n    },\n    {\n      \"scope\": \"markup.italic.markdown\",\n      \"settings\": {\n        \"fontStyle\": \"italic\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.diff.range\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c4a7e7\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.tag\",\n        \"meta.brace\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e0def4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"meta.import\",\n        \"meta.export\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#3e8fb0\"\n      }\n    },\n    {\n      \"scope\": \"meta.directive.vue\",\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#c4a7e7\"\n      }\n    },\n    {\n      \"scope\": \"meta.property-name.css\",\n      \"settings\": {\n        \"foreground\": \"#9ccfd8\"\n      }\n    },\n    {\n      \"scope\": \"meta.property-value.css\",\n      \"settings\": {\n        \"foreground\": \"#f6c177\"\n      }\n    },\n    {\n      \"scope\": \"meta.tag.other.html\",\n      \"settings\": {\n        \"foreground\": \"#908caa\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#908caa\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.accessor\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#3e8fb0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f6c177\"\n      }\n    },\n    {\n      \"scope\": [\n        \"punctuation.definition.tag\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#6e6a86\"\n      }\n    },\n    {\n      \"scope\": [\n        \"storage.type\",\n        \"storage.modifier\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#3e8fb0\"\n      }\n    },\n    {\n      \"scope\": [\n        \"string\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f6c177\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#9ccfd8\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.constant\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#f6c177\"\n      }\n    },\n    {\n      \"scope\": [\n        \"support.function\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#eb6f92\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable\"\n      ],\n      \"settings\": {\n        \"fontStyle\": \"italic\",\n        \"foreground\": \"#ea9a97\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.other\",\n        \"variable.language\",\n        \"variable.function\",\n        \"variable.argument\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#e0def4\"\n      }\n    },\n    {\n      \"scope\": [\n        \"variable.parameter\"\n      ],\n      \"settings\": {\n        \"foreground\": \"#c4a7e7\"\n      }\n    }\n  ],\n  \"type\": \"dark\"\n});\n\nexport { rosePineMoon as default };\n"], "mappings": ";;;AAAA,IAAI,eAAe,OAAO,OAAO;AAAA,EAC/B,UAAU;AAAA,IACR,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,0BAA0B;AAAA,IAC1B,0BAA0B;AAAA,IAC1B,kCAAkC;AAAA,IAClC,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,yBAAyB;AAAA,IACzB,wCAAwC;AAAA,IACxC,yBAAyB;AAAA,IACzB,8BAA8B;AAAA,IAC9B,yBAAyB;AAAA,IACzB,+BAA+B;AAAA,IAC/B,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,0BAA0B;AAAA,IAC1B,8BAA8B;AAAA,IAC9B,8BAA8B;AAAA,IAC9B,mCAAmC;AAAA,IACnC,eAAe;AAAA,IACf,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,IACvB,mCAAmC;AAAA,IACnC,+BAA+B;AAAA,IAC/B,mDAAmD;AAAA,IACnD,0CAA0C;AAAA,IAC1C,kCAAkC;AAAA,IAClC,4CAA4C;AAAA,IAC5C,4CAA4C;AAAA,IAC5C,gCAAgC;AAAA,IAChC,kCAAkC;AAAA,IAClC,6BAA6B;AAAA,IAC7B,+BAA+B;AAAA,IAC/B,6BAA6B;AAAA,IAC7B,gCAAgC;AAAA,IAChC,gCAAgC;AAAA,IAChC,+BAA+B;AAAA,IAC/B,gCAAgC;AAAA,IAChC,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,uBAAuB;AAAA,IACvB,yBAAyB;AAAA,IACzB,qBAAqB;AAAA,IACrB,2BAA2B;AAAA,IAC3B,qCAAqC;AAAA,IACrC,qCAAqC;AAAA,IACrC,oCAAoC;AAAA,IACpC,oCAAoC;AAAA,IACpC,yCAAyC;AAAA,IACzC,wCAAwC;AAAA,IACxC,uBAAuB;AAAA,IACvB,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,IACvB,2BAA2B;AAAA,IAC3B,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,uCAAuC;AAAA,IACvC,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,+CAA+C;AAAA,IAC/C,yBAAyB;AAAA,IACzB,qBAAqB;AAAA,IACrB,mCAAmC;AAAA,IACnC,sCAAsC;AAAA,IACtC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,kCAAkC;AAAA,IAClC,mCAAmC;AAAA,IACnC,8BAA8B;AAAA,IAC9B,8BAA8B;AAAA,IAC9B,uCAAuC;AAAA,IACvC,mCAAmC;AAAA,IACnC,iDAAiD;AAAA,IACjD,6CAA6C;AAAA,IAC7C,4CAA4C;AAAA,IAC5C,wCAAwC;AAAA,IACxC,wCAAwC;AAAA,IACxC,oCAAoC;AAAA,IACpC,gCAAgC;AAAA,IAChC,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,wCAAwC;AAAA,IACxC,oCAAoC;AAAA,IACpC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,iCAAiC;AAAA,IACjC,6BAA6B;AAAA,IAC7B,4CAA4C;AAAA,IAC5C,4CAA4C;AAAA,IAC5C,4CAA4C;AAAA,IAC5C,4CAA4C;AAAA,IAC5C,4CAA4C;AAAA,IAC5C,4CAA4C;AAAA,IAC5C,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,sBAAsB;AAAA,IACtB,0BAA0B;AAAA,IAC1B,8BAA8B;AAAA,IAC9B,sBAAsB;AAAA,IACtB,8BAA8B;AAAA,IAC9B,+BAA+B;AAAA,IAC/B,kCAAkC;AAAA,IAClC,sCAAsC;AAAA,IACtC,oCAAoC;AAAA,IACpC,gCAAgC;AAAA,IAChC,gCAAgC;AAAA,IAChC,2BAA2B;AAAA,IAC3B,uCAAuC;AAAA,IACvC,kCAAkC;AAAA,IAClC,yCAAyC;AAAA,IACzC,mCAAmC;AAAA,IACnC,qBAAqB;AAAA,IACrB,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,4BAA4B;AAAA,IAC5B,gCAAgC;AAAA,IAChC,yCAAyC;AAAA,IACzC,yCAAyC;AAAA,IACzC,sCAAsC;AAAA,IACtC,gCAAgC;AAAA,IAChC,qBAAqB;AAAA,IACrB,yBAAyB;AAAA,IACzB,8BAA8B;AAAA,IAC9B,8BAA8B;AAAA,IAC9B,uCAAuC;AAAA,IACvC,uCAAuC;AAAA,IACvC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,qCAAqC;AAAA,IACrC,qCAAqC;AAAA,IACrC,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,qCAAqC;AAAA,IACrC,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,uCAAuC;AAAA,IACvC,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,8CAA8C;AAAA,IAC9C,+CAA+C;AAAA,IAC/C,gDAAgD;AAAA,IAChD,yCAAyC;AAAA,IACzC,uCAAuC;AAAA,IACvC,2CAA2C;AAAA,IAC3C,iDAAiD;AAAA,IACjD,sCAAsC;AAAA,IACtC,0CAA0C;AAAA,IAC1C,gDAAgD;AAAA,IAChD,oDAAoD;AAAA,IACpD,yCAAyC;AAAA,IACzC,+CAA+C;AAAA,IAC/C,qDAAqD;AAAA,IACrD,yBAAyB;AAAA,IACzB,0BAA0B;AAAA,IAC1B,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,gDAAgD;AAAA,IAChD,kCAAkC;AAAA,IAClC,2CAA2C;AAAA,IAC3C,0CAA0C;AAAA,IAC1C,0CAA0C;AAAA,IAC1C,8CAA8C;AAAA,IAC9C,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,wBAAwB;AAAA,IACxB,4BAA4B;AAAA,IAC5B,+BAA+B;AAAA,IAC/B,2BAA2B;AAAA,IAC3B,uBAAuB;AAAA,IACvB,2BAA2B;AAAA,IAC3B,6BAA6B;AAAA,IAC7B,mBAAmB;AAAA,IACnB,mCAAmC;AAAA,IACnC,mCAAmC;AAAA,IACnC,uCAAuC;AAAA,IACvC,uCAAuC;AAAA,IACvC,4CAA4C;AAAA,IAC5C,sCAAsC;AAAA,IACtC,gCAAgC;AAAA,IAChC,oCAAoC;AAAA,IACpC,eAAe;AAAA,IACf,cAAc;AAAA,IACd,yCAAyC;AAAA,IACzC,+CAA+C;AAAA,IAC/C,2CAA2C;AAAA,IAC3C,2CAA2C;AAAA,IAC3C,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,gDAAgD;AAAA,IAChD,iDAAiD;AAAA,IACjD,6CAA6C;AAAA,IAC7C,6CAA6C;AAAA,IAC7C,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,gCAAgC;AAAA,IAChC,gCAAgC;AAAA,IAChC,mCAAmC;AAAA,IACnC,+BAA+B;AAAA,IAC/B,mCAAmC;AAAA,IACnC,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,kCAAkC;AAAA,IAClC,qCAAqC;AAAA,IACrC,iCAAiC;AAAA,IACjC,qCAAqC;AAAA,IACrC,8BAA8B;AAAA,IAC9B,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,8BAA8B;AAAA,IAC9B,oCAAoC;AAAA,IACpC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,+BAA+B;AAAA,IAC/B,uBAAuB;AAAA,IACvB,wBAAwB;AAAA,IACxB,8BAA8B;AAAA,IAC9B,0BAA0B;AAAA,IAC1B,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,qBAAqB;AAAA,IACrB,4BAA4B;AAAA,IAC5B,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,gCAAgC;AAAA,IAChC,oCAAoC;AAAA,IACpC,oCAAoC;AAAA,IACpC,8BAA8B;AAAA,IAC9B,0BAA0B;AAAA,IAC1B,+BAA+B;AAAA,IAC/B,qCAAqC;AAAA,IACrC,4BAA4B;AAAA,IAC5B,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,4BAA4B;AAAA,IAC5B,wBAAwB;AAAA,IACxB,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,+BAA+B;AAAA,IAC/B,2BAA2B;AAAA,IAC3B,+BAA+B;AAAA,IAC/B,gBAAgB;AAAA,IAChB,iCAAiC;AAAA,IACjC,gCAAgC;AAAA,IAChC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,mCAAmC;AAAA,IACnC,kCAAkC;AAAA,IAClC,sBAAsB;AAAA,IACtB,0BAA0B;AAAA,IAC1B,8BAA8B;AAAA,IAC9B,8BAA8B;AAAA,IAC9B,4BAA4B;AAAA,IAC5B,iCAAiC;AAAA,IACjC,mCAAmC;AAAA,IACnC,oCAAoC;AAAA,IACpC,kCAAkC;AAAA,IAClC,4BAA4B;AAAA,IAC5B,iCAAiC;AAAA,IACjC,4BAA4B;AAAA,IAC5B,iCAAiC;AAAA,IACjC,gCAAgC;AAAA,IAChC,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,2CAA2C;AAAA,IAC3C,6BAA6B;AAAA,IAC7B,uCAAuC;AAAA,IACvC,uCAAuC;AAAA,IACvC,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,wBAAwB;AAAA,IACxB,4BAA4B;AAAA,IAC5B,qCAAqC;AAAA,IACrC,oCAAoC;AAAA,IACpC,uCAAuC;AAAA,IACvC,oBAAoB;AAAA,IACpB,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,+BAA+B;AAAA,IAC/B,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,2BAA2B;AAAA,IAC3B,+BAA+B;AAAA,IAC/B,iCAAiC;AAAA,IACjC,mBAAmB;AAAA,IACnB,6BAA6B;AAAA,IAC7B,2CAA2C;AAAA,IAC3C,6BAA6B;AAAA,IAC7B,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,sCAAsC;AAAA,IACtC,4BAA4B;AAAA,IAC5B,uCAAuC;AAAA,IACvC,sBAAsB;AAAA,IACtB,0BAA0B;AAAA,IAC1B,sCAAsC;AAAA,IACtC,gCAAgC;AAAA,IAChC,+BAA+B;AAAA,IAC/B,kCAAkC;AAAA,IAClC,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,IACzB,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,sCAAsC;AAAA,IACtC,oBAAoB;AAAA,IACpB,oCAAoC;AAAA,IACpC,8BAA8B;AAAA,IAC9B,mCAAmC;AAAA,IACnC,oCAAoC;AAAA,IACpC,wBAAwB;AAAA,IACxB,iCAAiC;AAAA,IACjC,6BAA6B;AAAA,IAC7B,6BAA6B;AAAA,IAC7B,kCAAkC;AAAA,IAClC,+BAA+B;AAAA,IAC/B,sBAAsB;AAAA,IACtB,0BAA0B;AAAA,IAC1B,sBAAsB;AAAA,IACtB,mCAAmC;AAAA,IACnC,+BAA+B;AAAA,IAC/B,wBAAwB;AAAA,IACxB,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,wBAAwB;AAAA,IACxB,gCAAgC;AAAA,IAChC,gCAAgC;AAAA,IAChC,kCAAkC;AAAA,IAClC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,qCAAqC;AAAA,IACrC,qCAAqC;AAAA,IACrC,0CAA0C;AAAA,IAC1C,kCAAkC;AAAA,IAClC,kCAAkC;AAAA,IAClC,8BAA8B;AAAA,IAC9B,8BAA8B;AAAA,IAC9B,8BAA8B;AAAA,IAC9B,iCAAiC;AAAA,IACjC,oCAAoC;AAAA,IACpC,mCAAmC;AAAA,IACnC,yCAAyC;AAAA,IACzC,8BAA8B;AAAA,IAC9B,8BAA8B;AAAA,IAC9B,6BAA6B;AAAA,IAC7B,+BAA+B;AAAA,IAC/B,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,4BAA4B;AAAA,IAC5B,gCAAgC;AAAA,IAChC,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,kCAAkC;AAAA,IAClC,6BAA6B;AAAA,IAC7B,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,iCAAiC;AAAA,IACjC,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,kCAAkC;AAAA,IAClC,gCAAgC;AAAA,IAChC,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,6BAA6B;AAAA,IAC7B,sCAAsC;AAAA,IACtC,6BAA6B;AAAA,IAC7B,iCAAiC;AAAA,IACjC,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,4BAA4B;AAAA,IAC5B,cAAc;AAAA,IACd,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,0BAA0B;AAAA,IAC1B,8BAA8B;AAAA,IAC9B,wBAAwB;AAAA,IACxB,iCAAiC;AAAA,IACjC,gCAAgC;AAAA,IAChC,mCAAmC;AAAA,IACnC,uCAAuC;AAAA,IACvC,sBAAsB;AAAA,IACtB,qBAAqB;AAAA,IACrB,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,4BAA4B;AAAA,IAC5B,8BAA8B;AAAA,IAC9B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,6BAA6B;AAAA,IAC7B,qBAAqB;AAAA,IACrB,sBAAsB;AAAA,IACtB,wBAAwB;AAAA,IACxB,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,uBAAuB;AAAA,IACvB,2BAA2B;AAAA,IAC3B,uBAAuB;AAAA,IACvB,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,6BAA6B;AAAA,IAC7B,6BAA6B;AAAA,IAC7B,6BAA6B;AAAA,IAC7B,yBAAyB;AAAA,IACzB,4BAA4B;AAAA,IAC5B,6BAA6B;AAAA,IAC7B,uBAAuB;AAAA,IACvB,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,6BAA6B;AAAA,IAC7B,6BAA6B;AAAA,IAC7B,+BAA+B;AAAA,IAC/B,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,wCAAwC;AAAA,IACxC,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,qCAAqC;AAAA,IACrC,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,yBAAyB;AAAA,EAC3B;AAAA,EACA,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,eAAe;AAAA,IACb;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,MACT,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP;AAAA,MACF;AAAA,MACA,YAAY;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AACV,CAAC;", "names": []}