{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/scheme.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Scheme\", \"fileTypes\": [\"scm\", \"ss\", \"sch\", \"rkt\"], \"name\": \"scheme\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#block-comment\" }, { \"include\": \"#sexp\" }, { \"include\": \"#string\" }, { \"include\": \"#language-functions\" }, { \"include\": \"#quote\" }, { \"include\": \"#illegal\" }], \"repository\": { \"block-comment\": { \"begin\": \"\\\\#\\\\|\", \"contentName\": \"comment\", \"end\": \"\\\\|\\\\#\", \"name\": \"comment\", \"patterns\": [{ \"include\": \"#block-comment\", \"name\": \"comment\" }] }, \"comment\": { \"begin\": \"(^[ \\\\t]+)?(?=;)\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.whitespace.comment.leading.scheme\" } }, \"end\": \"(?!\\\\G)\", \"patterns\": [{ \"begin\": \";\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.scheme\" } }, \"end\": \"\\\\n\", \"name\": \"comment.line.semicolon.scheme\" }] }, \"constants\": { \"patterns\": [{ \"match\": \"#[t|f]\", \"name\": \"constant.language.boolean.scheme\" }, { \"match\": `(?<=[\\\\(\\\\s])((#e|#i)?[0-9]+(\\\\.[0-9]+)?|(#x)[0-9a-fA-F]+|(#o)[0-7]+|(#b)[01]+)(?=[\\\\s;()'\",\\\\[\\\\]])`, \"name\": \"constant.numeric.scheme\" }] }, \"illegal\": { \"match\": \"[()\\\\[\\\\]]\", \"name\": \"invalid.illegal.parenthesis.scheme\" }, \"language-functions\": { \"patterns\": [{ \"match\": \"(?x)\\n(?<=(\\\\s|\\\\(|\\\\[))\\n( do|or|and|else|quasiquote|begin|if|case|set!|\\ncond|let|unquote|define|let\\\\*|unquote-splicing|delay|\\nletrec)\\n(?=(\\\\s|\\\\())\", \"name\": \"keyword.control.scheme\" }, { \"comment\": \"\\n\t\t\t\t\t\tThese functions run a test, and return a boolean\\n\t\t\t\t\t\tanswer.\\n\t\t\t\t\t\", \"match\": \"(?x)\\n(?<=(\\\\s|\\\\())\\n( char-alphabetic|char-lower-case|char-numeric|\\nchar-ready|char-upper-case|char-whitespace|\\n(?:char|string)(?:-ci)?(?:=|<=?|>=?)|\\natom|boolean|bound-identifier=|char|complex|\\nidentifier|integer|symbol|free-identifier=|inexact|\\neof-object|exact|list|(?:input|output)-port|pair|\\nreal|rational|zero|vector|negative|odd|null|string|\\neq|equal|eqv|even|number|positive|procedure\\n)\\n(\\\\?)\\n(?=(\\\\s|\\\\())\\n\", \"name\": \"support.function.boolean-test.scheme\" }, { \"comment\": \"\\n\t\t\t\t\t\tThese functions change one type into another.\\n\t\t\t\t\t\", \"match\": \"(?x)\\n(?<=(\\\\s|\\\\())\\n( char->integer|exact->inexact|inexact->exact|\\ninteger->char|symbol->string|list->vector|\\nlist->string|identifier->symbol|vector->list|\\nstring->list|string->number|string->symbol|\\nnumber->string\\n)\\n(?=(\\\\s|\\\\())\\n\", \"name\": \"support.function.convert-type.scheme\" }, { \"comment\": \"\\n\t\t\t\t\t\tThese functions are potentially dangerous because\\n\t\t\t\t\t\tthey have side-effects which could affect other\\n\t\t\t\t\t\tparts of the program.\\n\t\t\t\t\t\", \"match\": \"(?x)\\n(?<=(\\\\s|\\\\())\\n( set-(?:car|cdr)|\\n(?:vector|string)-(?:fill|set)\\n)\\n(!)\\n(?=(\\\\s|\\\\())\\n\", \"name\": \"support.function.with-side-effects.scheme\" }, { \"comment\": \"\\n\t\t\t\t\t\t+, -, *, /, =, >, etc. \\n\t\t\t\t\t\", \"match\": \"(?x)\\n(?<=(\\\\s|\\\\())\\n( >=?|<=?|=|[*/+-])\\n(?=(\\\\s|\\\\())\\n\", \"name\": \"keyword.operator.arithmetic.scheme\" }, { \"match\": \"(?x)\\n(?<=(\\\\s|\\\\())\\n( append|apply|approximate|\\ncall-with-current-continuation|call/cc|catch|\\nconstruct-identifier|define-syntax|display|foo|\\nfor-each|force|format|cd|gen-counter|gen-loser|\\ngenerate-identifier|last-pair|length|let-syntax|\\nletrec-syntax|list|list-ref|list-tail|load|log|\\nmacro|magnitude|map|map-streams|max|member|memq|\\nmemv|min|newline|nil|not|peek-char|rationalize|\\nread|read-char|return|reverse|sequence|substring|\\nsyntax|syntax-rules|transcript-off|transcript-on|\\ntruncate|unwrap-syntax|values-list|write|write-char|\\n\\n\\ncons|c(a|d){1,4}r|\\n\\n\\nabs|acos|angle|asin|assoc|assq|assv|atan|ceiling|\\ncos|floor|round|sin|sqrt|tan|\\n(?:real|imag)-part|numerator|denominator\\n\\n\\nmodulo|exp|expt|remainder|quotient|lcm|\\n\\n\\ncall-with-(?:input|output)-file|\\n(?:close|current)-(?:input|output)-port|\\nwith-(?:input|output)-from-file|\\nopen-(?:input|output)-file|\\n\\n\\nchar-(?:downcase|upcase|ready)|\\n\\n\\nmake-(?:polar|promise|rectangular|string|vector)\\n\\n\\nstring(?:-(?:append|copy|length|ref))?|\\nvector(?:-length|-ref)\\n)\\n(?=(\\\\s|\\\\())\\n\", \"name\": \"support.function.general.scheme\" }] }, \"quote\": { \"comment\": \"\\n\t\t\t\tWe need to be able to quote any kind of item, which creates\\n\t\t\t\ta tiny bit of complexity in our grammar.  It is hopefully\\n\t\t\t\tnot overwhelming complexity.\\n\t\t\t\t\\n\t\t\t\tNote: the first two matches are special cases.  quoted\\n\t\t\t\tsymbols, and quoted empty lists are considered constant.other\\n\t\t\t\t\\n\t\t\t\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.section.quoted.symbol.scheme\" } }, \"match\": \"(?x)\\n(')\\\\s*\\n([[:alnum:]][[:alnum:]!$%&*+-./:<=>?@^_~]*)\\n\", \"name\": \"constant.other.symbol.scheme\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.section.quoted.empty-list.scheme\" }, \"2\": { \"name\": \"meta.expression.scheme\" }, \"3\": { \"name\": \"punctuation.section.expression.begin.scheme\" }, \"4\": { \"name\": \"punctuation.section.expression.end.scheme\" } }, \"match\": \"(?x)\\n(')\\\\s*\\n((\\\\()\\\\s*(\\\\)))\\n\", \"name\": \"constant.other.empty-list.schem\" }, { \"begin\": \"(')\\\\s*\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.quoted.scheme\" } }, \"comment\": \"quoted double-quoted string or s-expression\", \"end\": \"(?=[\\\\s()])|(?<=\\\\n)\", \"name\": \"string.other.quoted-object.scheme\", \"patterns\": [{ \"include\": \"#quoted\" }] }] }, \"quote-sexp\": { \"begin\": \"(?<=\\\\()\\\\s*(quote)\\\\s+\", \"beginCaptures\": { \"1\": { \"name\": \"keyword.control.quote.scheme\" } }, \"comment\": \"\\n\t\t\t\tSomething quoted with (quote \\xABthing\\xBB).  In this case \\xABthing\\xBB\\n\t\t\t\twill not be evaluated, so we are considering it a string.\\n\t\t\t\", \"contentName\": \"string.other.quote.scheme\", \"end\": \"(?=[\\\\s)])|(?<=\\\\n)\", \"patterns\": [{ \"include\": \"#quoted\" }] }, \"quoted\": { \"patterns\": [{ \"include\": \"#string\" }, { \"begin\": \"(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.expression.begin.scheme\" } }, \"end\": \"(\\\\))\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.expression.end.scheme\" } }, \"name\": \"meta.expression.scheme\", \"patterns\": [{ \"include\": \"#quoted\" }] }, { \"include\": \"#quote\" }, { \"include\": \"#illegal\" }] }, \"sexp\": { \"begin\": \"(\\\\()\", \"beginCaptures\": { \"1\": { \"name\": \"punctuation.section.expression.begin.scheme\" } }, \"end\": \"(\\\\))(\\\\n)?\", \"endCaptures\": { \"1\": { \"name\": \"punctuation.section.expression.end.scheme\" }, \"2\": { \"name\": \"meta.after-expression.scheme\" } }, \"name\": \"meta.expression.scheme\", \"patterns\": [{ \"include\": \"#comment\" }, { \"begin\": \"(?x)\\n(?<=\\\\()\\n(define)\\\\s+\\n(\\\\()\\n([[:alnum:]][[:alnum:]!$%&*+-./:<=>?@^_~]*)\\n((\\\\s+\\n([[:alnum:]][[:alnum:]!$%&*+-./:<=>?@^_~]*|[._])\\n)*\\n)\\\\s*\\n(\\\\))\\n\", \"captures\": { \"1\": { \"name\": \"keyword.control.scheme\" }, \"2\": { \"name\": \"punctuation.definition.function.scheme\" }, \"3\": { \"name\": \"entity.name.function.scheme\" }, \"4\": { \"name\": \"variable.parameter.function.scheme\" }, \"7\": { \"name\": \"punctuation.definition.function.scheme\" } }, \"end\": \"(?=\\\\))\", \"name\": \"meta.declaration.procedure.scheme\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#sexp\" }, { \"include\": \"#illegal\" }] }, { \"begin\": \"(?x)\\n(?<=\\\\()\\n(lambda)\\\\s+\\n(\\\\()\\n((?:\\n([[:alnum:]][[:alnum:]!$%&*+-./:<=>?@^_~]*|[._])\\n\\\\s+\\n)*(?:\\n([[:alnum:]][[:alnum:]!$%&*+-./:<=>?@^_~]*|[._])\\n)?)\\n(\\\\))\\n\", \"captures\": { \"1\": { \"name\": \"keyword.control.scheme\" }, \"2\": { \"name\": \"punctuation.definition.variable.scheme\" }, \"3\": { \"name\": \"variable.parameter.scheme\" }, \"6\": { \"name\": \"punctuation.definition.variable.scheme\" } }, \"comment\": \"\\n\t\t\t\t\t\tNot sure this one is quite correct.  That \\\\s* is\\n\t\t\t\t\t\tparticularly troubling\\n\t\t\t\t\t\", \"end\": \"(?=\\\\))\", \"name\": \"meta.declaration.procedure.scheme\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#sexp\" }, { \"include\": \"#illegal\" }] }, { \"begin\": \"(?<=\\\\()(define)\\\\s([[:alnum:]][[:alnum:]!$%&*+-./:<=>?@^_~]*)\\\\s*.*?\", \"captures\": { \"1\": { \"name\": \"keyword.control.scheme\" }, \"2\": { \"name\": \"variable.other.scheme\" } }, \"end\": \"(?=\\\\))\", \"name\": \"meta.declaration.variable.scheme\", \"patterns\": [{ \"include\": \"#comment\" }, { \"include\": \"#sexp\" }, { \"include\": \"#illegal\" }] }, { \"include\": \"#quote-sexp\" }, { \"include\": \"#quote\" }, { \"include\": \"#language-functions\" }, { \"include\": \"#string\" }, { \"include\": \"#constants\" }, { \"match\": \"(?<=[\\\\(\\\\s])(#\\\\\\\\)(space|newline|tab)(?=[\\\\s\\\\)])\", \"name\": \"constant.character.named.scheme\" }, { \"match\": \"(?<=[\\\\(\\\\s])(#\\\\\\\\)x[0-9A-F]{2,4}(?=[\\\\s\\\\)])\", \"name\": \"constant.character.hex-literal.scheme\" }, { \"match\": \"(?<=[\\\\(\\\\s])(#\\\\\\\\).(?=[\\\\s\\\\)])\", \"name\": \"constant.character.escape.scheme\" }, { \"comment\": \"\\n\t\t\t\t\t\tthe . in (a . b) which conses together two elements\\n\t\t\t\t\t\ta and b. (a b c) == (a . (b . (c . nil)))\\n\t\t\t\t\t\", \"match\": \"(?<=[ ()])\\\\.(?=[ ()])\", \"name\": \"punctuation.separator.cons.scheme\" }, { \"include\": \"#sexp\" }, { \"include\": \"#illegal\" }] }, \"string\": { \"begin\": '(\")', \"beginCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.begin.scheme\" } }, \"end\": '(\")', \"endCaptures\": { \"1\": { \"name\": \"punctuation.definition.string.end.scheme\" } }, \"name\": \"string.quoted.double.scheme\", \"patterns\": [{ \"match\": \"\\\\\\\\.\", \"name\": \"constant.character.escape.scheme\" }] } }, \"scopeName\": \"source.scheme\" });\nvar scheme = [\n  lang\n];\n\nexport { scheme as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,UAAU,aAAa,CAAC,OAAO,MAAM,OAAO,KAAK,GAAG,QAAQ,UAAU,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,iBAAiB,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,WAAW,CAAC,GAAG,cAAc,EAAE,iBAAiB,EAAE,SAAS,UAAU,eAAe,WAAW,OAAO,UAAU,QAAQ,WAAW,YAAY,CAAC,EAAE,WAAW,kBAAkB,QAAQ,UAAU,CAAC,EAAE,GAAG,WAAW,EAAE,SAAS,oBAAoB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,gDAAgD,EAAE,GAAG,OAAO,WAAW,YAAY,CAAC,EAAE,SAAS,KAAK,iBAAiB,EAAE,KAAK,EAAE,QAAQ,wCAAwC,EAAE,GAAG,OAAO,OAAO,QAAQ,gCAAgC,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,SAAS,UAAU,QAAQ,mCAAmC,GAAG,EAAE,SAAS,wGAAwG,QAAQ,0BAA0B,CAAC,EAAE,GAAG,WAAW,EAAE,SAAS,cAAc,QAAQ,qCAAqC,GAAG,sBAAsB,EAAE,YAAY,CAAC,EAAE,SAAS,6JAA6J,QAAQ,yBAAyB,GAAG,EAAE,WAAW,kFAAkF,SAAS,gbAAgb,QAAQ,uCAAuC,GAAG,EAAE,WAAW,gEAAgE,SAAS,oPAAoP,QAAQ,uCAAuC,GAAG,EAAE,WAAW,wJAAwJ,SAAS,qGAAqG,QAAQ,4CAA4C,GAAG,EAAE,WAAW,0CAA0C,SAAS,8DAA8D,QAAQ,qCAAqC,GAAG,EAAE,SAAS,gjCAAgjC,QAAQ,kCAAkC,CAAC,EAAE,GAAG,SAAS,EAAE,WAAW,sTAAsT,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,SAAS,gEAAgE,QAAQ,+BAA+B,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,yBAAyB,GAAG,KAAK,EAAE,QAAQ,8CAA8C,GAAG,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,SAAS,qCAAqC,QAAQ,kCAAkC,GAAG,EAAE,SAAS,WAAW,iBAAiB,EAAE,KAAK,EAAE,QAAQ,oCAAoC,EAAE,GAAG,WAAW,+CAA+C,OAAO,wBAAwB,QAAQ,qCAAqC,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,cAAc,EAAE,SAAS,2BAA2B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,WAAW,0IAAsJ,eAAe,6BAA6B,OAAO,uBAAuB,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,WAAW,UAAU,GAAG,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,OAAO,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,QAAQ,0BAA0B,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,QAAQ,EAAE,SAAS,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,OAAO,eAAe,eAAe,EAAE,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,QAAQ,0BAA0B,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,SAAS,kKAAkK,YAAY,EAAE,KAAK,EAAE,QAAQ,yBAAyB,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,qCAAqC,GAAG,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,OAAO,WAAW,QAAQ,qCAAqC,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,EAAE,SAAS,4KAA4K,YAAY,EAAE,KAAK,EAAE,QAAQ,yBAAyB,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,KAAK,EAAE,QAAQ,4BAA4B,GAAG,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,WAAW,kGAAkG,OAAO,WAAW,QAAQ,qCAAqC,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,EAAE,SAAS,yEAAyE,YAAY,EAAE,KAAK,EAAE,QAAQ,yBAAyB,GAAG,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,OAAO,WAAW,QAAQ,oCAAoC,YAAY,CAAC,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,SAAS,GAAG,EAAE,WAAW,sBAAsB,GAAG,EAAE,WAAW,UAAU,GAAG,EAAE,WAAW,aAAa,GAAG,EAAE,SAAS,uDAAuD,QAAQ,kCAAkC,GAAG,EAAE,SAAS,kDAAkD,QAAQ,wCAAwC,GAAG,EAAE,SAAS,qCAAqC,QAAQ,mCAAmC,GAAG,EAAE,WAAW,uHAAuH,SAAS,0BAA0B,QAAQ,oCAAoC,GAAG,EAAE,WAAW,QAAQ,GAAG,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,UAAU,EAAE,SAAS,OAAO,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,OAAO,eAAe,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,QAAQ,+BAA+B,YAAY,CAAC,EAAE,SAAS,SAAS,QAAQ,mCAAmC,CAAC,EAAE,EAAE,GAAG,aAAa,gBAAgB,CAAC;AACtyR,IAAI,SAAS;AAAA,EACX;AACF;", "names": []}