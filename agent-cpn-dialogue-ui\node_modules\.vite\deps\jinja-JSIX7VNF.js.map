{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/jinja.mjs"], "sourcesContent": ["import html from './html.mjs';\nimport './javascript.mjs';\nimport './css.mjs';\n\nconst lang$1 = Object.freeze({ \"displayName\": \"jinja-html\", \"firstLineMatch\": `^{% extends [\"'][^\"']+[\"'] %}`, \"foldingStartMarker\": \"(<(?i:(head|table|tr|div|style|script|ul|ol|form|dl))\\\\b.*?>|{%\\\\s*(block|filter|for|if|macro|raw))\", \"foldingStopMarker\": \"(</(?i:(head|table|tr|div|style|script|ul|ol|form|dl))\\\\b.*?>|{%\\\\s*(endblock|endfilter|endfor|endif|endmacro|endraw)\\\\s*%})\", \"name\": \"jinja-html\", \"patterns\": [{ \"include\": \"source.jinja\" }, { \"include\": \"text.html.basic\" }], \"scopeName\": \"text.html.jinja\", \"embeddedLangs\": [\"html\"] });\nvar jinja_html = [\n  ...html,\n  lang$1\n];\n\nconst lang = Object.freeze({ \"displayName\": \"Jinja\", \"foldingStartMarker\": \"({%\\\\s*(block|filter|for|if|macro|raw))\", \"foldingStopMarker\": \"({%\\\\s*(endblock|endfilter|endfor|endif|endmacro|endraw)\\\\s*%})\", \"name\": \"jinja\", \"patterns\": [{ \"begin\": \"({%)\\\\s*(raw)\\\\s*(%})\", \"captures\": { \"1\": { \"name\": \"entity.other.jinja.delimiter.tag\" }, \"2\": { \"name\": \"keyword.control.jinja\" }, \"3\": { \"name\": \"entity.other.jinja.delimiter.tag\" } }, \"end\": \"({%)\\\\s*(endraw)\\\\s*(%})\", \"name\": \"comment.block.jinja.raw\" }, { \"include\": \"#comments\" }, { \"begin\": \"{{-?\", \"captures\": [{ \"name\": \"variable.entity.other.jinja.delimiter\" }], \"end\": \"-?}}\", \"name\": \"variable.meta.scope.jinja\", \"patterns\": [{ \"include\": \"#expression\" }] }, { \"begin\": \"{%-?\", \"captures\": [{ \"name\": \"entity.other.jinja.delimiter.tag\" }], \"end\": \"-?%}\", \"name\": \"meta.scope.jinja.tag\", \"patterns\": [{ \"include\": \"#expression\" }] }], \"repository\": { \"comments\": { \"begin\": \"{#-?\", \"captures\": [{ \"name\": \"entity.other.jinja.delimiter.comment\" }], \"end\": \"-?#}\", \"name\": \"comment.block.jinja\", \"patterns\": [{ \"include\": \"#comments\" }] }, \"escaped_char\": { \"match\": \"\\\\\\\\x[0-9A-F]{2}\", \"name\": \"constant.character.escape.hex.jinja\" }, \"escaped_unicode_char\": { \"captures\": { \"1\": { \"name\": \"constant.character.escape.unicode.16-bit-hex.jinja\" }, \"2\": { \"name\": \"constant.character.escape.unicode.32-bit-hex.jinja\" }, \"3\": { \"name\": \"constant.character.escape.unicode.name.jinja\" } }, \"match\": \"(\\\\\\\\U[0-9A-Fa-f]{8})|(\\\\\\\\u[0-9A-Fa-f]{4})|(\\\\\\\\N\\\\{[a-zA-Z ]+\\\\})\" }, \"expression\": { \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"keyword.control.jinja\" }, \"2\": { \"name\": \"variable.other.jinja.block\" } }, \"match\": \"\\\\s*\\\\b(block)\\\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.jinja\" }, \"2\": { \"name\": \"variable.other.jinja.filter\" } }, \"match\": \"\\\\s*\\\\b(filter)\\\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.jinja\" }, \"2\": { \"name\": \"variable.other.jinja.test\" } }, \"match\": \"\\\\s*\\\\b(is)\\\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\\\b\" }, { \"captures\": { \"1\": { \"name\": \"keyword.control.jinja\" } }, \"match\": \"(?<=\\\\{\\\\%-|\\\\{\\\\%)\\\\s*\\\\b([a-zA-Z_][a-zA-Z0-9_]*)\\\\b(?!\\\\s*[,=])\" }, { \"match\": \"\\\\b(and|else|if|in|import|not|or|recursive|with(out)?\\\\s+context)\\\\b\", \"name\": \"keyword.control.jinja\" }, { \"match\": \"\\\\b(true|false|none)\\\\b\", \"name\": \"constant.language.jinja\" }, { \"match\": \"\\\\b(loop|super|self|varargs|kwargs)\\\\b\", \"name\": \"variable.language.jinja\" }, { \"match\": \"[a-zA-Z_][a-zA-Z0-9_]*\", \"name\": \"variable.other.jinja\" }, { \"match\": \"(\\\\+|\\\\-|\\\\*\\\\*|\\\\*|//|/|%)\", \"name\": \"keyword.operator.arithmetic.jinja\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.other.jinja\" }, \"2\": { \"name\": \"variable.other.jinja.filter\" } }, \"match\": \"(\\\\|)([a-zA-Z_][a-zA-Z0-9_]*)\" }, { \"captures\": { \"1\": { \"name\": \"punctuation.other.jinja\" }, \"2\": { \"name\": \"variable.other.jinja.attribute\" } }, \"match\": \"(\\\\.)([a-zA-Z_][a-zA-Z0-9_]*)\" }, { \"begin\": \"\\\\[\", \"captures\": [{ \"name\": \"punctuation.other.jinja\" }], \"end\": \"\\\\]\", \"patterns\": [{ \"include\": \"#expression\" }] }, { \"begin\": \"\\\\(\", \"captures\": [{ \"name\": \"punctuation.other.jinja\" }], \"end\": \"\\\\)\", \"patterns\": [{ \"include\": \"#expression\" }] }, { \"begin\": \"\\\\{\", \"captures\": [{ \"name\": \"punctuation.other.jinja\" }], \"end\": \"\\\\}\", \"patterns\": [{ \"include\": \"#expression\" }] }, { \"match\": \"(\\\\.|:|\\\\||,)\", \"name\": \"punctuation.other.jinja\" }, { \"match\": \"(==|<=|=>|<|>|!=)\", \"name\": \"keyword.operator.comparison.jinja\" }, { \"match\": \"=\", \"name\": \"keyword.operator.assignment.jinja\" }, { \"begin\": '\"', \"beginCaptures\": [{ \"name\": \"punctuation.definition.string.begin.jinja\" }], \"end\": '\"', \"endCaptures\": [{ \"name\": \"punctuation.definition.string.end.jinja\" }], \"name\": \"string.quoted.double.jinja\", \"patterns\": [{ \"include\": \"#string\" }] }, { \"begin\": \"'\", \"beginCaptures\": [{ \"name\": \"punctuation.definition.string.begin.jinja\" }], \"end\": \"'\", \"endCaptures\": [{ \"name\": \"punctuation.definition.string.end.jinja\" }], \"name\": \"string.quoted.single.jinja\", \"patterns\": [{ \"include\": \"#string\" }] }, { \"begin\": \"@/\", \"beginCaptures\": [{ \"name\": \"punctuation.definition.regexp.begin.jinja\" }], \"end\": \"/\", \"endCaptures\": [{ \"name\": \"punctuation.definition.regexp.end.jinja\" }], \"name\": \"string.regexp.jinja\", \"patterns\": [{ \"include\": \"#simple_escapes\" }] }] }, \"simple_escapes\": { \"captures\": { \"1\": { \"name\": \"constant.character.escape.newline.jinja\" }, \"2\": { \"name\": \"constant.character.escape.backlash.jinja\" }, \"3\": { \"name\": \"constant.character.escape.double-quote.jinja\" }, \"4\": { \"name\": \"constant.character.escape.single-quote.jinja\" }, \"5\": { \"name\": \"constant.character.escape.bell.jinja\" }, \"6\": { \"name\": \"constant.character.escape.backspace.jinja\" }, \"7\": { \"name\": \"constant.character.escape.formfeed.jinja\" }, \"8\": { \"name\": \"constant.character.escape.linefeed.jinja\" }, \"9\": { \"name\": \"constant.character.escape.return.jinja\" }, \"10\": { \"name\": \"constant.character.escape.tab.jinja\" }, \"11\": { \"name\": \"constant.character.escape.vertical-tab.jinja\" } }, \"match\": `(\\\\\\\\\\\\n)|(\\\\\\\\\\\\\\\\)|(\\\\\\\\\\\\\")|(\\\\\\\\')|(\\\\\\\\a)|(\\\\\\\\b)|(\\\\\\\\f)|(\\\\\\\\n)|(\\\\\\\\r)|(\\\\\\\\t)|(\\\\\\\\v)` }, \"string\": { \"patterns\": [{ \"include\": \"#simple_escapes\" }, { \"include\": \"#escaped_char\" }, { \"include\": \"#escaped_unicode_char\" }] } }, \"scopeName\": \"source.jinja\", \"embeddedLangs\": [\"jinja-html\"] });\nvar jinja = [\n  ...jinja_html,\n  lang\n];\n\nexport { jinja as default };\n"], "mappings": ";;;;;;;;AAIA,IAAM,SAAS,OAAO,OAAO,EAAE,eAAe,cAAc,kBAAkB,iCAAiC,sBAAsB,uGAAuG,qBAAqB,gIAAgI,QAAQ,cAAc,YAAY,CAAC,EAAE,WAAW,eAAe,GAAG,EAAE,WAAW,kBAAkB,CAAC,GAAG,aAAa,mBAAmB,iBAAiB,CAAC,MAAM,EAAE,CAAC;AACjiB,IAAI,aAAa;AAAA,EACf,GAAG;AAAA,EACH;AACF;AAEA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,SAAS,sBAAsB,2CAA2C,qBAAqB,mEAAmE,QAAQ,SAAS,YAAY,CAAC,EAAE,SAAS,yBAAyB,YAAY,EAAE,KAAK,EAAE,QAAQ,mCAAmC,GAAG,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,mCAAmC,EAAE,GAAG,OAAO,4BAA4B,QAAQ,0BAA0B,GAAG,EAAE,WAAW,YAAY,GAAG,EAAE,SAAS,QAAQ,YAAY,CAAC,EAAE,QAAQ,wCAAwC,CAAC,GAAG,OAAO,QAAQ,QAAQ,6BAA6B,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,QAAQ,YAAY,CAAC,EAAE,QAAQ,mCAAmC,CAAC,GAAG,OAAO,QAAQ,QAAQ,wBAAwB,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,YAAY,EAAE,SAAS,QAAQ,YAAY,CAAC,EAAE,QAAQ,uCAAuC,CAAC,GAAG,OAAO,QAAQ,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,YAAY,CAAC,EAAE,GAAG,gBAAgB,EAAE,SAAS,oBAAoB,QAAQ,sCAAsC,GAAG,wBAAwB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qDAAqD,GAAG,KAAK,EAAE,QAAQ,qDAAqD,GAAG,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,SAAS,sEAAsE,GAAG,cAAc,EAAE,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,6BAA6B,EAAE,GAAG,SAAS,gDAAgD,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,SAAS,iDAAiD,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wBAAwB,GAAG,KAAK,EAAE,QAAQ,4BAA4B,EAAE,GAAG,SAAS,6CAA6C,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,wBAAwB,EAAE,GAAG,SAAS,oEAAoE,GAAG,EAAE,SAAS,wEAAwE,QAAQ,wBAAwB,GAAG,EAAE,SAAS,2BAA2B,QAAQ,0BAA0B,GAAG,EAAE,SAAS,0CAA0C,QAAQ,0BAA0B,GAAG,EAAE,SAAS,0BAA0B,QAAQ,uBAAuB,GAAG,EAAE,SAAS,+BAA+B,QAAQ,oCAAoC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0BAA0B,GAAG,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,SAAS,gCAAgC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0BAA0B,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,SAAS,gCAAgC,GAAG,EAAE,SAAS,OAAO,YAAY,CAAC,EAAE,QAAQ,0BAA0B,CAAC,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,YAAY,CAAC,EAAE,QAAQ,0BAA0B,CAAC,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,YAAY,CAAC,EAAE,QAAQ,0BAA0B,CAAC,GAAG,OAAO,OAAO,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,iBAAiB,QAAQ,0BAA0B,GAAG,EAAE,SAAS,qBAAqB,QAAQ,oCAAoC,GAAG,EAAE,SAAS,KAAK,QAAQ,oCAAoC,GAAG,EAAE,SAAS,KAAK,iBAAiB,CAAC,EAAE,QAAQ,4CAA4C,CAAC,GAAG,OAAO,KAAK,eAAe,CAAC,EAAE,QAAQ,0CAA0C,CAAC,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,KAAK,iBAAiB,CAAC,EAAE,QAAQ,4CAA4C,CAAC,GAAG,OAAO,KAAK,eAAe,CAAC,EAAE,QAAQ,0CAA0C,CAAC,GAAG,QAAQ,8BAA8B,YAAY,CAAC,EAAE,WAAW,UAAU,CAAC,EAAE,GAAG,EAAE,SAAS,MAAM,iBAAiB,CAAC,EAAE,QAAQ,4CAA4C,CAAC,GAAG,OAAO,KAAK,eAAe,CAAC,EAAE,QAAQ,0CAA0C,CAAC,GAAG,QAAQ,uBAAuB,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,EAAE,CAAC,EAAE,GAAG,kBAAkB,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,0CAA0C,GAAG,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,+CAA+C,GAAG,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,4CAA4C,GAAG,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,yCAAyC,GAAG,MAAM,EAAE,QAAQ,sCAAsC,GAAG,MAAM,EAAE,QAAQ,+CAA+C,EAAE,GAAG,SAAS,iGAAiG,GAAG,UAAU,EAAE,YAAY,CAAC,EAAE,WAAW,kBAAkB,GAAG,EAAE,WAAW,gBAAgB,GAAG,EAAE,WAAW,wBAAwB,CAAC,EAAE,EAAE,GAAG,aAAa,gBAAgB,iBAAiB,CAAC,YAAY,EAAE,CAAC;AACztK,IAAI,QAAQ;AAAA,EACV,GAAG;AAAA,EACH;AACF;", "names": []}