{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/jsonnet.mjs"], "sourcesContent": ["const lang = Object.freeze({ \"displayName\": \"Jsonnet\", \"name\": \"jsonnet\", \"patterns\": [{ \"include\": \"#expression\" }, { \"include\": \"#keywords\" }], \"repository\": { \"builtin-functions\": { \"patterns\": [{ \"match\": \"\\\\bstd[.](acos|asin|atan|ceil|char|codepoint|cos|exp|exponent)\\\\b\", \"name\": \"support.function.jsonnet\" }, { \"match\": \"\\\\bstd[.](filter|floor|force|length|log|makeArray|mantissa)\\\\b\", \"name\": \"support.function.jsonnet\" }, { \"match\": \"\\\\bstd[.](objectFields|objectHas|pow|sin|sqrt|tan|type|thisFile)\\\\b\", \"name\": \"support.function.jsonnet\" }, { \"match\": \"\\\\bstd[.](acos|asin|atan|ceil|char|codepoint|cos|exp|exponent)\\\\b\", \"name\": \"support.function.jsonnet\" }, { \"match\": \"\\\\bstd[.](abs|assertEqual|escapeString(Bash|Dollars|Json|Python))\\\\b\", \"name\": \"support.function.jsonnet\" }, { \"match\": \"\\\\bstd[.](filterMap|flattenArrays|foldl|foldr|format|join)\\\\b\", \"name\": \"support.function.jsonnet\" }, { \"match\": \"\\\\bstd[.](lines|manifest(Ini|Python(Vars)?)|map|max|min|mod)\\\\b\", \"name\": \"support.function.jsonnet\" }, { \"match\": \"\\\\bstd[.](set|set(Diff|Inter|Member|Union)|sort)\\\\b\", \"name\": \"support.function.jsonnet\" }, { \"match\": \"\\\\bstd[.](range|split|stringChars|substr|toString|uniq)\\\\b\", \"name\": \"support.function.jsonnet\" }] }, \"comment\": { \"patterns\": [{ \"begin\": \"/\\\\*\", \"end\": \"\\\\*/\", \"name\": \"comment.block.jsonnet\" }, { \"match\": \"//.*$\", \"name\": \"comment.line.jsonnet\" }, { \"match\": \"#.*$\", \"name\": \"comment.block.jsonnet\" }] }, \"double-quoted-strings\": { \"begin\": '\"', \"end\": '\"', \"name\": \"string.quoted.double.jsonnet\", \"patterns\": [{ \"match\": '\\\\\\\\([\"\\\\\\\\/bfnrt]|(u[0-9a-fA-F]{4}))', \"name\": \"constant.character.escape.jsonnet\" }, { \"match\": '\\\\\\\\[^\"\\\\\\\\/bfnrtu]', \"name\": \"invalid.illegal.jsonnet\" }] }, \"expression\": { \"patterns\": [{ \"include\": \"#literals\" }, { \"include\": \"#comment\" }, { \"include\": \"#single-quoted-strings\" }, { \"include\": \"#double-quoted-strings\" }, { \"include\": \"#triple-quoted-strings\" }, { \"include\": \"#builtin-functions\" }, { \"include\": \"#functions\" }] }, \"functions\": { \"patterns\": [{ \"begin\": \"\\\\b([a-zA-Z_][a-z0-9A-Z_]*)\\\\s*\\\\(\", \"beginCaptures\": { \"1\": { \"name\": \"entity.name.function.jsonnet\" } }, \"end\": \"\\\\)\", \"name\": \"meta.function\", \"patterns\": [{ \"include\": \"#expression\" }] }] }, \"keywords\": { \"patterns\": [{ \"match\": \"[!:~\\\\+\\\\-&\\\\|\\\\^=<>\\\\*\\\\/%]\", \"name\": \"keyword.operator.jsonnet\" }, { \"match\": \"\\\\$\", \"name\": \"keyword.other.jsonnet\" }, { \"match\": \"\\\\b(self|super|import|importstr|local|tailstrict)\\\\b\", \"name\": \"keyword.other.jsonnet\" }, { \"match\": \"\\\\b(if|then|else|for|in|error|assert)\\\\b\", \"name\": \"keyword.control.jsonnet\" }, { \"match\": \"\\\\b(function)\\\\b\", \"name\": \"storage.type.jsonnet\" }, { \"match\": \"[a-zA-Z_][a-z0-9A-Z_]*\\\\s*(:::|\\\\+:::)\", \"name\": \"variable.parameter.jsonnet\" }, { \"match\": \"[a-zA-Z_][a-z0-9A-Z_]*\\\\s*(::|\\\\+::)\", \"name\": \"entity.name.type\" }, { \"match\": \"[a-zA-Z_][a-z0-9A-Z_]*\\\\s*(:|\\\\+:)\", \"name\": \"variable.parameter.jsonnet\" }] }, \"literals\": { \"patterns\": [{ \"match\": \"\\\\b(true|false|null)\\\\b\", \"name\": \"constant.language.jsonnet\" }, { \"match\": \"\\\\b(\\\\d+([Ee][+-]?\\\\d+)?)\\\\b\", \"name\": \"constant.numeric.jsonnet\" }, { \"match\": \"\\\\b\\\\d+[.]\\\\d*([Ee][+-]?\\\\d+)?\\\\b\", \"name\": \"constant.numeric.jsonnet\" }, { \"match\": \"\\\\b[.]\\\\d+([Ee][+-]?\\\\d+)?\\\\b\", \"name\": \"constant.numeric.jsonnet\" }] }, \"single-quoted-strings\": { \"begin\": \"'\", \"end\": \"'\", \"name\": \"string.quoted.double.jsonnet\", \"patterns\": [{ \"match\": \"\\\\\\\\(['\\\\\\\\/bfnrt]|(u[0-9a-fA-F]{4}))\", \"name\": \"constant.character.escape.jsonnet\" }, { \"match\": \"\\\\\\\\[^'\\\\\\\\/bfnrtu]\", \"name\": \"invalid.illegal.jsonnet\" }] }, \"triple-quoted-strings\": { \"patterns\": [{ \"begin\": \"\\\\|\\\\|\\\\|\", \"end\": \"\\\\|\\\\|\\\\|\", \"name\": \"string.quoted.triple.jsonnet\" }] } }, \"scopeName\": \"source.jsonnet\" });\nvar jsonnet = [\n  lang\n];\n\nexport { jsonnet as default };\n"], "mappings": ";;;AAAA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,WAAW,QAAQ,WAAW,YAAY,CAAC,EAAE,WAAW,cAAc,GAAG,EAAE,WAAW,YAAY,CAAC,GAAG,cAAc,EAAE,qBAAqB,EAAE,YAAY,CAAC,EAAE,SAAS,qEAAqE,QAAQ,2BAA2B,GAAG,EAAE,SAAS,kEAAkE,QAAQ,2BAA2B,GAAG,EAAE,SAAS,uEAAuE,QAAQ,2BAA2B,GAAG,EAAE,SAAS,qEAAqE,QAAQ,2BAA2B,GAAG,EAAE,SAAS,wEAAwE,QAAQ,2BAA2B,GAAG,EAAE,SAAS,iEAAiE,QAAQ,2BAA2B,GAAG,EAAE,SAAS,mEAAmE,QAAQ,2BAA2B,GAAG,EAAE,SAAS,uDAAuD,QAAQ,2BAA2B,GAAG,EAAE,SAAS,8DAA8D,QAAQ,2BAA2B,CAAC,EAAE,GAAG,WAAW,EAAE,YAAY,CAAC,EAAE,SAAS,QAAQ,OAAO,QAAQ,QAAQ,wBAAwB,GAAG,EAAE,SAAS,SAAS,QAAQ,uBAAuB,GAAG,EAAE,SAAS,QAAQ,QAAQ,wBAAwB,CAAC,EAAE,GAAG,yBAAyB,EAAE,SAAS,KAAK,OAAO,KAAK,QAAQ,gCAAgC,YAAY,CAAC,EAAE,SAAS,yCAAyC,QAAQ,oCAAoC,GAAG,EAAE,SAAS,uBAAuB,QAAQ,0BAA0B,CAAC,EAAE,GAAG,cAAc,EAAE,YAAY,CAAC,EAAE,WAAW,YAAY,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,yBAAyB,GAAG,EAAE,WAAW,qBAAqB,GAAG,EAAE,WAAW,aAAa,CAAC,EAAE,GAAG,aAAa,EAAE,YAAY,CAAC,EAAE,SAAS,sCAAsC,iBAAiB,EAAE,KAAK,EAAE,QAAQ,+BAA+B,EAAE,GAAG,OAAO,OAAO,QAAQ,iBAAiB,YAAY,CAAC,EAAE,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,gCAAgC,QAAQ,2BAA2B,GAAG,EAAE,SAAS,OAAO,QAAQ,wBAAwB,GAAG,EAAE,SAAS,wDAAwD,QAAQ,wBAAwB,GAAG,EAAE,SAAS,4CAA4C,QAAQ,0BAA0B,GAAG,EAAE,SAAS,oBAAoB,QAAQ,uBAAuB,GAAG,EAAE,SAAS,0CAA0C,QAAQ,6BAA6B,GAAG,EAAE,SAAS,wCAAwC,QAAQ,mBAAmB,GAAG,EAAE,SAAS,sCAAsC,QAAQ,6BAA6B,CAAC,EAAE,GAAG,YAAY,EAAE,YAAY,CAAC,EAAE,SAAS,2BAA2B,QAAQ,4BAA4B,GAAG,EAAE,SAAS,gCAAgC,QAAQ,2BAA2B,GAAG,EAAE,SAAS,qCAAqC,QAAQ,2BAA2B,GAAG,EAAE,SAAS,iCAAiC,QAAQ,2BAA2B,CAAC,EAAE,GAAG,yBAAyB,EAAE,SAAS,KAAK,OAAO,KAAK,QAAQ,gCAAgC,YAAY,CAAC,EAAE,SAAS,yCAAyC,QAAQ,oCAAoC,GAAG,EAAE,SAAS,uBAAuB,QAAQ,0BAA0B,CAAC,EAAE,GAAG,yBAAyB,EAAE,YAAY,CAAC,EAAE,SAAS,aAAa,OAAO,aAAa,QAAQ,+BAA+B,CAAC,EAAE,EAAE,GAAG,aAAa,iBAAiB,CAAC;AACroH,IAAI,UAAU;AAAA,EACZ;AACF;", "names": []}