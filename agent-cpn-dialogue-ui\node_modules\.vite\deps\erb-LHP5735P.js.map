{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/erb.mjs"], "sourcesContent": ["import html from './html.mjs';\nimport ruby from './ruby.mjs';\nimport './javascript.mjs';\nimport './css.mjs';\nimport './xml.mjs';\nimport './java.mjs';\nimport './sql.mjs';\nimport './c.mjs';\nimport './shellscript.mjs';\nimport './lua.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"ERB\", \"fileTypes\": [\"erb\", \"rhtml\", \"html.erb\"], \"injections\": { \"text.html.erb - (meta.embedded.block.erb | meta.embedded.line.erb | comment)\": { \"patterns\": [{ \"begin\": \"(^\\\\s*)(?=<%+#(?![^%]*%>))\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.comment.leading.erb\" } }, \"end\": \"(?!\\\\G)(\\\\s*$\\\\n)?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.comment.trailing.erb\" } }, \"patterns\": [{ \"include\": \"#comment\" }] }, { \"begin\": \"(^\\\\s*)(?=<%(?![^%]*%>))\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.embedded.leading.erb\" } }, \"end\": \"(?!\\\\G)(\\\\s*$\\\\n)?\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.whitespace.embedded.trailing.erb\" } }, \"patterns\": [{ \"include\": \"#tags\" }] }, { \"include\": \"#comment\" }, { \"include\": \"#tags\" }] } }, \"name\": \"erb\", \"patterns\": [{ \"include\": \"text.html.basic\" }], \"repository\": { \"comment\": { \"patterns\": [{ \"begin\": \"<%+#\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.begin.erb\" } }, \"end\": \"%>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.definition.comment.end.erb\" } }, \"name\": \"comment.block.erb\" }] }, \"tags\": { \"patterns\": [{ \"begin\": \"<%+(?!>)[-=]?(?![^%]*%>)\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.begin.erb\" } }, \"contentName\": \"source.ruby\", \"end\": \"(-?%)>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.erb\" }, \"1\": { \"name\": \"source.ruby\" } }, \"name\": \"meta.embedded.block.erb\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.erb\" } }, \"match\": \"(#).*?(?=-?%>)\", \"name\": \"comment.line.number-sign.erb\" }, { \"include\": \"source.ruby\" }] }, { \"begin\": \"<%+(?!>)[-=]?\", \"beginCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.begin.erb\" } }, \"contentName\": \"source.ruby\", \"end\": \"(-?%)>\", \"endCaptures\": { \"0\": { \"name\": \"punctuation.section.embedded.end.erb\" }, \"1\": { \"name\": \"source.ruby\" } }, \"name\": \"meta.embedded.line.erb\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.erb\" } }, \"match\": \"(#).*?(?=-?%>)\", \"name\": \"comment.line.number-sign.erb\" }, { \"include\": \"source.ruby\" }] }] } }, \"scopeName\": \"text.html.erb\", \"embeddedLangs\": [\"html\", \"ruby\"] });\nvar erb = [\n  ...html,\n  ...ruby,\n  lang\n];\n\nexport { erb as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAWA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,OAAO,aAAa,CAAC,OAAO,SAAS,UAAU,GAAG,cAAc,EAAE,gFAAgF,EAAE,YAAY,CAAC,EAAE,SAAS,8BAA8B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,6CAA6C,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,EAAE,SAAS,4BAA4B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,8CAA8C,EAAE,GAAG,OAAO,sBAAsB,eAAe,EAAE,KAAK,EAAE,QAAQ,+CAA+C,EAAE,GAAG,YAAY,CAAC,EAAE,WAAW,QAAQ,CAAC,EAAE,GAAG,EAAE,WAAW,WAAW,GAAG,EAAE,WAAW,QAAQ,CAAC,EAAE,EAAE,GAAG,QAAQ,OAAO,YAAY,CAAC,EAAE,WAAW,kBAAkB,CAAC,GAAG,cAAc,EAAE,WAAW,EAAE,YAAY,CAAC,EAAE,SAAS,QAAQ,iBAAiB,EAAE,KAAK,EAAE,QAAQ,2CAA2C,EAAE,GAAG,OAAO,MAAM,eAAe,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,QAAQ,oBAAoB,CAAC,EAAE,GAAG,QAAQ,EAAE,YAAY,CAAC,EAAE,SAAS,4BAA4B,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,eAAe,eAAe,OAAO,UAAU,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,cAAc,EAAE,GAAG,QAAQ,2BAA2B,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,SAAS,kBAAkB,QAAQ,+BAA+B,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,GAAG,EAAE,SAAS,iBAAiB,iBAAiB,EAAE,KAAK,EAAE,QAAQ,yCAAyC,EAAE,GAAG,eAAe,eAAe,OAAO,UAAU,eAAe,EAAE,KAAK,EAAE,QAAQ,uCAAuC,GAAG,KAAK,EAAE,QAAQ,cAAc,EAAE,GAAG,QAAQ,0BAA0B,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,qCAAqC,EAAE,GAAG,SAAS,kBAAkB,QAAQ,+BAA+B,GAAG,EAAE,WAAW,cAAc,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,aAAa,iBAAiB,iBAAiB,CAAC,QAAQ,MAAM,EAAE,CAAC;AAChrE,IAAI,MAAM;AAAA,EACR,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACF;", "names": []}