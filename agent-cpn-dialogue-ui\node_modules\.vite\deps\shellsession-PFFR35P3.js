import {
  shellscript
} from "./chunk-IV2JESD6.js";
import "./chunk-2LSFTFF7.js";

// node_modules/.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/shellsession.mjs
var lang = Object.freeze({ "displayName": "Shell Session", "fileTypes": ["sh-session"], "name": "shellsession", "patterns": [{ "captures": { "1": { "name": "entity.other.prompt-prefix.shell-session" }, "2": { "name": "punctuation.separator.prompt.shell-session" }, "3": { "name": "source.shell", "patterns": [{ "include": "source.shell" }] } }, "match": "(?x) ^ (?: ( (?:\\(\\S+\\)\\s*)? (?: sh\\S*?                       | \\w+\\S+[@:]\\S+(?:\\s+\\S+)? | \\[\\S+?[@:][^\\n]+?\\].*? ) ) \\s* )? ( [>$#%❯➜] | \\p{Greek} ) \\s+ (.*) $" }, { "match": "^.+$", "name": "meta.output.shell-session" }], "scopeName": "text.shell-session", "embeddedLangs": ["shellscript"], "aliases": ["console"] });
var shellsession = [
  ...shellscript,
  lang
];
export {
  shellsession as default
};
//# sourceMappingURL=shellsession-PFFR35P3.js.map
