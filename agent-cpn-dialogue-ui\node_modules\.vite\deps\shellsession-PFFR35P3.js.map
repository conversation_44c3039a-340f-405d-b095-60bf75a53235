{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/shellsession.mjs"], "sourcesContent": ["import shellscript from './shellscript.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"Shell Session\", \"fileTypes\": [\"sh-session\"], \"name\": \"shellsession\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"entity.other.prompt-prefix.shell-session\" }, \"2\": { \"name\": \"punctuation.separator.prompt.shell-session\" }, \"3\": { \"name\": \"source.shell\", \"patterns\": [{ \"include\": \"source.shell\" }] } }, \"match\": \"(?x) ^ (?: ( (?:\\\\(\\\\S+\\\\)\\\\s*)? (?: sh\\\\S*?                       | \\\\w+\\\\S+[@:]\\\\S+(?:\\\\s+\\\\S+)? | \\\\[\\\\S+?[@:][^\\\\n]+?\\\\].*? ) ) \\\\s* )? ( [>$#%\\u276F\\u279C] | \\\\p{Greek} ) \\\\s+ (.*) $\" }, { \"match\": \"^.+$\", \"name\": \"meta.output.shell-session\" }], \"scopeName\": \"text.shell-session\", \"embeddedLangs\": [\"shellscript\"], \"aliases\": [\"console\"] });\nvar shellsession = [\n  ...shellscript,\n  lang\n];\n\nexport { shellsession as default };\n"], "mappings": ";;;;;;AAEA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,iBAAiB,aAAa,CAAC,YAAY,GAAG,QAAQ,gBAAgB,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,2CAA2C,GAAG,KAAK,EAAE,QAAQ,6CAA6C,GAAG,KAAK,EAAE,QAAQ,gBAAgB,YAAY,CAAC,EAAE,WAAW,eAAe,CAAC,EAAE,EAAE,GAAG,SAAS,oLAA8L,GAAG,EAAE,SAAS,QAAQ,QAAQ,4BAA4B,CAAC,GAAG,aAAa,sBAAsB,iBAAiB,CAAC,aAAa,GAAG,WAAW,CAAC,SAAS,EAAE,CAAC;AAC7rB,IAAI,eAAe;AAAA,EACjB,GAAG;AAAA,EACH;AACF;", "names": []}