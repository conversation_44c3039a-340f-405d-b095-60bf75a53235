{"version": 3, "sources": ["../../.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/langs/git-rebase.mjs"], "sourcesContent": ["import shellscript from './shellscript.mjs';\n\nconst lang = Object.freeze({ \"displayName\": \"Git Rebase Message\", \"name\": \"git-rebase\", \"patterns\": [{ \"captures\": { \"1\": { \"name\": \"punctuation.definition.comment.git-rebase\" } }, \"match\": \"^\\\\s*(#).*$\\\\n?\", \"name\": \"comment.line.number-sign.git-rebase\" }, { \"captures\": { \"1\": { \"name\": \"support.function.git-rebase\" }, \"2\": { \"name\": \"constant.sha.git-rebase\" }, \"3\": { \"name\": \"meta.commit-message.git-rebase\" } }, \"match\": \"^\\\\s*(pick|p|reword|r|edit|e|squash|s|fixup|f|drop|d)\\\\s+([0-9a-f]+)\\\\s+(.*)$\", \"name\": \"meta.commit-command.git-rebase\" }, { \"captures\": { \"1\": { \"name\": \"support.function.git-rebase\" }, \"2\": { \"patterns\": [{ \"include\": \"source.shell\" }] } }, \"match\": \"^\\\\s*(exec|x)\\\\s+(.*)$\", \"name\": \"meta.commit-command.git-rebase\" }, { \"captures\": { \"1\": { \"name\": \"support.function.git-rebase\" } }, \"match\": \"^\\\\s*(break|b)\\\\s*$\", \"name\": \"meta.commit-command.git-rebase\" }], \"scopeName\": \"text.git-rebase\", \"embeddedLangs\": [\"shellscript\"] });\nvar gitRebase = [\n  ...shellscript,\n  lang\n];\n\nexport { gitRebase as default };\n"], "mappings": ";;;;;;AAEA,IAAM,OAAO,OAAO,OAAO,EAAE,eAAe,sBAAsB,QAAQ,cAAc,YAAY,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,4CAA4C,EAAE,GAAG,SAAS,mBAAmB,QAAQ,sCAAsC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,QAAQ,0BAA0B,GAAG,KAAK,EAAE,QAAQ,iCAAiC,EAAE,GAAG,SAAS,iFAAiF,QAAQ,iCAAiC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,GAAG,KAAK,EAAE,YAAY,CAAC,EAAE,WAAW,eAAe,CAAC,EAAE,EAAE,GAAG,SAAS,0BAA0B,QAAQ,iCAAiC,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,8BAA8B,EAAE,GAAG,SAAS,uBAAuB,QAAQ,iCAAiC,CAAC,GAAG,aAAa,mBAAmB,iBAAiB,CAAC,aAAa,EAAE,CAAC;AACj8B,IAAI,YAAY;AAAA,EACd,GAAG;AAAA,EACH;AACF;", "names": []}