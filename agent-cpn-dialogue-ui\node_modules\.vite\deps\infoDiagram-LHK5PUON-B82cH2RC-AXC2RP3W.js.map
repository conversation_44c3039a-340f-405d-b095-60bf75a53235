{"version": 3, "sources": ["../../.pnpm/vue-element-plus-x@1.3.2_ro_f64eaa4e19edf3f2ff51e1f90e13b5c3/node_modules/mermaid/dist/chunks/mermaid.core/infoDiagram-LHK5PUON.mjs"], "sourcesContent": ["import {\n  package_default\n} from \"./chunk-RKKUNAVE.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-P3VETL53.mjs\";\nimport {\n  __name,\n  configureSvgSize,\n  log\n} from \"./chunk-3XYRH5AP.mjs\";\n\n// src/diagrams/info/infoParser.ts\nimport { parse } from \"@mermaid-js/parser\";\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"info\", input);\n    log.debug(ast);\n  }, \"parse\")\n};\n\n// src/diagrams/info/infoDb.ts\nvar DEFAULT_INFO_DB = {\n  version: package_default.version + (true ? \"\" : \"-tiny\")\n};\nvar getVersion = /* @__PURE__ */ __name(() => DEFAULT_INFO_DB.version, \"getVersion\");\nvar db = {\n  getVersion\n};\n\n// src/diagrams/info/infoRenderer.ts\nvar draw = /* @__PURE__ */ __name((text, id, version) => {\n  log.debug(\"rendering info diagram\\n\" + text);\n  const svg = selectSvgElement(id);\n  configureSvgSize(svg, 100, 400, true);\n  const group = svg.append(\"g\");\n  group.append(\"text\").attr(\"x\", 100).attr(\"y\", 40).attr(\"class\", \"version\").attr(\"font-size\", 32).style(\"text-anchor\", \"middle\").text(`v${version}`);\n}, \"draw\");\nvar renderer = { draw };\n\n// src/diagrams/info/infoDiagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer\n};\nexport {\n  diagram\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAcA,IAAI,SAAS;EACX,OAAuB,OAAO,OAAO,UAAU;AAC7C,UAAM,MAAM,MAAM,MAAM,QAAQ,KAAK;AACrC,QAAI,MAAM,GAAG;EACf,GAAG,OAAO;AACZ;AAGA,IAAI,kBAAkB;EACpB,SAAS,gBAAgB,UAAkB;AAC7C;AACA,IAAI,aAA6B,OAAO,MAAM,gBAAgB,SAAS,YAAY;AACnF,IAAI,KAAK;EACP;AACF;AAGA,IAAI,OAAuB,OAAO,CAAC,MAAM,IAAI,YAAY;AACvD,MAAI,MAAM,6BAA6B,IAAI;AAC3C,QAAM,MAAM,iBAAiB,EAAE;AAC/B,mBAAiB,KAAK,KAAK,KAAK,IAAI;AACpC,QAAM,QAAQ,IAAI,OAAO,GAAG;AAC5B,QAAM,OAAO,MAAM,EAAE,KAAK,KAAK,GAAG,EAAE,KAAK,KAAK,EAAE,EAAE,KAAK,SAAS,SAAS,EAAE,KAAK,aAAa,EAAE,EAAE,MAAM,eAAe,QAAQ,EAAE,KAAK,IAAI,OAAO,EAAE;AACpJ,GAAG,MAAM;AACT,IAAI,WAAW,EAAE,KAAI;AAGlB,IAAC,UAAU;EACZ;EACA;EACA;AACF;", "names": []}