import "./chunk-2LSFTFF7.js";

// node_modules/.pnpm/shikiji@0.10.2/node_modules/shikiji/dist/themes/material-theme-darker.mjs
var materialThemeDarker = Object.freeze({
  "colors": {
    "activityBar.activeBorder": "#80CBC4",
    "activityBar.background": "#212121",
    "activityBar.border": "#21212160",
    "activityBar.dropBackground": "#f0717880",
    "activityBar.foreground": "#EEFFFF",
    "activityBarBadge.background": "#80CBC4",
    "activityBarBadge.foreground": "#000000",
    "badge.background": "#00000030",
    "badge.foreground": "#545454",
    "breadcrumb.activeSelectionForeground": "#80CBC4",
    "breadcrumb.background": "#212121",
    "breadcrumb.focusForeground": "#EEFFFF",
    "breadcrumb.foreground": "#676767",
    "breadcrumbPicker.background": "#212121",
    "button.background": "#61616150",
    "button.foreground": "#ffffff",
    "debugConsole.errorForeground": "#f07178",
    "debugConsole.infoForeground": "#89DDFF",
    "debugConsole.warningForeground": "#FFCB6B",
    "debugToolBar.background": "#212121",
    "diffEditor.insertedTextBackground": "#89DDFF20",
    "diffEditor.removedTextBackground": "#ff9cac20",
    "dropdown.background": "#212121",
    "dropdown.border": "#FFFFFF10",
    "editor.background": "#212121",
    "editor.findMatchBackground": "#000000",
    "editor.findMatchBorder": "#80CBC4",
    "editor.findMatchHighlight": "#EEFFFF",
    "editor.findMatchHighlightBackground": "#00000050",
    "editor.findMatchHighlightBorder": "#ffffff30",
    "editor.findRangeHighlightBackground": "#FFCB6B30",
    "editor.foreground": "#EEFFFF",
    "editor.lineHighlightBackground": "#00000050",
    "editor.lineHighlightBorder": "#00000000",
    "editor.rangeHighlightBackground": "#FFFFFF0d",
    "editor.selectionBackground": "#61616150",
    "editor.selectionHighlightBackground": "#FFCC0020",
    "editor.wordHighlightBackground": "#ff9cac30",
    "editor.wordHighlightStrongBackground": "#C3E88D30",
    "editorBracketMatch.background": "#212121",
    "editorBracketMatch.border": "#FFCC0050",
    "editorCursor.foreground": "#FFCC00",
    "editorError.foreground": "#f0717870",
    "editorGroup.border": "#00000030",
    "editorGroup.dropBackground": "#f0717880",
    "editorGroup.focusedEmptyBorder": "#f07178",
    "editorGroupHeader.tabsBackground": "#212121",
    "editorGutter.addedBackground": "#C3E88D60",
    "editorGutter.deletedBackground": "#f0717860",
    "editorGutter.modifiedBackground": "#82AAFF60",
    "editorHoverWidget.background": "#212121",
    "editorHoverWidget.border": "#FFFFFF10",
    "editorIndentGuide.activeBackground": "#424242",
    "editorIndentGuide.background": "#42424270",
    "editorInfo.foreground": "#82AAFF70",
    "editorLineNumber.activeForeground": "#676767",
    "editorLineNumber.foreground": "#424242",
    "editorLink.activeForeground": "#EEFFFF",
    "editorMarkerNavigation.background": "#EEFFFF05",
    "editorOverviewRuler.border": "#212121",
    "editorOverviewRuler.errorForeground": "#f0717840",
    "editorOverviewRuler.findMatchForeground": "#80CBC4",
    "editorOverviewRuler.infoForeground": "#82AAFF40",
    "editorOverviewRuler.warningForeground": "#FFCB6B40",
    "editorRuler.foreground": "#424242",
    "editorSuggestWidget.background": "#212121",
    "editorSuggestWidget.border": "#FFFFFF10",
    "editorSuggestWidget.foreground": "#EEFFFF",
    "editorSuggestWidget.highlightForeground": "#80CBC4",
    "editorSuggestWidget.selectedBackground": "#00000050",
    "editorWarning.foreground": "#FFCB6B70",
    "editorWhitespace.foreground": "#EEFFFF40",
    "editorWidget.background": "#212121",
    "editorWidget.border": "#80CBC4",
    "editorWidget.resizeBorder": "#80CBC4",
    "extensionBadge.remoteForeground": "#EEFFFF",
    "extensionButton.prominentBackground": "#C3E88D90",
    "extensionButton.prominentForeground": "#EEFFFF",
    "extensionButton.prominentHoverBackground": "#C3E88D",
    "focusBorder": "#FFFFFF00",
    "foreground": "#EEFFFF",
    "gitDecoration.conflictingResourceForeground": "#FFCB6B90",
    "gitDecoration.deletedResourceForeground": "#f0717890",
    "gitDecoration.ignoredResourceForeground": "#67676790",
    "gitDecoration.modifiedResourceForeground": "#82AAFF90",
    "gitDecoration.untrackedResourceForeground": "#C3E88D90",
    "input.background": "#2B2B2B",
    "input.border": "#FFFFFF10",
    "input.foreground": "#EEFFFF",
    "input.placeholderForeground": "#EEFFFF60",
    "inputOption.activeBackground": "#EEFFFF30",
    "inputOption.activeBorder": "#EEFFFF30",
    "inputValidation.errorBorder": "#f07178",
    "inputValidation.infoBorder": "#82AAFF",
    "inputValidation.warningBorder": "#FFCB6B",
    "list.activeSelectionBackground": "#212121",
    "list.activeSelectionForeground": "#80CBC4",
    "list.dropBackground": "#f0717880",
    "list.focusBackground": "#EEFFFF20",
    "list.focusForeground": "#EEFFFF",
    "list.highlightForeground": "#80CBC4",
    "list.hoverBackground": "#212121",
    "list.hoverForeground": "#FFFFFF",
    "list.inactiveSelectionBackground": "#00000030",
    "list.inactiveSelectionForeground": "#80CBC4",
    "listFilterWidget.background": "#00000030",
    "listFilterWidget.noMatchesOutline": "#00000030",
    "listFilterWidget.outline": "#00000030",
    "menu.background": "#212121",
    "menu.foreground": "#EEFFFF",
    "menu.selectionBackground": "#00000050",
    "menu.selectionBorder": "#00000030",
    "menu.selectionForeground": "#80CBC4",
    "menu.separatorBackground": "#EEFFFF",
    "menubar.selectionBackground": "#00000030",
    "menubar.selectionBorder": "#00000030",
    "menubar.selectionForeground": "#80CBC4",
    "notebook.focusedCellBorder": "#80CBC4",
    "notebook.inactiveFocusedCellBorder": "#80CBC450",
    "notificationLink.foreground": "#80CBC4",
    "notifications.background": "#212121",
    "notifications.foreground": "#EEFFFF",
    "panel.background": "#212121",
    "panel.border": "#21212160",
    "panel.dropBackground": "#EEFFFF",
    "panelTitle.activeBorder": "#80CBC4",
    "panelTitle.activeForeground": "#FFFFFF",
    "panelTitle.inactiveForeground": "#EEFFFF",
    "peekView.border": "#00000030",
    "peekViewEditor.background": "#EEFFFF05",
    "peekViewEditor.matchHighlightBackground": "#61616150",
    "peekViewEditorGutter.background": "#EEFFFF05",
    "peekViewResult.background": "#EEFFFF05",
    "peekViewResult.matchHighlightBackground": "#61616150",
    "peekViewResult.selectionBackground": "#67676770",
    "peekViewTitle.background": "#EEFFFF05",
    "peekViewTitleDescription.foreground": "#EEFFFF60",
    "pickerGroup.border": "#FFFFFF1a",
    "pickerGroup.foreground": "#80CBC4",
    "progressBar.background": "#80CBC4",
    "quickInput.background": "#212121",
    "quickInput.foreground": "#676767",
    "quickInput.list.focusBackground": "#EEFFFF20",
    "sash.hoverBorder": "#80CBC450",
    "scrollbar.shadow": "#00000030",
    "scrollbarSlider.activeBackground": "#80CBC4",
    "scrollbarSlider.background": "#EEFFFF20",
    "scrollbarSlider.hoverBackground": "#EEFFFF10",
    "selection.background": "#00000080",
    "settings.checkboxBackground": "#212121",
    "settings.checkboxForeground": "#EEFFFF",
    "settings.dropdownBackground": "#212121",
    "settings.dropdownForeground": "#EEFFFF",
    "settings.headerForeground": "#80CBC4",
    "settings.modifiedItemIndicator": "#80CBC4",
    "settings.numberInputBackground": "#212121",
    "settings.numberInputForeground": "#EEFFFF",
    "settings.textInputBackground": "#212121",
    "settings.textInputForeground": "#EEFFFF",
    "sideBar.background": "#212121",
    "sideBar.border": "#21212160",
    "sideBar.foreground": "#676767",
    "sideBarSectionHeader.background": "#212121",
    "sideBarSectionHeader.border": "#21212160",
    "sideBarTitle.foreground": "#EEFFFF",
    "statusBar.background": "#212121",
    "statusBar.border": "#21212160",
    "statusBar.debuggingBackground": "#C792EA",
    "statusBar.debuggingForeground": "#ffffff",
    "statusBar.foreground": "#616161",
    "statusBar.noFolderBackground": "#212121",
    "statusBarItem.activeBackground": "#f0717880",
    "statusBarItem.hoverBackground": "#54545420",
    "statusBarItem.remoteBackground": "#80CBC4",
    "statusBarItem.remoteForeground": "#000000",
    "tab.activeBackground": "#212121",
    "tab.activeBorder": "#80CBC4",
    "tab.activeForeground": "#FFFFFF",
    "tab.activeModifiedBorder": "#676767",
    "tab.border": "#212121",
    "tab.inactiveBackground": "#212121",
    "tab.inactiveForeground": "#676767",
    "tab.inactiveModifiedBorder": "#904348",
    "tab.unfocusedActiveBorder": "#545454",
    "tab.unfocusedActiveForeground": "#EEFFFF",
    "tab.unfocusedActiveModifiedBorder": "#c05a60",
    "tab.unfocusedInactiveModifiedBorder": "#904348",
    "terminal.ansiBlack": "#000000",
    "terminal.ansiBlue": "#82AAFF",
    "terminal.ansiBrightBlack": "#545454",
    "terminal.ansiBrightBlue": "#82AAFF",
    "terminal.ansiBrightCyan": "#89DDFF",
    "terminal.ansiBrightGreen": "#C3E88D",
    "terminal.ansiBrightMagenta": "#C792EA",
    "terminal.ansiBrightRed": "#f07178",
    "terminal.ansiBrightWhite": "#ffffff",
    "terminal.ansiBrightYellow": "#FFCB6B",
    "terminal.ansiCyan": "#89DDFF",
    "terminal.ansiGreen": "#C3E88D",
    "terminal.ansiMagenta": "#C792EA",
    "terminal.ansiRed": "#f07178",
    "terminal.ansiWhite": "#ffffff",
    "terminal.ansiYellow": "#FFCB6B",
    "terminalCursor.background": "#000000",
    "terminalCursor.foreground": "#FFCB6B",
    "textLink.activeForeground": "#EEFFFF",
    "textLink.foreground": "#80CBC4",
    "titleBar.activeBackground": "#212121",
    "titleBar.activeForeground": "#EEFFFF",
    "titleBar.border": "#21212160",
    "titleBar.inactiveBackground": "#212121",
    "titleBar.inactiveForeground": "#676767",
    "tree.indentGuidesStroke": "#424242",
    "widget.shadow": "#00000030"
  },
  "displayName": "Material Theme Darker",
  "name": "material-theme-darker",
  "semanticHighlighting": true,
  "tokenColors": [
    {
      "settings": {
        "background": "#212121",
        "foreground": "#EEFFFF"
      }
    },
    {
      "scope": "string",
      "settings": {
        "foreground": "#C3E88D"
      }
    },
    {
      "scope": "punctuation, constant.other.symbol",
      "settings": {
        "foreground": "#89DDFF"
      }
    },
    {
      "scope": "constant.character.escape, text.html constant.character.entity.named",
      "settings": {
        "foreground": "#EEFFFF"
      }
    },
    {
      "scope": "constant.language.boolean",
      "settings": {
        "foreground": "#ff9cac"
      }
    },
    {
      "scope": "constant.numeric",
      "settings": {
        "foreground": "#F78C6C"
      }
    },
    {
      "scope": "variable, variable.parameter, support.variable, variable.language, support.constant, meta.definition.variable entity.name.function, meta.function-call.arguments",
      "settings": {
        "foreground": "#EEFFFF"
      }
    },
    {
      "scope": "keyword.other",
      "settings": {
        "foreground": "#F78C6C"
      }
    },
    {
      "scope": "keyword, modifier, variable.language.this, support.type.object, constant.language",
      "settings": {
        "foreground": "#89DDFF"
      }
    },
    {
      "scope": "entity.name.function, support.function",
      "settings": {
        "foreground": "#82AAFF"
      }
    },
    {
      "scope": "storage.type, storage.modifier, storage.control",
      "settings": {
        "foreground": "#C792EA"
      }
    },
    {
      "scope": "support.module, support.node",
      "settings": {
        "fontStyle": "italic",
        "foreground": "#f07178"
      }
    },
    {
      "scope": "support.type, constant.other.key",
      "settings": {
        "foreground": "#FFCB6B"
      }
    },
    {
      "scope": "entity.name.type, entity.other.inherited-class, entity.other",
      "settings": {
        "foreground": "#FFCB6B"
      }
    },
    {
      "scope": "comment",
      "settings": {
        "fontStyle": "italic",
        "foreground": "#545454"
      }
    },
    {
      "scope": "comment punctuation.definition.comment, string.quoted.docstring",
      "settings": {
        "fontStyle": "italic",
        "foreground": "#545454"
      }
    },
    {
      "scope": "punctuation",
      "settings": {
        "foreground": "#89DDFF"
      }
    },
    {
      "scope": "entity.name, entity.name.type.class, support.type, support.class, meta.use",
      "settings": {
        "foreground": "#FFCB6B"
      }
    },
    {
      "scope": "variable.object.property, meta.field.declaration entity.name.function",
      "settings": {
        "foreground": "#f07178"
      }
    },
    {
      "scope": "meta.definition.method entity.name.function",
      "settings": {
        "foreground": "#f07178"
      }
    },
    {
      "scope": "meta.function entity.name.function",
      "settings": {
        "foreground": "#82AAFF"
      }
    },
    {
      "scope": "template.expression.begin, template.expression.end, punctuation.definition.template-expression.begin, punctuation.definition.template-expression.end",
      "settings": {
        "foreground": "#89DDFF"
      }
    },
    {
      "scope": "meta.embedded, source.groovy.embedded, meta.template.expression",
      "settings": {
        "foreground": "#EEFFFF"
      }
    },
    {
      "scope": "entity.name.tag.yaml",
      "settings": {
        "foreground": "#f07178"
      }
    },
    {
      "scope": "meta.object-literal.key, meta.object-literal.key string, support.type.property-name.json",
      "settings": {
        "foreground": "#f07178"
      }
    },
    {
      "scope": "constant.language.json",
      "settings": {
        "foreground": "#89DDFF"
      }
    },
    {
      "scope": "entity.other.attribute-name.class",
      "settings": {
        "foreground": "#FFCB6B"
      }
    },
    {
      "scope": "entity.other.attribute-name.id",
      "settings": {
        "foreground": "#F78C6C"
      }
    },
    {
      "scope": "source.css entity.name.tag",
      "settings": {
        "foreground": "#FFCB6B"
      }
    },
    {
      "scope": "support.type.property-name.css",
      "settings": {
        "foreground": "#B2CCD6"
      }
    },
    {
      "scope": "meta.tag, punctuation.definition.tag",
      "settings": {
        "foreground": "#89DDFF"
      }
    },
    {
      "scope": "entity.name.tag",
      "settings": {
        "foreground": "#f07178"
      }
    },
    {
      "scope": "entity.other.attribute-name",
      "settings": {
        "foreground": "#C792EA"
      }
    },
    {
      "scope": "punctuation.definition.entity.html",
      "settings": {
        "foreground": "#EEFFFF"
      }
    },
    {
      "scope": "markup.heading",
      "settings": {
        "foreground": "#89DDFF"
      }
    },
    {
      "scope": "text.html.markdown meta.link.inline, meta.link.reference",
      "settings": {
        "foreground": "#f07178"
      }
    },
    {
      "scope": "text.html.markdown beginning.punctuation.definition.list",
      "settings": {
        "foreground": "#89DDFF"
      }
    },
    {
      "scope": "markup.italic",
      "settings": {
        "fontStyle": "italic",
        "foreground": "#f07178"
      }
    },
    {
      "scope": "markup.bold",
      "settings": {
        "fontStyle": "bold",
        "foreground": "#f07178"
      }
    },
    {
      "scope": "markup.bold markup.italic, markup.italic markup.bold",
      "settings": {
        "fontStyle": "italic bold",
        "foreground": "#f07178"
      }
    },
    {
      "scope": "markup.fenced_code.block.markdown punctuation.definition.markdown",
      "settings": {
        "foreground": "#C3E88D"
      }
    },
    {
      "scope": "markup.inline.raw.string.markdown",
      "settings": {
        "foreground": "#C3E88D"
      }
    },
    {
      "scope": "keyword.other.definition.ini",
      "settings": {
        "foreground": "#f07178"
      }
    },
    {
      "scope": "entity.name.section.group-title.ini",
      "settings": {
        "foreground": "#89DDFF"
      }
    },
    {
      "scope": "source.cs meta.class.identifier storage.type",
      "settings": {
        "foreground": "#FFCB6B"
      }
    },
    {
      "scope": "source.cs meta.method.identifier entity.name.function",
      "settings": {
        "foreground": "#f07178"
      }
    },
    {
      "scope": "source.cs meta.method-call meta.method, source.cs entity.name.function",
      "settings": {
        "foreground": "#82AAFF"
      }
    },
    {
      "scope": "source.cs storage.type",
      "settings": {
        "foreground": "#FFCB6B"
      }
    },
    {
      "scope": "source.cs meta.method.return-type",
      "settings": {
        "foreground": "#FFCB6B"
      }
    },
    {
      "scope": "source.cs meta.preprocessor",
      "settings": {
        "foreground": "#545454"
      }
    },
    {
      "scope": "source.cs entity.name.type.namespace",
      "settings": {
        "foreground": "#EEFFFF"
      }
    },
    {
      "scope": "meta.jsx.children, SXNested",
      "settings": {
        "foreground": "#EEFFFF"
      }
    },
    {
      "scope": "support.class.component",
      "settings": {
        "foreground": "#FFCB6B"
      }
    },
    {
      "scope": "source.cpp meta.block variable.other",
      "settings": {
        "foreground": "#EEFFFF"
      }
    },
    {
      "scope": "source.python meta.member.access.python",
      "settings": {
        "foreground": "#f07178"
      }
    },
    {
      "scope": "source.python meta.function-call.python, meta.function-call.arguments",
      "settings": {
        "foreground": "#82AAFF"
      }
    },
    {
      "scope": "meta.block",
      "settings": {
        "foreground": "#f07178"
      }
    },
    {
      "scope": "entity.name.function.call",
      "settings": {
        "foreground": "#82AAFF"
      }
    },
    {
      "scope": "source.php support.other.namespace, source.php meta.use support.class",
      "settings": {
        "foreground": "#EEFFFF"
      }
    },
    {
      "scope": "constant.keyword",
      "settings": {
        "fontStyle": "italic",
        "foreground": "#89DDFF"
      }
    },
    {
      "scope": "entity.name.function",
      "settings": {
        "foreground": "#82AAFF"
      }
    },
    {
      "settings": {
        "background": "#212121",
        "foreground": "#EEFFFF"
      }
    },
    {
      "scope": [
        "constant.other.placeholder"
      ],
      "settings": {
        "foreground": "#f07178"
      }
    },
    {
      "scope": [
        "markup.deleted"
      ],
      "settings": {
        "foreground": "#f07178"
      }
    },
    {
      "scope": [
        "markup.inserted"
      ],
      "settings": {
        "foreground": "#C3E88D"
      }
    },
    {
      "scope": [
        "markup.underline"
      ],
      "settings": {
        "fontStyle": "underline"
      }
    },
    {
      "scope": [
        "keyword.control"
      ],
      "settings": {
        "fontStyle": "italic",
        "foreground": "#89DDFF"
      }
    },
    {
      "scope": [
        "variable.parameter"
      ],
      "settings": {
        "fontStyle": "italic"
      }
    },
    {
      "scope": [
        "variable.parameter.function.language.special.self.python"
      ],
      "settings": {
        "fontStyle": "italic",
        "foreground": "#f07178"
      }
    },
    {
      "scope": [
        "constant.character.format.placeholder.other.python"
      ],
      "settings": {
        "foreground": "#F78C6C"
      }
    },
    {
      "scope": [
        "markup.quote"
      ],
      "settings": {
        "fontStyle": "italic",
        "foreground": "#89DDFF"
      }
    },
    {
      "scope": [
        "markup.fenced_code.block"
      ],
      "settings": {
        "foreground": "#EEFFFF90"
      }
    },
    {
      "scope": [
        "punctuation.definition.quote"
      ],
      "settings": {
        "foreground": "#ff9cac"
      }
    },
    {
      "scope": [
        "meta.structure.dictionary.json support.type.property-name.json"
      ],
      "settings": {
        "foreground": "#C792EA"
      }
    },
    {
      "scope": [
        "meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json"
      ],
      "settings": {
        "foreground": "#FFCB6B"
      }
    },
    {
      "scope": [
        "meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json"
      ],
      "settings": {
        "foreground": "#F78C6C"
      }
    },
    {
      "scope": [
        "meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json"
      ],
      "settings": {
        "foreground": "#f07178"
      }
    },
    {
      "scope": [
        "meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json"
      ],
      "settings": {
        "foreground": "#916b53"
      }
    },
    {
      "scope": [
        "meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json"
      ],
      "settings": {
        "foreground": "#82AAFF"
      }
    },
    {
      "scope": [
        "meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json"
      ],
      "settings": {
        "foreground": "#ff9cac"
      }
    },
    {
      "scope": [
        "meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json"
      ],
      "settings": {
        "foreground": "#C792EA"
      }
    },
    {
      "scope": [
        "meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json meta.structure.dictionary.value.json meta.structure.dictionary.json support.type.property-name.json"
      ],
      "settings": {
        "foreground": "#C3E88D"
      }
    }
  ],
  "type": "dark"
});
export {
  materialThemeDarker as default
};
//# sourceMappingURL=material-theme-darker-RIGJJ7MF.js.map
